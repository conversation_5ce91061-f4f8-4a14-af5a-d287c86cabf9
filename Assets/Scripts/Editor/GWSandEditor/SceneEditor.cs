using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace War.UI
{
    public class SceneEditor : EditorWindow
    {
        private Vector3 angle;
        
        [MenuItem("Tools/Scene/设置scene角度")]
        static void OpenWnd()
        {
            GetWindow<SceneEditor>().Show();
        }

        private void OnGUI()
        {
            angle = EditorGUILayout.Vector3Field("角度", angle);
            if (GUILayout.Button("设置scene角度"))
            {
                SetAngle();
            }

            GUILayout.BeginHorizontal();
            if (GUILayout.But<PERSON>("还原Home"))
            {
                angle = new Vector3(40, -45, 0);
                SetAngle();
            }
            if (GUILayout.Button("还原Sand"))
            {
                angle = new Vector3(40, 0, 0);
                SetAngle();
            }
            GUILayout.EndHorizontal();
            
            Event e = Event.current;
            if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Return)
            {
                SetAngle();
                e.Use();
            }
        }

        private void SetAngle()
        {
            SceneView.lastActiveSceneView.rotation = Quaternion.Euler (angle.x, angle.y, angle.z);
        }
    }
}