-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('msg_pb')


V1M=V(4,"MSG_LOGIN_MESSAGE_NTF",0,402)
V2M=V(4,"MSG_LOGIN_HANDSHAKE_REQ",1,403)
V3M=V(4,"MSG_LOGIN_LOGIN_REQ",2,404)
V4M=V(4,"MSG_LOGIN_ACTOR_INFO_NTF",3,405)
V5M=V(4,"MSG_LOGIN_CREATE_ACTOR_REQ",4,406)
V6M=V(4,"MSG_LOGIN_DELETE_ACTOR_REQ",5,407)
V7M=V(4,"MSG_LOGIN_SELECT_ACTOR_REQ",6,408)
V8M=V(4,"MSG_LOGIN_SWITCH_STATE_REQ",7,409)
V9M=V(4,"MSG_LOGIN_QUEUE_ORDER_NTF",8,410)
V10M=V(4,"MSG_LOGIN_REGISTERACCOUNT_REQ",9,411)
V11M=V(4,"MSG_LOGIN_REGISTERACCOUNT_RSP",10,412)
V12M=V(4,"MSG_LOGIN_ENTER_PLAYERNUM_NTF",11,413)
V13M=V(4,"MSG_LOGIN_AUTO_LOGIN_REQ",12,414)
V14M=V(4,"MSG_LOGIN_AUTO_LOGIN_REQ_V2",13,415)
V15M=V(4,"MSG_LOGIN_LOGIN_ERROR_NTF",14,416)
V16M=V(4,"MSG_LOGIN_UPDATE_ALSESSION_NTF",15,417)
V17M=V(4,"MSG_LOGIN_KICKOUT_NTF",16,418)
V18M=V(4,"MSG_LOGIN_NEW_ACCOUNT_NTF",17,419)
V19M=V(4,"MSG_LOGIN_HANDSHAKE_RSP",18,420)
V20M=V(4,"MSG_LOGIN_LOGIN_RSP",19,421)
V21M=V(4,"MSG_LOGIN_BAN_ACCOUNT_NTF",20,422)
V22M=V(4,"MSG_MAP_LOADMAP_NTF",21,430)
V23M=V(4,"MSG_MAP_DESTORYMAP_NTF",22,431)
V24M=V(4,"MSG_LUA_DEBUGINFO_NTF",23,470)
V25M=V(4,"MSG_LUA_RUN_FUNCA_REQ",24,471)
V26M=V(4,"MSG_LUA_RUN_FUNCB_REQ",25,472)
V27M=V(4,"MSG_LUA_RUN_MICRO_FUNCB_REQ",26,473)
V28M=V(4,"MSG_LUA_TRANSMIT_MICRO_REQ",27,474)
V29M=V(4,"MSG_LUA_RUN_FUNCB_NEW_REQ",28,475)
V30M=V(4,"MSG_PROP_PERSONOFFLINE_NTF",29,489)
V31M=V(4,"MSG_PROP_CREATEENTITY_NTF",30,490)
V32M=V(4,"MSG_PROP_DESTROYENTITY_NTF",31,491)
V33M=V(4,"MSG_PROP_UPDATE_NTF",32,493)
V34M=V(4,"MSG_ROGUE_ENTITY_CREATE_NTF",33,494)
V35M=V(4,"MSG_ROGUE_ENTITY_DEL_NTF",34,495)
V36M=V(4,"MSG_PROP_UPDATE_TOPIC_NTF",35,496)
V37M=V(4,"MSG_ZONE_SERVER_TIMESTAMP_NTF",36,497)
V38M=V(4,"MSG_PROP_UPDATE_TOPICS_NTF",37,498)
V39M=V(4,"MSG_ROLE_LOGIN_KEY_NTF",38,499)
V40M=V(4,"MSG_SKEP_CREATE_NTF",39,500)
V41M=V(4,"MSG_SKEP_DESTROY_NTF",40,501)
V42M=V(4,"MSG_GOODS_CREATE_NTF",41,510)
V43M=V(4,"MSG_GOODS_DESTROY_NTF",42,511)
V44M=V(4,"MSG_PAL_CREATE_NTF",43,520)
V45M=V(4,"MSG_PAL_DESTROY_NTF",44,521)
V46M=V(4,"MSG_ZONE_NEW_BATTLE_CLIENT_REQ",45,537)
V47M=V(4,"MSG_ZONE_COUNTER_NTF",46,540)
V48M=V(4,"MSG_ZONE_SERVER_EXIT_REQ",47,541)
V49M=V(4,"MSG_ZONE_SERVER_EXIT_RSP",48,542)
V50M=V(4,"MSG_ZONE_VOICESETTING_NTF",49,549)
V51M=V(4,"MSG_MATCH_INVITE_MEMBER_REQ",50,550)
V52M=V(4,"MSG_MATCH_INVITE_MEMBER_RSP",51,551)
V53M=V(4,"MSG_MATCH_BEGIN_MATCH_NTF",52,552)
V54M=V(4,"MSG_MATCH_EXIT_MATCH_NTF",53,553)
V55M=V(4,"MSG_MATCH_CANCEL_MATCH_NTF",54,554)
V56M=V(4,"MSG_MATCH_SET_PARAM_REQ",55,555)
V57M=V(4,"MSG_MATCH_START_GAME_NTF",56,556)
V58M=V(4,"MSG_MATCH_LOCK_NTF",57,557)
V59M=V(4,"MSG_MATCH_READY_ENTER_NTF",58,558)
V60M=V(4,"MSG_MATCH_ENTER_NTF",59,559)
V61M=V(4,"MSG_MATCH_ENTERQUEUE_REQ",60,560)
V62M=V(4,"MSG_MATCH_UNIT_INFO_NTF",61,561)
V63M=V(4,"MSG_MATCH_SEND_UNIT_REQ",62,562)
V64M=V(4,"MSG_MATCH_CONNECT_BATTLE_GATE_NTF",63,563)
V65M=V(4,"MSG_MATCH_RECONNECT_BATTLE_QUERY_NTF",64,564)
V66M=V(4,"MSG_MATCH_RECONNECT_BATTLE_ANSWER_REQ",65,565)
V67M=V(4,"MSG_MATCH_RECONNECT_BATTLE_ANSWER_RSP",66,566)
V68M=V(4,"MSG_MATCH_RECONNECT_BATTLE_CANCEL_REQ",67,567)
V69M=V(4,"MSG_MATCH_RECONNECT_BATTLE_CANCEL_RSP",68,568)
V70M=V(4,"MSG_GATEWAY_PING_REQ",69,600)
V71M=V(4,"MSG_GATEWAY_PING_RSP",70,601)
V72M=V(4,"MSG_GATEWAY_HANDSHAKE_NTF",71,602)
V73M=V(4,"MSG_GATEWAY_UCODE_NTF",72,603)
V74M=V(4,"MSG_GATEWAY_CONNECTSUCCESS_NTF",73,604)
V75M=V(4,"MSG_GATEWAY_KICKOUT_CLIENT",74,605)
V76M=V(4,"MSG_GATEWAY_TEST_PACKET_REQ",75,606)
V77M=V(4,"MSG_GATEWAY_TEST_PACKET_RSP",76,607)
V78M=V(4,"MSG_GATEWAY_TEST_PING_REQ",77,608)
V79M=V(4,"MSG_GATEWAY_TEST_PONG_RSP",78,609)
V80M=V(4,"MSG_GATEWAY_SYN_RST_NTF",79,610)
V81M=V(4,"MSG_GATEWAY_SYN_RST_RSP",80,611)
V82M=V(4,"MSG_GATEWAY_SYN_RUM_NTF",81,612)
V83M=V(4,"MSG_GATEWAY_SYN_CHK_NTF",82,613)
V84M=V(4,"MSG_GATEWAY_SYNC_CHK_RSP",83,614)
V85M=V(4,"MSG_GATEWAY_PING_RSP_RSP",84,615)
V86M=V(4,"MSG_GATEWAY_NETSPEED_TEST_REQ",85,616)
V87M=V(4,"MSG_GATEWAY_NETSPEED_TEST_RSP",86,617)
V88M=V(4,"MSG_GATEWAY_HANDSHAKE_STEP2_NTF",87,618)
V89M=V(4,"MSG_GATEWAY_POST_HACKINFO_NTF",88,619)
V90M=V(4,"MSG_TEAM_CREATE_NTF",89,750)
V91M=V(4,"MSG_TEAM_UPDATE_NTF",90,751)
V92M=V(4,"MSG_TEAM_ADD_PLAYER_NTF",91,752)
V93M=V(4,"MSG_TEAM_MODIFY_INFO_REQ",92,753)
V94M=V(4,"MSG_TEAM_MARK_NTF",93,754)
V95M=V(4,"MSG_SKYDIVE_START_NTF",94,800)
V96M=V(4,"MSG_SKYDIVE_MOVE_REQ",95,801)
V97M=V(4,"MSG_SKYDIVE_MOVE_RSP",96,802)
V98M=V(4,"MSG_SKYDIVE_AIR_NTF",97,803)
V99M=V(4,"MSG_SKYDIVE_LAND_NTF",98,804)
V100M=V(4,"MSG_GOODS_COMPOSE_REQ",99,1000)
V101M=V(4,"MSG_GOODS_COMPOSE_RSP",100,1001)
V102M=V(4,"MSG_GOODS_SELL_REQ",101,1002)
V103M=V(4,"MSG_GOODS_SELL_RSP",102,1003)
V104M=V(4,"MSG_GOODS_USEITEM_REQ",103,1004)
V105M=V(4,"MSG_GOODS_USEITEM_RSP",104,1005)
V106M=V(4,"MSG_GOODS_USEITEM_NTF",105,1006)
V107M=V(4,"MSG_GEMSTONE_UPGRADE_REQ",106,1007)
V108M=V(4,"MSG_GEMSTONE_UPGRADE_RSP",107,1008)
V109M=V(4,"MSG_GEMSTONE_HANDBOOK_ACTIVE_NTF",108,1009)
V110M=V(4,"MSG_HERO_EXCLUSIVE_ACTIVE_REQ",109,1010)
V111M=V(4,"MSG_HERO_EXCLUSIVE_ACTIVE_RSP",110,1011)
V112M=V(4,"MSG_HERO_EXCLUSIVE_UPGRADE_REQ",111,1012)
V113M=V(4,"MSG_HERO_EXCLUSIVE_UPGRADE_RSP",112,1013)
V114M=V(4,"MSG_GEMSTONE_HANDBOOK_NTF",113,1014)
V115M=V(4,"MSG_HERO_UPDATE_GEMSTONE_REQ",114,1015)
V116M=V(4,"MSG_HERO_UPDATE_GEMSTONE_RSP",115,1016)
V117M=V(4,"MSG_GOODS_GET_REASON_NTF",116,1017)
V118M=V(4,"MSG_EQUIP_UPGRADE_REQ",117,1018)
V119M=V(4,"MSG_EQUIP_UPGRADE_RSP",118,1019)
V120M=V(4,"MSG_EQUIP_DECOMPOSE_REQ",119,1020)
V121M=V(4,"MSG_EQUIP_DECOMPOSE_RSP",120,1021)
V122M=V(4,"MSG_EQUIP_LOCK_REQ",121,1022)
V123M=V(4,"MSG_EQUIP_LOCK_RSP",122,1023)
V124M=V(4,"MSG_EQUIP_RECAST_REQ",123,1024)
V125M=V(4,"MSG_EQUIP_RECAST_RSP",124,1025)
V126M=V(4,"MSG_EQUIP_RECAST_NTF",125,1026)
V127M=V(4,"MSG_EQUIP_RECAST_SAVE_REQ",126,1027)
V128M=V(4,"MSG_EQUIP_RECAST_SAVE_RSP",127,1028)
V129M=V(4,"MSG_GOODS_PLACE_HOLDER_END",128,1050)
V130M=V(4,"MSG_HERO_UPGRADE_REQ",129,1051)
V131M=V(4,"MSG_HERO_EQUIPMENT_UPDATE_REQ",130,1052)
V132M=V(4,"MSG_HERO_EQUIPMENT_UPDATE_RSP",131,1053)
V133M=V(4,"MSG_HERO_ADVANCE_REQ",132,1054)
V134M=V(4,"MSG_HERO_ADVANCE_RSP",133,1055)
V135M=V(4,"MSG_HERO_SKILL_UPDATE_REQ",134,1056)
V136M=V(4,"MSG_HERO_SKILL_UPDATE_RSP",135,1057)
V137M=V(4,"MSG_HERO_SKILL_UPGRADE_REQ",136,1058)
V138M=V(4,"MSG_HERO_SKILL_UPGRADE_RSP",137,1059)
V139M=V(4,"MSG_HERO_UPGRADE_RSP",138,1060)
V140M=V(4,"MSG_HERO_AWAKEN_REQ",139,1061)
V141M=V(4,"MSG_HERO_AWAKEN_RSP",140,1062)
V142M=V(4,"MSG_EQUIP_DEVOUR_REQ",141,1063)
V143M=V(4,"MSG_EQUIP_DEVOUR_RSP",142,1064)
V144M=V(4,"MSG_HERO_COMPOSE_REQ",143,1065)
V145M=V(4,"MSG_HERO_COMPOSE_RSP",144,1066)
V146M=V(4,"MSG_BUY_HERO_LIST_REQ",145,1067)
V147M=V(4,"MSG_BUY_HERO_LIST_RSP",146,1068)
V148M=V(4,"MSG_HERO_GOODS_REVERT_NTF",147,1069)
V149M=V(4,"MSG_HERO_CHANGE_REQ",148,1070)
V150M=V(4,"MSG_HERO_CHANGE_RSP",149,1071)
V151M=V(4,"MSG_HERO_CHANGE_RESTORE_REQ",150,1072)
V152M=V(4,"MSG_HERO_CHANGE_RESTORE_RSP",151,1073)
V153M=V(4,"MSG_HERO_TALENT_SKILL_ACTIVE_REQ",152,1074)
V154M=V(4,"MSG_HERO_TALENT_SKILL_ACTIVE_RSP",153,1075)
V155M=V(4,"MSG_HERO_HANDBOOK_LV_LIST_NTF",154,1076)
V156M=V(4,"MSG_HERO_HANDBOOK_LV_NTF",155,1077)
V157M=V(4,"MSG_HERO_HANDBOOK_UPGRADE_REQ",156,1078)
V158M=V(4,"MSG_HERO_HANDBOOK_UPGRADE_RSP",157,1079)
V159M=V(4,"MSG_HERO_ACTIVE_AWAKENSKILL_REQ",158,1080)
V160M=V(4,"MSG_HERO_ACTIVE_AWAKENSKILL_RSP",159,1081)
V161M=V(4,"MSG_HERO_FAST_UPGRADE_REQ",160,1082)
V162M=V(4,"MSG_HERO_FAST_UPGRADE_RSP",161,1083)
V163M=V(4,"MSG_HERO_DECO_UPDATE_REQ",162,1084)
V164M=V(4,"MSG_HERO_DECO_UPDATE_RSP",163,1085)
V165M=V(4,"MSG_HERO_PLACE_HOLDER_END",164,1200)
V166M=V(4,"MSG_BATTLE_FRAME_CMD_NTF",165,1201)
V167M=V(4,"MSG_BATTLE_ATTACK_CASTLE_REQ",166,1202)
V168M=V(4,"MSG_BATTLE_ATTACK_CASTLE_RSP",167,1203)
V169M=V(4,"MSG_BATTLE_CHANGEPERSONSTATE_REQ",168,1204)
V170M=V(4,"MSG_BATTLE_CHANGEPERSONSTATE_RSP",169,1205)
V171M=V(4,"MSG_BATTLE_SYNCALLFRAME_NTF",170,1206)
V172M=V(4,"MSG_BATTLE_RESULT_GAME_RESULT_NTF",171,1207)
V173M=V(4,"MSG_BATTLE_ENTER_FINAL_BATTLE_NTF",172,1208)
V174M=V(4,"MSG_BATTLE_FINAL_BATTLE_START_NTF",173,1209)
V175M=V(4,"MSG_BATTLE_TRUSTEE_REQ",174,1210)
V176M=V(4,"MSG_BATTLE_TRUSTEE_RSP",175,1211)
V177M=V(4,"MSG_BATTLE_TRSUTEEREPORT_NTF",176,1212)
V178M=V(4,"MSG_BATTLE_TRSUTEEREPORT_REQ",177,1213)
V179M=V(4,"MSG_BATTLE_TRSUTEEREPORT_RSP",178,1214)
V180M=V(4,"MSG_BATTLE_RECORD_WATCH_REQ",179,1215)
V181M=V(4,"MSG_SYNC_BATTLE_RECORD_NTF",180,1216)
V182M=V(4,"MSG_BATTLE_FINAL_BATTLE_COUNTDOWN_NTF",181,1217)
V183M=V(4,"MSG_BATTLE_SYNC_CARD_NTF",182,1218)
V184M=V(4,"MSG_BATTLE_SYNC_USER_INFO_NTF",183,1219)
V185M=V(4,"MSG_BATTLE_CLEAN_NTF",184,1220)
V186M=V(4,"MSG_BATTLE_RSP_ATTACK_CASTLE_NTF",185,1221)
V187M=V(4,"MSG_BATTLE_REQUEST_SUPPORT_REQ",186,1222)
V188M=V(4,"MSG_BATTLE_REQUEST_SUPPORT_NTF",187,1223)
V189M=V(4,"MSG_BATTLE_TEAMMATE_REFUSE_SUPPORT_REQ",188,1224)
V190M=V(4,"MSG_BATTLE_TEAMMATE_REFUSE_SUPPORT_NTF",189,1225)
V191M=V(4,"MSG_BATTLE_TEAMMATE_ACCEPT_BATTLE_NTF",190,1226)
V192M=V(4,"MSG_BATTLE_RESULT",191,1227)
V193M=V(4,"MSG_BATTLE_BUFF_ACTIVE_NTF",192,1228)
V194M=V(4,"MSG_BATTLE_MANUALTOLOBBYNOTRECDC_NTF",193,1229)
V195M=V(4,"MSG_BATTLE_MAXID_END",194,1300)
V196M=V(4,"MSG_GOLD_FINGER_NTF",195,1301)
V197M=V(4,"MSG_GOLD_FINGER_STATE_CHANGE_NTF",196,1302)
V198M=V(4,"MSG_SHOP_ITEMLIST_REQ",197,1310)
V199M=V(4,"MSG_SHOP_ITEMLIST_RSP",198,1311)
V200M=V(4,"MSG_SHOP_BUYGOODS_REQ",199,1312)
V201M=V(4,"MSG_SHOP_BUYGOODS_RSP",200,1313)
V202M=V(4,"MSG_SHOP_FRESH_SHOPITEM_REQ",201,1314)
V203M=V(4,"MSG_SHOP_FRESH_SHOPITEM_RSP",202,1316)
V204M=V(4,"MSG_SHOP_ITEMLIST_NTF",203,1317)
V205M=V(4,"MSG_SHOP_MAXID_END",204,1350)
V206M=V(4,"MSG_ARENA_GET_TIME_REQ",205,1351)
V207M=V(4,"MSG_ARENA_GET_TIME_RSP",206,1352)
V208M=V(4,"MSG_ARENA_ENTER_REQ",207,1353)
V209M=V(4,"MSG_ARENA_ENTER_RSP",208,1354)
V210M=V(4,"MSG_ARENA_BATTLE_RECORD_REQ",209,1355)
V211M=V(4,"MSG_ARENA_BATTLE_RECORD_RSP",210,1356)
V212M=V(4,"MSG_ARENA_WATCH_PLAYBACK_REQ",211,1357)
V213M=V(4,"MSG_ARENA_WATCH_PLAYBACK_RSP",212,1358)
V214M=V(4,"MSG_ARENA_GET_REWARD_REQ",213,1359)
V215M=V(4,"MSG_ARENA_LAST_RANKING_LIST_REQ",214,1360)
V216M=V(4,"MSG_ARENA_LAST_RANKING_LIST_RSP",215,1361)
V217M=V(4,"MSG_ARENA_DEFEND_LINEUP_REQ",216,1362)
V218M=V(4,"MSG_ARENA_DEFEND_LINEUP_RSP",217,1363)
V219M=V(4,"MSG_PLAYER_DETAILS_REQ",218,1364)
V220M=V(4,"MSG_PLAYER_DETAILS_RSP",219,1365)
V221M=V(4,"MSG_ARENA_ERROR_NTF",220,1366)
V222M=V(4,"MSG_ARENA_CREATE_TEAM_REQ",221,1367)
V223M=V(4,"MSG_ARENA_CREATE_TEAM_RSP",222,1368)
V224M=V(4,"MSG_ARENA_KICK_MEMBER_REQ",223,1369)
V225M=V(4,"MSG_ARENA_KICK_MEMBER_RSP",224,1370)
V226M=V(4,"MSG_ARENA_TRANSFER_CAPTAIN_REQ",225,1371)
V227M=V(4,"MSG_ARENA_TRANSFER_CAPTAIN_RSP",226,1372)
V228M=V(4,"MSG_ARENA_DISMISS_TEAM_REQ",227,1373)
V229M=V(4,"MSG_ARENA_DISMISS_TEAM_RSP",228,1374)
V230M=V(4,"MSG_ARENA_JOIN_TEAM_REQ",229,1375)
V231M=V(4,"MSG_ARENA_JOIN_TEAM_RSP",230,1376)
V232M=V(4,"MSG_ARENA_WITHDRAW_TEAM_REQ",231,1377)
V233M=V(4,"MSG_ARENA_WITHDRAW_TEAM_RSP",232,1378)
V234M=V(4,"MSG_ARENA_ENROLL_REQ",233,1379)
V235M=V(4,"MSG_ARENA_ENROLL_RSP",234,1380)
V236M=V(4,"MSG_ARENA_APPROVE_JOIN_REQ",235,1381)
V237M=V(4,"MSG_ARENA_APPROVE_JOIN_RSP",236,1382)
V238M=V(4,"MSG_ARENA_DECLINE_JOIN_REQ",237,1383)
V239M=V(4,"MSG_ARENA_DECLINE_JOIN_RSP",238,1384)
V240M=V(4,"MSG_ARENA_APPLYLIST_REQ",239,1385)
V241M=V(4,"MSG_ARENA_APPLYLIST_RSP",240,1386)
V242M=V(4,"MSG_ARENA_IGNORE_APPLYLIST_REQ",241,1387)
V243M=V(4,"MSG_ARENA_IGNORE_APPLYLIST_RSP",242,1388)
V244M=V(4,"MSG_ARENA_GET_TEAMINFO_REQ",243,1389)
V245M=V(4,"MSG_ARENA_GET_TEAMINFO_RSP",244,1390)
V246M=V(4,"MSG_ARENA_ENTER_ASSEMBLY_HALL_REQ",245,1391)
V247M=V(4,"MSG_ARENA_ENTER_ASSEMBLY_HALL_RSP",246,1392)
V248M=V(4,"MSG_ARENA_GET_RANKINGLIST_REQ",247,1393)
V249M=V(4,"MSG_ARENA_GET_RANKINGLIST_RSP",248,1394)
V250M=V(4,"MSG_ARENA_GET_TEAMLINEUP_REQ",249,1395)
V251M=V(4,"MSG_ARENA_GET_TEAMLINEUP_RSP",250,1396)
V252M=V(4,"MSG_ARENA_SAVE_TEAMLINEUP_REQ",251,1397)
V253M=V(4,"MSG_ARENA_SAVE_TEAMLINEUP_RSP",252,1398)
V254M=V(4,"MSG_ARENA_NEW_EVENT_NTF",253,1399)
V255M=V(4,"MSG_ARENA_GET_RIVAL_REQ",254,1400)
V256M=V(4,"MSG_ARENA_GET_RIVAL_RSP",255,1401)
V257M=V(4,"MSG_ARENA_SET_DEFENCE_LINEUP_REQ",256,1402)
V258M=V(4,"MSG_ARENA_SET_DEFENCE_LINEUP_RSP",257,1403)
V259M=V(4,"MSG_ARENA_GET_BATTLE_ID_REQ",258,1404)
V260M=V(4,"MSG_ARENA_GET_BATTLE_ID_RSP",259,1405)
V261M=V(4,"MSG_ARENA_OPEN_INVITATION_REQ",260,1406)
V262M=V(4,"MSG_ARENA_OPEN_INVITATION_RSP",261,1407)
V263M=V(4,"MSG_ARENA_INVITE_JOINTEAM_REQ",262,1408)
V264M=V(4,"MSG_ARENA_INVITE_JOINTEAM_RSP",263,1409)
V265M=V(4,"MSG_ARENA_GET_INVITATIONLIST_REQ",264,1410)
V266M=V(4,"MSG_ARENA_GET_INVITATIONLIST_RSP",265,1411)
V267M=V(4,"MSG_AREAN_INVITATIONLIST_OPR_REQ",266,1412)
V268M=V(4,"MSG_AREAN_INVITATIONLIST_OPR_RSP",267,1413)
V269M=V(4,"MSG_AREAN_GET_HANGUP_REWARD_REQ",268,1414)
V270M=V(4,"MSG_AREAN_GET_HANGUP_REWARD_RSP",269,1415)
V271M=V(4,"MSG_ARENA_SAESON_START_NTF",270,1416)
V272M=V(4,"MSG_ARENA_GET_LEVEL_REWARD_REQ",271,1417)
V273M=V(4,"MSG_ARENA_GET_LEVEL_REWARD_RSP",272,1418)
V274M=V(4,"MSG_ARENA_GET_LEVEL_REWARD_INFO_REQ",273,1419)
V275M=V(4,"MSG_ARENA_GET_LEVEL_REWARD_INFO_RSP",274,1420)
V276M=V(4,"MSG_ARENA_GET_VICTORY_REWARD_REQ",275,1421)
V277M=V(4,"MSG_ARENA_GET_VICTORY_REWARD_RSP",276,1422)
V278M=V(4,"MSG_ARENA_REFRESH_BTN_STATE_REQ",277,1423)
V279M=V(4,"MSG_ARENA_REFRESH_BTN_STATE_RSP",278,1424)
V280M=V(4,"MSG_ARENA_CHANGE_RIVAL_LIST_REQ",279,1425)
V281M=V(4,"MSG_ARENA_CHANGE_RIVAL_LIST_RSP",280,1426)
V282M=V(4,"MSG_ARENA_GET_CURRENT_RIVAL_REQ",281,1427)
V283M=V(4,"MSG_ARENA_GET_CURRENT_RIVAL_RSP",282,1428)
V284M=V(4,"MSG_ARENA_GET_RANK_AND_REWARD_FLAG_REQ",283,1429)
V285M=V(4,"MSG_ARENA_GET_RANK_AND_REWARD_FLAG_RSP",284,1430)
V286M=V(4,"MSG_ARENA_BUY_TICKET_NUM_NTF",285,1431)
V287M=V(4,"MSG_ARENA_GET_RECORD_ID_REQ",286,1432)
V288M=V(4,"MSG_ARENA_GET_RECORD_ID_RSP",287,1433)
V289M=V(4,"MSG_ACTIVITY_DATA_REQ",288,1434)
V290M=V(4,"MSG_ACTIVITY_DATA_RSP",289,1435)
V291M=V(4,"MSG_ARENA_BUY_TICKET_REQ",290,1436)
V292M=V(4,"MSG_ARENA_BUY_TICKET_RSP",291,1437)
V293M=V(4,"MSG_ARENA_REFRESH_RIVAL_REQ",292,1438)
V294M=V(4,"MSG_ARENA_REFRESH_RIVAL_RSP",293,1439)
V295M=V(4,"MSG_ARENA_MAXID_END",294,1499)
V296M=V(4,"MSG_ZONE_ROLE_FACE_UPDATE_REQ",295,1500)
V297M=V(4,"MSG_ZONE_ROLE_FACE_UPDATE_RSP",296,1501)
V298M=V(4,"MSG_ZONE_ROLE_FACE_PROP_UPDATE_REQ",297,1502)
V299M=V(4,"MSG_ZONE_ROLE_FACE_PROP_UPDATE_RSP",298,1503)
V300M=V(4,"MSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF",299,1504)
V301M=V(4,"MSG_ZONE_ROLE_NEW_FACE_NTF",300,1505)
V302M=V(4,"MSG_ZONE_ROLE_UPDATE_NAME_REQ",301,1506)
V303M=V(4,"MSG_ZONE_ROLE_UPDATE_NAME_RSP",302,1507)
V304M=V(4,"MSG_ZONE_ROLE_FACE_PROP_UNLOAD_REQ",303,1508)
V305M=V(4,"MSG_ZONE_ROLE_FACE_PROP_UNLOAD_RSP",304,1509)
V306M=V(4,"MSG_ROLE_RESOURCE_STAT_NTF",305,1510)
V307M=V(4,"MSG_ROLE_CUSTOM_TITLE_NTF",306,1511)
V308M=V(4,"MSG_ROLE_NATIONALFLAG_STATE_NTF",307,1512)
V309M=V(4,"MSG_ROLE_NATIONALFLAG_MDF_REQ",308,1513)
V310M=V(4,"MSG_ROLE_NATIONALFLAG_MDF_RSP",309,1514)
V311M=V(4,"MSG_ZONE_ROLE_UPDATE_SEX_REQ",310,1515)
V312M=V(4,"MSG_ZONE_ROLE_UPDATE_SEX_RSP",311,1516)
V313M=V(4,"MSG_ZONE_ROLE_CITY_UPDATE_REQ",312,1517)
V314M=V(4,"MSG_ZONE_ROLE_CITY_UPDATE_RSP",313,1518)
V315M=V(4,"MSG_ZONE_ROLE_PRAISE_UPDATE_REQ",314,1519)
V316M=V(4,"MSG_ZONE_ROLE_PRAISE_UPDATE_RSP",315,1520)
V317M=V(4,"MSG_ZONE_ROLE_NEW_SCHLOSS_NTF",316,1521)
V318M=V(4,"MSG_ZONE_NEW_ROLEINFO_REQ",317,1522)
V319M=V(4,"MSG_ZONE_NEW_ROLEINFO_RSP",318,1523)
V320M=V(4,"MSG_CUSTOM_FACE_UPLOAD_REQ",319,1524)
V321M=V(4,"MSG_CUSTOM_FACE_UPLOAD_RSP",320,1525)
V322M=V(4,"MSG_CUSTOM_FACE_VERIFY_NTF",321,1526)
V323M=V(4,"MSG_CUSTOM_FACE_ACTIVATE_NTF",322,1527)
V324M=V(4,"MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ",323,1528)
V325M=V(4,"MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP",324,1529)
V326M=V(4,"MSG_CUSTOM_FACE_REMOVE_REQ",325,1530)
V327M=V(4,"MSG_CUSTOM_FACE_REMOVE_RSP",326,1531)
V328M=V(4,"MSG_CUSTOM_FACE_USE_REQ",327,1532)
V329M=V(4,"MSG_CUSTOM_FACE_USE_RSP",328,1533)
V330M=V(4,"MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ",329,1534)
V331M=V(4,"MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP",330,1535)
V332M=V(4,"MSG_ZONE_ROLE_NEW_SCHLOSS_EFFECT_NTF",331,1536)
V333M=V(4,"MSG_GET_MAIL_LIKE_REQ",332,1537)
V334M=V(4,"MSG_GET_MAIL_LIKE_RSP",333,1538)
V335M=V(4,"MSG_GET_SP_LIKENUM_REQ",334,1539)
V336M=V(4,"MSG_GET_SP_LIKENUM_RSP",335,1540)
V337M=V(4,"MSG_PRIVATE_CHAT_LIKE_REQ",336,1541)
V338M=V(4,"MSG_PRIVATE_CHAT_LIKE_RSP",337,1542)
V339M=V(4,"MSG_PRIVATE_CHAT_LIKE_NTF",338,1543)
V340M=V(4,"MSG_PRIVATE_CHAT_LIKE_ALL_NTF",339,1544)
V341M=V(4,"MSG_ZONE_ROLE_PLATE_UPDATE_REQ",340,1545)
V342M=V(4,"MSG_ZONE_ROLE_PLATE_UPDATE_RSP",341,1546)
V343M=V(4,"MSG_ZONE_ROLE_PLATE_NTF",342,1547)
V344M=V(4,"MSG_ZONE_ROLE_FACE_MAX",343,1600)
V345M=V(4,"MSG_CHAT_SPEAK_REQ",344,1601)
V346M=V(4,"MSG_CHAT_SPEAK_RSP",345,1602)
V347M=V(4,"MSG_CHAT_NEW_MSG_NTF",346,1603)
V348M=V(4,"MSG_CHAT_MSG_NTF",347,1604)
V349M=V(4,"MSG_CHAT_BLOCK_REQ",348,1605)
V350M=V(4,"MSG_CHAT_BLOCK_RSP",349,1606)
V351M=V(4,"MSG_CHAT_SEND_MAIL_REQ",350,1607)
V352M=V(4,"MSG_CHAT_SEND_MAIL_RSP",351,1608)
V353M=V(4,"MSG_CHAT_REPORT_REQ",352,1609)
V354M=V(4,"MSG_CHAT_REPORT_RSP",353,1610)
V355M=V(4,"MSG_CHAT_BLOCK_CHANNEL_REQ",354,1611)
V356M=V(4,"MSG_CHAT_BLOCK_CHANNEL_RSP",355,1612)
V357M=V(4,"MSG_CHAT_BAN_NTF",356,1613)
V358M=V(4,"MSG_CHAT_BAN_REMOVE_NTF",357,1614)
V359M=V(4,"MSG_PRIVATE_MSG_SPEAK_REQ",358,1615)
V360M=V(4,"MSG_PRIVATE_MSG_SPEAK_RSP",359,1616)
V361M=V(4,"MSG_PRIVATE_MSG_NTF",360,1617)
V362M=V(4,"MSG_PRIVATE_MSG_SET_NTF",361,1618)
V363M=V(4,"MSG_MATE_MSG_SET_NTF",362,1619)
V364M=V(4,"MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ",363,1620)
V365M=V(4,"MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP",364,1621)
V366M=V(4,"MSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF",365,1622)
V367M=V(4,"MSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ",366,1623)
V368M=V(4,"MSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP",367,1624)
V369M=V(4,"MSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF",368,1625)
V370M=V(4,"MSG_CHAT_LANGUAGE_CHANNEL_UPDATE_NTF",369,1626)
V371M=V(4,"MSG_CHAT_REPORT_CHANNEL_REQ",370,1627)
V372M=V(4,"MSG_CHAT_REPORT_CHANNEL_RSP",371,1628)
V373M=V(4,"MSG_CHAT_GET_SIGN_REQ",372,1629)
V374M=V(4,"MSG_CHAT_GET_SIGN_RSP",373,1630)
V375M=V(4,"MSG_CHAT_GM_CLOSE_CONVERSATION_NTF",374,1631)
V376M=V(4,"MSG_CHAT_LEAVE_LEAGUE_NTF",375,1632)
V377M=V(4,"MSG_USER_UPDATE_NAME_RSP",376,1633)
V378M=V(4,"MSG_CHAT_MSG_GETBEFORE_REQ",377,1634)
V379M=V(4,"MSG_CHAT_MSG_GETBEFORE_RSP",378,1635)
V380M=V(4,"MSG_CHAT_RECALL_MSG_REQ",379,1636)
V381M=V(4,"MSG_CHAT_RECALL_MSG_RSP",380,1637)
V382M=V(4,"MSG_CHAT_LANGUAGE_MSG_REQ",381,1638)
V383M=V(4,"MSG_CHAT_LANGUAGE_MSG_RSP",382,1639)
V384M=V(4,"MSG_CHAT_BLOCK_LIST_REQ",383,1640)
V385M=V(4,"MSG_CHAT_BLOCK_LIST_RSP",384,1641)
V386M=V(4,"MSG_CHAT_BLOCK_LIST_NTF",385,1642)
V387M=V(4,"MSG_CHAT_MAX",386,1700)
V388M=V(4,"MSG_VIP_ENTER_DIAMONDSHOP_REQ",387,1701)
V389M=V(4,"MSG_VIP_ENTER_DIAMONDSHOP_RSP",388,1702)
V390M=V(4,"MSG_VIP_GOLDEN_HAND_REQ",389,1703)
V391M=V(4,"MSG_VIP_GOLDEN_HAND_RSP",390,1704)
V392M=V(4,"MSG_VIP_GET_GOLDEN_HAND_REWARD_REQ",391,1705)
V393M=V(4,"MSG_VIP_GET_GOLDEN_HAND_REWARD_RSP",392,1706)
V394M=V(4,"MSG_VIP_MAX",393,1720)
V395M=V(4,"MSG_HALO_BASE_HALO_UPGRADE_REQ",394,1730)
V396M=V(4,"MSG_HALO_BASE_HALO_UPGRADE_RSP",395,1731)
V397M=V(4,"MSG_HALO_BASE_HALO_RESTORE_REQ",396,1732)
V398M=V(4,"MSG_HALO_BASE_HALO_RESTORE_RSP",397,1733)
V399M=V(4,"MSG_HALO_USE_PROP_REQ",398,1734)
V400M=V(4,"MSG_HALO_USE_PROP_RSP",399,1735)
V401M=V(4,"TMSG_HALO_MAX",400,1750)
V402M=V(4,"MSG_FARM_MAIN_CITY_UPGRADE_REQ",401,1760)
V403M=V(4,"MSG_FARM_MAIN_CITY_UPGRADE_RSP",402,1761)
V404M=V(4,"MSG_FARM_SOIL_UNLOCK_NTF",403,1762)
V405M=V(4,"MSG_FARM_BOTANY_REQ",404,1763)
V406M=V(4,"MSG_FARM_BOTANY_RSP",405,1764)
V407M=V(4,"MSG_FARM_TREE_UPGRADE_REQ",406,1765)
V408M=V(4,"MSG_FARM_TREE_UPGRADE_RSP",407,1766)
V409M=V(4,"MSG_FARM_COLLECT_REQ",408,1767)
V410M=V(4,"MSG_FARM_COLLECT_RSP",409,1768)
V411M=V(4,"MSG_FARM_DISMANTLE_REQ",410,1769)
V412M=V(4,"MSG_FARM_DISMANTLE_RSP",411,1770)
V413M=V(4,"MSG_FARMPART_DATA_NTF",412,1771)
V414M=V(4,"MSG_WEAPON_UNLOCK_NTF",413,1772)
V415M=V(4,"MSG_WEAPON_ACTIVATE_REQ",414,1773)
V416M=V(4,"MSG_WEAPON_ACTIVATE_RSP",415,1774)
V417M=V(4,"MSG_WEAPON_UPGRADE_REQ",416,1775)
V418M=V(4,"MSG_WEAPON_UPGRADE_RSP",417,1776)
V419M=V(4,"MSG_WEAPON_DISMANTLE_REQ",418,1777)
V420M=V(4,"MSG_WEAPON_DISMANTLE_RSP",419,1778)
V421M=V(4,"MSG_WEAPON_CHOOSE_REQ",420,1779)
V422M=V(4,"MSG_WEAPON_CHOOSE_RSP",421,1780)
V423M=V(4,"MSG_WEAPON_PART_UNLOCK_NTF",422,1781)
V424M=V(4,"MSG_WEAPON_GET_CHOOSE_INFO_REQ",423,1782)
V425M=V(4,"MSG_WEAPON_GET_CHOOSE_INFO_RSP",424,1783)
V426M=V(4,"MSG_FARM_MAX",425,1800)
V427M=V(4,"MSG_BROADCAST_MSG_NTF",426,1801)
V428M=V(4,"MSG_BROADCAST_SYS_MSG_NTF",427,1802)
V429M=V(4,"MSG_ACTIVITY_PROSPECT_REWARD_REQ",428,1803)
V430M=V(4,"MSG_ACTIVITY_PROSPECT_REWARD_RSP",429,1804)
V431M=V(4,"MSG_BROADCAST_MAX",430,1850)
V432M=V(4,"MSG_LOTTERY_SET_WISH_LIST_REQ",431,1900)
V433M=V(4,"MSG_LOTTERY_SET_WISH_LIST_RSP",432,1901)
V434M=V(4,"MSG_WEEKEND_ARENA_RANK_INFO_REQ",433,1950)
V435M=V(4,"MSG_WEEKEND_ARENA_RANK_INFO_RSP",434,1951)
V436M=V(4,"MSG_WEEKEND_ARENA_CHALLENGE_NUM_NTF",435,1952)
V437M=V(4,"MSG_WEEKEND_ARENA_LIKE_NUM_NTF",436,1953)
V438M=V(4,"MSG_WEEKEND_ARENA_NO1_REQ",437,1954)
V439M=V(4,"MSG_WEEKEND_ARENA_NO1_RSP",438,1955)
V440M=V(4,"MSG_WEEKEND_ARENA_SET_LINEUP_REQ",439,1956)
V441M=V(4,"MSG_WEEKEND_ARENA_SET_LINEUP_RSP",440,1957)
V442M=V(4,"MSG_WEEKEND_ARENA_ENTER_BATTLE_REQ",441,1958)
V443M=V(4,"MSG_WEEKEND_ARENA_ENTER_BATTLE_RSP",442,1959)
V444M=V(4,"MSG_WEEKEND_ARENA_BATTLE_VIDEO_REQ",443,1960)
V445M=V(4,"MSG_WEEKEND_ARENA_BATTLE_VIDEO_RSP",444,1961)
V446M=V(4,"MSG_WEEKEND_ARENA_GET_LINEUP_REQ",445,1962)
V447M=V(4,"MSG_WEEKEND_ARENA_GET_LINEUP_RSP",446,1963)
V448M=V(4,"MSG_WEEKEND_ARENA_RANK_CHANGE_NTF",447,1964)
V449M=V(4,"MSG_WEEKEND_ARENA_LIKE_REQ",448,1965)
V450M=V(4,"MSG_WEEKEND_ARENA_LIKE_RSP",449,1966)
V451M=V(4,"MSG_WEEKEND_ARENA_LIKE_INFO_NTF",450,1967)
V452M=V(4,"MSG_WEEKEND_ARENA_CHALLENGE_RIVAL_REQ",451,1968)
V453M=V(4,"MSG_WEEKEND_ARENA_CHALLENGE_RIVAL_RSP",452,1969)
V454M=V(4,"MSG_WEEKEND_BATTLE_RECORD_REQ",453,1970)
V455M=V(4,"MSG_WEEKEND_BATTLE_RECORD_RSP",454,1971)
V456M=V(4,"MSG_WEEKEND_ARENA_GET_VICTORY_REWARD_REQ",455,1972)
V457M=V(4,"MSG_WEEKEND_ARENA_GET_VICTORY_REWARD_RSP",456,1973)
V458M=V(4,"MSG_WEEKEND_ARENA_QUALIFICATE_REQ",457,1974)
V459M=V(4,"MSG_WEEKEND_ARENA_QUALIFICATE_RSP",458,1975)
V460M=V(4,"MSG_ACCOUNT_BUILD_USER_INFO_NTF",459,100001)
V461M=V(4,"MSG_ACCOUNT_BUILD_PLAYER_INFO_NTF",460,100002)
V462M=V(4,"MSG_ACCOUNT_BUILD_GATEWAYMIGRATE_NTF",461,100003)
V463M=V(4,"MSG_ACCOUNT_BIND_REQ",462,100004)
V464M=V(4,"MSG_ACCOUNT_BIND_RSP",463,100005)
V465M=V(4,"MSG_ACCOUNT_RUN_SCRIPT_NTF",464,100006)
V466M=V(4,"MSG_ACCOUNT_LUA_ACTION_REQ",465,100007)
V467M=V(4,"MSG_ACCOUNT_NEWER_GUIDE_REQ",466,100008)
V468M=V(4,"MSG_ACCOUNT_SELECT_PROFICIENCY_REQ",467,100009)
V469M=V(4,"MSG_ACCOUNT_SELECT_PROFICIENCY_RSP",468,100010)
V470M=V(4,"MSG_ACCOUNT_ROLE_LIST_ALL_WORLD_REQ",469,100011)
V471M=V(4,"MSG_ACCOUNT_ROLE_LIST_ALL_WORLD_RSP",470,100012)
V472M=V(4,"MSG_ACCOUNT_CHANGE_WORLD_REQ",471,100013)
V473M=V(4,"MSG_ACCOUNT_CHANGE_WORLD_RSP",472,100014)
V474M=V(4,"MSG_ACCOUNT_CHANGE_WORLD_LOGIN_REQ",473,100015)
V475M=V(4,"MSG_ACCOUNT_CHANGE_WORLD_LOGIN_RSP",474,100016)
V476M=V(4,"MSG_ACCOUNT_LOGIN_QUEUE_NTF",475,100017)
V477M=V(4,"MSG_FACEBOOK_GUIDE_REQ",476,100021)
V478M=V(4,"MSG_FACEBOOK_GUIDE_RSP",477,100022)
V479M=V(4,"MSG_WARE_USE_ITEM_REQ",478,100050)
V480M=V(4,"MSG_WARE_USE_ITEM_RSP",479,100051)
V481M=V(4,"MSG_LOBWARE_RECYCLE_ITEM_REQ",480,100065)
V482M=V(4,"MSG_LOBWARE_RECYCLE_ITEM_RSP",481,100066)
V483M=V(4,"MSG_LOBBYCNT_CREATE_NTF",482,100100)
V484M=V(4,"MSG_LOBBYCNT_DESTROY_NTF",483,100101)
V485M=V(4,"MSG_LOBBYCNT_ADD_GOODS_NTF",484,100102)
V486M=V(4,"MSG_LOBBYCNT_DEL_GOODS_NTF",485,100103)
V487M=V(4,"MSG_LOBBY_PROP_CREATEROLE_NTF",486,100104)
V488M=V(4,"MSG_LOBBY_PROP_DESTROYROLE_NTF",487,100105)
V489M=V(4,"MSG_LOBBY_PROP_CREATEGOODS_NTF",488,100106)
V490M=V(4,"MSG_LOBBY_PROP_DESTROYGOODS_NTF",489,100107)
V491M=V(4,"MSG_LOBBY_PROP_UPDATE_NTF",490,100108)
V492M=V(4,"MSG_LOBBY_PROP_CLEAN_CONTROL_COMMAND_NTF",491,100109)
V493M=V(4,"MSG_LOBBY_PROP_CHANGENATION_REQ",492,100110)
V494M=V(4,"MSG_LOBBY_PROP_CHANGENATION_RSP",493,100111)
V495M=V(4,"MSG_LOBBY_PROP_UPDATE_MATCH_DATA_NTF",494,100112)
V496M=V(4,"MSG_FRIEND_SEARCH_REQ",495,100150)
V497M=V(4,"MSG_FRIEND_SEARCH_RSP",496,100151)
V498M=V(4,"MSG_FRIEND_LIST_REQ",497,100152)
V499M=V(4,"MSG_FRIEND_LIST_RSP",498,100153)
V500M=V(4,"MSG_FRIEND_APPLY_LIST_REQ",499,100154)
V501M=V(4,"MSG_FRIEND_APPLY_LIST_RSP",500,100155)
V502M=V(4,"MSG_FRIEND_DATA_UPDATE_NTF",501,100156)
V503M=V(4,"MSG_FRIEND_ADD_APPLY_REQ",502,100157)
V504M=V(4,"MSG_FRIEND_ADD_APPLY_RSP",503,100158)
V505M=V(4,"MSG_FRIEND_RECV_APPLY_NTF",504,100159)
V506M=V(4,"MSG_FRIEND_HANDLE_APPLY_REQ",505,100160)
V507M=V(4,"MSG_FRIEND_HANDLE_APPLY_RSP",506,100161)
V508M=V(4,"MSG_FRIEND_APPLY_RESULT_NTF",507,100162)
V509M=V(4,"MSG_FRIEND_REMOVE_FRIEND_REQ",508,100163)
V510M=V(4,"MSG_FRIEND_REMOVE_FRIEND_RSP",509,100164)
V511M=V(4,"MSG_FRIEND_REMOVE_FRIEND_NTF",510,100165)
V512M=V(4,"MSG_FRIEND_APPLY_DATA_UPDATE_NTF",511,100166)
V513M=V(4,"MSG_FRIEND_CHAT_REQ",512,100167)
V514M=V(4,"MSG_FRIEND_CHAT_RSP",513,100168)
V515M=V(4,"MSG_FRIEND_CHAT_NTF",514,100169)
V516M=V(4,"MSG_FRIEND_CHAT_LIST_REQ",515,100170)
V517M=V(4,"MSG_FRIEND_CHAT_LIST_RSP",516,100171)
V518M=V(4,"MSG_FRIEND_LOAD_FINISH_NTF",517,100172)
V519M=V(4,"MSG_LOBBYTEAM_CHANGE_SETTING_C2L_REQ",518,100200)
V520M=V(4,"MSG_LOBBYTEAM_CHANGE_SETTING_L2C_RSP",519,100201)
V521M=V(4,"MSG_LOBBYTEAM_CHANGE_PREPARE_STATE_C2L_REQ",520,100202)
V522M=V(4,"MSG_LOBBYTEAM_CHANGE_PREPARE_STATE_L2C_RSP",521,100203)
V523M=V(4,"MSG_LOBBYTEAM_SEND_INVITATION_C2L_REQ",522,100204)
V524M=V(4,"MSG_LOBBYTEAM_SEND_INVITATION_L2C_RSP",523,100205)
V525M=V(4,"MSG_LOBBYTEAM_INVITATION_L2C_NTF",524,100206)
V526M=V(4,"MSG_LOBBYTEAM_REPLY_TO_INVITATION_C2L_REQ",525,100207)
V527M=V(4,"MSG_LOBBYTEAM_REPLY_TO_INVITATION_L2C_RSP",526,100208)
V528M=V(4,"MSG_LOBBYTEAM_INVITATION_RESULT_L2C_NTF",527,100209)
V529M=V(4,"MSG_LOBBYTEAM_UPDATE_MATCH_SETTING_L2C_NTF",528,100210)
V530M=V(4,"MSG_LOBBYTEAM_UPDATE_MEMBER_INFO_L2C_NTF",529,100211)
V531M=V(4,"MSG_LOBBYTEAM_QUIT_C2L_REQ",530,100212)
V532M=V(4,"MSG_LOBBYTEAM_QUIT_L2C_RSP",531,100213)
V533M=V(4,"MSG_LOBBYTEAM_ADD_MEMBER_L2C_NTF",532,100214)
V534M=V(4,"MSG_LOBBYTEAM_DEL_MEMBER_L2C_NTF",533,100215)
V535M=V(4,"MSG_LOBSHOP_BUY_GOODS_REQ",534,100250)
V536M=V(4,"MSG_LOBSHOP_BUY_GOODS_RSP",535,100251)
V537M=V(4,"MSG_LOBSHOP_FLUSH_GOODS_PRICE_REQ",536,100252)
V538M=V(4,"MSG_LOBSHOP_FLUSH_GOODS_PRICE_RSP",537,100253)
V539M=V(4,"MSG_RECHARGE_REQ",538,100280)
V540M=V(4,"MSG_RECHARGE_RSP",539,100281)
V541M=V(4,"MSG_RECHARGE_RESULT_NTF",540,100282)
V542M=V(4,"MSG_FIRSTRECHARGEREWARD_REQ",541,100283)
V543M=V(4,"MSG_FIRSTRECHARGEREWARD_RSP",542,100284)
V544M=V(4,"MSG_RECHARGE_PAYMENT_RESULT_NTF",543,100285)
V545M=V(4,"MSG_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF",544,100286)
V546M=V(4,"MSG_RECHARGE_MONTH_CARD_INFO_RSP",545,100287)
V547M=V(4,"MSG_RECHARGE_MONTH_CARD_INFO_REQ",546,100288)
V548M=V(4,"MSG_RECHARGE_TOKENBUY_REQ",547,100289)
V549M=V(4,"MSG_RECHARGE_TOKENBUY_RSP",548,100290)
V550M=V(4,"MSG_MAIL_EVENT_L2C_NTF",549,100300)
V551M=V(4,"MSG_MAIL_GET_LIST_C2L_REQ",550,100301)
V552M=V(4,"MSG_MAIL_GET_LIST_L2C_RSP",551,100302)
V553M=V(4,"MSG_MAIL_GET_DETAIL_C2L_REQ",552,100303)
V554M=V(4,"MSG_MAIL_GET_DETAIL_L2C_RSP",553,100304)
V555M=V(4,"MSG_MAIL_DELETE_C2L_REQ",554,100305)
V556M=V(4,"MSG_MAIL_DELETE_L2C_RSP",555,100306)
V557M=V(4,"MSG_MAIL_GET_ATTACHMENT_C2L_REQ",556,100307)
V558M=V(4,"MSG_MAIL_GET_ATTACHMENT_L2C_RSP",557,100308)
V559M=V(4,"MSG_MAIL_UNREAD_L2C_NTF",558,100309)
V560M=V(4,"MSG_MAIL_SEND_C2L_REQ",559,100310)
V561M=V(4,"MSG_MAIL_SEND_C2L_RSP",560,100311)
V562M=V(4,"MSG_SHARETASK_SHOW_ATTEND_INFO_REQ",561,100350)
V563M=V(4,"MSG_SHARETASK_SHOW_ATTEND_INFO_RSP",562,100351)
V564M=V(4,"MSG_SHARETASK_SHOW_ATTEND_INFO_NTF",563,100352)
V565M=V(4,"MSG_SHARETASK_GET_PRIZE_REQ",564,100353)
V566M=V(4,"MSG_SHARETASK_GET_PRIZE_RSP",565,100354)
V567M=V(4,"MSG_SHARETASK_GET_PRIZE_NTF",566,100355)
V568M=V(4,"MSG_SHARETASK_ATTEND_SHARE_NTF",567,100356)
V569M=V(4,"MSG_MAGIC_STONE_NTF",568,100400)
V570M=V(4,"MSG_PLAYMODE_VOTE_BEGIN_REQ",569,100450)
V571M=V(4,"MSG_PLAYMODE_VOTE_BEGIN_RSP",570,100451)
V572M=V(4,"MSG_PLAYMODE_VOTE_BEGIN_NTF",571,100452)
V573M=V(4,"MSG_PLAYMODE_VOTE_ANSWER_REQ",572,100453)
V574M=V(4,"MSG_PLAYMODE_VOTE_ANSWER_RSP",573,100454)
V575M=V(4,"MSG_PLAYMODE_VOTE_END_NTF",574,100455)
V576M=V(4,"MSG_PLAYMODE_VOTE_CANCEL_REQ",575,100456)
V577M=V(4,"MSG_PLAYMODE_VOTE_CANCEL_RSP",576,100457)
V578M=V(4,"MSG_PLAYMODE_VOTE_CANCEL_NTF",577,100458)
V579M=V(4,"MSG_PLAYMODE_VOTE_RESET_REQ",578,100459)
V580M=V(4,"MSG_PLAYMODE_VOTE_RESET_RSP",579,100460)
V581M=V(4,"MSG_LOBBY_MATCH_BANNED_L2C_NTF",580,100500)
V582M=V(4,"MSG_LOBBY_MATCH_READY_C2L_REQ",581,100501)
V583M=V(4,"MSG_LOBBY_MATCH_READY_L2C_RSP",582,100502)
V584M=V(4,"MSG_LOBBY_ERROR_L2C_NTF",583,100503)
V585M=V(4,"MSG_CUSTOMROOM_CREATE_REQ",584,100550)
V586M=V(4,"MSG_CUSTOMROOM_CREATE_RSP",585,100551)
V587M=V(4,"MSG_CUSTOMROOM_JOIN_REQ",586,100552)
V588M=V(4,"MSG_CUSTOMROOM_JOIN_RSP",587,100553)
V589M=V(4,"MSG_CUSTOMROOM_QUIT_REQ",588,100554)
V590M=V(4,"MSG_CUSTOMROOM_QUIT_RSP",589,100555)
V591M=V(4,"MSG_CUSTOMROOM_KICK_NTF",590,100556)
V592M=V(4,"MSG_CUSTOMROOM_START_REQ",591,100557)
V593M=V(4,"MSG_CUSTOMROOM_START_RSP",592,100558)
V594M=V(4,"MSG_CUSTOMROOM_START_NTF",593,100559)
V595M=V(4,"MSG_CUSTOMROOM_SWITCH_POS_REQ",594,100560)
V596M=V(4,"MSG_CUSTOMROOM_SWITCH_POS_RSP",595,100561)
V597M=V(4,"MSG_CUSTOMROOM_LIST_REQ",596,100562)
V598M=V(4,"MSG_CUSTOMROOM_LIST_RSP",597,100563)
V599M=V(4,"MSG_CUSTOMROOM_MEMBER_UPDATE_NTF",598,100564)
V600M=V(4,"MSG_CUSTOMROOM_SEARCH_REQ",599,100565)
V601M=V(4,"MSG_CUSTOMROOM_SEARCH_RSP",600,100566)
V602M=V(4,"MSG_CUSTOMROOM_INVITE_FRIEND_REQ",601,100567)
V603M=V(4,"MSG_CUSTOMROOM_INVITE_FRIEND_RSP",602,100568)
V604M=V(4,"MSG_CUSTOMROOM_RECV_INVITE_NTF",603,100569)
V605M=V(4,"MSG_CUSTOMROOM_KICK_REQ",604,100570)
V606M=V(4,"MSG_CUSTOMROOM_KICK_RSP",605,100571)
V607M=V(4,"MSG_CUSTOMROOM_UPDATE_SETUP_REQ",606,100572)
V608M=V(4,"MSG_CUSTOMROOM_UPDATE_SETUP_RSP",607,100573)
V609M=V(4,"MSG_CUSTOMROOM_HANDLE_INVITE_REQ",608,100574)
V610M=V(4,"MSG_CUSTOMROOM_HANDLE_INVITE_RSP",609,100575)
V611M=V(4,"MSG_CUSTOMROOM_ALLDATA_NTF",610,100576)
V612M=V(4,"MSG_CUSTOMROOM_FETCH_SETUP_REQ",611,100577)
V613M=V(4,"MSG_CUSTOMROOM_FETCH_SETUP_RSP",612,100578)
V614M=V(4,"MSG_CUSTOMROOM_ROOMDATA_UPDATE_NTF",613,100579)
V615M=V(4,"MSG_CUSTOMROOM_INVITE_RESULT_NTF",614,100580)
V616M=V(4,"MSG_NEWHERO_SETHERO_REQ",615,100581)
V617M=V(4,"MSG_NEWHERO_SETHERO_RSP",616,100582)
V618M=V(4,"MSG_BLESSHERO_GET_REQ",617,100583)
V619M=V(4,"MSG_BLESSHERO_GET_RSP",618,100584)
V620M=V(4,"MSG_LOBBY_UPDATE_HERO_REQ",619,100600)
V621M=V(4,"MSG_LOBBY_UPDATE_HERO_RSP",620,100601)
V622M=V(4,"MSG_LOBBY_HERO_SYNC_NTF",621,100602)
V623M=V(4,"MSG_LOBBY_CARD_UPGRADE_REQ",622,100610)
V624M=V(4,"MSG_LOBBY_CARD_UPGRADE_RSP",623,100611)
V625M=V(4,"MSG_LOBBY_CARD_GET_DATA_REQ",624,100612)
V626M=V(4,"MSG_LOBBY_CARD_GET_DATA_RSP",625,100613)
V627M=V(4,"MSG_LOBBY_LOTTERY_REQ",626,100650)
V628M=V(4,"MSG_LOBBY_LOTTERY_RSP",627,100651)
V629M=V(4,"MSG_LOBBY_LOTTERY_SYNC_NTF",628,100652)
V630M=V(4,"MSG_LOTTERY_SETCHOICE_REQ",629,100653)
V631M=V(4,"MSG_LOTTERY_SETCHOICE_RSP",630,100654)
V632M=V(4,"MSG_LOBBY_TEAM_JOIN_NTF",631,100660)
V633M=V(4,"MSG_LOBBY_TEAM_LEAVE_NTF",632,100661)
V634M=V(4,"MSG_LOBBY_TEAM_LEAVE_REQ",633,100662)
V635M=V(4,"MSG_LOBBY_TEAM_LEAVE_RSP",634,100663)
V636M=V(4,"MSG_LOBBY_TEAM_READY_REQ",635,100664)
V637M=V(4,"MSG_LOBBY_TEAM_READY_RSP",636,100665)
V638M=V(4,"MSG_LOBBY_TEAM_CANCEL_REQ",637,100666)
V639M=V(4,"MSG_LOBBY_TEAM_CANCEL_RSP",638,100667)
V640M=V(4,"MSG_LOBBY_TEAM_KICK_REQ",639,100668)
V641M=V(4,"MSG_LOBBY_TEAM_KICK_RSP",640,100669)
V642M=V(4,"MSG_LOBBY_TEAM_KICK_NTF",641,100670)
V643M=V(4,"MSG_LOBBY_TEAM_MEMBER_UPDATE_NTF",642,100671)
V644M=V(4,"MSG_LOBBY_TEAM_CREATE_REQ",643,100672)
V645M=V(4,"MSG_LOBBY_TEAM_CREATE_RSP",644,100673)
V646M=V(4,"MSG_LOBBY_MATCH_BEGIN_REQ",645,100680)
V647M=V(4,"MSG_LOBBY_MATCH_BEGIN_RSP",646,100681)
V648M=V(4,"MSG_LOBBY_MATCH_CANCEL_REQ",647,100682)
V649M=V(4,"MSG_LOBBY_MATCH_CANCEL_RSP",648,100683)
V650M=V(4,"MSG_LOBBY_MATCH_BEGIN_NTF",649,100684)
V651M=V(4,"MSG_LOBBY_MATCH_CANCEL_NTF",650,100685)
V652M=V(4,"MSG_LOBBY_MATCH_LIMIT_NTF",651,100686)
V653M=V(4,"MSG_LOBBY_MATCH_COUNT_NTF",652,100687)
V654M=V(4,"MSG_LOBBY_MATCH_STARTSOON_NTF",653,100688)
V655M=V(4,"MSG_LOBBY_RECENT_PLAYER_LIST_REQ",654,100700)
V656M=V(4,"MSG_LOBBY_RECENT_PLAYER_LIST_RSP",655,100701)
V657M=V(4,"MSG_LOBBY_RECENT_PLAYER_SYNC_NTF",656,100702)
V658M=V(4,"MSG_LOBBY_NEARBY_PLAYER_LIST_REQ",657,100703)
V659M=V(4,"MSG_LOBBY_NEARBY_PLAYER_LIST_RSP",658,100704)
V660M=V(4,"MSG_LOBBY_NEARBY_PLAYER_SYNC_NTF",659,100705)
V661M=V(4,"MSG_LOBBY_SURFACE_SWITCH_REQ",660,100720)
V662M=V(4,"MSG_LOBBY_SURFACE_SWITCH_RSP",661,100721)
V663M=V(4,"MSG_LOBBY_FETCH_MATCH_DATA_REQ",662,100730)
V664M=V(4,"MSG_LOBBY_FETCH_MATCH_DATA_RSP",663,100731)
V665M=V(4,"MSG_LOBBY_COMPLETEGAME_NTF",664,100740)
V666M=V(4,"MSG_LOBBY_COMPLETEGAME_PRIZE_REQ",665,100741)
V667M=V(4,"MSG_LOBBY_COMPLETEGAME_PRIZE_RSP",666,100742)
V668M=V(4,"MSG_INTERATION_BASE_INFO_NTF",667,100800)
V669M=V(4,"MSG_INTERATION_LIKE_REQ",668,100801)
V670M=V(4,"MSG_INTERATION_COLLECT_COINS_REQ",669,100802)
V671M=V(4,"MSG_INTERATION_COLLECT_COINS_RSP",670,100803)
V672M=V(4,"MSG_INTERATION_INFO_REQ",671,100804)
V673M=V(4,"MSG_INTERATION_INFO_RSP",672,100805)
V674M=V(4,"MSG_INTERATION_REWARD_COINS_REQ",673,100806)
V675M=V(4,"MSG_PROMOTE_DEVELOP_LV_REQ",674,100807)
V676M=V(4,"MSG_PROMOTE_DEVELOP_LV_RSP",675,100808)
V677M=V(4,"MSG_PLAYER_CHALLENGE_REQ",676,100809)
V678M=V(4,"MSG_PLAYER_CHALLENGE_RSP",677,100810)
V679M=V(4,"MSG_NEARBY_BASE_INFO_REQ",678,100811)
V680M=V(4,"MSG_LIKED_GAME_INFO_NTF",679,100812)
V681M=V(4,"MSG_INTERACTION_RECENT_PLAYER_LOGOUT_NTF",680,100813)
V682M=V(4,"MSG_PLAYER_INPUT_CHALLENGE_REQ",681,100901)
V683M=V(4,"MSG_PLAYER_INPUT_CHALLENGE_RSP",682,100902)
V684M=V(4,"MSG_PLAYER_IDLE_REWARD_NTF",683,100903)
V685M=V(4,"MSG_PLAYER_GET_IDLE_REWARD_REQ",684,100904)
V686M=V(4,"MSG_PLAYER_GET_IDLE_REWARD_RSP",685,100905)
V687M=V(4,"MSG_PLAYER_ENTER_BATTLE_REQ",686,100906)
V688M=V(4,"MSG_PLAYER_ENTER_BATTLE_RSP",687,100907)
V689M=V(4,"MSG_PLAYER_MODIFY_IDLE_STAGE_REQ",688,100908)
V690M=V(4,"MSG_PLAYER_MODIFY_IDLE_STAGE_RSP",689,100909)
V691M=V(4,"MSG_GAMEOVER_NTF",690,100910)
V692M=V(4,"MSG_SAVE_PLAYER_LINEUP_REQ",691,100911)
V693M=V(4,"MSG_SAVE_PLAYER_LINEUP_RSP",692,100912)
V694M=V(4,"MSG_EXIT_MODULE_NTF",693,100913)
V695M=V(4,"MSG_CLIENT_GET_RANK_LIST_REQ",694,100914)
V696M=V(4,"MSG_CLIENT_GET_RANK_LIST_RSP",695,100915)
V697M=V(4,"MSG_CLIENT_UPDATE_RANK_LIST_NTF",696,100916)
V698M=V(4,"MSG_ILLUSION_TOWER_SWEEP_REQ",697,100920)
V699M=V(4,"MSG_ILLUSION_TOWER_SWEEP_RSP",698,100921)
V700M=V(4,"MSG_ILLUSION_TOWER_BATTLE_VIDEO_REQ",699,100924)
V701M=V(4,"MSG_ILLUSION_TOWER_BATTLE_VIDEO_RSP",700,100925)
V702M=V(4,"MSG_BATTLE_END_OF_THE_PLAY_NTF",701,100926)
V703M=V(4,"MSG_PLAYER_BATTLE_RESULT_REQ",702,100927)
V704M=V(4,"MSG_PLAYER_BATTLE_RESULT_RSP",703,100928)
V705M=V(4,"StorySystemData",704,100929)
V706M=V(4,"MSG_QIDLE_BUY_SWEEP_TIMES_REQ",705,100930)
V707M=V(4,"MSG_QIDLE_BUY_SWEEP_TIMES_RSP",706,100931)
V708M=V(4,"MSG_QIDLE_SWEEP_TIMES_REQ",707,100932)
V709M=V(4,"MSG_QIDLE_SWEEP_TIMES_RSP",708,100933)
V710M=V(4,"MSG_QIDLE_DATA_INFORM_NTF",709,100934)
V711M=V(4,"MSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF",710,100935)
V712M=V(4,"MSG_ENTER_MAIN_BATTLE_NTF",711,100936)
V713M=V(4,"MSG_IDLE_BATTLE_RATING_PROP_NTF",712,100950)
V714M=V(4,"MSG_ILLUSION_TOWER_BIG_PRIZE_REQ",713,100951)
V715M=V(4,"MSG_ILLUSION_TOWER_BIG_PRIZE_RSP",714,100952)
V716M=V(4,"MSG_EQUIP_ECTYPE_SPECIAL_PRIZE_REQ",715,100953)
V717M=V(4,"MSG_EQUIP_ECTYPE_SPECIAL_PRIZE_RSP",716,100954)
V718M=V(4,"MSG_BATTLE_PLAYBACK_REQ",717,100955)
V719M=V(4,"MSG_BATTLE_PLAYBACK_RSP",718,100956)
V720M=V(4,"MSG_EQUIP_ECTYPE_MODUP_REQ",719,100957)
V721M=V(4,"MSG_EQUIP_ECTYPE_MODUP_RSP",720,100958)
V722M=V(4,"MSG_BATTLE_RESULT_REQ",721,100959)
V723M=V(4,"MSG_BATTLE_RESULT_RSP",722,100960)
V724M=V(4,"MSG_IDLE_DATA_REQ",723,100961)
V725M=V(4,"MSG_IDLE_DATA_RSP",724,100962)
V726M=V(4,"MSG_LOTTERYWISH_REQ",725,101000)
V727M=V(4,"MSG_LOTTERYWISH_RSP",726,101001)
V728M=V(4,"MSG_LOTTERYWISH_SYNC_NTF",727,101002)
V729M=V(4,"MSG_LOTTERYWISH_REFRESH_REQ",728,101003)
V730M=V(4,"MSG_LOTTERYWISH_REFRESH_RSP",729,101004)
V731M=V(4,"MSG_LOTTERYWISH_FREE_REFRESH_NTF",730,101005)
V732M=V(4,"MSG_TAVERN_TASK_UPDATE_NTF",731,101020)
V733M=V(4,"MSG_TAVERN_TASK_START_REQ",732,101022)
V734M=V(4,"MSG_TAVERN_TASK_START_RSP",733,101023)
V735M=V(4,"MSG_TAVERN_TASK_RESET_REQ",734,101024)
V736M=V(4,"MSG_TAVERN_TASK_RESET_RSP",735,101025)
V737M=V(4,"MSG_TAVERN_TASK_RWARD_REQ",736,101026)
V738M=V(4,"MSG_TAVERN_TASK_RWARD_RSP",737,101027)
V739M=V(4,"MSG_TAVERN_TASK_SPEEDUP_REQ",738,101028)
V740M=V(4,"MSG_TAVERN_TASK_SPEEDUP_RSP",739,101029)
V741M=V(4,"MSG_TAVERN_TASK_LOCK_REQ",740,101030)
V742M=V(4,"MSG_TAVERN_TASK_LOCK_RSP",741,101031)
V743M=V(4,"MSG_TAVERN_TASK_UNLOCK_REQ",742,101032)
V744M=V(4,"MSG_TAVERN_TASK_UNLOCK_RSP",743,101033)
V745M=V(4,"MSG_TAVERN_REFRESH_REQ",744,101034)
V746M=V(4,"MSG_TAVERN_REFRESH_RSP",745,101035)
V747M=V(4,"MSG_TAVERN_ADD_REQ",746,101036)
V748M=V(4,"MSG_TAVERN_ADD_RSP",747,101037)
V749M=V(4,"MSG_TAVERN_DATA_REQ",748,101038)
V750M=V(4,"MSG_TAVERN_DATA_RSP",749,101039)
V751M=V(4,"MSG_HERO_TRANSFORM_REQ",750,101050)
V752M=V(4,"MSG_HERO_TRANSFORM_RSP",751,101051)
V753M=V(4,"MSG_HERO_TRANSFORM_SELECT_REQ",752,101052)
V754M=V(4,"MSG_HERO_TRANSFORM_SELECT_RSP",753,101053)
V755M=V(4,"MSG_HERO_TRANSFORM_NTF",754,101054)
V756M=V(4,"MSG_TRIAL_SETTEAM_REQ",755,101060)
V757M=V(4,"MSG_TRIAL_SETTEAM_RSP",756,101061)
V758M=V(4,"MSG_TRIAL_MOVE_REQ",757,101062)
V759M=V(4,"MSG_TRIAL_MOVE_RSP",758,101063)
V760M=V(4,"MSG_TRIAL_TASKEVENT_REQ",759,101064)
V761M=V(4,"MSG_TRIAL_TASKEVENT_RSP",760,101065)
V762M=V(4,"MSG_TRIAL_UPDATE_NTF",761,101066)
V763M=V(4,"MSG_TRIAL_NEXTLEVEL_REQ",762,101067)
V764M=V(4,"MSG_TRIAL_NEXTLEVEL_RSP",763,101068)
V765M=V(4,"MSG_TRIAL_PLAYERBTLEVENT_UPDATE_NTF",764,101069)
V766M=V(4,"MSG_TRIAL_LOCK_TEAM_REQ",765,101070)
V767M=V(4,"MSG_TRIAL_LOCK_TEAM_RSP",766,101071)
V768M=V(4,"MSG_TRIAL_MAX",767,101100)
V769M=V(4,"MSG_ASHDUNGEON_UPDATE_PART_NTF",768,101101)
V770M=V(4,"MSG_ASHDUNGEON_SELECT_DRUG_REQ",769,101102)
V771M=V(4,"MSG_ASHDUNGEON_SELECT_DRUG_RSP",770,101103)
V772M=V(4,"MSG_ASHDUNGEON_USE_DRUG_REQ",771,101104)
V773M=V(4,"MSG_ASHDUNGEON_USE_DRUG_RSP",772,101105)
V774M=V(4,"MSG_ASHDUNGEON_GET_RANK_REQ",773,101106)
V775M=V(4,"MSG_ASHDUNGEON_GET_RANK_RSP",774,101107)
V776M=V(4,"MSG_ASHDUNGEON_UPDATE_RANK_NTF",775,101108)
V777M=V(4,"MSG_ASHDUNGEON_SWEEP_REQ",776,101109)
V778M=V(4,"MSG_ASHDUNGEON_SWEEP_RSP",777,101110)
V779M=V(4,"MSG_ASHDUNGEON_GETHERO_REQ",778,101111)
V780M=V(4,"MSG_ASHDUNGEON_GETHERO_RSP",779,101112)
V781M=V(4,"MSG_ASHDUNGEON_SETTEAM_REQ",780,101113)
V782M=V(4,"MSG_ASHDUNGEON_SETTEAM_RSP",781,101114)
V783M=V(4,"MSG_ASHDUNGEON_PASSREWARD_REQ",782,101115)
V784M=V(4,"MSG_ASHDUNGEON_PASSREWARD_RSP",783,101116)
V785M=V(4,"MSG_ASHDUNGEON_RESET_REQ",784,101117)
V786M=V(4,"MSG_ASHDUNGEON_RESET_RSP",785,101118)
V787M=V(4,"MSG_ASHDUNGEON_MAX",786,101200)
V788M=V(4,"MSG_ALTAR_HERO_DECOMPOSE_REQ",787,101300)
V789M=V(4,"MSG_ALTAR_HERO_DECOMPOSE_RSP",788,101301)
V790M=V(4,"MSG_BROKENST_MOPUP_REWARD_NTF",789,101350)
V791M=V(4,"MSG_BROKENST_UPDATA_BADGES_NTF",790,101351)
V792M=V(4,"MSG_ACTIVITY_COMPLETE_NTF",791,101352)
V793M=V(4,"MSG_SPACE_DOMINATOR_INFO_NTF",792,101353)
V794M=V(4,"MSG_SPACE_DOMINATOR_GET_RANK_REWARD_REQ",793,101354)
V795M=V(4,"MSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP",794,101355)
V796M=V(4,"MSG_SPACE_DOMINATOR_GET_RANK_INFO_REQ",795,101356)
V797M=V(4,"MSG_SPACE_DOMINATOR_GET_RANK_INFO_RSP",796,101357)
V798M=V(4,"MSG_SPACE_DOMINATOR_SELF_RANK_NTF",797,101358)
V799M=V(4,"MSG_SPACE_DOMINATOR_USER_SETTLE_NTF",798,101359)
V800M=V(4,"MSG_SPACE_DOMINATOR_CHALLENGE_COUNT_NTF",799,101360)
V801M=V(4,"MSG_SPACE_DOMINATOR_END_GROUP_NTF",800,101361)
V802M=V(4,"MSG_SPACE_DOMINATOR_GROUP_ID_NTF",801,101362)
V803M=V(4,"MSG_SPACE_DOMINATOR_BATTLE_VIDEO_REQ",802,101363)
V804M=V(4,"MSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP",803,101364)
V805M=V(4,"MSG_SERVER_TIME_MODIFY_NTF",804,101400)
V806M=V(4,"MSG_TECH_UPGRADE_REQ",805,101410)
V807M=V(4,"MSG_TECH_UPGRADE_RSP",806,101411)
V808M=V(4,"MSG_TECH_RESET_REQ",807,101412)
V809M=V(4,"MSG_TECH_RESET_RSP",808,101413)
V810M=V(4,"MSG_LEAGUE_UPDATE_NTF",809,101420)
V811M=V(4,"MSG_LEAGUE_MEMBER_NTF",810,101421)
V812M=V(4,"MSG_LEAGUE_MODIFY_REQ",811,101425)
V813M=V(4,"MSG_LEAGUE_MODIFY_RSP",812,101426)
V814M=V(4,"MSG_LEAGUE_LOG_REQ",813,101427)
V815M=V(4,"MSG_LEAGUE_LOG_RSP",814,101428)
V816M=V(4,"MSG_LEAGUE_MAIL_REQ",815,101429)
V817M=V(4,"MSG_LEAGUE_MAIL_RSP",816,101430)
V818M=V(4,"MSG_LEAGUE_HANDLE_APPLY_REQ",817,101431)
V819M=V(4,"MSG_LEAGUE_HANDLE_APPLY_RSP",818,101432)
V820M=V(4,"MSG_LEAGUE_EXPEL_REQ",819,101433)
V821M=V(4,"MSG_LEAGUE_EXPEL_RSP",820,101434)
V822M=V(4,"MSG_LEAGUE_OFFICER_POSITION_REQ",821,101435)
V823M=V(4,"MSG_LEAGUE_OFFICER_POSITION_RSP",822,101436)
V824M=V(4,"MSG_LEAGUE_OFFICER_RESIGN_REQ",823,101437)
V825M=V(4,"MSG_LEAGUE_OFFICER_RESIGN_RSP",824,101438)
V826M=V(4,"MSG_LEAGUE_RECOMMEND_REQ",825,101439)
V827M=V(4,"MSG_LEAGUE_RECOMMEND_RSP",826,101440)
V828M=V(4,"MSG_LEAGUE_TRANSFER_REQ",827,101441)
V829M=V(4,"MSG_LEAGUE_TRANSFER_RSP",828,101442)
V830M=V(4,"MSG_LEAGUE_APPLY_REQ",829,101443)
V831M=V(4,"MSG_LEAGUE_APPLY_RSP",830,101444)
V832M=V(4,"MSG_LEAGUE_SEARCH_REQ",831,101445)
V833M=V(4,"MSG_LEAGUE_SEARCH_RSP",832,101446)
V834M=V(4,"MSG_LEAGUE_CREATE_REQ",833,101449)
V835M=V(4,"MSG_LEAGUE_CREATE_RSP",834,101450)
V836M=V(4,"MSG_LEAGUE_INVITED_INFO_REQ",835,101451)
V837M=V(4,"MSG_LEAGUE_INVITED_INFO_RSP",836,101452)
V838M=V(4,"MSG_LEAGUE_INVITED_REQ",837,101453)
V839M=V(4,"MSG_LEAGUE_INVITED_RSP",838,101454)
V840M=V(4,"MSG_LEAGUE_INVITED_APPLY_REQ",839,101455)
V841M=V(4,"MSG_LEAGUE_INVITED_APPLY_RSP",840,101456)
V842M=V(4,"MSG_LEAGUE_INVITED_APPLY_NTF",841,101457)
V843M=V(4,"MSG_LEAGUE_QUICK_ADD_REQ",842,101458)
V844M=V(4,"MSG_LEAGUE_QUICK_ADD_RSP",843,101459)
V845M=V(4,"MSG_LEAGUE_APPLY_INVITE_LIST_REQ",844,101460)
V846M=V(4,"MSG_LEAGUE_APPLY_INVITE_LIST_RSP",845,101461)
V847M=V(4,"MSG_LEAGUE_SYNC_EXTRA_NTF",846,101462)
V848M=V(4,"MSG_LEAGUE_RECRUIT_REQ",847,101463)
V849M=V(4,"MSG_LEAGUE_RECRUIT_RSP",848,101464)
V850M=V(4,"MSG_LEAGUE_RECRUIT_SAVE_REQ",849,101465)
V851M=V(4,"MSG_LEAGUE_RECRUIT_SAVE_RSP",850,101466)
V852M=V(4,"MSG_LEAGUEPART_UPDATE_NTF",851,101500)
V853M=V(4,"MSG_LEAGUE_SIGNIN_REQ",852,101501)
V854M=V(4,"MSG_LEAGUE_SIGNIN_RSP",853,101502)
V855M=V(4,"MSG_LEAGUE_NATIONALFLAG_STATE_NTF",854,101503)
V856M=V(4,"MSG_LEAGUE_NATIONALFLAG_MODIFY_REQ",855,101504)
V857M=V(4,"MSG_LEAGUE_NATIONALFLAG_MODIFY_RSP",856,101505)
V858M=V(4,"MSG_LEAGUE_NATIONALFLAG_INFO_REQ",857,101506)
V859M=V(4,"MSG_LEAGUE_NATIONALFLAG_INFO_RSP",858,101507)
V860M=V(4,"MSG_LEAGUE_MAX",859,101600)
V861M=V(4,"MSG_MILL_UPDATE_NTF",860,101601)
V862M=V(4,"MSG_MILL_GET_ORDER_REQ",861,101602)
V863M=V(4,"MSG_MILL_GET_ORDER_RSP",862,101603)
V864M=V(4,"MSG_MILL_START_ORDER_REQ",863,101604)
V865M=V(4,"MSG_MILL_START_ORDER_RSP",864,101605)
V866M=V(4,"MSG_MILL_UPGRADE_ORDER_REQ",865,101606)
V867M=V(4,"MSG_MILL_UPGRADE_ORDER_RSP",866,101607)
V868M=V(4,"MSG_MILL_GET_ORDER_REWARD_REQ",867,101608)
V869M=V(4,"MSG_MILL_GET_ORDER_REWARD_RSP",868,101609)
V870M=V(4,"MSG_CONTRI_UPDATE_NTF",869,101650)
V871M=V(4,"MSG_CONTRI_RANK_REQ",870,101651)
V872M=V(4,"MSG_CONTRI_RANK_RSP",871,101652)
V873M=V(4,"MSG_CONTRI_MAX",872,101700)
V874M=V(4,"MSG_LEAGUEBOSS_UPDATE_NTF",873,101701)
V875M=V(4,"MSG_LEAGUEBOSS_RANKLIST_REQ",874,101702)
V876M=V(4,"MSG_LEAGUEBOSS_RANKLIST_RSP",875,101703)
V877M=V(4,"MSG_LEAGUEBOSS_GAIN_REWARD_REQ",876,101704)
V878M=V(4,"MSG_LEAGUEBOSS_GAIN_REWARD_RSP",877,101705)
V879M=V(4,"MSG_LEAGUEBOSS_MAX",878,101800)
V880M=V(4,"MSG_LEAGUE_COMP_BASE_DATA_REQ",879,101811)
V881M=V(4,"MSG_LEAGUE_COMP_BASE_DATA_RSP",880,101812)
V882M=V(4,"MSG_LEAGUE_COMP_BASE_DATA_NTF",881,101813)
V883M=V(4,"MSG_LEAGUE_COMP_CELL_REQ",882,101814)
V884M=V(4,"MSG_LEAGUE_COMP_CELL_RSP",883,101815)
V885M=V(4,"MSG_LEAGUE_COMP_UPDATE_CELL_NTF",884,101816)
V886M=V(4,"MSG_LEAGUE_COMP_DISPATCH_REQ",885,101817)
V887M=V(4,"MSG_LEAGUE_COMP_DISPATCH_RSP",886,101818)
V888M=V(4,"MSG_LEAGUE_COMP_RANK_REQ",887,101819)
V889M=V(4,"MSG_LEAGUE_COMP_RANK_RSP",888,101820)
V890M=V(4,"MSG_LEAGUE_COMP_UPDATE_RANK_NTF",889,101821)
V891M=V(4,"MSG_LEAGUE_COMP_PLAYBACK_REQ",890,101822)
V892M=V(4,"MSG_LEAGUE_COMP_PLAYBACK_RSP",891,101823)
V893M=V(4,"MSG_LEAGUE_COMP_UPDATE_PART_NTF",892,101824)
V894M=V(4,"MSG_LEAGUE_COMP_GET_RES_REQ",893,101825)
V895M=V(4,"MSG_LEAGUE_COMP_GET_RES_RSP",894,101826)
V896M=V(4,"MSG_LEAGUE_COMP_BASE_INFO_REQ",895,101827)
V897M=V(4,"MSG_LEAGUE_COMP_BASE_INFO_RSP",896,101828)
V898M=V(4,"MSG_LEAGUE_COMP_BASE_INFO_NTF",897,101829)
V899M=V(4,"MSG_LEAGUE_COMP_BATTLE_RECORD_REQ",898,101830)
V900M=V(4,"MSG_LEAGUE_COMP_BATTLE_RECORD_RSP",899,101831)
V901M=V(4,"MSG_LEAGUE_COMP_BATTLE_RECORD_NTF",900,101832)
V902M=V(4,"MSG_LEAGUE_COMP_BATTLE_RECORD_DETAIL_REQ",901,101833)
V903M=V(4,"MSG_LEAGUE_COMP_BATTLE_RECORD_DETAIL_RSP",902,101834)
V904M=V(4,"MSG_LEAGUE_COMP_HARVEST_INFO_REQ",903,101835)
V905M=V(4,"MSG_LEAGUE_COMP_HARVEST_INFO_RSP",904,101836)
V906M=V(4,"MSG_LEAGUE_COMP_DISPATCH_NTF",905,101837)
V907M=V(4,"MSG_LEAGUE_COMP_GET_RES_NTF",906,101838)
V908M=V(4,"MSG_LEAGUE_COMP_UPDATE_PART_REQ",907,101839)
V909M=V(4,"MSG_LEAGUE_COMP_SPECIAL_BATTLE_RECORD_REQ",908,101840)
V910M=V(4,"MSG_LEAGUE_COMP_SPECIAL_BATTLE_RECORD_NTF",909,101841)
V911M=V(4,"MSG_LEAGUE_COMP_MARK_REQ",910,101842)
V912M=V(4,"MSG_LEAGUE_COMP_MARK_RSP",911,101843)
V913M=V(4,"MSG_LEAGUE_COMP_UPDATE_PART_RSP",912,101844)
V914M=V(4,"MSG_LEAGUE_COMP_REDDOT_REQ",913,101845)
V915M=V(4,"MSG_LEAGUE_COMP_REDDOT_RSP",914,101846)
V916M=V(4,"MSG_LEAGUE_COMP_BATTLE_EFFECT_NTF",915,101847)
V917M=V(4,"MSG_LEAGUE_COMP_TASK_REQ",916,101848)
V918M=V(4,"MSG_LEAGUE_COMP_TASK_RSP",917,101849)
V919M=V(4,"MSG_LEAGUE_COMP_TASK_NTF",918,101850)
V920M=V(4,"MSG_LEAGUE_COMP_TASK_GAIN_REQ",919,101851)
V921M=V(4,"MSG_LEAGUE_COMP_TASK_GAIN_RSP",920,101852)
V922M=V(4,"MSG_LEAGUE_COMP_BATTLE_RESULT_NTF",921,101853)
V923M=V(4,"MSG_LEAGUE_COMP_COMMON_REQ",922,101860)
V924M=V(4,"MSG_LEAGUE_COMP_COMMON_RSP",923,101861)
V925M=V(4,"MSG_LEAGUE_COMP_MAX",924,101924)
V926M=V(4,"MSG_HERO_HANDBOOK_ADD_HERO_NTF",925,102000)
V927M=V(4,"MSG_HERO_ROLEPLOT_MOD_INFO_NTF",926,102050)
V928M=V(4,"MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ",927,102051)
V929M=V(4,"MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP",928,102052)
V930M=V(4,"MSG_TREASURE_SEND_FLOWERS_REQ",929,102100)
V931M=V(4,"MSG_TREASURE_SEND_FLOWERS_RSP",930,102101)
V932M=V(4,"MSG_SEND_FLOWERS_TO_EACH_OTHER_REQ",931,102102)
V933M=V(4,"MSG_SEND_FLOWERS_TO_EACH_OTHER_RSP",932,102103)
V934M=V(4,"MSG_TREASURE_HUNT_REQ",933,102104)
V935M=V(4,"MSG_TREASURE_HUNT_RSP",934,102105)
V936M=V(4,"MSG_GET_AWAY_FROM_PITFALL_REQ",935,102106)
V937M=V(4,"MSG_GET_AWAY_FROM_PITFALL_RSP",936,102107)
V938M=V(4,"MSG_TO_THE_BOSS_TO_FIGHT_REQ",937,102108)
V939M=V(4,"MSG_TO_THE_BOSS_TO_FIGHT_RSP",938,102109)
V940M=V(4,"MSG_UPDATE_PLAYER_TREASURE_NTF",939,102110)
V941M=V(4,"MSG_TREASURE_STATE_OR_PHYSICAL_UPDATE_NTF",940,102111)
V942M=V(4,"MSG_GET_SEND_FLOWER_RANK_LIST_REQ",941,102112)
V943M=V(4,"MSG_GET_SEND_FLOWER_RANK_LIST_RSP",942,102113)
V944M=V(4,"MSG_GET_LEAGUE_BOSS_HP_RANK_LIST_REQ",943,102114)
V945M=V(4,"MSG_GET_LEAGUE_BOSS_HP_RANK_LIST_RSP",944,102115)
V946M=V(4,"MSG_GET_NOVICE_GUIDE_DATA_REQ",945,102150)
V947M=V(4,"MSG_GET_NOVICE_GUIDE_DATA_RSP",946,102151)
V948M=V(4,"MSG_ADD_NOVICE_GUIDE_DATA_NTF",947,102152)
V949M=V(4,"MSG_ADD_PASS_STAGE_PLOT_DATA_NTF",948,102155)
V950M=V(4,"MSG_REAL_NAME_VERIFIED_REQ",949,102200)
V951M=V(4,"MSG_ANTI_ADDICTION_NTF",950,102201)
V952M=V(4,"MSG_CERTIFICATION_NTF",951,102202)
V953M=V(4,"MSG_MATE_UPDATE_NTF",952,102300)
V954M=V(4,"MSG_MATE_RECOMMEND_REQ",953,102301)
V955M=V(4,"MSG_MATE_RECOMMEND_RSP",954,102302)
V956M=V(4,"MSG_MATE_APPLY_REQ",955,102303)
V957M=V(4,"MSG_MATE_APPLY_RSP",956,102304)
V958M=V(4,"MSG_MATE_HANDLE_APPLY_REQ",957,102305)
V959M=V(4,"MSG_MATE_HANDLE_APPLY_RSP",958,102306)
V960M=V(4,"MSG_MATE_FIND_REQ",959,102307)
V961M=V(4,"MSG_MATE_FIND_RSP",960,102308)
V962M=V(4,"MSG_MATE_END_REQ",961,102309)
V963M=V(4,"MSG_MATE_END_RSP",962,102310)
V964M=V(4,"MSG_MATE_WATER_REQ",963,102311)
V965M=V(4,"MSG_MATE_WATER_RSP",964,102312)
V966M=V(4,"MSG_MATE_REPAIR_REQ",965,102313)
V967M=V(4,"MSG_MATE_REPAIR_RSP",966,102314)
V968M=V(4,"MSG_MATE_LOG_REQ",967,102315)
V969M=V(4,"MSG_MATE_LOG_RSP",968,102316)
V970M=V(4,"MSG_MATE_SELFEVENT_UPDATE_NTF",969,102317)
V971M=V(4,"MSG_MATE_LOG_REWARD_REQ",970,102318)
V972M=V(4,"MSG_MATE_LOG_REWARD_RSP",971,102319)
V973M=V(4,"MSG_MATE_LOG_REDDOT_NTF",972,102320)
V974M=V(4,"MSG_MATE_MAX",973,102400)
V975M=V(4,"MSG_HERO_COMMNET_CONN_SIGN_REQ",974,102401)
V976M=V(4,"MSG_HERO_COMMNET_CONN_SIGN_RSP",975,102402)
V977M=V(4,"MSG_TASK_SUBSEQUENT_REWARD_STAT_UPDATA_NTF",976,102500)
V978M=V(4,"MSG_TASK_SUBSEQUENT_REWARD_GET_REWARD_REQ",977,102501)
V979M=V(4,"MSG_TASK_SUBSEQUENT_REWARD_GET_REWARD_RSP",978,102502)
V980M=V(4,"MSG_TASK_SUBSEQUENT_REWARD_UPDATE_UI_REQ",979,102503)
V981M=V(4,"MSG_TASK_SUBSEQUENT_REWARD_MAX",980,102510)
V982M=V(4,"MSG_HERO_RANK_LIST_UPGRADE_NTF",981,102511)
V983M=V(4,"MSG_HERO_RANK_LIST_MAX",982,102520)
V984M=V(4,"MSG_GET_CLIENT_LOG_RSP",983,102521)
V985M=V(4,"MSG_RUN_CMD_NTF",984,102522)
V986M=V(4,"MSG_RUN_CMD_RSP",985,102523)
V987M=V(4,"MSG_MSG_RUN_CMD_MAX",986,102530)
V988M=V(4,"MSG_SWITCH_LANGUAGE_NTF",987,102531)
V989M=V(4,"MSG_DEV_LANGUAGE_NTF",988,102532)
V990M=V(4,"MSG_ROLE_TIME_ZONE_NTF",989,102533)
V991M=V(4,"MSG_RANK_TOP_REQ",990,102701)
V992M=V(4,"MSG_RANK_TOP_RSP",991,102702)
V993M=V(4,"MSG_RANK_TOP_NTF",992,102703)
V994M=V(4,"MSG_RANK_DETAIL_REQ",993,102704)
V995M=V(4,"MSG_RANK_DETAIL_RSP",994,102705)
V996M=V(4,"MSG_RANK_DETAIL_NTF",995,102706)
V997M=V(4,"MSG_RANK_ACHV_TOP5_REQ",996,102707)
V998M=V(4,"MSG_RANK_ACHV_TOP5_NTF",997,102708)
V999M=V(4,"MSG_RANK_ACHV_REACH_NTF",998,102709)
V1000M=V(4,"MSG_RANK_DETAIL_PALINFO_REQ",999,102710)
V1001M=V(4,"MSG_RANK_DETAIL_PALINFO_RSP",1000,102711)
V1002M=V(4,"MSG_RANK_DETAIL_PALINFO_NTF",1001,102712)
V1003M=V(4,"MSG_HISTORY_PALINFOS_REQ",1002,102713)
V1004M=V(4,"MSG_HISTORY_PALINFOS_RSP",1003,102714)
V1005M=V(4,"MSG_HISTORY_PALINFOS_NTF",1004,102715)
V1006M=V(4,"MSG_RANK_MAX",1005,102800)
V1007M=V(4,"MSG_PASSPORT_REWARD_GAIN_REQ",1006,102801)
V1008M=V(4,"MSG_PASSPORT_REWARD_GAIN_RSP",1007,102802)
V1009M=V(4,"MSG_PASSPORT_PART_NTF",1008,102803)
V1010M=V(4,"MSG_PASSPORT_MAX",1009,102830)
V1011M=V(4,"MSG_KQFRIEND_SEARCH_REQ",1010,102831)
V1012M=V(4,"MSG_KQFRIEND_SEARCH_RSP",1011,102832)
V1013M=V(4,"MSG_KQFRIEND_APPLY_REQ",1012,102833)
V1014M=V(4,"MSG_KQFRIEND_APPLY_RSP",1013,102834)
V1015M=V(4,"MSG_KQFRIEND_APPLY_NTF",1014,102835)
V1016M=V(4,"MSG_KQFRIEND_ANSWER_APPLY_REQ",1015,102836)
V1017M=V(4,"MSG_KQFRIEND_ANSWER_APPLY_RSP",1016,102837)
V1018M=V(4,"MSG_KQFRIEND_ANSWER_APPLY_NTF",1017,102838)
V1019M=V(4,"MSG_KQFRIEND_DELETE_REQ",1018,102839)
V1020M=V(4,"MSG_KQFRIEND_DELETE_RSP",1019,102840)
V1021M=V(4,"MSG_KQFRIEND_DELETE_NTF",1020,102841)
V1022M=V(4,"MSG_KQFRIEND_GIVE_STAR_REQ",1021,102842)
V1023M=V(4,"MSG_KQFRIEND_GIVE_STAR_RSP",1022,102843)
V1024M=V(4,"MSG_KQFRIEND_GIVE_STAR_NTF",1023,102844)
V1025M=V(4,"MSG_KQFRIEND_GAIN_STAR_REQ",1024,102845)
V1026M=V(4,"MSG_KQFRIEND_GAIN_STAR_RSP",1025,102846)
V1027M=V(4,"MSG_KQFRIEND_FRIEND_LIST_REQ",1026,102847)
V1028M=V(4,"MSG_KQFRIEND_FRIEND_LIST_RSP",1027,102848)
V1029M=V(4,"MSG_KQFRIEND_APPLY_LIST_REQ",1028,102849)
V1030M=V(4,"MSG_KQFRIEND_APPLY_LIST_RSP",1029,102850)
V1031M=V(4,"MSG_KQFRIEND_LIST_UPDATE_NTF",1030,102851)
V1032M=V(4,"MSG_KQFRIEND_BLOCK_REQ",1031,102852)
V1033M=V(4,"MSG_KQFRIEND_BLOCK_RSP",1032,102853)
V1034M=V(4,"MSG_KQFRIEND_UNBLOCK_REQ",1033,102854)
V1035M=V(4,"MSG_KQFRIEND_UNBLOCK_RSP",1034,102855)
V1036M=V(4,"MSG_KQFRIEND_GIVE_AND_GAIN_STAR_REQ",1035,102856)
V1037M=V(4,"MSG_KQFRIEND_GIVE_AND_GAIN_STAR_RSP",1036,102857)
V1038M=V(4,"MSG_KQFRIEND_ANSWER_ALL_APPLY_REQ",1037,102858)
V1039M=V(4,"MSG_KQFRIEND_ANSWER_ALL_APPLY_RSP",1038,102859)
V1040M=V(4,"MSG_KQFRIEND_APPLY_AND_BLOCK_LIST_REQ",1039,102860)
V1041M=V(4,"MSG_KQFRIEND_APPLY_AND_BLOCK_LIST_RSP",1040,102861)
V1042M=V(4,"MSG_KQFRIEND_RECOMMEND_REQ",1041,102862)
V1043M=V(4,"MSG_KQFRIEND_RECOMMEND_RSP",1042,102863)
V1044M=V(4,"MSG_KQFRIEND_MAX",1043,102890)
V1045M=V(4,"MSG_FIRST_CREATE_ROLE_NTF",1044,102891)
V1046M=V(4,"MSG_UNLOCAK_THE_SOUL_LINK_SLOT_REQ",1045,102900)
V1047M=V(4,"MSG_UNLOCAK_THE_SOUL_LINK_SLOT_RSP",1046,102901)
V1048M=V(4,"MSG_INCREASE_SOUL_LINK_SLOT_LIMIT_REQ",1047,102902)
V1049M=V(4,"MSG_INCREASE_SOUL_LINK_SLOT_LIMIT_RSP",1048,102903)
V1050M=V(4,"MSG_HERO_IN_SOUL_LINK_SLOT_REQ",1049,102904)
V1051M=V(4,"MSG_HERO_IN_SOUL_LINK_SLOT_RSP",1050,102905)
V1052M=V(4,"MSG_HERO_OUT_SOUL_LINK_SLOT_REQ",1051,102906)
V1053M=V(4,"MSG_HERO_OUT_SOUL_LINK_SLOT_RSP",1052,102907)
V1054M=V(4,"MSG_UPDATE_SOUL_LINK_MOUDLE_NTF",1053,102908)
V1055M=V(4,"MSG_SOUL_LINK_SLOT_COOL_RESET_REQ",1054,102909)
V1056M=V(4,"MSG_SOUL_LINK_SLOT_COOL_RESET_RSP",1055,102910)
V1057M=V(4,"MSG_SOUL_LINK_SLOT_MAX",1056,102920)
V1058M=V(4,"MSG_FRAME_SELECT_REQ",1057,102921)
V1059M=V(4,"MSG_FRAME_SELECT_RSP",1058,102922)
V1060M=V(4,"MSG_FRAME_SELECTPROP_REQ",1059,102924)
V1061M=V(4,"MSG_FRAME_SELECTPROP_RSP",1060,102925)
V1062M=V(4,"MSG_FRAME_MAX",1061,102950)
V1063M=V(4,"MSG_PEAK_UPDATE_NTF",1062,102951)
V1064M=V(4,"MSG_PEAK_LEVEL_REQ",1063,102952)
V1065M=V(4,"MSG_PEAK_LEVEL_RSP",1064,102953)
V1066M=V(4,"MSG_PEAK_RELIC_REQ",1065,102954)
V1067M=V(4,"MSG_PEAK_RELIC_RSP",1066,102955)
V1068M=V(4,"MSG_PEAK_RESET_REQ",1067,102956)
V1069M=V(4,"MSG_PEAK_RESET_RSP",1068,102957)
V1070M=V(4,"MSG_PEAK_MOVE_REQ",1069,102958)
V1071M=V(4,"MSG_PEAK_MOVE_RSP",1070,102959)
V1072M=V(4,"MSG_PEAK_EVENT_COMMON_REQ",1071,102960)
V1073M=V(4,"MSG_PEAK_EVENT_COMMON_RSP",1072,102961)
V1074M=V(4,"MSG_PEAK_LEVEL_EXIT_REQ",1073,102962)
V1075M=V(4,"MSG_PEAK_SET_DIALOGFLAG_REQ",1074,102963)
V1076M=V(4,"MSG_PEAK_SET_DIALOGFLAG_RSP",1075,102964)
V1077M=V(4,"MSG_PEAK_MOVE_END_REQ",1076,102965)
V1078M=V(4,"MSG_PEAK_MOVE_END_RSP",1077,102966)
V1079M=V(4,"MSG_PEAK_FOG_REQ",1078,102967)
V1080M=V(4,"MSG_PEAK_FOG_RSP",1079,102968)
V1081M=V(4,"MSG_PEAK_SET_CLIENT_REQ",1080,102969)
V1082M=V(4,"MSG_PEAK_SET_CLIENT_RSP",1081,102970)
V1083M=V(4,"MSG_PEAK_MAX",1082,103100)
V1084M=V(4,"MSG_PLEAGUEHELP_INFO_NTF",1083,103101)
V1085M=V(4,"MSG_PLEAGUEHELP_AIDALLIES_REQ",1084,103102)
V1086M=V(4,"MSG_PLEAGUEHELP_AIDALLIES_RSP",1085,103103)
V1087M=V(4,"MSG_PLEAGUEHELP_RECIEVEAWARD_REQ",1086,103104)
V1088M=V(4,"MSG_PLEAGUEHELP_RECIEVEAWARD_RSP",1087,103105)
V1089M=V(4,"MSG_PLEAGUEGELP_REWARDINFO_NTF",1088,103106)
V1090M=V(4,"MSG_PLEAGUEHELP_SEEKHELP_REQ",1089,103107)
V1091M=V(4,"MSG_PLEAGUEHELP_SEEKHELP_RSP",1090,103108)
V1092M=V(4,"MSG_PLEAGUEHELP_SEEKHELP_POOL_REQ",1091,103109)
V1093M=V(4,"MSG_PLEAGUEHELP_SEEKHELP_POOL_RSP",1092,103110)
V1094M=V(4,"MSG_PLEAGUEHELP_SEL_SEEKHELP_REQ",1093,103111)
V1095M=V(4,"MSG_PLEAGUEHELP_SEL_SEEKHELP_RSP",1094,103112)
V1096M=V(4,"MSG_PLEAGUEHELP_MAX",1095,103130)
V1097M=V(4,"MSG_CHAMPIONSHIPS_GET_RANKS_REQ",1096,103131)
V1098M=V(4,"MSG_CHAMPIONSHIPS_GET_RANKS_RSP",1097,103132)
V1099M=V(4,"MSG_CURIDLE_CHAPTER_PLAYER_PROMOTE_REQ",1098,103133)
V1100M=V(4,"MSG_CURIDLE_CHAPTER_PLAYER_PROMOTE_RSP",1099,103134)
V1101M=V(4,"MSG_CHAMPIONSHIPS_MAX",1100,103150)
V1102M=V(4,"MSG_FARM_SLOT_INIT_DATA_REQ",1101,103151)
V1103M=V(4,"MSG_FARM_SLOT_INIT_DATA_RSP",1102,103152)
V1104M=V(4,"MSG_FARM_SLOT_SPIN_REQ",1103,103153)
V1105M=V(4,"MSG_FARM_SLOT_SPIN_RSP",1104,103154)
V1106M=V(4,"MSG_FARM_SLOT_UPDATE_NTF",1105,103155)
V1107M=V(4,"MSG_FARM_SLOT_DIG_REQ",1106,103156)
V1108M=V(4,"MSG_FARM_SLOT_DIG_RSP",1107,103157)
V1109M=V(4,"MSG_FARM_SLOT_DETIAL_DATA_REQ",1108,103158)
V1110M=V(4,"MSG_FARM_SLOT_DETIAL_DATA_RSP",1109,103159)
V1111M=V(4,"MSG_FARM_SLOT_ATTACK_REQ",1110,103160)
V1112M=V(4,"MSG_FARM_SLOT_ATTACK_RSP",1111,103161)
V1113M=V(4,"MSG_FARM_SLOT_REPAIR_REQ",1112,103162)
V1114M=V(4,"MSG_FARM_SLOT_REPAIR_RSP",1113,103163)
V1115M=V(4,"MSG_FARM_ACHV_GAIN_REQ",1114,103164)
V1116M=V(4,"MSG_FARM_ACHV_GAIN_RSP",1115,103165)
V1117M=V(4,"MSG_FARM_SLOT_MAX",1116,103250)
V1118M=V(4,"MSG_LEAGUEWAR_APPLY_REQ",1117,104000)
V1119M=V(4,"MSG_LEAGUEWAR_APPLY_RSP",1118,104001)
V1120M=V(4,"MSG_LEAGUEWAR_BASE_INFO_REQ",1119,104002)
V1121M=V(4,"MSG_LEAGUEWAR_BASE_INFO_RSP",1120,104003)
V1122M=V(4,"MSG_LEAGUEWAR_REWARD_REQ",1121,104004)
V1123M=V(4,"MSG_LEAGUEWAR_REWARD_RSP",1122,104005)
V1124M=V(4,"MSG_LEAGUEWAR_SET_LINEUP_REQ",1123,104006)
V1125M=V(4,"MSG_LEAGUEWAR_SET_LINEUP_RSP",1124,104007)
V1126M=V(4,"MSG_LEAGUEWAR_REMOVE_LINEUP_REQ",1125,104008)
V1127M=V(4,"MSG_LEAGUEWAR_REMOVE_LINEUP_RSP",1126,104009)
V1128M=V(4,"MSG_LEAGUEWAR_RANK_REQ",1127,104010)
V1129M=V(4,"MSG_LEAGUEWAR_RANK_RSP",1128,104011)
V1130M=V(4,"MSG_LEAGUEWAR_BATTLE_INFO_REQ",1129,104012)
V1131M=V(4,"MSG_LEAGUEWAR_BATTLE_INFO_RSP",1130,104013)
V1132M=V(4,"MSG_LEAGUEWAR_ATTACK_INFO_REQ",1131,104016)
V1133M=V(4,"MSG_LEAGUEWAR_ATTACK_INFO_RSP",1132,104017)
V1134M=V(4,"MSG_LEAGUEWAR_SCORE_RANK_REQ",1133,104018)
V1135M=V(4,"MSG_LEAGUEWAR_SCORE_RANK_RSP",1134,104019)
V1136M=V(4,"MSG_LEAGUEWAR_ENTER_BATTLE_REQ",1135,104020)
V1137M=V(4,"MSG_LEAGUEWAR_ENTER_BATTLE_RSP",1136,104021)
V1138M=V(4,"MSG_LEAGUEWAR_ATTACK_INFO_NTF",1137,104022)
V1139M=V(4,"MSG_LEAGUEWAR_REPORT_NTF",1138,104023)
V1140M=V(4,"MSG_LEAGUEWAR_UPDATE_TROOP_NTF",1139,104024)
V1141M=V(4,"MSG_LEAGUEBOSSWAR_BASE_INFO_REQ",1140,104025)
V1142M=V(4,"MSG_LEAGUEBOSSWAR_BASE_INFO_RSP",1141,104026)
V1143M=V(4,"MSG_LEAGUEBOSSWAR_REWARD_REQ",1142,104027)
V1144M=V(4,"MSG_LEAGUEBOSSWAR_REWARD_RSP",1143,104028)
V1145M=V(4,"MSG_LEAGUEBOSSWAR_ENTER_BATTLE_REQ",1144,104029)
V1146M=V(4,"MSG_LEAGUEBOSSWAR_ENTER_BATTLE_RSP",1145,104030)
V1147M=V(4,"MSG_LEAGUEBOSSWAR_RANK_INFO_REQ",1146,104031)
V1148M=V(4,"MSG_LEAGUEBOSSWAR_RANK_INFO_RSP",1147,104032)
V1149M=V(4,"MSG_LEAGUEBOSSWAR_LEAGUE_INFO_REQ",1148,104033)
V1150M=V(4,"MSG_LEAGUEBOSSWAR_LEAGUE_INFO_RSP",1149,104034)
V1151M=V(4,"MSG_LEAGUEBOSSWAR_SHOP_BUYGOODS_REQ",1150,104035)
V1152M=V(4,"MSG_LEAGUEBOSSWAR_SHOP_BUYGOODS_RSP",1151,104036)
V1153M=V(4,"MSG_LEAGUEWAR_USEDPALS_REQ",1152,104037)
V1154M=V(4,"MSG_LEAGUEWAR_USEDPALS_RSP",1153,104038)
V1155M=V(4,"MSG_LEAGUEBOSSWAR_ATTACK_INFO_REQ",1154,104039)
V1156M=V(4,"MSG_LEAGUEBOSSWAR_ATTACK_INFO_RSP",1155,104040)
V1157M=V(4,"MSG_LEAGUE_RECRUIT_POINT_NTF",1156,104041)
V1158M=V(4,"MSG_LEAGUE_GET_ROLE_NAME_REQ",1157,104042)
V1159M=V(4,"MSG_LEAGUE_GET_ROLE_NAME_RSP",1158,104043)
V1160M=V(4,"MSG_LEAGUE_UPDATEINFO_REQ",1159,104044)
V1161M=V(4,"MSG_LEAGUE_UPDATEINFO_RSP",1160,104045)
V1162M=V(4,"MSG_LEAGUEWAR_MAX",1161,104050)
V1163M=V(4,"MSG_LEA_ACTIVITY_BOSS_CALL_REQ",1162,104100)
V1164M=V(4,"MSG_LEA_ACTIVITY_BOSS_CALL_RSP",1163,104101)
V1165M=V(4,"MSG_LEA_ACTIVITY_BOSS_RANK_INFO_REQ",1164,104102)
V1166M=V(4,"MSG_LEA_ACTIVITY_BOSS_RANK_INFO_RSP",1165,104103)
V1167M=V(4,"MSG_LEA_ACTIVITY_BOSS_GET_RANK_REWARD_REQ",1166,104104)
V1168M=V(4,"MSG_LEA_ACTIVITY_BOSS_GET_RANK_REWARD_RSP",1167,104105)
V1169M=V(4,"MSG_LEA_ACTIVITY_BOSS_OPEN_INFO_REQ",1168,104106)
V1170M=V(4,"MSG_LEA_ACTIVITY_BOSS_OPEN_INFO_RSP",1169,104107)
V1171M=V(4,"MSG_LEA_ACTIVITY_BOSS_SETTLE_NTF",1170,104108)
V1172M=V(4,"MSG_LEA_ACTIVITY_BOSS_MAX",1171,104200)
V1173M=V(4,"MSG_SEVENCHALLENGE_UPDATE_NTF",1172,104201)
V1174M=V(4,"MSG_SEVENCHALLENGE_MAX",1173,104240)
V1175M=V(4,"MSG_OPERATIVE_PART_DATA",1174,104306)
V1176M=V(4,"MSG_ACTIVITY_GROW_GIFT2_STATE",1175,104307)
V1177M=V(4,"MSG_OPERATIVE_SHARER_PRIZE_RSP",1176,104310)
V1178M=V(4,"MSG_SET_CST_LINEUP_REQ",1177,104311)
V1179M=V(4,"MSG_SET_CST_LINEUP_RSP",1178,104312)
V1180M=V(4,"MSG_MERGE_SERVER_NTF",1179,104350)
V1181M=V(4,"MSG_RETURNACT_NTF",1180,104351)
V1182M=V(4,"MSG_TITLE_SELECT_REQ",1181,104370)
V1183M=V(4,"MSG_TITLE_SELECT_RSP",1182,104371)
V1184M=V(4,"MSG_TITLE_SELECTPROP_REQ",1183,104373)
V1185M=V(4,"MSG_TITLE_SELECTPROP_RSP",1184,104374)
V1186M=V(4,"MSG_TITLE_MAX",1185,104400)
V1187M=V(4,"MSG_VOIDARENA_DATA_REQ",1186,104401)
V1188M=V(4,"MSG_VOIDARENA_DATA_RSP",1187,104402)
V1189M=V(4,"MSG_VOIDARENA_UPDATE_NTF",1188,104403)
V1190M=V(4,"MSG_VOIDARENA_LOST_NTF",1189,104404)
V1191M=V(4,"MSG_VOIDARENA_LOST_DEL_REQ",1190,104405)
V1192M=V(4,"MSG_VOIDARENA_LOST_DEL_RSP",1191,104406)
V1193M=V(4,"MSG_VOIDARENA_MAX",1192,104500)
V1194M=V(4,"MSG_TOPRACE_BASE_DATA_REQ",1193,104501)
V1195M=V(4,"MSG_TOPRACE_BASE_DATA_RSP",1194,104502)
V1196M=V(4,"MSG_TOPRACE_BATTLEVIDEO_REQ",1195,104503)
V1197M=V(4,"MSG_TOPRACE_BATTLEVIDEO_RSP",1196,104504)
V1198M=V(4,"MSG_TOPRACE_SETDEFENSELINEUP_REQ",1197,104505)
V1199M=V(4,"MSG_TOPRACE_SETDEFENSELINEUP_RSP",1198,104506)
V1200M=V(4,"MSG_TOPRACE_GUESSPUSH_REQ",1199,104507)
V1201M=V(4,"MSG_TOPRACE_GUESSPUSH_RSP",1200,104508)
V1202M=V(4,"MSG_TOPRACE_GUESS_REQ",1201,104509)
V1203M=V(4,"MSG_TOPRACE_GUESS_RSP",1202,104510)
V1204M=V(4,"MSG_TOPRACE_GETRANK_REWARD_REQ",1203,104511)
V1205M=V(4,"MSG_TOPRACE_GETRANK_REWARD_RSP",1204,104512)
V1206M=V(4,"MSG_TOPRACE_GETTOPLIST_REQ",1205,104513)
V1207M=V(4,"MSG_TOPRACE_GETTOPLIST_RSP",1206,104514)
V1208M=V(4,"MSG_TOPRACE_GROUPSTATE_REQ",1207,104515)
V1209M=V(4,"MSG_TOPRACE_GROUPSTATE_RSP",1208,104516)
V1210M=V(4,"MSG_TOPRACE_18PAL_LIST_REQ",1209,104517)
V1211M=V(4,"MSG_TOPRACE_18PAL_LIST_RSP",1210,104518)
V1212M=V(4,"MSG_TOPRACE_VOTE_NDAY_AGTABLE_REQ",1211,104519)
V1213M=V(4,"MSG_TOPRACE_VOTE_NDAY_AGTABLE_RSP",1212,104520)
V1214M=V(4,"MSG_TOPRACE_CHAMPION_APPLAUD_REQ",1213,104521)
V1215M=V(4,"MSG_TOPRACE_CHAMPION_APPLAUD_RSP",1214,104522)
V1216M=V(4,"MSG_TOPRACE_BASE_DATA_NTF",1215,104523)
V1217M=V(4,"MSG_TOPRACE_MAX",1216,104550)
V1218M=V(4,"MSG_SLG_MAPDETAIL_REQ",1217,104601)
V1219M=V(4,"MSG_SLG_MAPDETAIL_RSP",1218,104602)
V1220M=V(4,"MSG_SLG_BASEINFO_REQ",1219,104603)
V1221M=V(4,"MSG_SLG_BASEINFO_RSP",1220,104604)
V1222M=V(4,"MSG_SLG_SCINFO_REQ",1221,104605)
V1223M=V(4,"MSG_SLG_SCINFO_RSP",1222,104606)
V1224M=V(4,"MSG_SLG_SCUPGRADE_REQ",1223,104607)
V1225M=V(4,"MSG_SLG_SCUPGRADE_RSP",1224,104608)
V1226M=V(4,"MSG_SLG_MOVECITY_REQ",1225,104609)
V1227M=V(4,"MSG_SLG_MOVECITY_RSP",1226,104610)
V1228M=V(4,"MSG_SLG_REFRESHMONSTER_REQ",1227,104611)
V1229M=V(4,"MSG_SLG_REFRESHMONSTER_RSP",1228,104612)
V1230M=V(4,"MSG_SLG_TASKINFO_REQ",1229,104613)
V1231M=V(4,"MSG_SLG_TASKINFO_RSP",1230,104614)
V1232M=V(4,"MSG_SLG_TASKREWARD_REQ",1231,104615)
V1233M=V(4,"MSG_SLG_TASKREWARD_RSP",1232,104616)
V1234M=V(4,"MSG_SLG_SCUPGRADE_CANCEL_REQ",1233,104617)
V1235M=V(4,"MSG_SLG_SCUPGRADE_CANCEL_RSP",1234,104618)
V1236M=V(4,"MSG_SLG_MAPDETAIL_NTF",1235,104619)
V1237M=V(4,"MSG_SLG_SEARCH_REQ",1236,104620)
V1238M=V(4,"MSG_SLG_SEARCH_RSP",1237,104621)
V1239M=V(4,"MSG_SLG_LEAGUEMEMBER_INFO_REQ",1238,104622)
V1240M=V(4,"MSG_SLG_LEAGUEMEMBER_INFO_RSP",1239,104623)
V1241M=V(4,"MSG_SLG_PRODUCE_REQ",1240,104624)
V1242M=V(4,"MSG_SLG_PRODUCE_RSP",1241,104625)
V1243M=V(4,"MSG_SLG_SET_TEAM_REQ",1242,104819)
V1244M=V(4,"MSG_SLG_SET_TEAM_RSP",1243,104820)
V1245M=V(4,"MSG_SLG_SINGLE_MOVE_TEAM_REQ",1244,104821)
V1246M=V(4,"MSG_SLG_SINGLE_MOVE_TEAM_RSP",1245,104822)
V1247M=V(4,"MSG_SLG_LAUNCH_MASS_TEAM_REQ",1246,104823)
V1248M=V(4,"MSG_SLG_LAUNCH_MASS_TEAM_RSP",1247,104824)
V1249M=V(4,"MSG_SLG_JOIN_MASS_TEAM_REQ",1248,104825)
V1250M=V(4,"MSG_SLG_JOIN_MASS_TEAM_RSP",1249,104826)
V1251M=V(4,"MSG_SLG_CANCEL_MASS_TEAM_REQ",1250,104827)
V1252M=V(4,"MSG_SLG_CANCEL_MASS_TEAM_RSP",1251,104828)
V1253M=V(4,"MSG_SLG_EXIT_MASS_TEAM_REQ",1252,104829)
V1254M=V(4,"MSG_SLG_EXIT_MASS_TEAM_RSP",1253,104830)
V1255M=V(4,"MSG_SLG_COLLECT_MOVE_TEAM_REQ",1254,104831)
V1256M=V(4,"MSG_SLG_COLLECT_MOVE_TEAM_RSP",1255,104832)
V1257M=V(4,"MSG_SLG_STATIONED_MOVE_TEAM_REQ",1256,104833)
V1258M=V(4,"MSG_SLG_STATIONED_MOVE_TEAM_RSP",1257,104834)
V1259M=V(4,"MSG_SLG_DETECT_MOVE_TEAM_REQ",1258,104835)
V1260M=V(4,"MSG_SLG_DETECT_MOVE_TEAM_RSP",1259,104836)
V1261M=V(4,"MSG_SLG_CANCEL_MOVE_TEAM_REQ",1260,104837)
V1262M=V(4,"MSG_SLG_CANCEL_MOVE_TEAM_RSP",1261,104838)
V1263M=V(4,"MSG_SLG_WALK_REQ",1262,104839)
V1264M=V(4,"MSG_SLG_WALK_RSP",1263,104840)
V1265M=V(4,"MSG_SLG_MASS_KICK_MEMBER_REQ",1264,104841)
V1266M=V(4,"MSG_SLG_MASS_KICK_MEMBER_RSP",1265,104842)
V1267M=V(4,"MSG_SLG_MAX",1266,105000)
V1268M=V(4,"MSG_LEAGUE_MEDAL_BASE_REQ",1267,105051)
V1269M=V(4,"MSG_LEAGUE_MEDAL_BASE_RSP",1268,105052)
V1270M=V(4,"MSG_LEAGUE_MEDAL_BASE_NTF",1269,105053)
V1271M=V(4,"MSG_LEAGUE_MEDAL_DETAIL_REQ",1270,105054)
V1272M=V(4,"MSG_LEAGUE_MEDAL_DETAIL_RSP",1271,105055)
V1273M=V(4,"MSG_LEAGUE_MEDAL_REWARD_REQ",1272,105056)
V1274M=V(4,"MSG_LEAGUE_MEDAL_REWARD_RSP",1273,105057)
V1275M=V(4,"MSG_LEAGUE_MEDAL_WARNDEL_REQ",1274,105058)
V1276M=V(4,"MSG_LEAGUE_MEDAL_WARNDEL_RSP",1275,105059)
V1277M=V(4,"MSG_LEAGUE_MEDAL_WARNAUTO_REQ",1276,105060)
V1278M=V(4,"MSG_LEAGUE_MEDAL_WARNAUTO_RSP",1277,105061)
V1279M=V(4,"MSG_LEAGUE_MEDAL_WARN_REQ",1278,105062)
V1280M=V(4,"MSG_LEAGUE_MEDAL_WARN_RSP",1279,105063)
V1281M=V(4,"MSG_LEAGUE_MEDAL_MAX",1280,105100)
V1282M=V(4,"MSG_LIFETIME_CARD_REWARD_RSP",1281,105101)
V1283M=V(4,"MSG_LIFETIME_CARD_MAX",1282,105110)
V1284M=V(4,"MSG_XYX_PASS_LV_REQ",1283,105111)
V1285M=V(4,"MSG_XYX_PASS_LV_RSP",1284,105112)
V1286M=V(4,"MSG_XYX_SET_LINK_REQ",1285,105113)
V1287M=V(4,"MSG_XYX_SET_LINK_RSP",1286,105114)
V1288M=V(4,"MSG_XYX_LINK_DATA_NTF",1287,105115)
V1289M=V(4,"MSG_XYX_SET_LEVEL_REQ",1288,105116)
V1290M=V(4,"MSG_XYX_SET_LEVEL_RSP",1289,105117)
V1291M=V(4,"MSG_XYX_LEVEL_DATA_NTF",1290,105118)
V1292M=V(4,"MSG_XYX_ITEMACTIVE_REQ",1291,105119)
V1293M=V(4,"MSG_XYX_ITEMACTIVE_RSP",1292,105120)
V1294M=V(4,"MSG_XYX_ADVRW_REQ",1293,105121)
V1295M=V(4,"MSG_XYX_ADVRW_RSP",1294,105122)
V1296M=V(4,"MSG_XYX_STAGE_REWARD_REQ",1295,105123)
V1297M=V(4,"MSG_XYX_STAGE_REWARD_RSP",1296,105124)
V1298M=V(4,"MSG_XYX_LEVEL_REQ",1297,105125)
V1299M=V(4,"MSG_XYX_LEVEL_RSP",1298,105126)
V1300M=V(4,"MSG_XYX_SET_PROP_REQ",1299,105127)
V1301M=V(4,"MSG_XYX_SET_PROP_RSP",1300,105128)
V1302M=V(4,"MSG_XYX_GET_PROP_REQ",1301,105129)
V1303M=V(4,"MSG_XYX_GET_PROP_RSP",1302,105130)
V1304M=V(4,"MSG_XYX_PROP_NTF",1303,105131)
V1305M=V(4,"MSG_XYX_STAGE_REWARD_MULTI_REQ",1304,105132)
V1306M=V(4,"MSG_XYX_STAGE_REWARD_MULTI_RSP",1305,105133)
V1307M=V(4,"MSG_XYX_DATA_NTF",1306,105134)
V1308M=V(4,"MSG_XYX_GOT_DELAYAWARD_REQ",1307,105135)
V1309M=V(4,"MSG_XYX_GOT_DELAYAWARD_RSP",1308,105136)
V1310M=V(4,"MSG_XYX_SET_FAILSTAGE_REQ",1309,105137)
V1311M=V(4,"MSG_XYX_SET_FAILSTAGE_RSP",1310,105138)
V1312M=V(4,"MSG_XYX_INITGAMEDATA_REQ",1311,105139)
V1313M=V(4,"MSG_XYX_INITGAMEDATA_RSP",1312,105140)
V1314M=V(4,"MSG_XYX_GET_DATA_REQ",1313,105141)
V1315M=V(4,"MSG_XYX_GOT_GAME_REWARD_REQ",1314,105142)
V1316M=V(4,"MSG_XYX_GOT_GAME_REWARD_RSP",1315,105143)
V1317M=V(4,"MSG_XYX_ENDLESS_SET_HEROPOWER_REQ",1316,105144)
V1318M=V(4,"MSG_XYX_ENDLESS_SET_HEROPOWER_RSP",1317,105145)
V1319M=V(4,"MSG_MODULEOPEN_UPDATE_NTF",1318,105150)
V1320M=V(4,"MSG_SPACEEXPLORTION_BASE_DATA_NTF",1319,105160)
V1321M=V(4,"MSG_SPACEEXPLORTION_PLANET_DATA_NTF",1320,105161)
V1322M=V(4,"MSG_SPACEEXPLORTION_PLANET_UPDATA_DATA_NTF",1321,105162)
V1323M=V(4,"MSG_SPACEEXPLORTION_PROP_DATA_NTF",1322,105163)
V1324M=V(4,"MSG_SPACEEXPLORTION_MAX",1323,105170)
V1325M=V(4,"MSG_BATTLESHIPTECH_LV_DATA_NTF",1324,105171)
V1326M=V(4,"MSG_BATTLESHIPTECH_MAX",1325,105180)
V1327M=V(4,"MSG_LEGEND_SETDEFENSELINEUP_REQ",1326,105201)
V1328M=V(4,"MSG_LEGEND_SETDEFENSELINEUP_RSP",1327,105202)
V1329M=V(4,"MSG_LEGEND_GETDEFTROOP_REQ",1328,105203)
V1330M=V(4,"MSG_LEGEND_GETDEFTROOP_RSP",1329,105204)
V1331M=V(4,"MSG_LEGEND_BATTLE_REQ",1330,105205)
V1332M=V(4,"MSG_LEGEND_BATTLE_RSP",1331,105206)
V1333M=V(4,"MSG_LEGEND_DATA_NTF",1332,105207)
V1334M=V(4,"MSG_LEGEND_RANK_DATA_REQ",1333,105208)
V1335M=V(4,"MSG_LEGEND_RANK_DATA_RSP",1334,105209)
V1336M=V(4,"MSG_LEGEND_CHALLENGE_LIST_REQ",1335,105210)
V1337M=V(4,"MSG_LEGEND_CHALLENGE_LIST_RSP",1336,105211)
V1338M=V(4,"MSG_LEGEND_BATTLE_RECORD_REQ",1337,105212)
V1339M=V(4,"MSG_LEGEND_BATTLE_RECORD_RSP",1338,105213)
V1340M=V(4,"MSG_LEGEND_SIGN_REQ",1339,105214)
V1341M=V(4,"MSG_LEGEND_SIGN_RSP",1340,105215)
V1342M=V(4,"MSG_LEGEND_PRIZE_REQ",1341,105216)
V1343M=V(4,"MSG_LEGEND_PRIZE_RSP",1342,105217)
V1344M=V(4,"MSG_LEGEND_TROOPS_REQ",1343,105218)
V1345M=V(4,"MSG_LEGEND_TROOPS_RSP",1344,105219)
V1346M=V(4,"MSG_LEGEND_GET_VICTORY_REWARD_REQ",1345,105220)
V1347M=V(4,"MSG_LEGEND_GET_VICTORY_REWARD_RSP",1346,105221)
V1348M=V(4,"MSG_BATTLE_TBS_NTF",1347,105351)
V1349M=V(4,"MSG_CHINARED_UPDATE_PART_NTF",1348,105352)
V1350M=V(4,"MSG_CHINARED_SWEEP_REQ",1349,105353)
V1351M=V(4,"MSG_CHINARED_SWEEP_RSP",1350,105354)
V1352M=V(4,"MSG_CHINARED_CHAPTER_GAIN_REQ",1351,105355)
V1353M=V(4,"MSG_CHINARED_CHAPTER_GAIN_RSP",1352,105356)
V1354M=V(4,"MSG_CHINARED_UPDATE_PART_MAX",1353,105380)
V1355M=V(4,"MSG_DERACOTE_REFINE_REQ",1354,105381)
V1356M=V(4,"MSG_DERACOTE_REFINE_RSP",1355,105382)
V1357M=V(4,"MSG_DERACOTE_LOCK_REQ",1356,105383)
V1358M=V(4,"MSG_DERACOTE_LOCK_RSP",1357,105384)
V1359M=V(4,"MSG_DERACOTE_SHOP_RAND_ONCE_REQ",1358,105385)
V1360M=V(4,"MSG_DERACOTE_SHOP_RAND_ONCE_RSP",1359,105386)
V1361M=V(4,"MSG_DERACOTE_SHOP_RAND_TEN_REQ",1360,105387)
V1362M=V(4,"MSG_DERACOTE_SHOP_RAND_TEN_RSP",1361,105388)
V1363M=V(4,"MSG_DERACOTE_SHOP_SET_WISH_PAL_REQ",1362,105389)
V1364M=V(4,"MSG_DERACOTE_SHOP_SET_WISH_PAL_RSP",1363,105390)
V1365M=V(4,"MSG_DERACOTE_SHOP_GET_WISH_PAL_REQ",1364,105391)
V1366M=V(4,"MSG_DERACOTE_SHOP_GET_WISH_PAL_RSP",1365,105392)
V1367M=V(4,"MSG_DERACOTE_SHOP_USE_EXCHANGE_CARD_REQ",1366,105393)
V1368M=V(4,"MSG_DERACOTE_SHOP_USE_EXCHANGE_CARD_RSP",1367,105394)
V1369M=V(4,"MSG_DERACOTE_SHOP_GET_INFO_REQ",1368,105395)
V1370M=V(4,"MSG_DERACOTE_SHOP_GET_INFO_RSP",1369,105396)
V1371M=V(4,"MSG_DERACOTE_DECOMPOSE_REQ",1370,105397)
V1372M=V(4,"MSG_DERACOTE_DECOMPOSE_RSP",1371,105398)
V1373M=V(4,"MSG_DERACOTE_SHOP_SET_ALL_WISH_PAL_REQ",1372,105399)
V1374M=V(4,"MSG_DECORATE_MAX",1373,105480)
V1375M=V(4,"MSG_TREASURERARE_REFRESH_DATA_NTF",1374,105801)
V1376M=V(4,"MSG_TREASURERARE_ADVANCE_REQ",1375,105802)
V1377M=V(4,"MSG_TREASURERARE_ADVANCE_RSP",1376,105803)
V1378M=V(4,"MSG_TREASURERARE_ENHANCE_REQ",1377,105804)
V1379M=V(4,"MSG_TREASURERARE_ENHANCE_RSP",1378,105805)
V1380M=V(4,"MSG_TREASURERARE_HAMMER_REQ",1379,105806)
V1381M=V(4,"MSG_TREASURERARE_HAMMER_RSP",1380,105807)
V1382M=V(4,"MSG_TREASURERARE_REWARD_REQ",1381,105808)
V1383M=V(4,"MSG_TREASURERARE_REWARD_RSP",1382,105809)
V1384M=V(4,"MSG_TREASURERARE_UNLOCK_MAKER_DATA_NTF",1383,105810)
V1385M=V(4,"MSG_TREASURERARE_RECYCLE_MAKER_DATA_NTF",1384,105811)
V1386M=V(4,"MSG_TREASURERARE_GUILD_REQ",1385,105812)
V1387M=V(4,"MSG_TREASURERARE_GUILD_RSP",1386,105813)
V1388M=V(4,"MSG_TREASURERARE_COMMON_GIFT_REQ",1387,105814)
V1389M=V(4,"MSG_TREASURERARE_COMMON_GIFT_RSP",1388,105815)
V1390M=V(4,"MSG_TREASURERARE_COMMON_GIFT_NTF",1389,105816)
V1391M=V(4,"MSG_TREASURERARE_SPECIAL_PROP_REQ",1390,105817)
V1392M=V(4,"MSG_TREASURERARE_SPECIAL_PROP_RSP",1391,105818)
V1393M=V(4,"MSG_TREASURERARE_OPENACTIVITY_NTF",1392,105819)
V1394M=V(4,"MSG_TREASURERARE_OPENACTIVITY_REQ",1393,105820)
V1395M=V(4,"MSG_TREASURERARE_OPENACTIVITY_RSP",1394,105821)
V1396M=V(4,"MSG_TREASURERARE_SPECIAL_PROP_NTF",1395,105822)
V1397M=V(4,"MSG_TREASURERARE_NEW_TREASURERARE_NTF",1396,105823)
V1398M=V(4,"MSG_TREASURERARE_RECYCLE_TREASURERARE_NTF",1397,105831)
V1399M=V(4,"MSG_TREASURERARE_PRIVILEGE_REDUCETIME_NTF",1398,105832)
V1400M=V(4,"MSG_TREASURERARE_PACKET_CONVERT_NTF",1399,105833)
V1401M=V(4,"MSG_TREASURERARE_RECYCLE_MAX",1400,105835)
V1402M=V(4,"MSG_REDPACKET_INFO_NTF",1401,105950)
V1403M=V(4,"MSG_NEWYEAR_GIVE_REDPACKET_REQ",1402,105951)
V1404M=V(4,"MSG_NEWYEAR_GIVE_REDPACKET_RSP",1403,105952)
V1405M=V(4,"MSG_NEWYEAR_GRAB_REDPACKET_REQ",1404,105953)
V1406M=V(4,"MSG_NEWYEAR_GRAB_REDPACKET_RSP",1405,105954)
V1407M=V(4,"MSG_NEWYEAR_REDPACKET_DETAIL_REQ",1406,105955)
V1408M=V(4,"MSG_NEWYEAR_REDPACKET_DETAIL_RSP",1407,105956)
V1409M=V(4,"MSG_NEWYEAR_GIVE_REDPACKET_RANK_REQ",1408,105957)
V1410M=V(4,"MSG_NEWYEAR_GIVE_REDPACKET_RANK_RSP",1409,105958)
V1411M=V(4,"MSG_NEWYEAR_SEND_FUCARD_REQ",1410,105959)
V1412M=V(4,"MSG_NEWYEAR_SEND_FUCARD_RSP",1411,105960)
V1413M=V(4,"MSG_ANNIVERSARY_GRAB_DATA_REQ",1412,105961)
V1414M=V(4,"MSG_ANNIVERSARY_GRAB_DATA_RSP",1413,105962)
V1415M=V(4,"MSG_REBIRTHSPACE_START_BATTLE_REQ",1414,105750)
V1416M=V(4,"MSG_REBIRTHSPACE_START_BATTLE_RSP",1415,105751)
V1417M=V(4,"MSG_REBIRTHSPACE_BATTLE_RECORD_REQ",1416,105754)
V1418M=V(4,"MSG_REBIRTHSPACE_BATTLE_RECORD_RSP",1417,105755)
V1419M=V(4,"MSG_REBIRTHSPACE_DATA_UPDATE_REQ",1418,105756)
V1420M=V(4,"MSG_REBIRTHSPACE_DATA_UPDATE_RSP",1419,105757)
V1421M=V(4,"MSG_REBIRTHSPACE_RANK_REQ",1420,105758)
V1422M=V(4,"MSG_REBIRTHSPACE_RANK_RSP",1421,105759)
V1423M=V(4,"MSG_REBIRTHSPACE_BUY_CHALLENGE_COUNT_REQ",1422,105760)
V1424M=V(4,"MSG_REBIRTHSPACE_BUY_CHALLENGE_COUNT_RSP",1423,105761)
V1425M=V(4,"MSG_REBIRTHSPACE_RECOMMEND_TEAM_REQ",1424,105762)
V1426M=V(4,"MSG_REBIRTHSPACE_RECOMMEND_TEAM_RSP",1425,105763)
V1427M=V(4,"MSG_REBIRTHSPACE_RANK_DATA_LENGTH_REQ",1426,105764)
V1428M=V(4,"MSG_REBIRTHSPACE_RANK_DATA_LENGTH_RSP",1427,105765)
V1429M=V(4,"MSG_REBIRTHSPACE_PLAYER_BATTLE_ID_REQ",1428,105766)
V1430M=V(4,"MSG_REBIRTHSPACE_PLAYER_BATTLE_ID_RSP",1429,105767)
V1431M=V(4,"MSG_REBIRTHSPACE_REPORT_RANK",1430,105768)
V1432M=V(4,"MSG_REBIRTHSPACE_USE_RECOMMEND_TEAM_REQ",1431,105769)
V1433M=V(4,"MSG_REBIRTHSPACE_USE_RECOMMEND_TEAM_RSP",1432,105770)
V1434M=V(4,"MSG_REBIRTHSPACE_BATTLE_RESULT_NTF",1433,105771)
V1435M=V(4,"MSG_REBIRTHSPACE_IS_OPEN_ENTRY_NTF",1434,105772)
V1436M=V(4,"MSG_REBIRTHSPACE_BAN_LIST_REQ",1435,105773)
V1437M=V(4,"MSG_REBIRTHSPACE_BAN_LIST_RSP",1436,105774)
V1438M=V(4,"MSG_REBIRTHSPACE_PALACE_REQ",1437,105775)
V1439M=V(4,"MSG_REBIRTHSPACE_PALACE_RSP",1438,105776)
V1440M=V(4,"MSG_REBIRTHSPACE_GET_TREASURE_REQ",1439,105777)
V1441M=V(4,"MSG_REBIRTHSPACE_GET_TREASURE_RSP",1440,105778)
V1442M=V(4,"MSG_REBIRTHSPACE_MY_TREASURE_HISTORY_REQ",1441,105779)
V1443M=V(4,"MSG_REBIRTHSPACE_MY_TREASURE_HISTORY_RSP",1442,105780)
V1444M=V(4,"MSG_REBIRTHSPACE_GET_TREASURE_HISTORY_REQ",1443,105781)
V1445M=V(4,"MSG_REBIRTHSPACE_GET_TREASURE_HISTORY_RSP",1444,105782)
V1446M=V(4,"MSG_REBIRTHSPACE_MAX",1445,105800)
V1447M=V(4,"MSG_STARTEMPLE_NEED_SELECT_HERO_REQ",1446,106051)
V1448M=V(4,"MSG_STARTEMPLE_NEED_SELECT_HERO_RSP",1447,106052)
V1449M=V(4,"MSG_STARTEMPLE_SELECT_HERO_REQ",1448,106053)
V1450M=V(4,"MSG_STARTEMPLE_SELECT_HERO_RSP",1449,106054)
V1451M=V(4,"MSG_STARTEMPLE_FIGHT_REQ",1450,106055)
V1452M=V(4,"MSG_STARTEMPLE_FIGHT_RSP",1451,106056)
V1453M=V(4,"MSG_STARTEMPLE_BUY_FIGHT_COUNT_REQ",1452,106057)
V1454M=V(4,"MSG_STARTEMPLE_BASE_REQ",1453,106058)
V1455M=V(4,"MSG_STARTEMPLE_BASE_RSP",1454,106059)
V1456M=V(4,"MSG_STARTEMPLE_STORE_REQ",1455,106060)
V1457M=V(4,"MSG_STARTEMPLE_STORE_RSP",1456,106061)
V1458M=V(4,"MSG_STARTEMPLE_REFRESH_STORE_REQ",1457,106062)
V1459M=V(4,"MSG_STARTEMPLE_SALE_REQ",1458,106063)
V1460M=V(4,"MSG_STARTEMPLE_SALE_RSP",1459,106064)
V1461M=V(4,"MSG_STARTEMPLE_BATTLE_HISTORY_REQ",1460,106065)
V1462M=V(4,"MSG_STARTEMPLE_BATTLE_HISTORY_RSP",1461,106066)
V1463M=V(4,"MSG_STARTEMPLE_GET_SEASON_REWARD_REQ",1462,106067)
V1464M=V(4,"MSG_STARTEMPLE_GET_SEASON_REWARD_RSP",1463,106068)
V1465M=V(4,"MSG_STARTEMPLE_BATTLE_STATUS",1464,106069)
V1466M=V(4,"MSG_STARTEMPLE_BUY_REQ",1465,106070)
V1467M=V(4,"MSG_STARTEMPLE_BUY_RSP",1466,106071)
V1468M=V(4,"MSG_STARTEMPLE_MONSTER_LINEUP_REQ",1467,106072)
V1469M=V(4,"MSG_STARTEMPLE_MONSTER_LINEUP_RSP",1468,106073)
V1470M=V(4,"MSG_STARTEMPLE_ARTIFACT_REQ",1469,106074)
V1471M=V(4,"MSG_STARTEMPLE_ARTIFACT_RSP",1470,106075)
V1472M=V(4,"MSG_STARTEMPLE_NEXTSTATUS_REQ",1471,106076)
V1473M=V(4,"MSG_STARTEMPLE_NEXTSTATUS_RSP",1472,106077)
V1474M=V(4,"MSG_STARTEMPLE_TASKAWARD_REQ",1473,106078)
V1475M=V(4,"MSG_STARTEMPLE_TASKAWARD_RSP",1474,106079)
V1476M=V(4,"MSG_STARTEMPLE_CHALLENGE_STATUS_REQ",1475,106091)
V1477M=V(4,"MSG_STARTEMPLE_CHALLENGE_STATUS_RSP",1476,106092)
V1478M=V(4,"MSG_STARTEMPLE_ZONE_RANK_REQ",1477,106093)
V1479M=V(4,"MSG_STARTEMPLE_ZONE_RANK_RSP",1478,106094)
V1480M=V(4,"MSG_STARTEMPLE_LOOK_ROLEINFO_REQ",1479,106095)
V1481M=V(4,"MSG_STARTEMPLE_LOOK_ROLEINFO_RSP",1480,106096)
V1482M=V(4,"MSG_STARTEMPLE_RANKAWARD_REQ",1481,106097)
V1483M=V(4,"MSG_STARTEMPLE_RANKAWARD_RSP",1482,106098)
V1484M=V(4,"MSG_STARTEMPLE_BATTLE_RECORD_REQ",1483,106099)
V1485M=V(4,"MSG_STARTEMPLE_BATTLE_RECORD_RSP",1484,106100)
V1486M=V(4,"MSG_STARTEMPLE_CHALLENGE_RANK_REQ",1485,106101)
V1487M=V(4,"MSG_STARTEMPLE_CHALLENGE_RANK_RSP",1486,106102)
V1488M=V(4,"MSG_STARTEMPLE_HISTORY_RECORD_REQ",1487,106103)
V1489M=V(4,"MSG_STARTEMPLE_HISTORY_RECORD_RSP",1488,106104)
V1490M=V(4,"MSG_STARTEMPLE_RANKAWARD_NTF",1489,106105)
V1491M=V(4,"MSG_STARTEMPLE_EVERYDAYAWARD_NTF",1490,106106)
V1492M=V(4,"MSG_STARTEMPLE_FINISH_GAME_REQ",1491,106107)
V1493M=V(4,"MSG_STARTEMPLE_FINISH_GAME_RSP",1492,106108)
V1494M=V(4,"MSG_STARTEMPLE_BATTLE_RANKINFO_NTF",1493,106109)
V1495M=V(4,"MSG_STARTEMPLE_CAN_BATTLERANK_REQ",1494,106110)
V1496M=V(4,"MSG_STARTEMPLE_CAN_BATTLERANK_RSP",1495,106111)
V1497M=V(4,"MSG_STARTEMPLE_PARTS_DATA_NTF",1496,106112)
V1498M=V(4,"MSG_STARTEMPLE_RANK_CHANGE_NTF",1497,106113)
V1499M=V(4,"MAX_STARTEMPLE",1498,106200)
V1500M=V(4,"MSG_WEAPON_DIAMOND_UPGRADE_REQ",1499,106201)
V1501M=V(4,"MSG_WEAPON_DIAMOND_UPGRADE_RSP",1500,106202)
V1502M=V(4,"MSG_WEAPON_DIAMOND_OPERATE_REQ",1501,106203)
V1503M=V(4,"MSG_WEAPON_DIAMOND_OPEATE_RSP",1502,106204)
V1504M=V(4,"MSG_WEAPON_DIAMOND_QUICK_MOUNT_REQ",1503,106205)
V1505M=V(4,"MSG_WEAPON_DIAMOND_QUICK_MOUNT_RSP",1504,106206)
V1506M=V(4,"MSG_WEAPON_DIAMOND_SKILL_CHOOSE_REQ",1505,106207)
V1507M=V(4,"MSG_WEAPON_DIAMOND_SKILL_CHOOSE_RSP",1506,106208)
V1508M=V(4,"MSG_WEAPON_DIAMOND_COMPOSITE_REQ",1507,106209)
V1509M=V(4,"MSG_WEAPON_DIAMOND_COMPOSITE_RSP",1508,106210)
V1510M=V(4,"MSG_WEAPON_DIAMOND_COMPOSITE_TAKEOFF_REQ",1509,106211)
V1511M=V(4,"MSG_WEAPON_DIAMOND_COMPOSITE_TAKEOFF_RSP",1510,106212)
V1512M=V(4,"MSG_WEAPON_DIAMOND_QUICK_TAKEOFF_REQ",1511,106213)
V1513M=V(4,"MSG_WEAPON_DIAMOND_QUICK_TAKEOFF_RSP",1512,106214)
V1514M=V(4,"MSG_WEAPON_DIAMOND_EXCHANGE_REQ",1513,106215)
V1515M=V(4,"MSG_WEAPON_DIAMOND_EXCHANGE_RSP",1514,106216)
V1516M=V(4,"MSG_WEAPON_DIAMOND_GUIDE_REQ",1515,106217)
V1517M=V(4,"MSG_WEAPON_DIAMOND_GUIDE_RSP",1516,106218)
V1518M=V(4,"MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_UNLOCK_REQ",1517,106219)
V1519M=V(4,"MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_UNLOCK_RSP",1518,106220)
V1520M=V(4,"MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_REQ",1519,106221)
V1521M=V(4,"MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_RSP",1520,106222)
V1522M=V(4,"MAX_WEAPON_DIAMOND",1521,106230)
V1523M=V(4,"MSG_ARTIFACT_AROUSAL_REWARD_REQ",1522,106231)
V1524M=V(4,"MSG_ARTIFACT_AROUSAL_REWARD_RSP",1523,106232)
V1525M=V(4,"MSG_DIMENSIONWAR_BATTLE_RECORD_REQ",1524,107500)
V1526M=V(4,"MSG_DIMENSIONWAR_BATTLE_RECORD_RSP",1525,107501)
V1527M=V(4,"MSG_DIMENSIONWAR_SELF_TROOP_REQ",1526,107502)
V1528M=V(4,"MSG_DIMENSIONWAR_SELF_TROOP_RSP",1527,107503)
V1529M=V(4,"MSG_DIMENSIONWAR_OHTERS_TEAM_REQ",1528,107504)
V1530M=V(4,"MSG_DIMENSIONWAR_OHTERS_TEAM_RSP",1529,107505)
V1531M=V(4,"MSG_DIMENSIONWAR_GET_BASE_POS_INFO_REQ",1530,107506)
V1532M=V(4,"MSG_DIMENSIONWAR_GET_BASE_POS_INFO_RSP",1531,107507)
V1533M=V(4,"MSG_DIMENSIONWAR_GET_STATION_INFO_REQ",1532,107508)
V1534M=V(4,"MSG_DIMENSIONWAR_GET_STATION_INFO_RSP",1533,107509)
V1535M=V(4,"MSG_DIMENSIONWAR_SAVE_TEAM_REQ",1534,107510)
V1536M=V(4,"MSG_DIMENSIONWAR_SAVE_TEAM_RSP",1535,107511)
V1537M=V(4,"MSG_DIMENSIONWAR_GET_STATION_INFO_PART2_RSP",1536,107512)
V1538M=V(4,"MSG_DIMENSIONWAR_GARRISON_TEAM_REQ",1537,107516)
V1539M=V(4,"MSG_DIMENSIONWAR_GARRISON_TEAM_RSP",1538,107517)
V1540M=V(4,"MSG_DIMENSIONWAR_DAILY_RESULT",1539,107528)
V1541M=V(4,"MSG_DIMENSIONWAR_SEASON_RESULT",1540,107529)
V1542M=V(4,"MSG_DIMENSIONWAR_LIKE_PLAYER_REQ",1541,107530)
V1543M=V(4,"MSG_DIMENSIONWAR_LIKE_PLAYER_RSP",1542,107531)
V1544M=V(4,"MSG_DIMENSIONWAR_GET_REWARD_RANK_REQ",1543,107532)
V1545M=V(4,"MSG_DIMENSIONWAR_GET_REWARD_RANK_RSP",1544,107533)
V1546M=V(4,"MSG_DIMENSIONWAR_GET_TASK_REQ",1545,107534)
V1547M=V(4,"MSG_DIMENSIONWAR_GET_TASK_RSP",1546,107535)
V1548M=V(4,"MSG_DIMENSIONWAR_GET_TASK_REWARD_REQ",1547,107536)
V1549M=V(4,"MSG_DIMENSIONWAR_GET_TASK_REWARD_RSP",1548,107537)
V1550M=V(4,"MSG_DIMENSIONWAR_GET_SELECT_REWARD_REQ",1549,107538)
V1551M=V(4,"MSG_DIMENSIONWAR_GET_SELECT_REWARD_RSP",1550,107539)
V1552M=V(4,"MSG_DIMENSIONWAR_SAVE_SELECT_REWARD_INDEX_REQ",1551,107540)
V1553M=V(4,"MSG_DIMENSIONWAR_GET_ALL_GRID_DATA_REQ",1552,107541)
V1554M=V(4,"MSG_DIMENSIONWAR_GET_ALL_GRID_DATA_RSP",1553,107542)
V1555M=V(4,"MSG_DIMENSIONWAR_GET_HISTORY_GRID_OWNER_REQ",1554,107543)
V1556M=V(4,"MSG_DIMENSIONWAR_GET_HISTORY_GRID_OWNER_RSP",1555,107544)
V1557M=V(4,"MSG_DIMENSIONWAR_SIGN_UP_REQ",1556,107545)
V1558M=V(4,"MSG_DIMENSIONWAR_SIGN_UP_RSP",1557,107546)
V1559M=V(4,"MSG_DIMENSIONWAR_JOIN_LEAGUE_INFO_REQ",1558,107547)
V1560M=V(4,"MSG_DIMENSIONWAR_JOIN_LEAGUE_INFO_RSP",1559,107548)
V1561M=V(4,"MSG_DIMENSIONWAR_PUSH_DATA_NTF",1560,107549)
V1562M=V(4,"MSG_DIMENSIONWAR_MAP_ID_REQ",1561,107550)
V1563M=V(4,"MSG_DIMENSIONWAR_MAP_ID_RSP",1562,107551)
V1564M=V(4,"MSG_DIMENSIONWAR_BATTLEFIELD_REQ",1563,107552)
V1565M=V(4,"MSG_DIMENSIONWAR_BATTLEFIELD_RSP",1564,107553)
V1566M=V(4,"MSG_DIMENSIONWAR_SIGNCOUNT_REQ",1565,107556)
V1567M=V(4,"MSG_DIMENSIONWAR_SIGNCOUNT_RSP",1566,107557)
V1568M=V(4,"MSG_DIMENSIONWAR_SPECIFY_RANK_REQ",1567,107562)
V1569M=V(4,"MSG_DIMENSIONWAR_SPECIFY_RANK_RSP",1568,107563)
V1570M=V(4,"MSG_DIMENSIONWAR_DAILY_RESULT_REQ",1569,107566)
V1571M=V(4,"MSG_DIMENSIONWAR_SEASON_SETTLE_INFO_REQ",1570,107575)
V1572M=V(4,"MSG_DIMENSIONWAR_SEASON_SETTLE_INFO_RSP",1571,107576)
V1573M=V(4,"MSG_DIMENSIONWAR_GRIDDATA_NTF",1572,107579)
V1574M=V(4,"MSG_DIMENSIONWAR_REPORT_NTF",1573,107580)
V1575M=V(4,"MSG_DIMENSIONWAR_DIVISION_RANK_REQ",1574,107585)
V1576M=V(4,"MSG_DIMENSIONWAR_DIVISION_RANK_RSP",1575,107586)
V1577M=V(4,"MSG_DIMENSIONWAR_BATTLE_RECORD_PLAYBACK_REQ",1576,107587)
V1578M=V(4,"MSG_DIMENSIONWAR_BATTLE_RECORD_PLAYBACK_RSP",1577,107588)
V1579M=V(4,"MSG_DIMENSIONWAR_MY_LAST_RANKING_REQ",1578,107589)
V1580M=V(4,"MSG_DIMENSIONWAR_MY_LAST_RANKING_RSP",1579,107590)
V1581M=V(4,"MSG_DIMENSIONWAR_PUSH_DATA_REQ",1580,107591)
V1582M=V(4,"MSG_DIMENSIONWAR_PUSH_DATA_RSP",1581,107592)
V1583M=V(4,"MSG_DIMENSIONWAR_GET_MEMEBERS_INFO_REQ",1582,107593)
V1584M=V(4,"MSG_DIMENSIONWAR_GET_MEMEBERS_INFO_RSP",1583,107594)
V1585M=V(4,"MSG_DIMENSIONWAR_GET_MEMEBER_TEAM_REQ",1584,107595)
V1586M=V(4,"MSG_DIMENSIONWAR_GET_MEMEBERS_TEAM_RSP",1585,107596)
V1587M=V(4,"MSG_DIMENSIONWAR_SIMPLE_PIONEERS_REQ",1586,107597)
V1588M=V(4,"MSG_DIMENSIONWAR_SIMPLE_PIONEERS_RSP",1587,107598)
V1589M=V(4,"MSG_DIMENSIONWAR_DETAIL_PIONEERS_REQ",1588,107599)
V1590M=V(4,"MSG_DIMENSIONWAR_DETAIL_PIONEERS_RSP",1589,107600)
V1591M=V(4,"MSG_DIMENSIONWAR_OCCUPY_REWARD_INFO_REQ",1590,107601)
V1592M=V(4,"MSG_DIMENSIONWAR_OCCUPY_REWARD_INFO_RSP",1591,107602)
V1593M=V(4,"MSG_DIMENSIONWAR_GET_OCCUPY_REWARD_REQ",1592,107603)
V1594M=V(4,"MSG_DIMENSIONWAR_GET_OCCUPY_REWARD_RSP",1593,107604)
V1595M=V(4,"MSG_DIMENSIONWAR_BUFF_INFO_REQ",1594,107605)
V1596M=V(4,"MSG_DIMENSIONWAR_BUFF_INFO_RSP",1595,107606)
V1597M=V(4,"MSG_DIMENSIONWAR_LIMITTASK_INFO_REQ",1596,107607)
V1598M=V(4,"MSG_DIMENSIONWAR_LIMITTASK_INFO_RSP",1597,107608)
V1599M=V(4,"MSG_DIMENSIONWAR_GET_LIMITTASK_REWARD_REQ",1598,107609)
V1600M=V(4,"MSG_DIMENSIONWAR_GET_LIMITTASK_REWARD_RSP",1599,107610)
V1601M=V(4,"MSG_DIMENSIONWAR_SUPPLY_INFO_REQ",1600,107611)
V1602M=V(4,"MSG_DIMENSIONWAR_SUPPLY_INFO_RSP",1601,107612)
V1603M=V(4,"MSG_DIMENSIONWAR_BOSS_INFO_REQ",1602,107613)
V1604M=V(4,"MSG_DIMENSIONWAR_BOSS_INFO_RSP",1603,107614)
V1605M=V(4,"MSG_DIMENSIONWAR_ATTACK_BOSS_REQ",1604,107615)
V1606M=V(4,"MSG_DIMENSIONWAR_ATTACK_BOSS_RSP",1605,107616)
V1607M=V(4,"MSG_DIMENSIONWAR_SUMMON_BOSS_REQ",1606,107617)
V1608M=V(4,"MSG_DIMENSIONWAR_SUMMON_BOSS_RSP",1607,107618)
V1609M=V(4,"MSG_DIMENSIONWAR_ATTACK_TEAM_REQ",1608,107619)
V1610M=V(4,"MSG_DIMENSIONWAR_ATTACK_TEAM_RSP",1609,107620)
V1611M=V(4,"MSG_DIMENSIONWAR_BOSS_VIDEO_INFO_REQ",1610,107621)
V1612M=V(4,"MSG_DIMENSIONWAR_BOSS_VIDEO_INFO_RSP",1611,107622)
V1613M=V(4,"MSG_DIMENSIONWAR_GRID_DATA_REQ",1612,107623)
V1614M=V(4,"MSG_DIMENSIONWAR_GRID_DATA_RSP",1613,107624)
V1615M=V(4,"MSG_DIMENSIONWAR_GET_BOSS_REWARD_REQ",1614,107625)
V1616M=V(4,"MSG_DIMENSIONWAR_GET_BOSS_REWARD_RSP",1615,107626)
V1617M=V(4,"MSG_DIMENSIONWAR_LEAGUEINFO_REQ",1616,107627)
V1618M=V(4,"MSG_DIMENSIONWAR_LEAGUEINFO_RSP",1617,107628)
V1619M=V(4,"MSG_DIMENSIONWAR_CHECK_TEAM_NTF",1618,107629)
V1620M=V(4,"MSG_DIMENSIONWAR_GATHER_REQ",1619,107630)
V1621M=V(4,"MSG_DIMENSIONWAR_GATHER_RSP",1620,107631)
V1622M=V(4,"MSG_DIMENSIONWAR_ALL_GATHER_REQ",1621,107632)
V1623M=V(4,"MSG_DIMENSIONWAR_ALL_GATHER_RSP",1622,107633)
V1624M=V(4,"MSG_DIMENSIONWAR_GRIDINFO_REQ",1623,107634)
V1625M=V(4,"MSG_DIMENSIONWAR_GRIDINFO_RSP",1624,107635)
V1626M=V(4,"MSG_DIMENSIONWAR_DROPRECORD_REQ",1625,107636)
V1627M=V(4,"MSG_DIMENSIONWAR_DROPRECORD_RSP",1626,107637)
V1628M=V(4,"MSG_DIMENSIONWAR_MEMBERS_TEAM_REQ",1627,107638)
V1629M=V(4,"MSG_DIMENSIONWAR_MEMBERS_TEAM_RSP",1628,107639)
V1630M=V(4,"MSG_DIMENSIONWAR_MAX",1629,107650)
V1631M=V(4,"MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ",1630,107751)
V1632M=V(4,"MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP",1631,107752)
V1633M=V(4,"MSG_LABOURDAYACTIVITY_Max",1632,107760)
V1634M=V(4,"MSG_MAZE_CRUSH_REQ",1633,107761)
V1635M=V(4,"MSG_MAZE_CRUSH_RSP",1634,107762)
V1636M=V(4,"MSG_LOGIN_MODIFY_FROMWORLDID_NTF",1635,107766)
V1637M=V(4,"MSG_PLAYER_LOGIN_FINISH_NOTIFY",1636,107767)
V1638M=V(4,"MSG_COMM_ACTIVITY_RANK_REQ",1637,107780)
V1639M=V(4,"MSG_COMM_ACTIVITY_RANK_RSP",1638,107781)
V1640M=V(4,"MSG_MAKE_FOOD_SHOW_REQ",1639,107782)
V1641M=V(4,"MSG_MAKE_FOOD_SHOW_RSP",1640,107783)
V1642M=V(4,"MSG_MAKE_FOOD_MAKE_REQ",1641,107784)
V1643M=V(4,"MSG_MAKE_FOOD_MAKE_RSP",1642,107785)
V1644M=V(4,"MSG_MAKE_FOOD_ROLEINFO_REQ",1643,107786)
V1645M=V(4,"MSG_MAKE_FOOD_ROLEINFO_RSP",1644,107787)
V1646M=V(4,"MSG_MAKE_FOOD_SHELF_REQ",1645,107788)
V1647M=V(4,"MSG_MAKE_FOOD_SHELF_RSP",1646,107789)
V1648M=V(4,"MSG_MAKE_FOOD_EXCHANGE_REQ",1647,107790)
V1649M=V(4,"MSG_MAKE_FOOD_EXCHANGE_RSP",1648,107791)
V1650M=V(4,"MSG_MAKE_FOOD_GET_REQ",1649,107792)
V1651M=V(4,"MSG_MAKE_FOOD_GET_RSP",1650,107793)
V1652M=V(4,"MSG_MAKE_FOOD_CANCEL_REQ",1651,107794)
V1653M=V(4,"MSG_MAKE_FOOD_CANCEL_RSP",1652,107795)
V1654M=V(4,"MSG_MAKE_FOOD_ORDERLIST_REQ",1653,107796)
V1655M=V(4,"MSG_MAKE_FOOD_ORDERLIST_RSP",1654,107797)
V1656M=V(4,"MSG_MAKE_FOOD_ACCOUNTBOOK_REQ",1655,107798)
V1657M=V(4,"MSG_MAKE_FOOD_ACCOUNTBOOK_RSP",1656,107799)
V1658M=V(4,"MSG_MAKE_FOOD_ORDER_NTF",1657,107800)
V1659M=V(4,"MSG_ALLSAINTSDAY_PROGRESS_NTF",1658,107801)
V1660M=V(4,"MSG_GAME_TO_CLIENT_REPROT_MAIL_NTF",1659,107802)
V1661M=V(4,"MSG_CHAT_REPORT_BACKSTAGE_REQ",1660,107803)
V1662M=V(4,"MSG_TRIALHERO_GET_REQ",1661,107804)
V1663M=V(4,"MSG_TRIALHERO_GET_RSP",1662,107805)
V1664M=V(4,"MSG_TRIALHERO_UPDATE_NTF",1663,107806)
V1665M=V(4,"MSG_TRIALHERO_MAX",1664,107820)
V1666M=V(4,"MSG_RETURNS_GETREFINDREWARD_REQ",1665,107821)
V1667M=V(4,"MSG_RETURNS_GETREFINDREWARD_RSP",1666,107822)
V1668M=V(4,"MSG_PICKTHEROUTE_ARCHIVED_RECORDS_REQ",1667,107871)
V1669M=V(4,"MSG_PICKTHEROUTE_ARCHIVED_RECORDS_RSP",1668,107872)
V1670M=V(4,"MSG_PICKTHEROUTE_EVENTREWARD_REQ",1669,107873)
V1671M=V(4,"MSG_PICKTHEROUTE_EVENTREWARD_RSP",1670,107874)
V1672M=V(4,"MSG_PICKTHEROUTE_ARCHIVED_SAVE_REQ",1671,107875)
V1673M=V(4,"MSG_PICKTHEROUTE_ARCHIVED_SAVE_RSP",1672,107876)
V1674M=V(4,"MSG_PICKTHEROUTE_FIRST_SHARE_REWRAD_REQ",1673,107877)
V1675M=V(4,"MSG_PICKTHEROUTE_FIRST_SHARE_REWRAD_RSP",1674,107878)
V1676M=V(4,"MSG_PICKTHEROUTE_SELECT_LEVEL_REQ",1675,107879)
V1677M=V(4,"MSG_PICKTHEROUTE_SELECT_LEVEL_RSP",1676,107880)
V1678M=V(4,"MSG_ALLSAINTSDAY_BASEINFO_REQ",1677,107881)
V1679M=V(4,"MSG_ALLSAINTSDAY_BASEINFO_RSP",1678,107882)
V1680M=V(4,"MSG_ALLSAINTSDAY_HISTORY_DRESS_REQ",1679,107883)
V1681M=V(4,"MSG_ALLSAINTSDAY_HISTORY_DRESS_RSP",1680,107884)
V1682M=V(4,"MSG_ALLSAINTSDAY_SUBMIT_REQ",1681,107885)
V1683M=V(4,"MSG_ALLSAINTSDAY_SUBMIT_RSP",1682,107886)
V1684M=V(4,"MSG_ALLSAINTSDAY_DETAILS_REQ",1683,107887)
V1685M=V(4,"MSG_ALLSAINTSDAY_DETAILS_RSP",1684,107888)
V1686M=V(4,"MSG_ALLSAINTSDAY_UPVOTE_REQ",1685,107889)
V1687M=V(4,"MSG_ALLSAINTSDAY_UPVOTE_RSP",1686,107890)
V1688M=V(4,"MSG_ALLSAINTSDAY_SCORE_REQ",1687,107891)
V1689M=V(4,"MSG_ALLSAINTSDAY_SCORE_RSP",1688,107892)
V1690M=V(4,"MSG_ALLSAINTSDAY_CHARM_REQ",1689,107893)
V1691M=V(4,"MSG_ALLSAINTSDAY_CHARM_RSP",1690,107894)
V1692M=V(4,"MSG_ALLSAINTSDAY_COLLECTION_NTF",1691,107895)
V1693M=V(4,"MSG_ALLSAINTSDAY_PROGRESS_REQ",1692,107896)
V1694M=V(4,"MSG_ALLSAINTSDAY_PROGRESS_RSP",1693,107897)
V1695M=V(4,"MSG_ALLSAINTSDAY_PROGRESS_MSG_REQ",1694,107898)
V1696M=V(4,"MSG_SLAVE_QUERY_TARGET_DETAIL_REQ",1695,107899)
V1697M=V(4,"MSG_SLAVE_QUERY_TARGET_DETAIL_RSP",1696,107900)
V1698M=V(4,"MSG_SLAVE_LIST_INFO_REQ",1697,107901)
V1699M=V(4,"MSG_SLAVE_LIST_INFO_RSP",1698,107902)
V1700M=V(4,"MSG_SLAVE_GET_REAL_AWARD_REQ",1699,107903)
V1701M=V(4,"MSG_SLAVE_GET_REAL_AWARD_RSP",1700,107904)
V1702M=V(4,"MSG_SLAVE_FIGHT_AGAINST_REPORT_REQ",1701,107905)
V1703M=V(4,"MSG_SLAVE_FIGHT_AGAINST_REPORT_RSP",1702,107906)
V1704M=V(4,"MSG_SLAVE_UPDATE_BATTLE_LINE_REQ",1703,107907)
V1705M=V(4,"MSG_SLAVE_UPDATE_BATTLE_LINE_RSP",1704,107908)
V1706M=V(4,"MSG_SLAVE_GET_BATTLE_LINE_REQ",1705,107909)
V1707M=V(4,"MSG_SLAVE_GET_BATTLE_LINE_RSP",1706,107910)
V1708M=V(4,"MSG_SLAVE_RELEASE_FREE_REQ",1707,107911)
V1709M=V(4,"MSG_SLAVE_RELEASE_FREE_RSP",1708,107912)
V1710M=V(4,"MSG_SLAVE_CATCH_LIST_REQ",1709,107913)
V1711M=V(4,"MSG_SLAVE_CATCH_LIST_RSP",1710,107914)
V1712M=V(4,"MSG_SLAVE_MAIL_LIST_REQ",1711,107915)
V1713M=V(4,"MSG_SLAVE_MAIL_LIST_RSP",1712,107916)
V1714M=V(4,"MSG_SLAVE_MAIL_OPER_REQ",1713,107917)
V1715M=V(4,"MSG_SLAVE_MAIL_OPER_RSP",1714,107918)
V1716M=V(4,"MSG_SLAVE_MAIL_NTF",1715,107919)
V1717M=V(4,"MSG_SLAVE_PERCHASE_ENERGY_REQ",1716,107920)
V1718M=V(4,"MSG_SLAVE_PERCHASE_ENERGY_RSP",1717,107921)
V1719M=V(4,"MSG_SLAVE_UPDATE_ENERGY_REQ",1718,107922)
V1720M=V(4,"MSG_SLAVE_UPDATE_ENERGY_RSP",1719,107923)
V1721M=V(4,"MSG_SLAVE_BATTLE_RECORD_REQ",1720,107924)
V1722M=V(4,"MSG_SLAVE_BATTLE_RECORD_RSP",1721,107925)
V1723M=V(4,"MSG_RESERVATION_EMAIL_BIND_REQ",1722,107926)
V1724M=V(4,"MSG_RESERVATION_EMAIL_UNBIND_REQ",1723,107927)
V1725M=V(4,"MSG_RESERVATION_EMAIL_SENDREWARD_REQ",1724,107928)
V1726M=V(4,"MSG_RESERVATION_EMAIL_SENDREWARD_RSP",1725,107929)
V1727M=V(4,"MSG_RESERVATION_EMAIL_BGETREWARD_REQ",1726,107930)
V1728M=V(4,"MSG_RESERVATION_EMAIL_BGETREWARD_RSP",1727,107931)
V1729M=V(4,"MSG_RESERVATION_EMAIL_MAX",1728,107950)
V1730M=V(4,"MSG_SLAVE_CLAIME_ONE_AWARD_REQ",1729,107951)
V1731M=V(4,"MSG_SLAVE_CLAIME_ONE_AWARD_RSP",1730,107952)
V1732M=V(4,"MSG_SLAVE_RECOMMEND_LIST_REQ",1731,107953)
V1733M=V(4,"MSG_SLAVE_RECOMMEND_LIST_RSP",1732,107954)
V1734M=V(4,"MSG_SLAVE_RECOMMEND_LIST_CHANGE_REQ",1733,107955)
V1735M=V(4,"MSG_SLAVE_RECOMMEND_LIST_CHANGE_RSP",1734,107956)
V1736M=V(4,"MSG_MAKE_FOOD_AUTO_MAKE_REQ",1735,107957)
V1737M=V(4,"MSG_MAKE_FOOD_AUTO_MAKE_RSP",1736,107958)
V1738M=V(4,"MSG_MAKE_FOOD_MAX",1737,107960)
V1739M=V(4,"MSG_WORDBOSS_INFO_REQ",1738,107961)
V1740M=V(4,"MSG_WORDBOSS_INFO_NTF",1739,107962)
V1741M=V(4,"MSG_WORDBOSS_LINE_INFO_REQ",1740,107963)
V1742M=V(4,"MSG_WORDBOSS_LINE_INFO_RSP",1741,107964)
V1743M=V(4,"MSG_WORDBOSS_DMGRECORD_NTF",1742,107965)
V1744M=V(4,"MSG_WORDBOSS_MAX",1743,107970)
V1745M=V(4,"MSG_GET_MAKE_HERO_EQUIP_REQ",1744,107971)
V1746M=V(4,"MSG_GET_MAKE_HERO_EQUIP_RSP",1745,107972)
V1747M=V(4,"MSG_HERO_COMPOUND_REQ",1746,107973)
V1748M=V(4,"MSG_HERO_COMPOUND_RSP",1747,107974)
V1749M=V(4,"MSG_HERO_EQUIP_DISMANTLE_REQ",1748,107975)
V1750M=V(4,"MSG_HERO_EQUIP_DISMANTLE_RSP",1749,107976)
V1751M=V(4,"MSG_ROLEFRAME_OPEN_REQ",1750,107981)
V1752M=V(4,"MSG_ROLEFRAME_OPEN_RSP",1751,107982)
V1753M=V(4,"MSG_ROLEFRAME_WEAR_REQ",1752,107983)
V1754M=V(4,"MSG_ROLEFRAME_WEAR_RSP",1753,107984)
V1755M=V(4,"MSG_ROLEFRAME_OVERTIME_NTF",1754,107985)
E1M=E(3,"ActionKey",".CSMsg.ActionKey")

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M,V123M,V124M,V125M,V126M,V127M,V128M,V129M,V130M,V131M,V132M,V133M,V134M,V135M,V136M,V137M,V138M,V139M,V140M,V141M,V142M,V143M,V144M,V145M,V146M,V147M,V148M,V149M,V150M,V151M,V152M,V153M,V154M,V155M,V156M,V157M,V158M,V159M,V160M,V161M,V162M,V163M,V164M,V165M,V166M,V167M,V168M,V169M,V170M,V171M,V172M,V173M,V174M,V175M,V176M,V177M,V178M,V179M,V180M,V181M,V182M,V183M,V184M,V185M,V186M,V187M,V188M,V189M,V190M,V191M,V192M,V193M,V194M,V195M,V196M,V197M,V198M,V199M,V200M,V201M,V202M,V203M,V204M,V205M,V206M,V207M,V208M,V209M,V210M,V211M,V212M,V213M,V214M,V215M,V216M,V217M,V218M,V219M,V220M,V221M,V222M,V223M,V224M,V225M,V226M,V227M,V228M,V229M,V230M,V231M,V232M,V233M,V234M,V235M,V236M,V237M,V238M,V239M,V240M,V241M,V242M,V243M,V244M,V245M,V246M,V247M,V248M,V249M,V250M,V251M,V252M,V253M,V254M,V255M,V256M,V257M,V258M,V259M,V260M,V261M,V262M,V263M,V264M,V265M,V266M,V267M,V268M,V269M,V270M,V271M,V272M,V273M,V274M,V275M,V276M,V277M,V278M,V279M,V280M,V281M,V282M,V283M,V284M,V285M,V286M,V287M,V288M,V289M,V290M,V291M,V292M,V293M,V294M,V295M,V296M,V297M,V298M,V299M,V300M,V301M,V302M,V303M,V304M,V305M,V306M,V307M,V308M,V309M,V310M,V311M,V312M,V313M,V314M,V315M,V316M,V317M,V318M,V319M,V320M,V321M,V322M,V323M,V324M,V325M,V326M,V327M,V328M,V329M,V330M,V331M,V332M,V333M,V334M,V335M,V336M,V337M,V338M,V339M,V340M,V341M,V342M,V343M,V344M,V345M,V346M,V347M,V348M,V349M,V350M,V351M,V352M,V353M,V354M,V355M,V356M,V357M,V358M,V359M,V360M,V361M,V362M,V363M,V364M,V365M,V366M,V367M,V368M,V369M,V370M,V371M,V372M,V373M,V374M,V375M,V376M,V377M,V378M,V379M,V380M,V381M,V382M,V383M,V384M,V385M,V386M,V387M,V388M,V389M,V390M,V391M,V392M,V393M,V394M,V395M,V396M,V397M,V398M,V399M,V400M,V401M,V402M,V403M,V404M,V405M,V406M,V407M,V408M,V409M,V410M,V411M,V412M,V413M,V414M,V415M,V416M,V417M,V418M,V419M,V420M,V421M,V422M,V423M,V424M,V425M,V426M,V427M,V428M,V429M,V430M,V431M,V432M,V433M,V434M,V435M,V436M,V437M,V438M,V439M,V440M,V441M,V442M,V443M,V444M,V445M,V446M,V447M,V448M,V449M,V450M,V451M,V452M,V453M,V454M,V455M,V456M,V457M,V458M,V459M,V460M,V461M,V462M,V463M,V464M,V465M,V466M,V467M,V468M,V469M,V470M,V471M,V472M,V473M,V474M,V475M,V476M,V477M,V478M,V479M,V480M,V481M,V482M,V483M,V484M,V485M,V486M,V487M,V488M,V489M,V490M,V491M,V492M,V493M,V494M,V495M,V496M,V497M,V498M,V499M,V500M,V501M,V502M,V503M,V504M,V505M,V506M,V507M,V508M,V509M,V510M,V511M,V512M,V513M,V514M,V515M,V516M,V517M,V518M,V519M,V520M,V521M,V522M,V523M,V524M,V525M,V526M,V527M,V528M,V529M,V530M,V531M,V532M,V533M,V534M,V535M,V536M,V537M,V538M,V539M,V540M,V541M,V542M,V543M,V544M,V545M,V546M,V547M,V548M,V549M,V550M,V551M,V552M,V553M,V554M,V555M,V556M,V557M,V558M,V559M,V560M,V561M,V562M,V563M,V564M,V565M,V566M,V567M,V568M,V569M,V570M,V571M,V572M,V573M,V574M,V575M,V576M,V577M,V578M,V579M,V580M,V581M,V582M,V583M,V584M,V585M,V586M,V587M,V588M,V589M,V590M,V591M,V592M,V593M,V594M,V595M,V596M,V597M,V598M,V599M,V600M,V601M,V602M,V603M,V604M,V605M,V606M,V607M,V608M,V609M,V610M,V611M,V612M,V613M,V614M,V615M,V616M,V617M,V618M,V619M,V620M,V621M,V622M,V623M,V624M,V625M,V626M,V627M,V628M,V629M,V630M,V631M,V632M,V633M,V634M,V635M,V636M,V637M,V638M,V639M,V640M,V641M,V642M,V643M,V644M,V645M,V646M,V647M,V648M,V649M,V650M,V651M,V652M,V653M,V654M,V655M,V656M,V657M,V658M,V659M,V660M,V661M,V662M,V663M,V664M,V665M,V666M,V667M,V668M,V669M,V670M,V671M,V672M,V673M,V674M,V675M,V676M,V677M,V678M,V679M,V680M,V681M,V682M,V683M,V684M,V685M,V686M,V687M,V688M,V689M,V690M,V691M,V692M,V693M,V694M,V695M,V696M,V697M,V698M,V699M,V700M,V701M,V702M,V703M,V704M,V705M,V706M,V707M,V708M,V709M,V710M,V711M,V712M,V713M,V714M,V715M,V716M,V717M,V718M,V719M,V720M,V721M,V722M,V723M,V724M,V725M,V726M,V727M,V728M,V729M,V730M,V731M,V732M,V733M,V734M,V735M,V736M,V737M,V738M,V739M,V740M,V741M,V742M,V743M,V744M,V745M,V746M,V747M,V748M,V749M,V750M,V751M,V752M,V753M,V754M,V755M,V756M,V757M,V758M,V759M,V760M,V761M,V762M,V763M,V764M,V765M,V766M,V767M,V768M,V769M,V770M,V771M,V772M,V773M,V774M,V775M,V776M,V777M,V778M,V779M,V780M,V781M,V782M,V783M,V784M,V785M,V786M,V787M,V788M,V789M,V790M,V791M,V792M,V793M,V794M,V795M,V796M,V797M,V798M,V799M,V800M,V801M,V802M,V803M,V804M,V805M,V806M,V807M,V808M,V809M,V810M,V811M,V812M,V813M,V814M,V815M,V816M,V817M,V818M,V819M,V820M,V821M,V822M,V823M,V824M,V825M,V826M,V827M,V828M,V829M,V830M,V831M,V832M,V833M,V834M,V835M,V836M,V837M,V838M,V839M,V840M,V841M,V842M,V843M,V844M,V845M,V846M,V847M,V848M,V849M,V850M,V851M,V852M,V853M,V854M,V855M,V856M,V857M,V858M,V859M,V860M,V861M,V862M,V863M,V864M,V865M,V866M,V867M,V868M,V869M,V870M,V871M,V872M,V873M,V874M,V875M,V876M,V877M,V878M,V879M,V880M,V881M,V882M,V883M,V884M,V885M,V886M,V887M,V888M,V889M,V890M,V891M,V892M,V893M,V894M,V895M,V896M,V897M,V898M,V899M,V900M,V901M,V902M,V903M,V904M,V905M,V906M,V907M,V908M,V909M,V910M,V911M,V912M,V913M,V914M,V915M,V916M,V917M,V918M,V919M,V920M,V921M,V922M,V923M,V924M,V925M,V926M,V927M,V928M,V929M,V930M,V931M,V932M,V933M,V934M,V935M,V936M,V937M,V938M,V939M,V940M,V941M,V942M,V943M,V944M,V945M,V946M,V947M,V948M,V949M,V950M,V951M,V952M,V953M,V954M,V955M,V956M,V957M,V958M,V959M,V960M,V961M,V962M,V963M,V964M,V965M,V966M,V967M,V968M,V969M,V970M,V971M,V972M,V973M,V974M,V975M,V976M,V977M,V978M,V979M,V980M,V981M,V982M,V983M,V984M,V985M,V986M,V987M,V988M,V989M,V990M,V991M,V992M,V993M,V994M,V995M,V996M,V997M,V998M,V999M,V1000M,V1001M,V1002M,V1003M,V1004M,V1005M,V1006M,V1007M,V1008M,V1009M,V1010M,V1011M,V1012M,V1013M,V1014M,V1015M,V1016M,V1017M,V1018M,V1019M,V1020M,V1021M,V1022M,V1023M,V1024M,V1025M,V1026M,V1027M,V1028M,V1029M,V1030M,V1031M,V1032M,V1033M,V1034M,V1035M,V1036M,V1037M,V1038M,V1039M,V1040M,V1041M,V1042M,V1043M,V1044M,V1045M,V1046M,V1047M,V1048M,V1049M,V1050M,V1051M,V1052M,V1053M,V1054M,V1055M,V1056M,V1057M,V1058M,V1059M,V1060M,V1061M,V1062M,V1063M,V1064M,V1065M,V1066M,V1067M,V1068M,V1069M,V1070M,V1071M,V1072M,V1073M,V1074M,V1075M,V1076M,V1077M,V1078M,V1079M,V1080M,V1081M,V1082M,V1083M,V1084M,V1085M,V1086M,V1087M,V1088M,V1089M,V1090M,V1091M,V1092M,V1093M,V1094M,V1095M,V1096M,V1097M,V1098M,V1099M,V1100M,V1101M,V1102M,V1103M,V1104M,V1105M,V1106M,V1107M,V1108M,V1109M,V1110M,V1111M,V1112M,V1113M,V1114M,V1115M,V1116M,V1117M,V1118M,V1119M,V1120M,V1121M,V1122M,V1123M,V1124M,V1125M,V1126M,V1127M,V1128M,V1129M,V1130M,V1131M,V1132M,V1133M,V1134M,V1135M,V1136M,V1137M,V1138M,V1139M,V1140M,V1141M,V1142M,V1143M,V1144M,V1145M,V1146M,V1147M,V1148M,V1149M,V1150M,V1151M,V1152M,V1153M,V1154M,V1155M,V1156M,V1157M,V1158M,V1159M,V1160M,V1161M,V1162M,V1163M,V1164M,V1165M,V1166M,V1167M,V1168M,V1169M,V1170M,V1171M,V1172M,V1173M,V1174M,V1175M,V1176M,V1177M,V1178M,V1179M,V1180M,V1181M,V1182M,V1183M,V1184M,V1185M,V1186M,V1187M,V1188M,V1189M,V1190M,V1191M,V1192M,V1193M,V1194M,V1195M,V1196M,V1197M,V1198M,V1199M,V1200M,V1201M,V1202M,V1203M,V1204M,V1205M,V1206M,V1207M,V1208M,V1209M,V1210M,V1211M,V1212M,V1213M,V1214M,V1215M,V1216M,V1217M,V1218M,V1219M,V1220M,V1221M,V1222M,V1223M,V1224M,V1225M,V1226M,V1227M,V1228M,V1229M,V1230M,V1231M,V1232M,V1233M,V1234M,V1235M,V1236M,V1237M,V1238M,V1239M,V1240M,V1241M,V1242M,V1243M,V1244M,V1245M,V1246M,V1247M,V1248M,V1249M,V1250M,V1251M,V1252M,V1253M,V1254M,V1255M,V1256M,V1257M,V1258M,V1259M,V1260M,V1261M,V1262M,V1263M,V1264M,V1265M,V1266M,V1267M,V1268M,V1269M,V1270M,V1271M,V1272M,V1273M,V1274M,V1275M,V1276M,V1277M,V1278M,V1279M,V1280M,V1281M,V1282M,V1283M,V1284M,V1285M,V1286M,V1287M,V1288M,V1289M,V1290M,V1291M,V1292M,V1293M,V1294M,V1295M,V1296M,V1297M,V1298M,V1299M,V1300M,V1301M,V1302M,V1303M,V1304M,V1305M,V1306M,V1307M,V1308M,V1309M,V1310M,V1311M,V1312M,V1313M,V1314M,V1315M,V1316M,V1317M,V1318M,V1319M,V1320M,V1321M,V1322M,V1323M,V1324M,V1325M,V1326M,V1327M,V1328M,V1329M,V1330M,V1331M,V1332M,V1333M,V1334M,V1335M,V1336M,V1337M,V1338M,V1339M,V1340M,V1341M,V1342M,V1343M,V1344M,V1345M,V1346M,V1347M,V1348M,V1349M,V1350M,V1351M,V1352M,V1353M,V1354M,V1355M,V1356M,V1357M,V1358M,V1359M,V1360M,V1361M,V1362M,V1363M,V1364M,V1365M,V1366M,V1367M,V1368M,V1369M,V1370M,V1371M,V1372M,V1373M,V1374M,V1375M,V1376M,V1377M,V1378M,V1379M,V1380M,V1381M,V1382M,V1383M,V1384M,V1385M,V1386M,V1387M,V1388M,V1389M,V1390M,V1391M,V1392M,V1393M,V1394M,V1395M,V1396M,V1397M,V1398M,V1399M,V1400M,V1401M,V1402M,V1403M,V1404M,V1405M,V1406M,V1407M,V1408M,V1409M,V1410M,V1411M,V1412M,V1413M,V1414M,V1415M,V1416M,V1417M,V1418M,V1419M,V1420M,V1421M,V1422M,V1423M,V1424M,V1425M,V1426M,V1427M,V1428M,V1429M,V1430M,V1431M,V1432M,V1433M,V1434M,V1435M,V1436M,V1437M,V1438M,V1439M,V1440M,V1441M,V1442M,V1443M,V1444M,V1445M,V1446M,V1447M,V1448M,V1449M,V1450M,V1451M,V1452M,V1453M,V1454M,V1455M,V1456M,V1457M,V1458M,V1459M,V1460M,V1461M,V1462M,V1463M,V1464M,V1465M,V1466M,V1467M,V1468M,V1469M,V1470M,V1471M,V1472M,V1473M,V1474M,V1475M,V1476M,V1477M,V1478M,V1479M,V1480M,V1481M,V1482M,V1483M,V1484M,V1485M,V1486M,V1487M,V1488M,V1489M,V1490M,V1491M,V1492M,V1493M,V1494M,V1495M,V1496M,V1497M,V1498M,V1499M,V1500M,V1501M,V1502M,V1503M,V1504M,V1505M,V1506M,V1507M,V1508M,V1509M,V1510M,V1511M,V1512M,V1513M,V1514M,V1515M,V1516M,V1517M,V1518M,V1519M,V1520M,V1521M,V1522M,V1523M,V1524M,V1525M,V1526M,V1527M,V1528M,V1529M,V1530M,V1531M,V1532M,V1533M,V1534M,V1535M,V1536M,V1537M,V1538M,V1539M,V1540M,V1541M,V1542M,V1543M,V1544M,V1545M,V1546M,V1547M,V1548M,V1549M,V1550M,V1551M,V1552M,V1553M,V1554M,V1555M,V1556M,V1557M,V1558M,V1559M,V1560M,V1561M,V1562M,V1563M,V1564M,V1565M,V1566M,V1567M,V1568M,V1569M,V1570M,V1571M,V1572M,V1573M,V1574M,V1575M,V1576M,V1577M,V1578M,V1579M,V1580M,V1581M,V1582M,V1583M,V1584M,V1585M,V1586M,V1587M,V1588M,V1589M,V1590M,V1591M,V1592M,V1593M,V1594M,V1595M,V1596M,V1597M,V1598M,V1599M,V1600M,V1601M,V1602M,V1603M,V1604M,V1605M,V1606M,V1607M,V1608M,V1609M,V1610M,V1611M,V1612M,V1613M,V1614M,V1615M,V1616M,V1617M,V1618M,V1619M,V1620M,V1621M,V1622M,V1623M,V1624M,V1625M,V1626M,V1627M,V1628M,V1629M,V1630M,V1631M,V1632M,V1633M,V1634M,V1635M,V1636M,V1637M,V1638M,V1639M,V1640M,V1641M,V1642M,V1643M,V1644M,V1645M,V1646M,V1647M,V1648M,V1649M,V1650M,V1651M,V1652M,V1653M,V1654M,V1655M,V1656M,V1657M,V1658M,V1659M,V1660M,V1661M,V1662M,V1663M,V1664M,V1665M,V1666M,V1667M,V1668M,V1669M,V1670M,V1671M,V1672M,V1673M,V1674M,V1675M,V1676M,V1677M,V1678M,V1679M,V1680M,V1681M,V1682M,V1683M,V1684M,V1685M,V1686M,V1687M,V1688M,V1689M,V1690M,V1691M,V1692M,V1693M,V1694M,V1695M,V1696M,V1697M,V1698M,V1699M,V1700M,V1701M,V1702M,V1703M,V1704M,V1705M,V1706M,V1707M,V1708M,V1709M,V1710M,V1711M,V1712M,V1713M,V1714M,V1715M,V1716M,V1717M,V1718M,V1719M,V1720M,V1721M,V1722M,V1723M,V1724M,V1725M,V1726M,V1727M,V1728M,V1729M,V1730M,V1731M,V1732M,V1733M,V1734M,V1735M,V1736M,V1737M,V1738M,V1739M,V1740M,V1741M,V1742M,V1743M,V1744M,V1745M,V1746M,V1747M,V1748M,V1749M,V1750M,V1751M,V1752M,V1753M,V1754M,V1755M}

MAX_STARTEMPLE = 106200
MAX_WEAPON_DIAMOND = 106230
MSG_ACCOUNT_BIND_REQ = 100004
MSG_ACCOUNT_BIND_RSP = 100005
MSG_ACCOUNT_BUILD_GATEWAYMIGRATE_NTF = 100003
MSG_ACCOUNT_BUILD_PLAYER_INFO_NTF = 100002
MSG_ACCOUNT_BUILD_USER_INFO_NTF = 100001
MSG_ACCOUNT_CHANGE_WORLD_LOGIN_REQ = 100015
MSG_ACCOUNT_CHANGE_WORLD_LOGIN_RSP = 100016
MSG_ACCOUNT_CHANGE_WORLD_REQ = 100013
MSG_ACCOUNT_CHANGE_WORLD_RSP = 100014
MSG_ACCOUNT_LOGIN_QUEUE_NTF = 100017
MSG_ACCOUNT_LUA_ACTION_REQ = 100007
MSG_ACCOUNT_NEWER_GUIDE_REQ = 100008
MSG_ACCOUNT_ROLE_LIST_ALL_WORLD_REQ = 100011
MSG_ACCOUNT_ROLE_LIST_ALL_WORLD_RSP = 100012
MSG_ACCOUNT_RUN_SCRIPT_NTF = 100006
MSG_ACCOUNT_SELECT_PROFICIENCY_REQ = 100009
MSG_ACCOUNT_SELECT_PROFICIENCY_RSP = 100010
MSG_ACTIVITY_COMPLETE_NTF = 101352
MSG_ACTIVITY_DATA_REQ = 1434
MSG_ACTIVITY_DATA_RSP = 1435
MSG_ACTIVITY_GROW_GIFT2_STATE = 104307
MSG_ACTIVITY_PROSPECT_REWARD_REQ = 1803
MSG_ACTIVITY_PROSPECT_REWARD_RSP = 1804
MSG_ADD_NOVICE_GUIDE_DATA_NTF = 102152
MSG_ADD_PASS_STAGE_PLOT_DATA_NTF = 102155
MSG_ALLSAINTSDAY_BASEINFO_REQ = 107881
MSG_ALLSAINTSDAY_BASEINFO_RSP = 107882
MSG_ALLSAINTSDAY_CHARM_REQ = 107893
MSG_ALLSAINTSDAY_CHARM_RSP = 107894
MSG_ALLSAINTSDAY_COLLECTION_NTF = 107895
MSG_ALLSAINTSDAY_DETAILS_REQ = 107887
MSG_ALLSAINTSDAY_DETAILS_RSP = 107888
MSG_ALLSAINTSDAY_HISTORY_DRESS_REQ = 107883
MSG_ALLSAINTSDAY_HISTORY_DRESS_RSP = 107884
MSG_ALLSAINTSDAY_PROGRESS_MSG_REQ = 107898
MSG_ALLSAINTSDAY_PROGRESS_NTF = 107801
MSG_ALLSAINTSDAY_PROGRESS_REQ = 107896
MSG_ALLSAINTSDAY_PROGRESS_RSP = 107897
MSG_ALLSAINTSDAY_SCORE_REQ = 107891
MSG_ALLSAINTSDAY_SCORE_RSP = 107892
MSG_ALLSAINTSDAY_SUBMIT_REQ = 107885
MSG_ALLSAINTSDAY_SUBMIT_RSP = 107886
MSG_ALLSAINTSDAY_UPVOTE_REQ = 107889
MSG_ALLSAINTSDAY_UPVOTE_RSP = 107890
MSG_ALTAR_HERO_DECOMPOSE_REQ = 101300
MSG_ALTAR_HERO_DECOMPOSE_RSP = 101301
MSG_ANNIVERSARY_GRAB_DATA_REQ = 105961
MSG_ANNIVERSARY_GRAB_DATA_RSP = 105962
MSG_ANTI_ADDICTION_NTF = 102201
MSG_AREAN_GET_HANGUP_REWARD_REQ = 1414
MSG_AREAN_GET_HANGUP_REWARD_RSP = 1415
MSG_AREAN_INVITATIONLIST_OPR_REQ = 1412
MSG_AREAN_INVITATIONLIST_OPR_RSP = 1413
MSG_ARENA_APPLYLIST_REQ = 1385
MSG_ARENA_APPLYLIST_RSP = 1386
MSG_ARENA_APPROVE_JOIN_REQ = 1381
MSG_ARENA_APPROVE_JOIN_RSP = 1382
MSG_ARENA_BATTLE_RECORD_REQ = 1355
MSG_ARENA_BATTLE_RECORD_RSP = 1356
MSG_ARENA_BUY_TICKET_NUM_NTF = 1431
MSG_ARENA_BUY_TICKET_REQ = 1436
MSG_ARENA_BUY_TICKET_RSP = 1437
MSG_ARENA_CHANGE_RIVAL_LIST_REQ = 1425
MSG_ARENA_CHANGE_RIVAL_LIST_RSP = 1426
MSG_ARENA_CREATE_TEAM_REQ = 1367
MSG_ARENA_CREATE_TEAM_RSP = 1368
MSG_ARENA_DECLINE_JOIN_REQ = 1383
MSG_ARENA_DECLINE_JOIN_RSP = 1384
MSG_ARENA_DEFEND_LINEUP_REQ = 1362
MSG_ARENA_DEFEND_LINEUP_RSP = 1363
MSG_ARENA_DISMISS_TEAM_REQ = 1373
MSG_ARENA_DISMISS_TEAM_RSP = 1374
MSG_ARENA_ENROLL_REQ = 1379
MSG_ARENA_ENROLL_RSP = 1380
MSG_ARENA_ENTER_ASSEMBLY_HALL_REQ = 1391
MSG_ARENA_ENTER_ASSEMBLY_HALL_RSP = 1392
MSG_ARENA_ENTER_REQ = 1353
MSG_ARENA_ENTER_RSP = 1354
MSG_ARENA_ERROR_NTF = 1366
MSG_ARENA_GET_BATTLE_ID_REQ = 1404
MSG_ARENA_GET_BATTLE_ID_RSP = 1405
MSG_ARENA_GET_CURRENT_RIVAL_REQ = 1427
MSG_ARENA_GET_CURRENT_RIVAL_RSP = 1428
MSG_ARENA_GET_INVITATIONLIST_REQ = 1410
MSG_ARENA_GET_INVITATIONLIST_RSP = 1411
MSG_ARENA_GET_LEVEL_REWARD_INFO_REQ = 1419
MSG_ARENA_GET_LEVEL_REWARD_INFO_RSP = 1420
MSG_ARENA_GET_LEVEL_REWARD_REQ = 1417
MSG_ARENA_GET_LEVEL_REWARD_RSP = 1418
MSG_ARENA_GET_RANKINGLIST_REQ = 1393
MSG_ARENA_GET_RANKINGLIST_RSP = 1394
MSG_ARENA_GET_RANK_AND_REWARD_FLAG_REQ = 1429
MSG_ARENA_GET_RANK_AND_REWARD_FLAG_RSP = 1430
MSG_ARENA_GET_RECORD_ID_REQ = 1432
MSG_ARENA_GET_RECORD_ID_RSP = 1433
MSG_ARENA_GET_REWARD_REQ = 1359
MSG_ARENA_GET_RIVAL_REQ = 1400
MSG_ARENA_GET_RIVAL_RSP = 1401
MSG_ARENA_GET_TEAMINFO_REQ = 1389
MSG_ARENA_GET_TEAMINFO_RSP = 1390
MSG_ARENA_GET_TEAMLINEUP_REQ = 1395
MSG_ARENA_GET_TEAMLINEUP_RSP = 1396
MSG_ARENA_GET_TIME_REQ = 1351
MSG_ARENA_GET_TIME_RSP = 1352
MSG_ARENA_GET_VICTORY_REWARD_REQ = 1421
MSG_ARENA_GET_VICTORY_REWARD_RSP = 1422
MSG_ARENA_IGNORE_APPLYLIST_REQ = 1387
MSG_ARENA_IGNORE_APPLYLIST_RSP = 1388
MSG_ARENA_INVITE_JOINTEAM_REQ = 1408
MSG_ARENA_INVITE_JOINTEAM_RSP = 1409
MSG_ARENA_JOIN_TEAM_REQ = 1375
MSG_ARENA_JOIN_TEAM_RSP = 1376
MSG_ARENA_KICK_MEMBER_REQ = 1369
MSG_ARENA_KICK_MEMBER_RSP = 1370
MSG_ARENA_LAST_RANKING_LIST_REQ = 1360
MSG_ARENA_LAST_RANKING_LIST_RSP = 1361
MSG_ARENA_MAXID_END = 1499
MSG_ARENA_NEW_EVENT_NTF = 1399
MSG_ARENA_OPEN_INVITATION_REQ = 1406
MSG_ARENA_OPEN_INVITATION_RSP = 1407
MSG_ARENA_REFRESH_BTN_STATE_REQ = 1423
MSG_ARENA_REFRESH_BTN_STATE_RSP = 1424
MSG_ARENA_REFRESH_RIVAL_REQ = 1438
MSG_ARENA_REFRESH_RIVAL_RSP = 1439
MSG_ARENA_SAESON_START_NTF = 1416
MSG_ARENA_SAVE_TEAMLINEUP_REQ = 1397
MSG_ARENA_SAVE_TEAMLINEUP_RSP = 1398
MSG_ARENA_SET_DEFENCE_LINEUP_REQ = 1402
MSG_ARENA_SET_DEFENCE_LINEUP_RSP = 1403
MSG_ARENA_TRANSFER_CAPTAIN_REQ = 1371
MSG_ARENA_TRANSFER_CAPTAIN_RSP = 1372
MSG_ARENA_WATCH_PLAYBACK_REQ = 1357
MSG_ARENA_WATCH_PLAYBACK_RSP = 1358
MSG_ARENA_WITHDRAW_TEAM_REQ = 1377
MSG_ARENA_WITHDRAW_TEAM_RSP = 1378
MSG_ARTIFACT_AROUSAL_REWARD_REQ = 106231
MSG_ARTIFACT_AROUSAL_REWARD_RSP = 106232
MSG_ASHDUNGEON_GETHERO_REQ = 101111
MSG_ASHDUNGEON_GETHERO_RSP = 101112
MSG_ASHDUNGEON_GET_RANK_REQ = 101106
MSG_ASHDUNGEON_GET_RANK_RSP = 101107
MSG_ASHDUNGEON_MAX = 101200
MSG_ASHDUNGEON_PASSREWARD_REQ = 101115
MSG_ASHDUNGEON_PASSREWARD_RSP = 101116
MSG_ASHDUNGEON_RESET_REQ = 101117
MSG_ASHDUNGEON_RESET_RSP = 101118
MSG_ASHDUNGEON_SELECT_DRUG_REQ = 101102
MSG_ASHDUNGEON_SELECT_DRUG_RSP = 101103
MSG_ASHDUNGEON_SETTEAM_REQ = 101113
MSG_ASHDUNGEON_SETTEAM_RSP = 101114
MSG_ASHDUNGEON_SWEEP_REQ = 101109
MSG_ASHDUNGEON_SWEEP_RSP = 101110
MSG_ASHDUNGEON_UPDATE_PART_NTF = 101101
MSG_ASHDUNGEON_UPDATE_RANK_NTF = 101108
MSG_ASHDUNGEON_USE_DRUG_REQ = 101104
MSG_ASHDUNGEON_USE_DRUG_RSP = 101105
MSG_BATTLESHIPTECH_LV_DATA_NTF = 105171
MSG_BATTLESHIPTECH_MAX = 105180
MSG_BATTLE_ATTACK_CASTLE_REQ = 1202
MSG_BATTLE_ATTACK_CASTLE_RSP = 1203
MSG_BATTLE_BUFF_ACTIVE_NTF = 1228
MSG_BATTLE_CHANGEPERSONSTATE_REQ = 1204
MSG_BATTLE_CHANGEPERSONSTATE_RSP = 1205
MSG_BATTLE_CLEAN_NTF = 1220
MSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF = 100935
MSG_BATTLE_END_OF_THE_PLAY_NTF = 100926
MSG_BATTLE_ENTER_FINAL_BATTLE_NTF = 1208
MSG_BATTLE_FINAL_BATTLE_COUNTDOWN_NTF = 1217
MSG_BATTLE_FINAL_BATTLE_START_NTF = 1209
MSG_BATTLE_FRAME_CMD_NTF = 1201
MSG_BATTLE_MANUALTOLOBBYNOTRECDC_NTF = 1229
MSG_BATTLE_MAXID_END = 1300
MSG_BATTLE_PLAYBACK_REQ = 100955
MSG_BATTLE_PLAYBACK_RSP = 100956
MSG_BATTLE_RECORD_WATCH_REQ = 1215
MSG_BATTLE_REQUEST_SUPPORT_NTF = 1223
MSG_BATTLE_REQUEST_SUPPORT_REQ = 1222
MSG_BATTLE_RESULT = 1227
MSG_BATTLE_RESULT_GAME_RESULT_NTF = 1207
MSG_BATTLE_RESULT_REQ = 100959
MSG_BATTLE_RESULT_RSP = 100960
MSG_BATTLE_RSP_ATTACK_CASTLE_NTF = 1221
MSG_BATTLE_SYNCALLFRAME_NTF = 1206
MSG_BATTLE_SYNC_CARD_NTF = 1218
MSG_BATTLE_SYNC_USER_INFO_NTF = 1219
MSG_BATTLE_TBS_NTF = 105351
MSG_BATTLE_TEAMMATE_ACCEPT_BATTLE_NTF = 1226
MSG_BATTLE_TEAMMATE_REFUSE_SUPPORT_NTF = 1225
MSG_BATTLE_TEAMMATE_REFUSE_SUPPORT_REQ = 1224
MSG_BATTLE_TRSUTEEREPORT_NTF = 1212
MSG_BATTLE_TRSUTEEREPORT_REQ = 1213
MSG_BATTLE_TRSUTEEREPORT_RSP = 1214
MSG_BATTLE_TRUSTEE_REQ = 1210
MSG_BATTLE_TRUSTEE_RSP = 1211
MSG_BLESSHERO_GET_REQ = 100583
MSG_BLESSHERO_GET_RSP = 100584
MSG_BROADCAST_MAX = 1850
MSG_BROADCAST_MSG_NTF = 1801
MSG_BROADCAST_SYS_MSG_NTF = 1802
MSG_BROKENST_MOPUP_REWARD_NTF = 101350
MSG_BROKENST_UPDATA_BADGES_NTF = 101351
MSG_BUY_HERO_LIST_REQ = 1067
MSG_BUY_HERO_LIST_RSP = 1068
MSG_CERTIFICATION_NTF = 102202
MSG_CHAMPIONSHIPS_GET_RANKS_REQ = 103131
MSG_CHAMPIONSHIPS_GET_RANKS_RSP = 103132
MSG_CHAMPIONSHIPS_MAX = 103150
MSG_CHAT_BAN_NTF = 1613
MSG_CHAT_BAN_REMOVE_NTF = 1614
MSG_CHAT_BLOCK_CHANNEL_REQ = 1611
MSG_CHAT_BLOCK_CHANNEL_RSP = 1612
MSG_CHAT_BLOCK_LIST_NTF = 1642
MSG_CHAT_BLOCK_LIST_REQ = 1640
MSG_CHAT_BLOCK_LIST_RSP = 1641
MSG_CHAT_BLOCK_REQ = 1605
MSG_CHAT_BLOCK_RSP = 1606
MSG_CHAT_GET_SIGN_REQ = 1629
MSG_CHAT_GET_SIGN_RSP = 1630
MSG_CHAT_GM_CLOSE_CONVERSATION_NTF = 1631
MSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF = 1622
MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ = 1620
MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP = 1621
MSG_CHAT_LANGUAGE_CHANNEL_UPDATE_NTF = 1626
MSG_CHAT_LANGUAGE_MSG_REQ = 1638
MSG_CHAT_LANGUAGE_MSG_RSP = 1639
MSG_CHAT_LEAVE_LEAGUE_NTF = 1632
MSG_CHAT_MAX = 1700
MSG_CHAT_MSG_GETBEFORE_REQ = 1634
MSG_CHAT_MSG_GETBEFORE_RSP = 1635
MSG_CHAT_MSG_NTF = 1604
MSG_CHAT_NEW_MSG_NTF = 1603
MSG_CHAT_RECALL_MSG_REQ = 1636
MSG_CHAT_RECALL_MSG_RSP = 1637
MSG_CHAT_REPORT_BACKSTAGE_REQ = 107803
MSG_CHAT_REPORT_CHANNEL_REQ = 1627
MSG_CHAT_REPORT_CHANNEL_RSP = 1628
MSG_CHAT_REPORT_REQ = 1609
MSG_CHAT_REPORT_RSP = 1610
MSG_CHAT_SEND_MAIL_REQ = 1607
MSG_CHAT_SEND_MAIL_RSP = 1608
MSG_CHAT_SPEAK_REQ = 1601
MSG_CHAT_SPEAK_RSP = 1602
MSG_CHINARED_CHAPTER_GAIN_REQ = 105355
MSG_CHINARED_CHAPTER_GAIN_RSP = 105356
MSG_CHINARED_SWEEP_REQ = 105353
MSG_CHINARED_SWEEP_RSP = 105354
MSG_CHINARED_UPDATE_PART_MAX = 105380
MSG_CHINARED_UPDATE_PART_NTF = 105352
MSG_CLIENT_GET_RANK_LIST_REQ = 100914
MSG_CLIENT_GET_RANK_LIST_RSP = 100915
MSG_CLIENT_UPDATE_RANK_LIST_NTF = 100916
MSG_COMM_ACTIVITY_RANK_REQ = 107780
MSG_COMM_ACTIVITY_RANK_RSP = 107781
MSG_CONTRI_MAX = 101700
MSG_CONTRI_RANK_REQ = 101651
MSG_CONTRI_RANK_RSP = 101652
MSG_CONTRI_UPDATE_NTF = 101650
MSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF = 1625
MSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ = 1623
MSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP = 1624
MSG_CURIDLE_CHAPTER_PLAYER_PROMOTE_REQ = 103133
MSG_CURIDLE_CHAPTER_PLAYER_PROMOTE_RSP = 103134
MSG_CUSTOMROOM_ALLDATA_NTF = 100576
MSG_CUSTOMROOM_CREATE_REQ = 100550
MSG_CUSTOMROOM_CREATE_RSP = 100551
MSG_CUSTOMROOM_FETCH_SETUP_REQ = 100577
MSG_CUSTOMROOM_FETCH_SETUP_RSP = 100578
MSG_CUSTOMROOM_HANDLE_INVITE_REQ = 100574
MSG_CUSTOMROOM_HANDLE_INVITE_RSP = 100575
MSG_CUSTOMROOM_INVITE_FRIEND_REQ = 100567
MSG_CUSTOMROOM_INVITE_FRIEND_RSP = 100568
MSG_CUSTOMROOM_INVITE_RESULT_NTF = 100580
MSG_CUSTOMROOM_JOIN_REQ = 100552
MSG_CUSTOMROOM_JOIN_RSP = 100553
MSG_CUSTOMROOM_KICK_NTF = 100556
MSG_CUSTOMROOM_KICK_REQ = 100570
MSG_CUSTOMROOM_KICK_RSP = 100571
MSG_CUSTOMROOM_LIST_REQ = 100562
MSG_CUSTOMROOM_LIST_RSP = 100563
MSG_CUSTOMROOM_MEMBER_UPDATE_NTF = 100564
MSG_CUSTOMROOM_QUIT_REQ = 100554
MSG_CUSTOMROOM_QUIT_RSP = 100555
MSG_CUSTOMROOM_RECV_INVITE_NTF = 100569
MSG_CUSTOMROOM_ROOMDATA_UPDATE_NTF = 100579
MSG_CUSTOMROOM_SEARCH_REQ = 100565
MSG_CUSTOMROOM_SEARCH_RSP = 100566
MSG_CUSTOMROOM_START_NTF = 100559
MSG_CUSTOMROOM_START_REQ = 100557
MSG_CUSTOMROOM_START_RSP = 100558
MSG_CUSTOMROOM_SWITCH_POS_REQ = 100560
MSG_CUSTOMROOM_SWITCH_POS_RSP = 100561
MSG_CUSTOMROOM_UPDATE_SETUP_REQ = 100572
MSG_CUSTOMROOM_UPDATE_SETUP_RSP = 100573
MSG_CUSTOM_FACE_ACTIVATE_NTF = 1527
MSG_CUSTOM_FACE_REMOVE_REQ = 1530
MSG_CUSTOM_FACE_REMOVE_RSP = 1531
MSG_CUSTOM_FACE_UPLOAD_REQ = 1524
MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ = 1528
MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP = 1529
MSG_CUSTOM_FACE_UPLOAD_RSP = 1525
MSG_CUSTOM_FACE_USE_REQ = 1532
MSG_CUSTOM_FACE_USE_RSP = 1533
MSG_CUSTOM_FACE_VERIFY_NTF = 1526
MSG_DECORATE_MAX = 105480
MSG_DERACOTE_DECOMPOSE_REQ = 105397
MSG_DERACOTE_DECOMPOSE_RSP = 105398
MSG_DERACOTE_LOCK_REQ = 105383
MSG_DERACOTE_LOCK_RSP = 105384
MSG_DERACOTE_REFINE_REQ = 105381
MSG_DERACOTE_REFINE_RSP = 105382
MSG_DERACOTE_SHOP_GET_INFO_REQ = 105395
MSG_DERACOTE_SHOP_GET_INFO_RSP = 105396
MSG_DERACOTE_SHOP_GET_WISH_PAL_REQ = 105391
MSG_DERACOTE_SHOP_GET_WISH_PAL_RSP = 105392
MSG_DERACOTE_SHOP_RAND_ONCE_REQ = 105385
MSG_DERACOTE_SHOP_RAND_ONCE_RSP = 105386
MSG_DERACOTE_SHOP_RAND_TEN_REQ = 105387
MSG_DERACOTE_SHOP_RAND_TEN_RSP = 105388
MSG_DERACOTE_SHOP_SET_ALL_WISH_PAL_REQ = 105399
MSG_DERACOTE_SHOP_SET_WISH_PAL_REQ = 105389
MSG_DERACOTE_SHOP_SET_WISH_PAL_RSP = 105390
MSG_DERACOTE_SHOP_USE_EXCHANGE_CARD_REQ = 105393
MSG_DERACOTE_SHOP_USE_EXCHANGE_CARD_RSP = 105394
MSG_DEV_LANGUAGE_NTF = 102532
MSG_DIMENSIONWAR_ALL_GATHER_REQ = 107632
MSG_DIMENSIONWAR_ALL_GATHER_RSP = 107633
MSG_DIMENSIONWAR_ATTACK_BOSS_REQ = 107615
MSG_DIMENSIONWAR_ATTACK_BOSS_RSP = 107616
MSG_DIMENSIONWAR_ATTACK_TEAM_REQ = 107619
MSG_DIMENSIONWAR_ATTACK_TEAM_RSP = 107620
MSG_DIMENSIONWAR_BATTLEFIELD_REQ = 107552
MSG_DIMENSIONWAR_BATTLEFIELD_RSP = 107553
MSG_DIMENSIONWAR_BATTLE_RECORD_PLAYBACK_REQ = 107587
MSG_DIMENSIONWAR_BATTLE_RECORD_PLAYBACK_RSP = 107588
MSG_DIMENSIONWAR_BATTLE_RECORD_REQ = 107500
MSG_DIMENSIONWAR_BATTLE_RECORD_RSP = 107501
MSG_DIMENSIONWAR_BOSS_INFO_REQ = 107613
MSG_DIMENSIONWAR_BOSS_INFO_RSP = 107614
MSG_DIMENSIONWAR_BOSS_VIDEO_INFO_REQ = 107621
MSG_DIMENSIONWAR_BOSS_VIDEO_INFO_RSP = 107622
MSG_DIMENSIONWAR_BUFF_INFO_REQ = 107605
MSG_DIMENSIONWAR_BUFF_INFO_RSP = 107606
MSG_DIMENSIONWAR_CHECK_TEAM_NTF = 107629
MSG_DIMENSIONWAR_DAILY_RESULT = 107528
MSG_DIMENSIONWAR_DAILY_RESULT_REQ = 107566
MSG_DIMENSIONWAR_DETAIL_PIONEERS_REQ = 107599
MSG_DIMENSIONWAR_DETAIL_PIONEERS_RSP = 107600
MSG_DIMENSIONWAR_DIVISION_RANK_REQ = 107585
MSG_DIMENSIONWAR_DIVISION_RANK_RSP = 107586
MSG_DIMENSIONWAR_DROPRECORD_REQ = 107636
MSG_DIMENSIONWAR_DROPRECORD_RSP = 107637
MSG_DIMENSIONWAR_GARRISON_TEAM_REQ = 107516
MSG_DIMENSIONWAR_GARRISON_TEAM_RSP = 107517
MSG_DIMENSIONWAR_GATHER_REQ = 107630
MSG_DIMENSIONWAR_GATHER_RSP = 107631
MSG_DIMENSIONWAR_GET_ALL_GRID_DATA_REQ = 107541
MSG_DIMENSIONWAR_GET_ALL_GRID_DATA_RSP = 107542
MSG_DIMENSIONWAR_GET_BASE_POS_INFO_REQ = 107506
MSG_DIMENSIONWAR_GET_BASE_POS_INFO_RSP = 107507
MSG_DIMENSIONWAR_GET_BOSS_REWARD_REQ = 107625
MSG_DIMENSIONWAR_GET_BOSS_REWARD_RSP = 107626
MSG_DIMENSIONWAR_GET_HISTORY_GRID_OWNER_REQ = 107543
MSG_DIMENSIONWAR_GET_HISTORY_GRID_OWNER_RSP = 107544
MSG_DIMENSIONWAR_GET_LIMITTASK_REWARD_REQ = 107609
MSG_DIMENSIONWAR_GET_LIMITTASK_REWARD_RSP = 107610
MSG_DIMENSIONWAR_GET_MEMEBERS_INFO_REQ = 107593
MSG_DIMENSIONWAR_GET_MEMEBERS_INFO_RSP = 107594
MSG_DIMENSIONWAR_GET_MEMEBERS_TEAM_RSP = 107596
MSG_DIMENSIONWAR_GET_MEMEBER_TEAM_REQ = 107595
MSG_DIMENSIONWAR_GET_OCCUPY_REWARD_REQ = 107603
MSG_DIMENSIONWAR_GET_OCCUPY_REWARD_RSP = 107604
MSG_DIMENSIONWAR_GET_REWARD_RANK_REQ = 107532
MSG_DIMENSIONWAR_GET_REWARD_RANK_RSP = 107533
MSG_DIMENSIONWAR_GET_SELECT_REWARD_REQ = 107538
MSG_DIMENSIONWAR_GET_SELECT_REWARD_RSP = 107539
MSG_DIMENSIONWAR_GET_STATION_INFO_PART2_RSP = 107512
MSG_DIMENSIONWAR_GET_STATION_INFO_REQ = 107508
MSG_DIMENSIONWAR_GET_STATION_INFO_RSP = 107509
MSG_DIMENSIONWAR_GET_TASK_REQ = 107534
MSG_DIMENSIONWAR_GET_TASK_REWARD_REQ = 107536
MSG_DIMENSIONWAR_GET_TASK_REWARD_RSP = 107537
MSG_DIMENSIONWAR_GET_TASK_RSP = 107535
MSG_DIMENSIONWAR_GRIDDATA_NTF = 107579
MSG_DIMENSIONWAR_GRIDINFO_REQ = 107634
MSG_DIMENSIONWAR_GRIDINFO_RSP = 107635
MSG_DIMENSIONWAR_GRID_DATA_REQ = 107623
MSG_DIMENSIONWAR_GRID_DATA_RSP = 107624
MSG_DIMENSIONWAR_JOIN_LEAGUE_INFO_REQ = 107547
MSG_DIMENSIONWAR_JOIN_LEAGUE_INFO_RSP = 107548
MSG_DIMENSIONWAR_LEAGUEINFO_REQ = 107627
MSG_DIMENSIONWAR_LEAGUEINFO_RSP = 107628
MSG_DIMENSIONWAR_LIKE_PLAYER_REQ = 107530
MSG_DIMENSIONWAR_LIKE_PLAYER_RSP = 107531
MSG_DIMENSIONWAR_LIMITTASK_INFO_REQ = 107607
MSG_DIMENSIONWAR_LIMITTASK_INFO_RSP = 107608
MSG_DIMENSIONWAR_MAP_ID_REQ = 107550
MSG_DIMENSIONWAR_MAP_ID_RSP = 107551
MSG_DIMENSIONWAR_MAX = 107650
MSG_DIMENSIONWAR_MEMBERS_TEAM_REQ = 107638
MSG_DIMENSIONWAR_MEMBERS_TEAM_RSP = 107639
MSG_DIMENSIONWAR_MY_LAST_RANKING_REQ = 107589
MSG_DIMENSIONWAR_MY_LAST_RANKING_RSP = 107590
MSG_DIMENSIONWAR_OCCUPY_REWARD_INFO_REQ = 107601
MSG_DIMENSIONWAR_OCCUPY_REWARD_INFO_RSP = 107602
MSG_DIMENSIONWAR_OHTERS_TEAM_REQ = 107504
MSG_DIMENSIONWAR_OHTERS_TEAM_RSP = 107505
MSG_DIMENSIONWAR_PUSH_DATA_NTF = 107549
MSG_DIMENSIONWAR_PUSH_DATA_REQ = 107591
MSG_DIMENSIONWAR_PUSH_DATA_RSP = 107592
MSG_DIMENSIONWAR_REPORT_NTF = 107580
MSG_DIMENSIONWAR_SAVE_SELECT_REWARD_INDEX_REQ = 107540
MSG_DIMENSIONWAR_SAVE_TEAM_REQ = 107510
MSG_DIMENSIONWAR_SAVE_TEAM_RSP = 107511
MSG_DIMENSIONWAR_SEASON_RESULT = 107529
MSG_DIMENSIONWAR_SEASON_SETTLE_INFO_REQ = 107575
MSG_DIMENSIONWAR_SEASON_SETTLE_INFO_RSP = 107576
MSG_DIMENSIONWAR_SELF_TROOP_REQ = 107502
MSG_DIMENSIONWAR_SELF_TROOP_RSP = 107503
MSG_DIMENSIONWAR_SIGNCOUNT_REQ = 107556
MSG_DIMENSIONWAR_SIGNCOUNT_RSP = 107557
MSG_DIMENSIONWAR_SIGN_UP_REQ = 107545
MSG_DIMENSIONWAR_SIGN_UP_RSP = 107546
MSG_DIMENSIONWAR_SIMPLE_PIONEERS_REQ = 107597
MSG_DIMENSIONWAR_SIMPLE_PIONEERS_RSP = 107598
MSG_DIMENSIONWAR_SPECIFY_RANK_REQ = 107562
MSG_DIMENSIONWAR_SPECIFY_RANK_RSP = 107563
MSG_DIMENSIONWAR_SUMMON_BOSS_REQ = 107617
MSG_DIMENSIONWAR_SUMMON_BOSS_RSP = 107618
MSG_DIMENSIONWAR_SUPPLY_INFO_REQ = 107611
MSG_DIMENSIONWAR_SUPPLY_INFO_RSP = 107612
MSG_ENTER_MAIN_BATTLE_NTF = 100936
MSG_EQUIP_DECOMPOSE_REQ = 1020
MSG_EQUIP_DECOMPOSE_RSP = 1021
MSG_EQUIP_DEVOUR_REQ = 1063
MSG_EQUIP_DEVOUR_RSP = 1064
MSG_EQUIP_ECTYPE_MODUP_REQ = 100957
MSG_EQUIP_ECTYPE_MODUP_RSP = 100958
MSG_EQUIP_ECTYPE_SPECIAL_PRIZE_REQ = 100953
MSG_EQUIP_ECTYPE_SPECIAL_PRIZE_RSP = 100954
MSG_EQUIP_LOCK_REQ = 1022
MSG_EQUIP_LOCK_RSP = 1023
MSG_EQUIP_RECAST_NTF = 1026
MSG_EQUIP_RECAST_REQ = 1024
MSG_EQUIP_RECAST_RSP = 1025
MSG_EQUIP_RECAST_SAVE_REQ = 1027
MSG_EQUIP_RECAST_SAVE_RSP = 1028
MSG_EQUIP_UPGRADE_REQ = 1018
MSG_EQUIP_UPGRADE_RSP = 1019
MSG_EXIT_MODULE_NTF = 100913
MSG_FACEBOOK_GUIDE_REQ = 100021
MSG_FACEBOOK_GUIDE_RSP = 100022
MSG_FARMPART_DATA_NTF = 1771
MSG_FARM_ACHV_GAIN_REQ = 103164
MSG_FARM_ACHV_GAIN_RSP = 103165
MSG_FARM_BOTANY_REQ = 1763
MSG_FARM_BOTANY_RSP = 1764
MSG_FARM_COLLECT_REQ = 1767
MSG_FARM_COLLECT_RSP = 1768
MSG_FARM_DISMANTLE_REQ = 1769
MSG_FARM_DISMANTLE_RSP = 1770
MSG_FARM_MAIN_CITY_UPGRADE_REQ = 1760
MSG_FARM_MAIN_CITY_UPGRADE_RSP = 1761
MSG_FARM_MAX = 1800
MSG_FARM_SLOT_ATTACK_REQ = 103160
MSG_FARM_SLOT_ATTACK_RSP = 103161
MSG_FARM_SLOT_DETIAL_DATA_REQ = 103158
MSG_FARM_SLOT_DETIAL_DATA_RSP = 103159
MSG_FARM_SLOT_DIG_REQ = 103156
MSG_FARM_SLOT_DIG_RSP = 103157
MSG_FARM_SLOT_INIT_DATA_REQ = 103151
MSG_FARM_SLOT_INIT_DATA_RSP = 103152
MSG_FARM_SLOT_MAX = 103250
MSG_FARM_SLOT_REPAIR_REQ = 103162
MSG_FARM_SLOT_REPAIR_RSP = 103163
MSG_FARM_SLOT_SPIN_REQ = 103153
MSG_FARM_SLOT_SPIN_RSP = 103154
MSG_FARM_SLOT_UPDATE_NTF = 103155
MSG_FARM_SOIL_UNLOCK_NTF = 1762
MSG_FARM_TREE_UPGRADE_REQ = 1765
MSG_FARM_TREE_UPGRADE_RSP = 1766
MSG_FIRSTRECHARGEREWARD_REQ = 100283
MSG_FIRSTRECHARGEREWARD_RSP = 100284
MSG_FIRST_CREATE_ROLE_NTF = 102891
MSG_FRAME_MAX = 102950
MSG_FRAME_SELECTPROP_REQ = 102924
MSG_FRAME_SELECTPROP_RSP = 102925
MSG_FRAME_SELECT_REQ = 102921
MSG_FRAME_SELECT_RSP = 102922
MSG_FRIEND_ADD_APPLY_REQ = 100157
MSG_FRIEND_ADD_APPLY_RSP = 100158
MSG_FRIEND_APPLY_DATA_UPDATE_NTF = 100166
MSG_FRIEND_APPLY_LIST_REQ = 100154
MSG_FRIEND_APPLY_LIST_RSP = 100155
MSG_FRIEND_APPLY_RESULT_NTF = 100162
MSG_FRIEND_CHAT_LIST_REQ = 100170
MSG_FRIEND_CHAT_LIST_RSP = 100171
MSG_FRIEND_CHAT_NTF = 100169
MSG_FRIEND_CHAT_REQ = 100167
MSG_FRIEND_CHAT_RSP = 100168
MSG_FRIEND_DATA_UPDATE_NTF = 100156
MSG_FRIEND_HANDLE_APPLY_REQ = 100160
MSG_FRIEND_HANDLE_APPLY_RSP = 100161
MSG_FRIEND_LIST_REQ = 100152
MSG_FRIEND_LIST_RSP = 100153
MSG_FRIEND_LOAD_FINISH_NTF = 100172
MSG_FRIEND_RECV_APPLY_NTF = 100159
MSG_FRIEND_REMOVE_FRIEND_NTF = 100165
MSG_FRIEND_REMOVE_FRIEND_REQ = 100163
MSG_FRIEND_REMOVE_FRIEND_RSP = 100164
MSG_FRIEND_SEARCH_REQ = 100150
MSG_FRIEND_SEARCH_RSP = 100151
MSG_GAMEOVER_NTF = 100910
MSG_GAME_TO_CLIENT_REPROT_MAIL_NTF = 107802
MSG_GATEWAY_CONNECTSUCCESS_NTF = 604
MSG_GATEWAY_HANDSHAKE_NTF = 602
MSG_GATEWAY_HANDSHAKE_STEP2_NTF = 618
MSG_GATEWAY_KICKOUT_CLIENT = 605
MSG_GATEWAY_NETSPEED_TEST_REQ = 616
MSG_GATEWAY_NETSPEED_TEST_RSP = 617
MSG_GATEWAY_PING_REQ = 600
MSG_GATEWAY_PING_RSP = 601
MSG_GATEWAY_PING_RSP_RSP = 615
MSG_GATEWAY_POST_HACKINFO_NTF = 619
MSG_GATEWAY_SYNC_CHK_RSP = 614
MSG_GATEWAY_SYN_CHK_NTF = 613
MSG_GATEWAY_SYN_RST_NTF = 610
MSG_GATEWAY_SYN_RST_RSP = 611
MSG_GATEWAY_SYN_RUM_NTF = 612
MSG_GATEWAY_TEST_PACKET_REQ = 606
MSG_GATEWAY_TEST_PACKET_RSP = 607
MSG_GATEWAY_TEST_PING_REQ = 608
MSG_GATEWAY_TEST_PONG_RSP = 609
MSG_GATEWAY_UCODE_NTF = 603
MSG_GEMSTONE_HANDBOOK_ACTIVE_NTF = 1009
MSG_GEMSTONE_HANDBOOK_NTF = 1014
MSG_GEMSTONE_UPGRADE_REQ = 1007
MSG_GEMSTONE_UPGRADE_RSP = 1008
MSG_GET_AWAY_FROM_PITFALL_REQ = 102106
MSG_GET_AWAY_FROM_PITFALL_RSP = 102107
MSG_GET_CLIENT_LOG_RSP = 102521
MSG_GET_LEAGUE_BOSS_HP_RANK_LIST_REQ = 102114
MSG_GET_LEAGUE_BOSS_HP_RANK_LIST_RSP = 102115
MSG_GET_MAIL_LIKE_REQ = 1537
MSG_GET_MAIL_LIKE_RSP = 1538
MSG_GET_MAKE_HERO_EQUIP_REQ = 107971
MSG_GET_MAKE_HERO_EQUIP_RSP = 107972
MSG_GET_NOVICE_GUIDE_DATA_REQ = 102150
MSG_GET_NOVICE_GUIDE_DATA_RSP = 102151
MSG_GET_SEND_FLOWER_RANK_LIST_REQ = 102112
MSG_GET_SEND_FLOWER_RANK_LIST_RSP = 102113
MSG_GET_SP_LIKENUM_REQ = 1539
MSG_GET_SP_LIKENUM_RSP = 1540
MSG_GOLD_FINGER_NTF = 1301
MSG_GOLD_FINGER_STATE_CHANGE_NTF = 1302
MSG_GOODS_COMPOSE_REQ = 1000
MSG_GOODS_COMPOSE_RSP = 1001
MSG_GOODS_CREATE_NTF = 510
MSG_GOODS_DESTROY_NTF = 511
MSG_GOODS_GET_REASON_NTF = 1017
MSG_GOODS_PLACE_HOLDER_END = 1050
MSG_GOODS_SELL_REQ = 1002
MSG_GOODS_SELL_RSP = 1003
MSG_GOODS_USEITEM_NTF = 1006
MSG_GOODS_USEITEM_REQ = 1004
MSG_GOODS_USEITEM_RSP = 1005
MSG_HALO_BASE_HALO_RESTORE_REQ = 1732
MSG_HALO_BASE_HALO_RESTORE_RSP = 1733
MSG_HALO_BASE_HALO_UPGRADE_REQ = 1730
MSG_HALO_BASE_HALO_UPGRADE_RSP = 1731
MSG_HALO_USE_PROP_REQ = 1734
MSG_HALO_USE_PROP_RSP = 1735
MSG_HERO_ACTIVE_AWAKENSKILL_REQ = 1080
MSG_HERO_ACTIVE_AWAKENSKILL_RSP = 1081
MSG_HERO_ADVANCE_REQ = 1054
MSG_HERO_ADVANCE_RSP = 1055
MSG_HERO_AWAKEN_REQ = 1061
MSG_HERO_AWAKEN_RSP = 1062
MSG_HERO_CHANGE_REQ = 1070
MSG_HERO_CHANGE_RESTORE_REQ = 1072
MSG_HERO_CHANGE_RESTORE_RSP = 1073
MSG_HERO_CHANGE_RSP = 1071
MSG_HERO_COMMNET_CONN_SIGN_REQ = 102401
MSG_HERO_COMMNET_CONN_SIGN_RSP = 102402
MSG_HERO_COMPOSE_REQ = 1065
MSG_HERO_COMPOSE_RSP = 1066
MSG_HERO_COMPOUND_REQ = 107973
MSG_HERO_COMPOUND_RSP = 107974
MSG_HERO_DECO_UPDATE_REQ = 1084
MSG_HERO_DECO_UPDATE_RSP = 1085
MSG_HERO_EQUIPMENT_UPDATE_REQ = 1052
MSG_HERO_EQUIPMENT_UPDATE_RSP = 1053
MSG_HERO_EQUIP_DISMANTLE_REQ = 107975
MSG_HERO_EQUIP_DISMANTLE_RSP = 107976
MSG_HERO_EXCLUSIVE_ACTIVE_REQ = 1010
MSG_HERO_EXCLUSIVE_ACTIVE_RSP = 1011
MSG_HERO_EXCLUSIVE_UPGRADE_REQ = 1012
MSG_HERO_EXCLUSIVE_UPGRADE_RSP = 1013
MSG_HERO_FAST_UPGRADE_REQ = 1082
MSG_HERO_FAST_UPGRADE_RSP = 1083
MSG_HERO_GOODS_REVERT_NTF = 1069
MSG_HERO_HANDBOOK_ADD_HERO_NTF = 102000
MSG_HERO_HANDBOOK_LV_LIST_NTF = 1076
MSG_HERO_HANDBOOK_LV_NTF = 1077
MSG_HERO_HANDBOOK_UPGRADE_REQ = 1078
MSG_HERO_HANDBOOK_UPGRADE_RSP = 1079
MSG_HERO_IN_SOUL_LINK_SLOT_REQ = 102904
MSG_HERO_IN_SOUL_LINK_SLOT_RSP = 102905
MSG_HERO_OUT_SOUL_LINK_SLOT_REQ = 102906
MSG_HERO_OUT_SOUL_LINK_SLOT_RSP = 102907
MSG_HERO_PLACE_HOLDER_END = 1200
MSG_HERO_RANK_LIST_MAX = 102520
MSG_HERO_RANK_LIST_UPGRADE_NTF = 102511
MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ = 102051
MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP = 102052
MSG_HERO_ROLEPLOT_MOD_INFO_NTF = 102050
MSG_HERO_SKILL_UPDATE_REQ = 1056
MSG_HERO_SKILL_UPDATE_RSP = 1057
MSG_HERO_SKILL_UPGRADE_REQ = 1058
MSG_HERO_SKILL_UPGRADE_RSP = 1059
MSG_HERO_TALENT_SKILL_ACTIVE_REQ = 1074
MSG_HERO_TALENT_SKILL_ACTIVE_RSP = 1075
MSG_HERO_TRANSFORM_NTF = 101054
MSG_HERO_TRANSFORM_REQ = 101050
MSG_HERO_TRANSFORM_RSP = 101051
MSG_HERO_TRANSFORM_SELECT_REQ = 101052
MSG_HERO_TRANSFORM_SELECT_RSP = 101053
MSG_HERO_UPDATE_GEMSTONE_REQ = 1015
MSG_HERO_UPDATE_GEMSTONE_RSP = 1016
MSG_HERO_UPGRADE_REQ = 1051
MSG_HERO_UPGRADE_RSP = 1060
MSG_HISTORY_PALINFOS_NTF = 102715
MSG_HISTORY_PALINFOS_REQ = 102713
MSG_HISTORY_PALINFOS_RSP = 102714
MSG_IDLE_BATTLE_RATING_PROP_NTF = 100950
MSG_IDLE_DATA_REQ = 100961
MSG_IDLE_DATA_RSP = 100962
MSG_ILLUSION_TOWER_BATTLE_VIDEO_REQ = 100924
MSG_ILLUSION_TOWER_BATTLE_VIDEO_RSP = 100925
MSG_ILLUSION_TOWER_BIG_PRIZE_REQ = 100951
MSG_ILLUSION_TOWER_BIG_PRIZE_RSP = 100952
MSG_ILLUSION_TOWER_SWEEP_REQ = 100920
MSG_ILLUSION_TOWER_SWEEP_RSP = 100921
MSG_INCREASE_SOUL_LINK_SLOT_LIMIT_REQ = 102902
MSG_INCREASE_SOUL_LINK_SLOT_LIMIT_RSP = 102903
MSG_INTERACTION_RECENT_PLAYER_LOGOUT_NTF = 100813
MSG_INTERATION_BASE_INFO_NTF = 100800
MSG_INTERATION_COLLECT_COINS_REQ = 100802
MSG_INTERATION_COLLECT_COINS_RSP = 100803
MSG_INTERATION_INFO_REQ = 100804
MSG_INTERATION_INFO_RSP = 100805
MSG_INTERATION_LIKE_REQ = 100801
MSG_INTERATION_REWARD_COINS_REQ = 100806
MSG_KQFRIEND_ANSWER_ALL_APPLY_REQ = 102858
MSG_KQFRIEND_ANSWER_ALL_APPLY_RSP = 102859
MSG_KQFRIEND_ANSWER_APPLY_NTF = 102838
MSG_KQFRIEND_ANSWER_APPLY_REQ = 102836
MSG_KQFRIEND_ANSWER_APPLY_RSP = 102837
MSG_KQFRIEND_APPLY_AND_BLOCK_LIST_REQ = 102860
MSG_KQFRIEND_APPLY_AND_BLOCK_LIST_RSP = 102861
MSG_KQFRIEND_APPLY_LIST_REQ = 102849
MSG_KQFRIEND_APPLY_LIST_RSP = 102850
MSG_KQFRIEND_APPLY_NTF = 102835
MSG_KQFRIEND_APPLY_REQ = 102833
MSG_KQFRIEND_APPLY_RSP = 102834
MSG_KQFRIEND_BLOCK_REQ = 102852
MSG_KQFRIEND_BLOCK_RSP = 102853
MSG_KQFRIEND_DELETE_NTF = 102841
MSG_KQFRIEND_DELETE_REQ = 102839
MSG_KQFRIEND_DELETE_RSP = 102840
MSG_KQFRIEND_FRIEND_LIST_REQ = 102847
MSG_KQFRIEND_FRIEND_LIST_RSP = 102848
MSG_KQFRIEND_GAIN_STAR_REQ = 102845
MSG_KQFRIEND_GAIN_STAR_RSP = 102846
MSG_KQFRIEND_GIVE_AND_GAIN_STAR_REQ = 102856
MSG_KQFRIEND_GIVE_AND_GAIN_STAR_RSP = 102857
MSG_KQFRIEND_GIVE_STAR_NTF = 102844
MSG_KQFRIEND_GIVE_STAR_REQ = 102842
MSG_KQFRIEND_GIVE_STAR_RSP = 102843
MSG_KQFRIEND_LIST_UPDATE_NTF = 102851
MSG_KQFRIEND_MAX = 102890
MSG_KQFRIEND_RECOMMEND_REQ = 102862
MSG_KQFRIEND_RECOMMEND_RSP = 102863
MSG_KQFRIEND_SEARCH_REQ = 102831
MSG_KQFRIEND_SEARCH_RSP = 102832
MSG_KQFRIEND_UNBLOCK_REQ = 102854
MSG_KQFRIEND_UNBLOCK_RSP = 102855
MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ = 107751
MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP = 107752
MSG_LABOURDAYACTIVITY_Max = 107760
MSG_LEAGUEBOSSWAR_ATTACK_INFO_REQ = 104039
MSG_LEAGUEBOSSWAR_ATTACK_INFO_RSP = 104040
MSG_LEAGUEBOSSWAR_BASE_INFO_REQ = 104025
MSG_LEAGUEBOSSWAR_BASE_INFO_RSP = 104026
MSG_LEAGUEBOSSWAR_ENTER_BATTLE_REQ = 104029
MSG_LEAGUEBOSSWAR_ENTER_BATTLE_RSP = 104030
MSG_LEAGUEBOSSWAR_LEAGUE_INFO_REQ = 104033
MSG_LEAGUEBOSSWAR_LEAGUE_INFO_RSP = 104034
MSG_LEAGUEBOSSWAR_RANK_INFO_REQ = 104031
MSG_LEAGUEBOSSWAR_RANK_INFO_RSP = 104032
MSG_LEAGUEBOSSWAR_REWARD_REQ = 104027
MSG_LEAGUEBOSSWAR_REWARD_RSP = 104028
MSG_LEAGUEBOSSWAR_SHOP_BUYGOODS_REQ = 104035
MSG_LEAGUEBOSSWAR_SHOP_BUYGOODS_RSP = 104036
MSG_LEAGUEBOSS_GAIN_REWARD_REQ = 101704
MSG_LEAGUEBOSS_GAIN_REWARD_RSP = 101705
MSG_LEAGUEBOSS_MAX = 101800
MSG_LEAGUEBOSS_RANKLIST_REQ = 101702
MSG_LEAGUEBOSS_RANKLIST_RSP = 101703
MSG_LEAGUEBOSS_UPDATE_NTF = 101701
MSG_LEAGUEPART_UPDATE_NTF = 101500
MSG_LEAGUEWAR_APPLY_REQ = 104000
MSG_LEAGUEWAR_APPLY_RSP = 104001
MSG_LEAGUEWAR_ATTACK_INFO_NTF = 104022
MSG_LEAGUEWAR_ATTACK_INFO_REQ = 104016
MSG_LEAGUEWAR_ATTACK_INFO_RSP = 104017
MSG_LEAGUEWAR_BASE_INFO_REQ = 104002
MSG_LEAGUEWAR_BASE_INFO_RSP = 104003
MSG_LEAGUEWAR_BATTLE_INFO_REQ = 104012
MSG_LEAGUEWAR_BATTLE_INFO_RSP = 104013
MSG_LEAGUEWAR_ENTER_BATTLE_REQ = 104020
MSG_LEAGUEWAR_ENTER_BATTLE_RSP = 104021
MSG_LEAGUEWAR_MAX = 104050
MSG_LEAGUEWAR_RANK_REQ = 104010
MSG_LEAGUEWAR_RANK_RSP = 104011
MSG_LEAGUEWAR_REMOVE_LINEUP_REQ = 104008
MSG_LEAGUEWAR_REMOVE_LINEUP_RSP = 104009
MSG_LEAGUEWAR_REPORT_NTF = 104023
MSG_LEAGUEWAR_REWARD_REQ = 104004
MSG_LEAGUEWAR_REWARD_RSP = 104005
MSG_LEAGUEWAR_SCORE_RANK_REQ = 104018
MSG_LEAGUEWAR_SCORE_RANK_RSP = 104019
MSG_LEAGUEWAR_SET_LINEUP_REQ = 104006
MSG_LEAGUEWAR_SET_LINEUP_RSP = 104007
MSG_LEAGUEWAR_UPDATE_TROOP_NTF = 104024
MSG_LEAGUEWAR_USEDPALS_REQ = 104037
MSG_LEAGUEWAR_USEDPALS_RSP = 104038
MSG_LEAGUE_APPLY_INVITE_LIST_REQ = 101460
MSG_LEAGUE_APPLY_INVITE_LIST_RSP = 101461
MSG_LEAGUE_APPLY_REQ = 101443
MSG_LEAGUE_APPLY_RSP = 101444
MSG_LEAGUE_COMP_BASE_DATA_NTF = 101813
MSG_LEAGUE_COMP_BASE_DATA_REQ = 101811
MSG_LEAGUE_COMP_BASE_DATA_RSP = 101812
MSG_LEAGUE_COMP_BASE_INFO_NTF = 101829
MSG_LEAGUE_COMP_BASE_INFO_REQ = 101827
MSG_LEAGUE_COMP_BASE_INFO_RSP = 101828
MSG_LEAGUE_COMP_BATTLE_EFFECT_NTF = 101847
MSG_LEAGUE_COMP_BATTLE_RECORD_DETAIL_REQ = 101833
MSG_LEAGUE_COMP_BATTLE_RECORD_DETAIL_RSP = 101834
MSG_LEAGUE_COMP_BATTLE_RECORD_NTF = 101832
MSG_LEAGUE_COMP_BATTLE_RECORD_REQ = 101830
MSG_LEAGUE_COMP_BATTLE_RECORD_RSP = 101831
MSG_LEAGUE_COMP_BATTLE_RESULT_NTF = 101853
MSG_LEAGUE_COMP_CELL_REQ = 101814
MSG_LEAGUE_COMP_CELL_RSP = 101815
MSG_LEAGUE_COMP_COMMON_REQ = 101860
MSG_LEAGUE_COMP_COMMON_RSP = 101861
MSG_LEAGUE_COMP_DISPATCH_NTF = 101837
MSG_LEAGUE_COMP_DISPATCH_REQ = 101817
MSG_LEAGUE_COMP_DISPATCH_RSP = 101818
MSG_LEAGUE_COMP_GET_RES_NTF = 101838
MSG_LEAGUE_COMP_GET_RES_REQ = 101825
MSG_LEAGUE_COMP_GET_RES_RSP = 101826
MSG_LEAGUE_COMP_HARVEST_INFO_REQ = 101835
MSG_LEAGUE_COMP_HARVEST_INFO_RSP = 101836
MSG_LEAGUE_COMP_MARK_REQ = 101842
MSG_LEAGUE_COMP_MARK_RSP = 101843
MSG_LEAGUE_COMP_MAX = 101924
MSG_LEAGUE_COMP_PLAYBACK_REQ = 101822
MSG_LEAGUE_COMP_PLAYBACK_RSP = 101823
MSG_LEAGUE_COMP_RANK_REQ = 101819
MSG_LEAGUE_COMP_RANK_RSP = 101820
MSG_LEAGUE_COMP_REDDOT_REQ = 101845
MSG_LEAGUE_COMP_REDDOT_RSP = 101846
MSG_LEAGUE_COMP_SPECIAL_BATTLE_RECORD_NTF = 101841
MSG_LEAGUE_COMP_SPECIAL_BATTLE_RECORD_REQ = 101840
MSG_LEAGUE_COMP_TASK_GAIN_REQ = 101851
MSG_LEAGUE_COMP_TASK_GAIN_RSP = 101852
MSG_LEAGUE_COMP_TASK_NTF = 101850
MSG_LEAGUE_COMP_TASK_REQ = 101848
MSG_LEAGUE_COMP_TASK_RSP = 101849
MSG_LEAGUE_COMP_UPDATE_CELL_NTF = 101816
MSG_LEAGUE_COMP_UPDATE_PART_NTF = 101824
MSG_LEAGUE_COMP_UPDATE_PART_REQ = 101839
MSG_LEAGUE_COMP_UPDATE_PART_RSP = 101844
MSG_LEAGUE_COMP_UPDATE_RANK_NTF = 101821
MSG_LEAGUE_CREATE_REQ = 101449
MSG_LEAGUE_CREATE_RSP = 101450
MSG_LEAGUE_EXPEL_REQ = 101433
MSG_LEAGUE_EXPEL_RSP = 101434
MSG_LEAGUE_GET_ROLE_NAME_REQ = 104042
MSG_LEAGUE_GET_ROLE_NAME_RSP = 104043
MSG_LEAGUE_HANDLE_APPLY_REQ = 101431
MSG_LEAGUE_HANDLE_APPLY_RSP = 101432
MSG_LEAGUE_INVITED_APPLY_NTF = 101457
MSG_LEAGUE_INVITED_APPLY_REQ = 101455
MSG_LEAGUE_INVITED_APPLY_RSP = 101456
MSG_LEAGUE_INVITED_INFO_REQ = 101451
MSG_LEAGUE_INVITED_INFO_RSP = 101452
MSG_LEAGUE_INVITED_REQ = 101453
MSG_LEAGUE_INVITED_RSP = 101454
MSG_LEAGUE_LOG_REQ = 101427
MSG_LEAGUE_LOG_RSP = 101428
MSG_LEAGUE_MAIL_REQ = 101429
MSG_LEAGUE_MAIL_RSP = 101430
MSG_LEAGUE_MAX = 101600
MSG_LEAGUE_MEDAL_BASE_NTF = 105053
MSG_LEAGUE_MEDAL_BASE_REQ = 105051
MSG_LEAGUE_MEDAL_BASE_RSP = 105052
MSG_LEAGUE_MEDAL_DETAIL_REQ = 105054
MSG_LEAGUE_MEDAL_DETAIL_RSP = 105055
MSG_LEAGUE_MEDAL_MAX = 105100
MSG_LEAGUE_MEDAL_REWARD_REQ = 105056
MSG_LEAGUE_MEDAL_REWARD_RSP = 105057
MSG_LEAGUE_MEDAL_WARNAUTO_REQ = 105060
MSG_LEAGUE_MEDAL_WARNAUTO_RSP = 105061
MSG_LEAGUE_MEDAL_WARNDEL_REQ = 105058
MSG_LEAGUE_MEDAL_WARNDEL_RSP = 105059
MSG_LEAGUE_MEDAL_WARN_REQ = 105062
MSG_LEAGUE_MEDAL_WARN_RSP = 105063
MSG_LEAGUE_MEMBER_NTF = 101421
MSG_LEAGUE_MODIFY_REQ = 101425
MSG_LEAGUE_MODIFY_RSP = 101426
MSG_LEAGUE_NATIONALFLAG_INFO_REQ = 101506
MSG_LEAGUE_NATIONALFLAG_INFO_RSP = 101507
MSG_LEAGUE_NATIONALFLAG_MODIFY_REQ = 101504
MSG_LEAGUE_NATIONALFLAG_MODIFY_RSP = 101505
MSG_LEAGUE_NATIONALFLAG_STATE_NTF = 101503
MSG_LEAGUE_OFFICER_POSITION_REQ = 101435
MSG_LEAGUE_OFFICER_POSITION_RSP = 101436
MSG_LEAGUE_OFFICER_RESIGN_REQ = 101437
MSG_LEAGUE_OFFICER_RESIGN_RSP = 101438
MSG_LEAGUE_QUICK_ADD_REQ = 101458
MSG_LEAGUE_QUICK_ADD_RSP = 101459
MSG_LEAGUE_RECOMMEND_REQ = 101439
MSG_LEAGUE_RECOMMEND_RSP = 101440
MSG_LEAGUE_RECRUIT_POINT_NTF = 104041
MSG_LEAGUE_RECRUIT_REQ = 101463
MSG_LEAGUE_RECRUIT_RSP = 101464
MSG_LEAGUE_RECRUIT_SAVE_REQ = 101465
MSG_LEAGUE_RECRUIT_SAVE_RSP = 101466
MSG_LEAGUE_SEARCH_REQ = 101445
MSG_LEAGUE_SEARCH_RSP = 101446
MSG_LEAGUE_SIGNIN_REQ = 101501
MSG_LEAGUE_SIGNIN_RSP = 101502
MSG_LEAGUE_SYNC_EXTRA_NTF = 101462
MSG_LEAGUE_TRANSFER_REQ = 101441
MSG_LEAGUE_TRANSFER_RSP = 101442
MSG_LEAGUE_UPDATEINFO_REQ = 104044
MSG_LEAGUE_UPDATEINFO_RSP = 104045
MSG_LEAGUE_UPDATE_NTF = 101420
MSG_LEA_ACTIVITY_BOSS_CALL_REQ = 104100
MSG_LEA_ACTIVITY_BOSS_CALL_RSP = 104101
MSG_LEA_ACTIVITY_BOSS_GET_RANK_REWARD_REQ = 104104
MSG_LEA_ACTIVITY_BOSS_GET_RANK_REWARD_RSP = 104105
MSG_LEA_ACTIVITY_BOSS_MAX = 104200
MSG_LEA_ACTIVITY_BOSS_OPEN_INFO_REQ = 104106
MSG_LEA_ACTIVITY_BOSS_OPEN_INFO_RSP = 104107
MSG_LEA_ACTIVITY_BOSS_RANK_INFO_REQ = 104102
MSG_LEA_ACTIVITY_BOSS_RANK_INFO_RSP = 104103
MSG_LEA_ACTIVITY_BOSS_SETTLE_NTF = 104108
MSG_LEGEND_BATTLE_RECORD_REQ = 105212
MSG_LEGEND_BATTLE_RECORD_RSP = 105213
MSG_LEGEND_BATTLE_REQ = 105205
MSG_LEGEND_BATTLE_RSP = 105206
MSG_LEGEND_CHALLENGE_LIST_REQ = 105210
MSG_LEGEND_CHALLENGE_LIST_RSP = 105211
MSG_LEGEND_DATA_NTF = 105207
MSG_LEGEND_GETDEFTROOP_REQ = 105203
MSG_LEGEND_GETDEFTROOP_RSP = 105204
MSG_LEGEND_GET_VICTORY_REWARD_REQ = 105220
MSG_LEGEND_GET_VICTORY_REWARD_RSP = 105221
MSG_LEGEND_PRIZE_REQ = 105216
MSG_LEGEND_PRIZE_RSP = 105217
MSG_LEGEND_RANK_DATA_REQ = 105208
MSG_LEGEND_RANK_DATA_RSP = 105209
MSG_LEGEND_SETDEFENSELINEUP_REQ = 105201
MSG_LEGEND_SETDEFENSELINEUP_RSP = 105202
MSG_LEGEND_SIGN_REQ = 105214
MSG_LEGEND_SIGN_RSP = 105215
MSG_LEGEND_TROOPS_REQ = 105218
MSG_LEGEND_TROOPS_RSP = 105219
MSG_LIFETIME_CARD_MAX = 105110
MSG_LIFETIME_CARD_REWARD_RSP = 105101
MSG_LIKED_GAME_INFO_NTF = 100812
MSG_LOBBYCNT_ADD_GOODS_NTF = 100102
MSG_LOBBYCNT_CREATE_NTF = 100100
MSG_LOBBYCNT_DEL_GOODS_NTF = 100103
MSG_LOBBYCNT_DESTROY_NTF = 100101
MSG_LOBBYTEAM_ADD_MEMBER_L2C_NTF = 100214
MSG_LOBBYTEAM_CHANGE_PREPARE_STATE_C2L_REQ = 100202
MSG_LOBBYTEAM_CHANGE_PREPARE_STATE_L2C_RSP = 100203
MSG_LOBBYTEAM_CHANGE_SETTING_C2L_REQ = 100200
MSG_LOBBYTEAM_CHANGE_SETTING_L2C_RSP = 100201
MSG_LOBBYTEAM_DEL_MEMBER_L2C_NTF = 100215
MSG_LOBBYTEAM_INVITATION_L2C_NTF = 100206
MSG_LOBBYTEAM_INVITATION_RESULT_L2C_NTF = 100209
MSG_LOBBYTEAM_QUIT_C2L_REQ = 100212
MSG_LOBBYTEAM_QUIT_L2C_RSP = 100213
MSG_LOBBYTEAM_REPLY_TO_INVITATION_C2L_REQ = 100207
MSG_LOBBYTEAM_REPLY_TO_INVITATION_L2C_RSP = 100208
MSG_LOBBYTEAM_SEND_INVITATION_C2L_REQ = 100204
MSG_LOBBYTEAM_SEND_INVITATION_L2C_RSP = 100205
MSG_LOBBYTEAM_UPDATE_MATCH_SETTING_L2C_NTF = 100210
MSG_LOBBYTEAM_UPDATE_MEMBER_INFO_L2C_NTF = 100211
MSG_LOBBY_CARD_GET_DATA_REQ = 100612
MSG_LOBBY_CARD_GET_DATA_RSP = 100613
MSG_LOBBY_CARD_UPGRADE_REQ = 100610
MSG_LOBBY_CARD_UPGRADE_RSP = 100611
MSG_LOBBY_COMPLETEGAME_NTF = 100740
MSG_LOBBY_COMPLETEGAME_PRIZE_REQ = 100741
MSG_LOBBY_COMPLETEGAME_PRIZE_RSP = 100742
MSG_LOBBY_ERROR_L2C_NTF = 100503
MSG_LOBBY_FETCH_MATCH_DATA_REQ = 100730
MSG_LOBBY_FETCH_MATCH_DATA_RSP = 100731
MSG_LOBBY_HERO_SYNC_NTF = 100602
MSG_LOBBY_LOTTERY_REQ = 100650
MSG_LOBBY_LOTTERY_RSP = 100651
MSG_LOBBY_LOTTERY_SYNC_NTF = 100652
MSG_LOBBY_MATCH_BANNED_L2C_NTF = 100500
MSG_LOBBY_MATCH_BEGIN_NTF = 100684
MSG_LOBBY_MATCH_BEGIN_REQ = 100680
MSG_LOBBY_MATCH_BEGIN_RSP = 100681
MSG_LOBBY_MATCH_CANCEL_NTF = 100685
MSG_LOBBY_MATCH_CANCEL_REQ = 100682
MSG_LOBBY_MATCH_CANCEL_RSP = 100683
MSG_LOBBY_MATCH_COUNT_NTF = 100687
MSG_LOBBY_MATCH_LIMIT_NTF = 100686
MSG_LOBBY_MATCH_READY_C2L_REQ = 100501
MSG_LOBBY_MATCH_READY_L2C_RSP = 100502
MSG_LOBBY_MATCH_STARTSOON_NTF = 100688
MSG_LOBBY_NEARBY_PLAYER_LIST_REQ = 100703
MSG_LOBBY_NEARBY_PLAYER_LIST_RSP = 100704
MSG_LOBBY_NEARBY_PLAYER_SYNC_NTF = 100705
MSG_LOBBY_PROP_CHANGENATION_REQ = 100110
MSG_LOBBY_PROP_CHANGENATION_RSP = 100111
MSG_LOBBY_PROP_CLEAN_CONTROL_COMMAND_NTF = 100109
MSG_LOBBY_PROP_CREATEGOODS_NTF = 100106
MSG_LOBBY_PROP_CREATEROLE_NTF = 100104
MSG_LOBBY_PROP_DESTROYGOODS_NTF = 100107
MSG_LOBBY_PROP_DESTROYROLE_NTF = 100105
MSG_LOBBY_PROP_UPDATE_MATCH_DATA_NTF = 100112
MSG_LOBBY_PROP_UPDATE_NTF = 100108
MSG_LOBBY_RECENT_PLAYER_LIST_REQ = 100700
MSG_LOBBY_RECENT_PLAYER_LIST_RSP = 100701
MSG_LOBBY_RECENT_PLAYER_SYNC_NTF = 100702
MSG_LOBBY_SURFACE_SWITCH_REQ = 100720
MSG_LOBBY_SURFACE_SWITCH_RSP = 100721
MSG_LOBBY_TEAM_CANCEL_REQ = 100666
MSG_LOBBY_TEAM_CANCEL_RSP = 100667
MSG_LOBBY_TEAM_CREATE_REQ = 100672
MSG_LOBBY_TEAM_CREATE_RSP = 100673
MSG_LOBBY_TEAM_JOIN_NTF = 100660
MSG_LOBBY_TEAM_KICK_NTF = 100670
MSG_LOBBY_TEAM_KICK_REQ = 100668
MSG_LOBBY_TEAM_KICK_RSP = 100669
MSG_LOBBY_TEAM_LEAVE_NTF = 100661
MSG_LOBBY_TEAM_LEAVE_REQ = 100662
MSG_LOBBY_TEAM_LEAVE_RSP = 100663
MSG_LOBBY_TEAM_MEMBER_UPDATE_NTF = 100671
MSG_LOBBY_TEAM_READY_REQ = 100664
MSG_LOBBY_TEAM_READY_RSP = 100665
MSG_LOBBY_UPDATE_HERO_REQ = 100600
MSG_LOBBY_UPDATE_HERO_RSP = 100601
MSG_LOBSHOP_BUY_GOODS_REQ = 100250
MSG_LOBSHOP_BUY_GOODS_RSP = 100251
MSG_LOBSHOP_FLUSH_GOODS_PRICE_REQ = 100252
MSG_LOBSHOP_FLUSH_GOODS_PRICE_RSP = 100253
MSG_LOBWARE_RECYCLE_ITEM_REQ = 100065
MSG_LOBWARE_RECYCLE_ITEM_RSP = 100066
MSG_LOGIN_ACTOR_INFO_NTF = 405
MSG_LOGIN_AUTO_LOGIN_REQ = 414
MSG_LOGIN_AUTO_LOGIN_REQ_V2 = 415
MSG_LOGIN_BAN_ACCOUNT_NTF = 422
MSG_LOGIN_CREATE_ACTOR_REQ = 406
MSG_LOGIN_DELETE_ACTOR_REQ = 407
MSG_LOGIN_ENTER_PLAYERNUM_NTF = 413
MSG_LOGIN_HANDSHAKE_REQ = 403
MSG_LOGIN_HANDSHAKE_RSP = 420
MSG_LOGIN_KICKOUT_NTF = 418
MSG_LOGIN_LOGIN_ERROR_NTF = 416
MSG_LOGIN_LOGIN_REQ = 404
MSG_LOGIN_LOGIN_RSP = 421
MSG_LOGIN_MESSAGE_NTF = 402
MSG_LOGIN_MODIFY_FROMWORLDID_NTF = 107766
MSG_LOGIN_NEW_ACCOUNT_NTF = 419
MSG_LOGIN_QUEUE_ORDER_NTF = 410
MSG_LOGIN_REGISTERACCOUNT_REQ = 411
MSG_LOGIN_REGISTERACCOUNT_RSP = 412
MSG_LOGIN_SELECT_ACTOR_REQ = 408
MSG_LOGIN_SWITCH_STATE_REQ = 409
MSG_LOGIN_UPDATE_ALSESSION_NTF = 417
MSG_LOTTERYWISH_FREE_REFRESH_NTF = 101005
MSG_LOTTERYWISH_REFRESH_REQ = 101003
MSG_LOTTERYWISH_REFRESH_RSP = 101004
MSG_LOTTERYWISH_REQ = 101000
MSG_LOTTERYWISH_RSP = 101001
MSG_LOTTERYWISH_SYNC_NTF = 101002
MSG_LOTTERY_SETCHOICE_REQ = 100653
MSG_LOTTERY_SETCHOICE_RSP = 100654
MSG_LOTTERY_SET_WISH_LIST_REQ = 1900
MSG_LOTTERY_SET_WISH_LIST_RSP = 1901
MSG_LUA_DEBUGINFO_NTF = 470
MSG_LUA_RUN_FUNCA_REQ = 471
MSG_LUA_RUN_FUNCB_NEW_REQ = 475
MSG_LUA_RUN_FUNCB_REQ = 472
MSG_LUA_RUN_MICRO_FUNCB_REQ = 473
MSG_LUA_TRANSMIT_MICRO_REQ = 474
MSG_MAGIC_STONE_NTF = 100400
MSG_MAIL_DELETE_C2L_REQ = 100305
MSG_MAIL_DELETE_L2C_RSP = 100306
MSG_MAIL_EVENT_L2C_NTF = 100300
MSG_MAIL_GET_ATTACHMENT_C2L_REQ = 100307
MSG_MAIL_GET_ATTACHMENT_L2C_RSP = 100308
MSG_MAIL_GET_DETAIL_C2L_REQ = 100303
MSG_MAIL_GET_DETAIL_L2C_RSP = 100304
MSG_MAIL_GET_LIST_C2L_REQ = 100301
MSG_MAIL_GET_LIST_L2C_RSP = 100302
MSG_MAIL_SEND_C2L_REQ = 100310
MSG_MAIL_SEND_C2L_RSP = 100311
MSG_MAIL_UNREAD_L2C_NTF = 100309
MSG_MAKE_FOOD_ACCOUNTBOOK_REQ = 107798
MSG_MAKE_FOOD_ACCOUNTBOOK_RSP = 107799
MSG_MAKE_FOOD_AUTO_MAKE_REQ = 107957
MSG_MAKE_FOOD_AUTO_MAKE_RSP = 107958
MSG_MAKE_FOOD_CANCEL_REQ = 107794
MSG_MAKE_FOOD_CANCEL_RSP = 107795
MSG_MAKE_FOOD_EXCHANGE_REQ = 107790
MSG_MAKE_FOOD_EXCHANGE_RSP = 107791
MSG_MAKE_FOOD_GET_REQ = 107792
MSG_MAKE_FOOD_GET_RSP = 107793
MSG_MAKE_FOOD_MAKE_REQ = 107784
MSG_MAKE_FOOD_MAKE_RSP = 107785
MSG_MAKE_FOOD_MAX = 107960
MSG_MAKE_FOOD_ORDERLIST_REQ = 107796
MSG_MAKE_FOOD_ORDERLIST_RSP = 107797
MSG_MAKE_FOOD_ORDER_NTF = 107800
MSG_MAKE_FOOD_ROLEINFO_REQ = 107786
MSG_MAKE_FOOD_ROLEINFO_RSP = 107787
MSG_MAKE_FOOD_SHELF_REQ = 107788
MSG_MAKE_FOOD_SHELF_RSP = 107789
MSG_MAKE_FOOD_SHOW_REQ = 107782
MSG_MAKE_FOOD_SHOW_RSP = 107783
MSG_MAP_DESTORYMAP_NTF = 431
MSG_MAP_LOADMAP_NTF = 430
MSG_MATCH_BEGIN_MATCH_NTF = 552
MSG_MATCH_CANCEL_MATCH_NTF = 554
MSG_MATCH_CONNECT_BATTLE_GATE_NTF = 563
MSG_MATCH_ENTERQUEUE_REQ = 560
MSG_MATCH_ENTER_NTF = 559
MSG_MATCH_EXIT_MATCH_NTF = 553
MSG_MATCH_INVITE_MEMBER_REQ = 550
MSG_MATCH_INVITE_MEMBER_RSP = 551
MSG_MATCH_LOCK_NTF = 557
MSG_MATCH_READY_ENTER_NTF = 558
MSG_MATCH_RECONNECT_BATTLE_ANSWER_REQ = 565
MSG_MATCH_RECONNECT_BATTLE_ANSWER_RSP = 566
MSG_MATCH_RECONNECT_BATTLE_CANCEL_REQ = 567
MSG_MATCH_RECONNECT_BATTLE_CANCEL_RSP = 568
MSG_MATCH_RECONNECT_BATTLE_QUERY_NTF = 564
MSG_MATCH_SEND_UNIT_REQ = 562
MSG_MATCH_SET_PARAM_REQ = 555
MSG_MATCH_START_GAME_NTF = 556
MSG_MATCH_UNIT_INFO_NTF = 561
MSG_MATE_APPLY_REQ = 102303
MSG_MATE_APPLY_RSP = 102304
MSG_MATE_END_REQ = 102309
MSG_MATE_END_RSP = 102310
MSG_MATE_FIND_REQ = 102307
MSG_MATE_FIND_RSP = 102308
MSG_MATE_HANDLE_APPLY_REQ = 102305
MSG_MATE_HANDLE_APPLY_RSP = 102306
MSG_MATE_LOG_REDDOT_NTF = 102320
MSG_MATE_LOG_REQ = 102315
MSG_MATE_LOG_REWARD_REQ = 102318
MSG_MATE_LOG_REWARD_RSP = 102319
MSG_MATE_LOG_RSP = 102316
MSG_MATE_MAX = 102400
MSG_MATE_MSG_SET_NTF = 1619
MSG_MATE_RECOMMEND_REQ = 102301
MSG_MATE_RECOMMEND_RSP = 102302
MSG_MATE_REPAIR_REQ = 102313
MSG_MATE_REPAIR_RSP = 102314
MSG_MATE_SELFEVENT_UPDATE_NTF = 102317
MSG_MATE_UPDATE_NTF = 102300
MSG_MATE_WATER_REQ = 102311
MSG_MATE_WATER_RSP = 102312
MSG_MAZE_CRUSH_REQ = 107761
MSG_MAZE_CRUSH_RSP = 107762
MSG_MERGE_SERVER_NTF = 104350
MSG_MILL_GET_ORDER_REQ = 101602
MSG_MILL_GET_ORDER_REWARD_REQ = 101608
MSG_MILL_GET_ORDER_REWARD_RSP = 101609
MSG_MILL_GET_ORDER_RSP = 101603
MSG_MILL_START_ORDER_REQ = 101604
MSG_MILL_START_ORDER_RSP = 101605
MSG_MILL_UPDATE_NTF = 101601
MSG_MILL_UPGRADE_ORDER_REQ = 101606
MSG_MILL_UPGRADE_ORDER_RSP = 101607
MSG_MODULEOPEN_UPDATE_NTF = 105150
MSG_MSG_RUN_CMD_MAX = 102530
MSG_NEARBY_BASE_INFO_REQ = 100811
MSG_NEWHERO_SETHERO_REQ = 100581
MSG_NEWHERO_SETHERO_RSP = 100582
MSG_NEWYEAR_GIVE_REDPACKET_RANK_REQ = 105957
MSG_NEWYEAR_GIVE_REDPACKET_RANK_RSP = 105958
MSG_NEWYEAR_GIVE_REDPACKET_REQ = 105951
MSG_NEWYEAR_GIVE_REDPACKET_RSP = 105952
MSG_NEWYEAR_GRAB_REDPACKET_REQ = 105953
MSG_NEWYEAR_GRAB_REDPACKET_RSP = 105954
MSG_NEWYEAR_REDPACKET_DETAIL_REQ = 105955
MSG_NEWYEAR_REDPACKET_DETAIL_RSP = 105956
MSG_NEWYEAR_SEND_FUCARD_REQ = 105959
MSG_NEWYEAR_SEND_FUCARD_RSP = 105960
MSG_OPERATIVE_PART_DATA = 104306
MSG_OPERATIVE_SHARER_PRIZE_RSP = 104310
MSG_PAL_CREATE_NTF = 520
MSG_PAL_DESTROY_NTF = 521
MSG_PASSPORT_MAX = 102830
MSG_PASSPORT_PART_NTF = 102803
MSG_PASSPORT_REWARD_GAIN_REQ = 102801
MSG_PASSPORT_REWARD_GAIN_RSP = 102802
MSG_PEAK_EVENT_COMMON_REQ = 102960
MSG_PEAK_EVENT_COMMON_RSP = 102961
MSG_PEAK_FOG_REQ = 102967
MSG_PEAK_FOG_RSP = 102968
MSG_PEAK_LEVEL_EXIT_REQ = 102962
MSG_PEAK_LEVEL_REQ = 102952
MSG_PEAK_LEVEL_RSP = 102953
MSG_PEAK_MAX = 103100
MSG_PEAK_MOVE_END_REQ = 102965
MSG_PEAK_MOVE_END_RSP = 102966
MSG_PEAK_MOVE_REQ = 102958
MSG_PEAK_MOVE_RSP = 102959
MSG_PEAK_RELIC_REQ = 102954
MSG_PEAK_RELIC_RSP = 102955
MSG_PEAK_RESET_REQ = 102956
MSG_PEAK_RESET_RSP = 102957
MSG_PEAK_SET_CLIENT_REQ = 102969
MSG_PEAK_SET_CLIENT_RSP = 102970
MSG_PEAK_SET_DIALOGFLAG_REQ = 102963
MSG_PEAK_SET_DIALOGFLAG_RSP = 102964
MSG_PEAK_UPDATE_NTF = 102951
MSG_PICKTHEROUTE_ARCHIVED_RECORDS_REQ = 107871
MSG_PICKTHEROUTE_ARCHIVED_RECORDS_RSP = 107872
MSG_PICKTHEROUTE_ARCHIVED_SAVE_REQ = 107875
MSG_PICKTHEROUTE_ARCHIVED_SAVE_RSP = 107876
MSG_PICKTHEROUTE_EVENTREWARD_REQ = 107873
MSG_PICKTHEROUTE_EVENTREWARD_RSP = 107874
MSG_PICKTHEROUTE_FIRST_SHARE_REWRAD_REQ = 107877
MSG_PICKTHEROUTE_FIRST_SHARE_REWRAD_RSP = 107878
MSG_PICKTHEROUTE_SELECT_LEVEL_REQ = 107879
MSG_PICKTHEROUTE_SELECT_LEVEL_RSP = 107880
MSG_PLAYER_BATTLE_RESULT_REQ = 100927
MSG_PLAYER_BATTLE_RESULT_RSP = 100928
MSG_PLAYER_CHALLENGE_REQ = 100809
MSG_PLAYER_CHALLENGE_RSP = 100810
MSG_PLAYER_DETAILS_REQ = 1364
MSG_PLAYER_DETAILS_RSP = 1365
MSG_PLAYER_ENTER_BATTLE_REQ = 100906
MSG_PLAYER_ENTER_BATTLE_RSP = 100907
MSG_PLAYER_GET_IDLE_REWARD_REQ = 100904
MSG_PLAYER_GET_IDLE_REWARD_RSP = 100905
MSG_PLAYER_IDLE_REWARD_NTF = 100903
MSG_PLAYER_INPUT_CHALLENGE_REQ = 100901
MSG_PLAYER_INPUT_CHALLENGE_RSP = 100902
MSG_PLAYER_LOGIN_FINISH_NOTIFY = 107767
MSG_PLAYER_MODIFY_IDLE_STAGE_REQ = 100908
MSG_PLAYER_MODIFY_IDLE_STAGE_RSP = 100909
MSG_PLAYMODE_VOTE_ANSWER_REQ = 100453
MSG_PLAYMODE_VOTE_ANSWER_RSP = 100454
MSG_PLAYMODE_VOTE_BEGIN_NTF = 100452
MSG_PLAYMODE_VOTE_BEGIN_REQ = 100450
MSG_PLAYMODE_VOTE_BEGIN_RSP = 100451
MSG_PLAYMODE_VOTE_CANCEL_NTF = 100458
MSG_PLAYMODE_VOTE_CANCEL_REQ = 100456
MSG_PLAYMODE_VOTE_CANCEL_RSP = 100457
MSG_PLAYMODE_VOTE_END_NTF = 100455
MSG_PLAYMODE_VOTE_RESET_REQ = 100459
MSG_PLAYMODE_VOTE_RESET_RSP = 100460
MSG_PLEAGUEGELP_REWARDINFO_NTF = 103106
MSG_PLEAGUEHELP_AIDALLIES_REQ = 103102
MSG_PLEAGUEHELP_AIDALLIES_RSP = 103103
MSG_PLEAGUEHELP_INFO_NTF = 103101
MSG_PLEAGUEHELP_MAX = 103130
MSG_PLEAGUEHELP_RECIEVEAWARD_REQ = 103104
MSG_PLEAGUEHELP_RECIEVEAWARD_RSP = 103105
MSG_PLEAGUEHELP_SEEKHELP_POOL_REQ = 103109
MSG_PLEAGUEHELP_SEEKHELP_POOL_RSP = 103110
MSG_PLEAGUEHELP_SEEKHELP_REQ = 103107
MSG_PLEAGUEHELP_SEEKHELP_RSP = 103108
MSG_PLEAGUEHELP_SEL_SEEKHELP_REQ = 103111
MSG_PLEAGUEHELP_SEL_SEEKHELP_RSP = 103112
MSG_PRIVATE_CHAT_LIKE_ALL_NTF = 1544
MSG_PRIVATE_CHAT_LIKE_NTF = 1543
MSG_PRIVATE_CHAT_LIKE_REQ = 1541
MSG_PRIVATE_CHAT_LIKE_RSP = 1542
MSG_PRIVATE_MSG_NTF = 1617
MSG_PRIVATE_MSG_SET_NTF = 1618
MSG_PRIVATE_MSG_SPEAK_REQ = 1615
MSG_PRIVATE_MSG_SPEAK_RSP = 1616
MSG_PROMOTE_DEVELOP_LV_REQ = 100807
MSG_PROMOTE_DEVELOP_LV_RSP = 100808
MSG_PROP_CREATEENTITY_NTF = 490
MSG_PROP_DESTROYENTITY_NTF = 491
MSG_PROP_PERSONOFFLINE_NTF = 489
MSG_PROP_UPDATE_NTF = 493
MSG_PROP_UPDATE_TOPICS_NTF = 498
MSG_PROP_UPDATE_TOPIC_NTF = 496
MSG_QIDLE_BUY_SWEEP_TIMES_REQ = 100930
MSG_QIDLE_BUY_SWEEP_TIMES_RSP = 100931
MSG_QIDLE_DATA_INFORM_NTF = 100934
MSG_QIDLE_SWEEP_TIMES_REQ = 100932
MSG_QIDLE_SWEEP_TIMES_RSP = 100933
MSG_RANK_ACHV_REACH_NTF = 102709
MSG_RANK_ACHV_TOP5_NTF = 102708
MSG_RANK_ACHV_TOP5_REQ = 102707
MSG_RANK_DETAIL_NTF = 102706
MSG_RANK_DETAIL_PALINFO_NTF = 102712
MSG_RANK_DETAIL_PALINFO_REQ = 102710
MSG_RANK_DETAIL_PALINFO_RSP = 102711
MSG_RANK_DETAIL_REQ = 102704
MSG_RANK_DETAIL_RSP = 102705
MSG_RANK_MAX = 102800
MSG_RANK_TOP_NTF = 102703
MSG_RANK_TOP_REQ = 102701
MSG_RANK_TOP_RSP = 102702
MSG_REAL_NAME_VERIFIED_REQ = 102200
MSG_REBIRTHSPACE_BAN_LIST_REQ = 105773
MSG_REBIRTHSPACE_BAN_LIST_RSP = 105774
MSG_REBIRTHSPACE_BATTLE_RECORD_REQ = 105754
MSG_REBIRTHSPACE_BATTLE_RECORD_RSP = 105755
MSG_REBIRTHSPACE_BATTLE_RESULT_NTF = 105771
MSG_REBIRTHSPACE_BUY_CHALLENGE_COUNT_REQ = 105760
MSG_REBIRTHSPACE_BUY_CHALLENGE_COUNT_RSP = 105761
MSG_REBIRTHSPACE_DATA_UPDATE_REQ = 105756
MSG_REBIRTHSPACE_DATA_UPDATE_RSP = 105757
MSG_REBIRTHSPACE_GET_TREASURE_HISTORY_REQ = 105781
MSG_REBIRTHSPACE_GET_TREASURE_HISTORY_RSP = 105782
MSG_REBIRTHSPACE_GET_TREASURE_REQ = 105777
MSG_REBIRTHSPACE_GET_TREASURE_RSP = 105778
MSG_REBIRTHSPACE_IS_OPEN_ENTRY_NTF = 105772
MSG_REBIRTHSPACE_MAX = 105800
MSG_REBIRTHSPACE_MY_TREASURE_HISTORY_REQ = 105779
MSG_REBIRTHSPACE_MY_TREASURE_HISTORY_RSP = 105780
MSG_REBIRTHSPACE_PALACE_REQ = 105775
MSG_REBIRTHSPACE_PALACE_RSP = 105776
MSG_REBIRTHSPACE_PLAYER_BATTLE_ID_REQ = 105766
MSG_REBIRTHSPACE_PLAYER_BATTLE_ID_RSP = 105767
MSG_REBIRTHSPACE_RANK_DATA_LENGTH_REQ = 105764
MSG_REBIRTHSPACE_RANK_DATA_LENGTH_RSP = 105765
MSG_REBIRTHSPACE_RANK_REQ = 105758
MSG_REBIRTHSPACE_RANK_RSP = 105759
MSG_REBIRTHSPACE_RECOMMEND_TEAM_REQ = 105762
MSG_REBIRTHSPACE_RECOMMEND_TEAM_RSP = 105763
MSG_REBIRTHSPACE_REPORT_RANK = 105768
MSG_REBIRTHSPACE_START_BATTLE_REQ = 105750
MSG_REBIRTHSPACE_START_BATTLE_RSP = 105751
MSG_REBIRTHSPACE_USE_RECOMMEND_TEAM_REQ = 105769
MSG_REBIRTHSPACE_USE_RECOMMEND_TEAM_RSP = 105770
MSG_RECHARGE_MONTH_CARD_INFO_REQ = 100288
MSG_RECHARGE_MONTH_CARD_INFO_RSP = 100287
MSG_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF = 100286
MSG_RECHARGE_PAYMENT_RESULT_NTF = 100285
MSG_RECHARGE_REQ = 100280
MSG_RECHARGE_RESULT_NTF = 100282
MSG_RECHARGE_RSP = 100281
MSG_RECHARGE_TOKENBUY_REQ = 100289
MSG_RECHARGE_TOKENBUY_RSP = 100290
MSG_REDPACKET_INFO_NTF = 105950
MSG_RESERVATION_EMAIL_BGETREWARD_REQ = 107930
MSG_RESERVATION_EMAIL_BGETREWARD_RSP = 107931
MSG_RESERVATION_EMAIL_BIND_REQ = 107926
MSG_RESERVATION_EMAIL_MAX = 107950
MSG_RESERVATION_EMAIL_SENDREWARD_REQ = 107928
MSG_RESERVATION_EMAIL_SENDREWARD_RSP = 107929
MSG_RESERVATION_EMAIL_UNBIND_REQ = 107927
MSG_RETURNACT_NTF = 104351
MSG_RETURNS_GETREFINDREWARD_REQ = 107821
MSG_RETURNS_GETREFINDREWARD_RSP = 107822
MSG_ROGUE_ENTITY_CREATE_NTF = 494
MSG_ROGUE_ENTITY_DEL_NTF = 495
MSG_ROLEFRAME_OPEN_REQ = 107981
MSG_ROLEFRAME_OPEN_RSP = 107982
MSG_ROLEFRAME_OVERTIME_NTF = 107985
MSG_ROLEFRAME_WEAR_REQ = 107983
MSG_ROLEFRAME_WEAR_RSP = 107984
MSG_ROLE_CUSTOM_TITLE_NTF = 1511
MSG_ROLE_LOGIN_KEY_NTF = 499
MSG_ROLE_NATIONALFLAG_MDF_REQ = 1513
MSG_ROLE_NATIONALFLAG_MDF_RSP = 1514
MSG_ROLE_NATIONALFLAG_STATE_NTF = 1512
MSG_ROLE_RESOURCE_STAT_NTF = 1510
MSG_ROLE_TIME_ZONE_NTF = 102533
MSG_RUN_CMD_NTF = 102522
MSG_RUN_CMD_RSP = 102523
MSG_SAVE_PLAYER_LINEUP_REQ = 100911
MSG_SAVE_PLAYER_LINEUP_RSP = 100912
MSG_SEND_FLOWERS_TO_EACH_OTHER_REQ = 102102
MSG_SEND_FLOWERS_TO_EACH_OTHER_RSP = 102103
MSG_SERVER_TIME_MODIFY_NTF = 101400
MSG_SET_CST_LINEUP_REQ = 104311
MSG_SET_CST_LINEUP_RSP = 104312
MSG_SEVENCHALLENGE_MAX = 104240
MSG_SEVENCHALLENGE_UPDATE_NTF = 104201
MSG_SHARETASK_ATTEND_SHARE_NTF = 100356
MSG_SHARETASK_GET_PRIZE_NTF = 100355
MSG_SHARETASK_GET_PRIZE_REQ = 100353
MSG_SHARETASK_GET_PRIZE_RSP = 100354
MSG_SHARETASK_SHOW_ATTEND_INFO_NTF = 100352
MSG_SHARETASK_SHOW_ATTEND_INFO_REQ = 100350
MSG_SHARETASK_SHOW_ATTEND_INFO_RSP = 100351
MSG_SHOP_BUYGOODS_REQ = 1312
MSG_SHOP_BUYGOODS_RSP = 1313
MSG_SHOP_FRESH_SHOPITEM_REQ = 1314
MSG_SHOP_FRESH_SHOPITEM_RSP = 1316
MSG_SHOP_ITEMLIST_NTF = 1317
MSG_SHOP_ITEMLIST_REQ = 1310
MSG_SHOP_ITEMLIST_RSP = 1311
MSG_SHOP_MAXID_END = 1350
MSG_SKEP_CREATE_NTF = 500
MSG_SKEP_DESTROY_NTF = 501
MSG_SKYDIVE_AIR_NTF = 803
MSG_SKYDIVE_LAND_NTF = 804
MSG_SKYDIVE_MOVE_REQ = 801
MSG_SKYDIVE_MOVE_RSP = 802
MSG_SKYDIVE_START_NTF = 800
MSG_SLAVE_BATTLE_RECORD_REQ = 107924
MSG_SLAVE_BATTLE_RECORD_RSP = 107925
MSG_SLAVE_CATCH_LIST_REQ = 107913
MSG_SLAVE_CATCH_LIST_RSP = 107914
MSG_SLAVE_CLAIME_ONE_AWARD_REQ = 107951
MSG_SLAVE_CLAIME_ONE_AWARD_RSP = 107952
MSG_SLAVE_FIGHT_AGAINST_REPORT_REQ = 107905
MSG_SLAVE_FIGHT_AGAINST_REPORT_RSP = 107906
MSG_SLAVE_GET_BATTLE_LINE_REQ = 107909
MSG_SLAVE_GET_BATTLE_LINE_RSP = 107910
MSG_SLAVE_GET_REAL_AWARD_REQ = 107903
MSG_SLAVE_GET_REAL_AWARD_RSP = 107904
MSG_SLAVE_LIST_INFO_REQ = 107901
MSG_SLAVE_LIST_INFO_RSP = 107902
MSG_SLAVE_MAIL_LIST_REQ = 107915
MSG_SLAVE_MAIL_LIST_RSP = 107916
MSG_SLAVE_MAIL_NTF = 107919
MSG_SLAVE_MAIL_OPER_REQ = 107917
MSG_SLAVE_MAIL_OPER_RSP = 107918
MSG_SLAVE_PERCHASE_ENERGY_REQ = 107920
MSG_SLAVE_PERCHASE_ENERGY_RSP = 107921
MSG_SLAVE_QUERY_TARGET_DETAIL_REQ = 107899
MSG_SLAVE_QUERY_TARGET_DETAIL_RSP = 107900
MSG_SLAVE_RECOMMEND_LIST_CHANGE_REQ = 107955
MSG_SLAVE_RECOMMEND_LIST_CHANGE_RSP = 107956
MSG_SLAVE_RECOMMEND_LIST_REQ = 107953
MSG_SLAVE_RECOMMEND_LIST_RSP = 107954
MSG_SLAVE_RELEASE_FREE_REQ = 107911
MSG_SLAVE_RELEASE_FREE_RSP = 107912
MSG_SLAVE_UPDATE_BATTLE_LINE_REQ = 107907
MSG_SLAVE_UPDATE_BATTLE_LINE_RSP = 107908
MSG_SLAVE_UPDATE_ENERGY_REQ = 107922
MSG_SLAVE_UPDATE_ENERGY_RSP = 107923
MSG_SLG_BASEINFO_REQ = 104603
MSG_SLG_BASEINFO_RSP = 104604
MSG_SLG_CANCEL_MASS_TEAM_REQ = 104827
MSG_SLG_CANCEL_MASS_TEAM_RSP = 104828
MSG_SLG_CANCEL_MOVE_TEAM_REQ = 104837
MSG_SLG_CANCEL_MOVE_TEAM_RSP = 104838
MSG_SLG_COLLECT_MOVE_TEAM_REQ = 104831
MSG_SLG_COLLECT_MOVE_TEAM_RSP = 104832
MSG_SLG_DETECT_MOVE_TEAM_REQ = 104835
MSG_SLG_DETECT_MOVE_TEAM_RSP = 104836
MSG_SLG_EXIT_MASS_TEAM_REQ = 104829
MSG_SLG_EXIT_MASS_TEAM_RSP = 104830
MSG_SLG_JOIN_MASS_TEAM_REQ = 104825
MSG_SLG_JOIN_MASS_TEAM_RSP = 104826
MSG_SLG_LAUNCH_MASS_TEAM_REQ = 104823
MSG_SLG_LAUNCH_MASS_TEAM_RSP = 104824
MSG_SLG_LEAGUEMEMBER_INFO_REQ = 104622
MSG_SLG_LEAGUEMEMBER_INFO_RSP = 104623
MSG_SLG_MAPDETAIL_NTF = 104619
MSG_SLG_MAPDETAIL_REQ = 104601
MSG_SLG_MAPDETAIL_RSP = 104602
MSG_SLG_MASS_KICK_MEMBER_REQ = 104841
MSG_SLG_MASS_KICK_MEMBER_RSP = 104842
MSG_SLG_MAX = 105000
MSG_SLG_MOVECITY_REQ = 104609
MSG_SLG_MOVECITY_RSP = 104610
MSG_SLG_PRODUCE_REQ = 104624
MSG_SLG_PRODUCE_RSP = 104625
MSG_SLG_REFRESHMONSTER_REQ = 104611
MSG_SLG_REFRESHMONSTER_RSP = 104612
MSG_SLG_SCINFO_REQ = 104605
MSG_SLG_SCINFO_RSP = 104606
MSG_SLG_SCUPGRADE_CANCEL_REQ = 104617
MSG_SLG_SCUPGRADE_CANCEL_RSP = 104618
MSG_SLG_SCUPGRADE_REQ = 104607
MSG_SLG_SCUPGRADE_RSP = 104608
MSG_SLG_SEARCH_REQ = 104620
MSG_SLG_SEARCH_RSP = 104621
MSG_SLG_SET_TEAM_REQ = 104819
MSG_SLG_SET_TEAM_RSP = 104820
MSG_SLG_SINGLE_MOVE_TEAM_REQ = 104821
MSG_SLG_SINGLE_MOVE_TEAM_RSP = 104822
MSG_SLG_STATIONED_MOVE_TEAM_REQ = 104833
MSG_SLG_STATIONED_MOVE_TEAM_RSP = 104834
MSG_SLG_TASKINFO_REQ = 104613
MSG_SLG_TASKINFO_RSP = 104614
MSG_SLG_TASKREWARD_REQ = 104615
MSG_SLG_TASKREWARD_RSP = 104616
MSG_SLG_WALK_REQ = 104839
MSG_SLG_WALK_RSP = 104840
MSG_SOUL_LINK_SLOT_COOL_RESET_REQ = 102909
MSG_SOUL_LINK_SLOT_COOL_RESET_RSP = 102910
MSG_SOUL_LINK_SLOT_MAX = 102920
MSG_SPACEEXPLORTION_BASE_DATA_NTF = 105160
MSG_SPACEEXPLORTION_MAX = 105170
MSG_SPACEEXPLORTION_PLANET_DATA_NTF = 105161
MSG_SPACEEXPLORTION_PLANET_UPDATA_DATA_NTF = 105162
MSG_SPACEEXPLORTION_PROP_DATA_NTF = 105163
MSG_SPACE_DOMINATOR_BATTLE_VIDEO_REQ = 101363
MSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP = 101364
MSG_SPACE_DOMINATOR_CHALLENGE_COUNT_NTF = 101360
MSG_SPACE_DOMINATOR_END_GROUP_NTF = 101361
MSG_SPACE_DOMINATOR_GET_RANK_INFO_REQ = 101356
MSG_SPACE_DOMINATOR_GET_RANK_INFO_RSP = 101357
MSG_SPACE_DOMINATOR_GET_RANK_REWARD_REQ = 101354
MSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP = 101355
MSG_SPACE_DOMINATOR_GROUP_ID_NTF = 101362
MSG_SPACE_DOMINATOR_INFO_NTF = 101353
MSG_SPACE_DOMINATOR_SELF_RANK_NTF = 101358
MSG_SPACE_DOMINATOR_USER_SETTLE_NTF = 101359
MSG_STARTEMPLE_ARTIFACT_REQ = 106074
MSG_STARTEMPLE_ARTIFACT_RSP = 106075
MSG_STARTEMPLE_BASE_REQ = 106058
MSG_STARTEMPLE_BASE_RSP = 106059
MSG_STARTEMPLE_BATTLE_HISTORY_REQ = 106065
MSG_STARTEMPLE_BATTLE_HISTORY_RSP = 106066
MSG_STARTEMPLE_BATTLE_RANKINFO_NTF = 106109
MSG_STARTEMPLE_BATTLE_RECORD_REQ = 106099
MSG_STARTEMPLE_BATTLE_RECORD_RSP = 106100
MSG_STARTEMPLE_BATTLE_STATUS = 106069
MSG_STARTEMPLE_BUY_FIGHT_COUNT_REQ = 106057
MSG_STARTEMPLE_BUY_REQ = 106070
MSG_STARTEMPLE_BUY_RSP = 106071
MSG_STARTEMPLE_CAN_BATTLERANK_REQ = 106110
MSG_STARTEMPLE_CAN_BATTLERANK_RSP = 106111
MSG_STARTEMPLE_CHALLENGE_RANK_REQ = 106101
MSG_STARTEMPLE_CHALLENGE_RANK_RSP = 106102
MSG_STARTEMPLE_CHALLENGE_STATUS_REQ = 106091
MSG_STARTEMPLE_CHALLENGE_STATUS_RSP = 106092
MSG_STARTEMPLE_EVERYDAYAWARD_NTF = 106106
MSG_STARTEMPLE_FIGHT_REQ = 106055
MSG_STARTEMPLE_FIGHT_RSP = 106056
MSG_STARTEMPLE_FINISH_GAME_REQ = 106107
MSG_STARTEMPLE_FINISH_GAME_RSP = 106108
MSG_STARTEMPLE_GET_SEASON_REWARD_REQ = 106067
MSG_STARTEMPLE_GET_SEASON_REWARD_RSP = 106068
MSG_STARTEMPLE_HISTORY_RECORD_REQ = 106103
MSG_STARTEMPLE_HISTORY_RECORD_RSP = 106104
MSG_STARTEMPLE_LOOK_ROLEINFO_REQ = 106095
MSG_STARTEMPLE_LOOK_ROLEINFO_RSP = 106096
MSG_STARTEMPLE_MONSTER_LINEUP_REQ = 106072
MSG_STARTEMPLE_MONSTER_LINEUP_RSP = 106073
MSG_STARTEMPLE_NEED_SELECT_HERO_REQ = 106051
MSG_STARTEMPLE_NEED_SELECT_HERO_RSP = 106052
MSG_STARTEMPLE_NEXTSTATUS_REQ = 106076
MSG_STARTEMPLE_NEXTSTATUS_RSP = 106077
MSG_STARTEMPLE_PARTS_DATA_NTF = 106112
MSG_STARTEMPLE_RANKAWARD_NTF = 106105
MSG_STARTEMPLE_RANKAWARD_REQ = 106097
MSG_STARTEMPLE_RANKAWARD_RSP = 106098
MSG_STARTEMPLE_RANK_CHANGE_NTF = 106113
MSG_STARTEMPLE_REFRESH_STORE_REQ = 106062
MSG_STARTEMPLE_SALE_REQ = 106063
MSG_STARTEMPLE_SALE_RSP = 106064
MSG_STARTEMPLE_SELECT_HERO_REQ = 106053
MSG_STARTEMPLE_SELECT_HERO_RSP = 106054
MSG_STARTEMPLE_STORE_REQ = 106060
MSG_STARTEMPLE_STORE_RSP = 106061
MSG_STARTEMPLE_TASKAWARD_REQ = 106078
MSG_STARTEMPLE_TASKAWARD_RSP = 106079
MSG_STARTEMPLE_ZONE_RANK_REQ = 106093
MSG_STARTEMPLE_ZONE_RANK_RSP = 106094
MSG_SWITCH_LANGUAGE_NTF = 102531
MSG_SYNC_BATTLE_RECORD_NTF = 1216
MSG_TASK_SUBSEQUENT_REWARD_GET_REWARD_REQ = 102501
MSG_TASK_SUBSEQUENT_REWARD_GET_REWARD_RSP = 102502
MSG_TASK_SUBSEQUENT_REWARD_MAX = 102510
MSG_TASK_SUBSEQUENT_REWARD_STAT_UPDATA_NTF = 102500
MSG_TASK_SUBSEQUENT_REWARD_UPDATE_UI_REQ = 102503
MSG_TAVERN_ADD_REQ = 101036
MSG_TAVERN_ADD_RSP = 101037
MSG_TAVERN_DATA_REQ = 101038
MSG_TAVERN_DATA_RSP = 101039
MSG_TAVERN_REFRESH_REQ = 101034
MSG_TAVERN_REFRESH_RSP = 101035
MSG_TAVERN_TASK_LOCK_REQ = 101030
MSG_TAVERN_TASK_LOCK_RSP = 101031
MSG_TAVERN_TASK_RESET_REQ = 101024
MSG_TAVERN_TASK_RESET_RSP = 101025
MSG_TAVERN_TASK_RWARD_REQ = 101026
MSG_TAVERN_TASK_RWARD_RSP = 101027
MSG_TAVERN_TASK_SPEEDUP_REQ = 101028
MSG_TAVERN_TASK_SPEEDUP_RSP = 101029
MSG_TAVERN_TASK_START_REQ = 101022
MSG_TAVERN_TASK_START_RSP = 101023
MSG_TAVERN_TASK_UNLOCK_REQ = 101032
MSG_TAVERN_TASK_UNLOCK_RSP = 101033
MSG_TAVERN_TASK_UPDATE_NTF = 101020
MSG_TEAM_ADD_PLAYER_NTF = 752
MSG_TEAM_CREATE_NTF = 750
MSG_TEAM_MARK_NTF = 754
MSG_TEAM_MODIFY_INFO_REQ = 753
MSG_TEAM_UPDATE_NTF = 751
MSG_TECH_RESET_REQ = 101412
MSG_TECH_RESET_RSP = 101413
MSG_TECH_UPGRADE_REQ = 101410
MSG_TECH_UPGRADE_RSP = 101411
MSG_TITLE_MAX = 104400
MSG_TITLE_SELECTPROP_REQ = 104373
MSG_TITLE_SELECTPROP_RSP = 104374
MSG_TITLE_SELECT_REQ = 104370
MSG_TITLE_SELECT_RSP = 104371
MSG_TOPRACE_18PAL_LIST_REQ = 104517
MSG_TOPRACE_18PAL_LIST_RSP = 104518
MSG_TOPRACE_BASE_DATA_NTF = 104523
MSG_TOPRACE_BASE_DATA_REQ = 104501
MSG_TOPRACE_BASE_DATA_RSP = 104502
MSG_TOPRACE_BATTLEVIDEO_REQ = 104503
MSG_TOPRACE_BATTLEVIDEO_RSP = 104504
MSG_TOPRACE_CHAMPION_APPLAUD_REQ = 104521
MSG_TOPRACE_CHAMPION_APPLAUD_RSP = 104522
MSG_TOPRACE_GETRANK_REWARD_REQ = 104511
MSG_TOPRACE_GETRANK_REWARD_RSP = 104512
MSG_TOPRACE_GETTOPLIST_REQ = 104513
MSG_TOPRACE_GETTOPLIST_RSP = 104514
MSG_TOPRACE_GROUPSTATE_REQ = 104515
MSG_TOPRACE_GROUPSTATE_RSP = 104516
MSG_TOPRACE_GUESSPUSH_REQ = 104507
MSG_TOPRACE_GUESSPUSH_RSP = 104508
MSG_TOPRACE_GUESS_REQ = 104509
MSG_TOPRACE_GUESS_RSP = 104510
MSG_TOPRACE_MAX = 104550
MSG_TOPRACE_SETDEFENSELINEUP_REQ = 104505
MSG_TOPRACE_SETDEFENSELINEUP_RSP = 104506
MSG_TOPRACE_VOTE_NDAY_AGTABLE_REQ = 104519
MSG_TOPRACE_VOTE_NDAY_AGTABLE_RSP = 104520
MSG_TO_THE_BOSS_TO_FIGHT_REQ = 102108
MSG_TO_THE_BOSS_TO_FIGHT_RSP = 102109
MSG_TREASURERARE_ADVANCE_REQ = 105802
MSG_TREASURERARE_ADVANCE_RSP = 105803
MSG_TREASURERARE_COMMON_GIFT_NTF = 105816
MSG_TREASURERARE_COMMON_GIFT_REQ = 105814
MSG_TREASURERARE_COMMON_GIFT_RSP = 105815
MSG_TREASURERARE_ENHANCE_REQ = 105804
MSG_TREASURERARE_ENHANCE_RSP = 105805
MSG_TREASURERARE_GUILD_REQ = 105812
MSG_TREASURERARE_GUILD_RSP = 105813
MSG_TREASURERARE_HAMMER_REQ = 105806
MSG_TREASURERARE_HAMMER_RSP = 105807
MSG_TREASURERARE_NEW_TREASURERARE_NTF = 105823
MSG_TREASURERARE_OPENACTIVITY_NTF = 105819
MSG_TREASURERARE_OPENACTIVITY_REQ = 105820
MSG_TREASURERARE_OPENACTIVITY_RSP = 105821
MSG_TREASURERARE_PACKET_CONVERT_NTF = 105833
MSG_TREASURERARE_PRIVILEGE_REDUCETIME_NTF = 105832
MSG_TREASURERARE_RECYCLE_MAKER_DATA_NTF = 105811
MSG_TREASURERARE_RECYCLE_MAX = 105835
MSG_TREASURERARE_RECYCLE_TREASURERARE_NTF = 105831
MSG_TREASURERARE_REFRESH_DATA_NTF = 105801
MSG_TREASURERARE_REWARD_REQ = 105808
MSG_TREASURERARE_REWARD_RSP = 105809
MSG_TREASURERARE_SPECIAL_PROP_NTF = 105822
MSG_TREASURERARE_SPECIAL_PROP_REQ = 105817
MSG_TREASURERARE_SPECIAL_PROP_RSP = 105818
MSG_TREASURERARE_UNLOCK_MAKER_DATA_NTF = 105810
MSG_TREASURE_HUNT_REQ = 102104
MSG_TREASURE_HUNT_RSP = 102105
MSG_TREASURE_SEND_FLOWERS_REQ = 102100
MSG_TREASURE_SEND_FLOWERS_RSP = 102101
MSG_TREASURE_STATE_OR_PHYSICAL_UPDATE_NTF = 102111
MSG_TRIALHERO_GET_REQ = 107804
MSG_TRIALHERO_GET_RSP = 107805
MSG_TRIALHERO_MAX = 107820
MSG_TRIALHERO_UPDATE_NTF = 107806
MSG_TRIAL_LOCK_TEAM_REQ = 101070
MSG_TRIAL_LOCK_TEAM_RSP = 101071
MSG_TRIAL_MAX = 101100
MSG_TRIAL_MOVE_REQ = 101062
MSG_TRIAL_MOVE_RSP = 101063
MSG_TRIAL_NEXTLEVEL_REQ = 101067
MSG_TRIAL_NEXTLEVEL_RSP = 101068
MSG_TRIAL_PLAYERBTLEVENT_UPDATE_NTF = 101069
MSG_TRIAL_SETTEAM_REQ = 101060
MSG_TRIAL_SETTEAM_RSP = 101061
MSG_TRIAL_TASKEVENT_REQ = 101064
MSG_TRIAL_TASKEVENT_RSP = 101065
MSG_TRIAL_UPDATE_NTF = 101066
MSG_UNLOCAK_THE_SOUL_LINK_SLOT_REQ = 102900
MSG_UNLOCAK_THE_SOUL_LINK_SLOT_RSP = 102901
MSG_UPDATE_PLAYER_TREASURE_NTF = 102110
MSG_UPDATE_SOUL_LINK_MOUDLE_NTF = 102908
MSG_USER_UPDATE_NAME_RSP = 1633
MSG_VIP_ENTER_DIAMONDSHOP_REQ = 1701
MSG_VIP_ENTER_DIAMONDSHOP_RSP = 1702
MSG_VIP_GET_GOLDEN_HAND_REWARD_REQ = 1705
MSG_VIP_GET_GOLDEN_HAND_REWARD_RSP = 1706
MSG_VIP_GOLDEN_HAND_REQ = 1703
MSG_VIP_GOLDEN_HAND_RSP = 1704
MSG_VIP_MAX = 1720
MSG_VOIDARENA_DATA_REQ = 104401
MSG_VOIDARENA_DATA_RSP = 104402
MSG_VOIDARENA_LOST_DEL_REQ = 104405
MSG_VOIDARENA_LOST_DEL_RSP = 104406
MSG_VOIDARENA_LOST_NTF = 104404
MSG_VOIDARENA_MAX = 104500
MSG_VOIDARENA_UPDATE_NTF = 104403
MSG_WARE_USE_ITEM_REQ = 100050
MSG_WARE_USE_ITEM_RSP = 100051
MSG_WEAPON_ACTIVATE_REQ = 1773
MSG_WEAPON_ACTIVATE_RSP = 1774
MSG_WEAPON_CHOOSE_REQ = 1779
MSG_WEAPON_CHOOSE_RSP = 1780
MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_REQ = 106221
MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_RSP = 106222
MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_UNLOCK_REQ = 106219
MSG_WEAPON_DIAMOND_BATCHSYNTHESIS_UNLOCK_RSP = 106220
MSG_WEAPON_DIAMOND_COMPOSITE_REQ = 106209
MSG_WEAPON_DIAMOND_COMPOSITE_RSP = 106210
MSG_WEAPON_DIAMOND_COMPOSITE_TAKEOFF_REQ = 106211
MSG_WEAPON_DIAMOND_COMPOSITE_TAKEOFF_RSP = 106212
MSG_WEAPON_DIAMOND_EXCHANGE_REQ = 106215
MSG_WEAPON_DIAMOND_EXCHANGE_RSP = 106216
MSG_WEAPON_DIAMOND_GUIDE_REQ = 106217
MSG_WEAPON_DIAMOND_GUIDE_RSP = 106218
MSG_WEAPON_DIAMOND_OPEATE_RSP = 106204
MSG_WEAPON_DIAMOND_OPERATE_REQ = 106203
MSG_WEAPON_DIAMOND_QUICK_MOUNT_REQ = 106205
MSG_WEAPON_DIAMOND_QUICK_MOUNT_RSP = 106206
MSG_WEAPON_DIAMOND_QUICK_TAKEOFF_REQ = 106213
MSG_WEAPON_DIAMOND_QUICK_TAKEOFF_RSP = 106214
MSG_WEAPON_DIAMOND_SKILL_CHOOSE_REQ = 106207
MSG_WEAPON_DIAMOND_SKILL_CHOOSE_RSP = 106208
MSG_WEAPON_DIAMOND_UPGRADE_REQ = 106201
MSG_WEAPON_DIAMOND_UPGRADE_RSP = 106202
MSG_WEAPON_DISMANTLE_REQ = 1777
MSG_WEAPON_DISMANTLE_RSP = 1778
MSG_WEAPON_GET_CHOOSE_INFO_REQ = 1782
MSG_WEAPON_GET_CHOOSE_INFO_RSP = 1783
MSG_WEAPON_PART_UNLOCK_NTF = 1781
MSG_WEAPON_UNLOCK_NTF = 1772
MSG_WEAPON_UPGRADE_REQ = 1775
MSG_WEAPON_UPGRADE_RSP = 1776
MSG_WEEKEND_ARENA_BATTLE_VIDEO_REQ = 1960
MSG_WEEKEND_ARENA_BATTLE_VIDEO_RSP = 1961
MSG_WEEKEND_ARENA_CHALLENGE_NUM_NTF = 1952
MSG_WEEKEND_ARENA_CHALLENGE_RIVAL_REQ = 1968
MSG_WEEKEND_ARENA_CHALLENGE_RIVAL_RSP = 1969
MSG_WEEKEND_ARENA_ENTER_BATTLE_REQ = 1958
MSG_WEEKEND_ARENA_ENTER_BATTLE_RSP = 1959
MSG_WEEKEND_ARENA_GET_LINEUP_REQ = 1962
MSG_WEEKEND_ARENA_GET_LINEUP_RSP = 1963
MSG_WEEKEND_ARENA_GET_VICTORY_REWARD_REQ = 1972
MSG_WEEKEND_ARENA_GET_VICTORY_REWARD_RSP = 1973
MSG_WEEKEND_ARENA_LIKE_INFO_NTF = 1967
MSG_WEEKEND_ARENA_LIKE_NUM_NTF = 1953
MSG_WEEKEND_ARENA_LIKE_REQ = 1965
MSG_WEEKEND_ARENA_LIKE_RSP = 1966
MSG_WEEKEND_ARENA_NO1_REQ = 1954
MSG_WEEKEND_ARENA_NO1_RSP = 1955
MSG_WEEKEND_ARENA_QUALIFICATE_REQ = 1974
MSG_WEEKEND_ARENA_QUALIFICATE_RSP = 1975
MSG_WEEKEND_ARENA_RANK_CHANGE_NTF = 1964
MSG_WEEKEND_ARENA_RANK_INFO_REQ = 1950
MSG_WEEKEND_ARENA_RANK_INFO_RSP = 1951
MSG_WEEKEND_ARENA_SET_LINEUP_REQ = 1956
MSG_WEEKEND_ARENA_SET_LINEUP_RSP = 1957
MSG_WEEKEND_BATTLE_RECORD_REQ = 1970
MSG_WEEKEND_BATTLE_RECORD_RSP = 1971
MSG_WORDBOSS_DMGRECORD_NTF = 107965
MSG_WORDBOSS_INFO_NTF = 107962
MSG_WORDBOSS_INFO_REQ = 107961
MSG_WORDBOSS_LINE_INFO_REQ = 107963
MSG_WORDBOSS_LINE_INFO_RSP = 107964
MSG_WORDBOSS_MAX = 107970
MSG_XYX_ADVRW_REQ = 105121
MSG_XYX_ADVRW_RSP = 105122
MSG_XYX_DATA_NTF = 105134
MSG_XYX_ENDLESS_SET_HEROPOWER_REQ = 105144
MSG_XYX_ENDLESS_SET_HEROPOWER_RSP = 105145
MSG_XYX_GET_DATA_REQ = 105141
MSG_XYX_GET_PROP_REQ = 105129
MSG_XYX_GET_PROP_RSP = 105130
MSG_XYX_GOT_DELAYAWARD_REQ = 105135
MSG_XYX_GOT_DELAYAWARD_RSP = 105136
MSG_XYX_GOT_GAME_REWARD_REQ = 105142
MSG_XYX_GOT_GAME_REWARD_RSP = 105143
MSG_XYX_INITGAMEDATA_REQ = 105139
MSG_XYX_INITGAMEDATA_RSP = 105140
MSG_XYX_ITEMACTIVE_REQ = 105119
MSG_XYX_ITEMACTIVE_RSP = 105120
MSG_XYX_LEVEL_DATA_NTF = 105118
MSG_XYX_LEVEL_REQ = 105125
MSG_XYX_LEVEL_RSP = 105126
MSG_XYX_LINK_DATA_NTF = 105115
MSG_XYX_PASS_LV_REQ = 105111
MSG_XYX_PASS_LV_RSP = 105112
MSG_XYX_PROP_NTF = 105131
MSG_XYX_SET_FAILSTAGE_REQ = 105137
MSG_XYX_SET_FAILSTAGE_RSP = 105138
MSG_XYX_SET_LEVEL_REQ = 105116
MSG_XYX_SET_LEVEL_RSP = 105117
MSG_XYX_SET_LINK_REQ = 105113
MSG_XYX_SET_LINK_RSP = 105114
MSG_XYX_SET_PROP_REQ = 105127
MSG_XYX_SET_PROP_RSP = 105128
MSG_XYX_STAGE_REWARD_MULTI_REQ = 105132
MSG_XYX_STAGE_REWARD_MULTI_RSP = 105133
MSG_XYX_STAGE_REWARD_REQ = 105123
MSG_XYX_STAGE_REWARD_RSP = 105124
MSG_ZONE_COUNTER_NTF = 540
MSG_ZONE_NEW_BATTLE_CLIENT_REQ = 537
MSG_ZONE_NEW_ROLEINFO_REQ = 1522
MSG_ZONE_NEW_ROLEINFO_RSP = 1523
MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ = 1534
MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP = 1535
MSG_ZONE_ROLE_CITY_UPDATE_REQ = 1517
MSG_ZONE_ROLE_CITY_UPDATE_RSP = 1518
MSG_ZONE_ROLE_FACE_MAX = 1600
MSG_ZONE_ROLE_FACE_PROP_UNLOAD_REQ = 1508
MSG_ZONE_ROLE_FACE_PROP_UNLOAD_RSP = 1509
MSG_ZONE_ROLE_FACE_PROP_UPDATE_NTF = 1504
MSG_ZONE_ROLE_FACE_PROP_UPDATE_REQ = 1502
MSG_ZONE_ROLE_FACE_PROP_UPDATE_RSP = 1503
MSG_ZONE_ROLE_FACE_UPDATE_REQ = 1500
MSG_ZONE_ROLE_FACE_UPDATE_RSP = 1501
MSG_ZONE_ROLE_NEW_FACE_NTF = 1505
MSG_ZONE_ROLE_NEW_SCHLOSS_EFFECT_NTF = 1536
MSG_ZONE_ROLE_NEW_SCHLOSS_NTF = 1521
MSG_ZONE_ROLE_PLATE_NTF = 1547
MSG_ZONE_ROLE_PLATE_UPDATE_REQ = 1545
MSG_ZONE_ROLE_PLATE_UPDATE_RSP = 1546
MSG_ZONE_ROLE_PRAISE_UPDATE_REQ = 1519
MSG_ZONE_ROLE_PRAISE_UPDATE_RSP = 1520
MSG_ZONE_ROLE_UPDATE_NAME_REQ = 1506
MSG_ZONE_ROLE_UPDATE_NAME_RSP = 1507
MSG_ZONE_ROLE_UPDATE_SEX_REQ = 1515
MSG_ZONE_ROLE_UPDATE_SEX_RSP = 1516
MSG_ZONE_SERVER_EXIT_REQ = 541
MSG_ZONE_SERVER_EXIT_RSP = 542
MSG_ZONE_SERVER_TIMESTAMP_NTF = 497
MSG_ZONE_VOICESETTING_NTF = 549
StorySystemData = 100929
TMSG_HALO_MAX = 1750

