local require = require

local table = table
local pairs = pairs
local ipairs = ipairs
local type = type
local string = string
local math = math

local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local gw_task_data = require "gw_task_data"
local data_mgr = require "data_mgr"
local event = require "event"
local red_system = require "red_system"
local red_const = require "red_const"
local event_task_define = require "event_task_define"
local gw_event_activity_define = require "gw_event_activity_define"
local gw_task_const = require "gw_task_const"

module("halloween_sign_in_mgr")

---所有数据存储
local _d = data_mgr:CreateData("halloween_sign_in_mgr")
---非服务器数据存储
local mc = _d.mde.const

function Init()
    event.Register(event.USER_DATA_RESET, Clear)
    
    event.Register(event_task_define.REFRESH_TASK, TaskPointUpdate) --任务刷新,红点注册
    red_system.RegisterRedFunc(red_const.Enum.HalloweenSignIn, TaskCanReceiveRewardRed)
    red_system.RegisterRedFunc(red_const.Enum.HalloweenSignInReward, GetCurTaskReceiveRewardRed)
end

---@public function 设置活动ID
function SetActivityID(activityID)
    mc.activityID = activityID
end

---@public function 获取活动ID
function GetActivityID()
    return mc.activityID
end


function GetActivityTaskList()
    local festivalCfgData = festival_activity_mgr.GetActivityCfgByActivityID(GetActivityID())
    local activityData = festival_activity_mgr.GetActivityDataByActivityID(GetActivityID())
    local taskListFromZero = festivalCfgData.ctnID1[1]
    local taskList = {}
    for i = 0, #taskListFromZero do
        table.insert(taskList, taskListFromZero[i])
    end
    return taskList
end



--- 通过TaskId 检查是否能领奖
function TaskIdCheckReqTask(taskId)
    local taskData = GetTaskMainData(taskId)
    local canReq = CheckReqTask(taskData)
    return canReq
end


-- 获取任务总表的数据
function GetTaskMainData(taskId)
    local taskData = game_scheme:TaskMain_0(taskId)
    return taskData
end

--获取任务数据的图标
function GetTaskMainDataIcon(taskId)
    local taskData = GetTaskMainData(taskId)
    return taskData.Icon.data[taskData.Icon.count - 1]
end


---检查是否能领取宝箱奖励
function CheckReqTask(taskData)
    local len = taskData.ConditionValue2.count
    local data = taskData.ConditionValue2.data
    if data == nil then
        return false
    end
    
    local result = 0
    for i = 0, len do
        local taskData = gw_task_data.GetTaskData(data[i])
        if taskData ~= nil then
            if taskData.rate >= taskData.completeValue then
                result = result + 1
            end
        end
    end
    return result == taskData.ConditionValue2.count
end

---检查宝箱是否已经给领取
function CheckBoxReqState(task)
    local taskData =  gw_task_data.GetTaskData(task)
    if taskData == nil then
        return false
    end
    
    return taskData.status
end


--- 某一个宝箱可以领取时红点
function GetCurTaskReceiveRewardRed(taskId)
    local canReceive = CheckBoxReqState(taskId)
    if canReceive == true then --已经给领取了没有红点
        return 0 
    end
    local gw_task_mgr = require "gw_task_mgr"
    local result =  gw_task_mgr.GetTaskIsReceive(taskId)
    if result == true then
        return 1
    end
    return 0
end

---@public function 任务刷新，更新红点
function TaskPointUpdate(_, _,moduleId, moduleList)
    local gw_task_const = require "gw_task_const"
    if moduleList[gw_task_const.TaskModuleType.HalloweenSignIn] then
        red_system.TriggerRed(red_const.Enum.HalloweenSignInReward)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,GetActivityID())
    end
end

---@public function 获取红点数量
function TaskCanReceiveRewardRed()
    local festivalData = festival_activity_mgr.GetActivityCfgByActivityID(GetActivityID())
    local receiveCount = 0
    if festivalData then
        local gw_task_mgr = require "gw_task_mgr"
        for i = 0, festivalData.ctnID1.count - 1 do
            local result =  gw_task_mgr.GetTaskIsReceive(festivalData.ctnID1.data[i])
            if result then
                receiveCount = receiveCount + 1
            end
        end
    end
    return receiveCount
end

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end