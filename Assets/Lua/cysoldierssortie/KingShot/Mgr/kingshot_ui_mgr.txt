---@class kingshot_ui_mgr : fusion_mgrbase
---@field lifeScope kingshot_lifescope
local mgr = bc_Class("kingshot_ui_mgr", Fusion.MgrBase)
---@type kingshot_res_mgr
mgr.resMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type kingshot_scene_mgr
mgr.sceneMgr = nil
---@type fusion_lang_mgr
mgr.langMgr = nil

mgr.DataSrc = nil
mgr.uiCamera = nil
---@type table 计算后的真实画布分辨率
mgr.RealCanvaSize = nil
---@type boolean
mgr.readyFlag = nil
mgr.touchStartFlag = nil
---@type fusion_delegate
mgr.DragEvent = nil

mgr.sliderDataWithBossSlider = nil
mgr.delayedHpSliderControllers = nil  -- 延迟血条控制器映射表
mgr.attackLockIcons = nil  -- 攻击锁定图标映射表
---@type boolean 预警是否显示过
mgr.attentFlag = nil

mgr.tween_Tips = nil
mgr.tween_Tut = nil
mgr.tween_Task = nil
mgr.tween_BossAttent = nil

mgr._check_interval = 0.05
mgr.AlertEffect = nil
mgr.FullScreenEffect = nil
---@type kingshot_uibossguide_ctrl
mgr.bossGuideCtrl = nil

local isShowWarningEffect = nil
local isShowFullScreenEffect = nil

local EventTrigger_Entry = CS.UnityEngine.EventSystems.EventTrigger.Entry
local EventTriggerType = CS.UnityEngine.EventSystems.EventTriggerType
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_ui_hud_anim_config = require("cysoldierssortie_ui_hud_anim_config")
local taskFormat = "%d/%d"
local event = event
local timerFormat = " %02d:%02d"
local isAsync = isAsync
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName

function mgr:__init()
    self.DragEvent = require("fusion_delegate").New()
end

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.resMgr = self.lifeScope:GetMgr("kingshot_res_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.sceneMgr = self.lifeScope:GetMgr("kingshot_scene_mgr")
    self.langMgr = self.lifeScope:GetMgr("fusion_lang_mgr")
end

function mgr:Ready()
    self.readyFlag = false
    local mainPanel = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.MainPanelPrefab)
    mainPanel.transform:SetParent(self.mono.globalMonoGO.transform)
    mainPanel.transform.localPosition = KingShot_Define.CacheVector3.Zero
    mainPanel.transform.localScale = KingShot_Define.CacheVector3.One
    self.DataSrc = {}
    local neeRefer = mainPanel:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)

    -- 初始化攻击锁定图标池和映射表
    local goPoolCls = require("fusion_gopool")
    self.attackLockPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.AttackLock, nil)
    self.attackLockIcons = {}

    -- 初始化延迟血条池（只有DelayedHpSlider使用新的fusion_gopool）
    self.delayedHpSliderPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.DelayedHpSlider, nil)

    self.uiCamera = self.DataSrc.UICamera
    if cysoldierssortie_KingShot_HOME then
        --self.uiCamera.gameObject:SetActive(false)
    else
        local util = require "util"
        util.SetLayer(self.sceneMgr.DataSrc.WorldCanvas, 0)
    end
    
    local tempRealSize = self.DataSrc.CanvasScaler.referenceResolution
    local safeArea = self.lifeScope.SafeArea
    local realScale = safeArea.ScreenWidth / safeArea.ScreenHeight
    local standardScale = tempRealSize.x / tempRealSize.y
    -- 更宽
    if realScale > standardScale then
        tempRealSize.x = tempRealSize.x * (realScale / standardScale)
    elseif realScale < standardScale then
        tempRealSize.y = tempRealSize.y * (standardScale / realScale)
    end
    self.RealCanvaSize = KingShot_Define.CS.Vector2(math.round(tempRealSize.x), math.round(tempRealSize.y))
    self.ScreenSize = KingShot_Define.CS.Vector2(safeArea.ScreenWidth, safeArea.ScreenHeight)
    self.TopOffY = safeArea.TopRatio * self.RealCanvaSize.y
    local topAnchorY = -50
    if safeArea.TopRatio > 0 then
        topAnchorY = -50 - self.TopOffY
    end
    self.DataSrc.TopRect.anchoredPosition = { x = 0, y = topAnchorY }

    local trigger = EventTrigger_Entry()
    trigger.eventID = EventTriggerType.PointerDown
    trigger.callback:AddListener(function(eventData)
        self:OnPointerDown(eventData)
    end)
    local trigger2 = EventTrigger_Entry()
    trigger2.eventID = EventTriggerType.PointerUp
    trigger2.callback:AddListener(function(eventData)
        self:OnPointerUp(eventData)
    end)
    local trigger3 = EventTrigger_Entry()
    trigger3.eventID = EventTriggerType.Drag
    trigger3.callback:AddListener(function(eventData)
        self:OnDrag(eventData)
    end)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger2)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger3)
    self.joySize = self.DataSrc.Joystick.sizeDelta * 0.5
    self.joyBoundOff = self.joySize.x + 10

    self.sceneMgr.BattleFlagBind:Register(function(value)
        self:BattleFlagListener(value)
    end)

    self.sliderDataWithBossSlider = {}
    self.delayedHpSliderControllers = {}  -- 初始化延迟血条控制器映射表
    -- 攻击锁定图标映射表已在Ready()中初始化
    self.DataSrc.TextLv.text = string.format(self.langMgr:GetLangById(1), KingShot_Define.minigame_mgr.GetHookLevelId())
    self.DataSrc.TextTaskTitle.text = self.langMgr:GetLangById(4)
    self.DataSrc.TextBossAttent.text = self.langMgr:GetLangById(3)
    self.DataSrc.TextTut.text = self.langMgr:GetLangById(2)

    self.sceneMgr.TaskNumBind:Register(function(value)
        --self.DataSrc.TextProcess.text = string.format(taskFormat, value, self.sceneMgr.TaskNumMax)
        --self.DataSrc.ProcessSlider.value = value / self.sceneMgr.TaskNumMax
        value = math.max(value,0)
        self.DataSrc.TargetText.text = string.format(taskFormat, value, self.sceneMgr.TaskNumMax)
    end)

    self.sceneMgr.CoinNumBind:Register(function(value)
        value = math.max(value,0)
        self.DataSrc.CoinText.text = value
    end)

    self.DataSrc.TipsRect.gameObject:SetActive(false)

    self.sceneMgr.enemyBossNumBind:Register(function(value)
        self:BossNumListener(value)
    end)

    self.sceneMgr.bossTimerBind:Register(function(value)
        self:TimerListener(value)
    end, true)
end

function mgr:GameStart()
    self.touchStartFlag = false
    self.readyFlag = true
    self:ShowJoy(false)
    self:ShowCtrlPanel(false)
    self:ShowInfoRoot(false)
    self:ShowTut(true)
    self:ShowTask(false)
    self:ShowBossAttent(false)
    self:ShowBossTask()
    self.attentFlag = false

    -- 清理所有攻击锁定图标
    if self.attackLockIcons and self.attackLockPool then
        for targetGo, iconGo in pairs(self.attackLockIcons) do
            if iconGo then
                self.attackLockPool:PushOne(iconGo)
            end
        end
        self.attackLockIcons = {}
    end
    local taskDetailId = self.resMgr.CurLvConfig.PassType == KingShot_Define.LevelPassType.AllEnemy and 5 or 6
    self.DataSrc.TextTaskDetail.text = string.format(self.langMgr:GetLangById(taskDetailId), self.sceneMgr.TaskNumMax)

    self:ShowUIModule("kingshot_ui_warning_panel")

    --self.bossGuideCtrl = require("kingshot_uibossguide_ctrl").New(self)
end

function mgr:BattleFlagListener(value)
    if value then
        self:ShowCtrlPanel(true)
        self:ShowInfoRoot(true)
        self:ShowTut(true)
        self:ShowTask(true)
        --根据怪物基地数量增加提示
        --for i = 1, #self.sceneMgr.enemyBaseDatasByPos do
        --    self:AddWarningItem("enemy",self.sceneMgr.enemyBaseDatasByPos[i].Pos,self.sceneMgr.enemyBaseDatasByPos[i].TeamID)
        --end
        --根据怪物路径数量增加提示
        for i = 1, #self.sceneMgr.enemyPathArr do
            self:AddWarningItem("enemy",self.sceneMgr.enemyPathArr[i].Pos,self.sceneMgr.enemyPathArr[i].PathName)
        end
        --显示基地的提示
        if self.sceneMgr.homePos ~= nil then
            self:AddWarningItem("home",self.sceneMgr.homePos,"home")
        end

    end
end

--显示boss的数量 头像
function mgr:ShowBossTask()
    self.processTextFormat = self.langMgr:GetLangById(9)
    --BOSS数量大于1
    if self.sceneMgr.BossDatasByTime and #self.sceneMgr.BossDatasByTime > 0 then
        self.DataSrc.TextLv.gameObject:SetActive(false)
        self.bossLogoArray = {}
        local prefab = self.DataSrc.BossLogoPrefab
        local tmpCls = require("kingshot_uibosslogo")
        self.bossLogoArray[1] = tmpCls.New(prefab)
        self.bossLogoArray[1]:Init()
        if #self.sceneMgr.BossDatasByTime > 1 then
            for i = 2, #self.sceneMgr.BossDatasByTime do
                local newObj = KingShot_Define.CS.GameObject.Instantiate(prefab)
                newObj.transform:SetParent(self.DataSrc.BossCon)
                newObj.transform.localPosition = KingShot_Define.CacheVector3.Zero
                newObj.transform.localScale = KingShot_Define.CacheVector3.One
                self.bossLogoArray[i] = tmpCls.New(newObj)
                self.bossLogoArray[i]:Init()
            end
        end
    else
        --隐藏
        self.DataSrc.BossRoot.gameObject:SetActive(false)
        self.DataSrc.TextLv.gameObject:SetActive(true)
    end
end

function mgr:BossNumListener(value)
    if self.sceneMgr.BossDatasByTime and #self.sceneMgr.BossDatasByTime > 0 then
        local reduce = #self.sceneMgr.BossDatasByTime - value
        if reduce > 0 then
            self.bossLogoArray[reduce]:PlayMark()
        end
    end
end


function mgr:TimerListener(value)
    if self.sceneMgr.BossDatasByTime and #self.sceneMgr.BossDatasByTime > 0 and self.sceneMgr.bossTimer then
        local showSlider = value < self.sceneMgr.bossTimer and value >= 0
        self.DataSrc.BossProcess.gameObject:SetActive(showSlider)
        if showSlider then
            local reduce = value
            self.DataSrc.BossTextProcess.text = string.format(self.processTextFormat,
                    string.format(timerFormat, math.floor(reduce / 60), math.ceil(reduce % 60)))
            self.DataSrc.BossProcess.value = value / self.sceneMgr.bossTimer
        end
    else
        self.DataSrc.BossProcess.gameObject:SetActive(false)
    end
end

function mgr:ShowWarningEffect(isShowWaring)
    isShowWarningEffect = isShowWaring
    if isShowWarningEffect then
        if self.AlertEffect then
            self.AlertEffect:SetVisible(false)
            self.AlertEffect:SetVisible(true)
        else
            local effectRoot = KingShot_Define.CS.GameObject.Find("UIRoot/CanvasScreenEffect")
            local gw_sand_effect_const = require "gw_sand_effect_const"
            local gw_sand_effect_param = require "gw_sand_effect_param"
            local gw_effect_object = require "gw_effect_object"

            local newEffectParams = gw_sand_effect_param.NewEffectParam(gw_sand_effect_const.EffectName.Alert, {showTime = 1})
            local newEffectBaseParams = gw_sand_effect_param.NewEffectBaseParam(gw_sand_effect_const.EffectName.Alert, nil)
            self.AlertEffect = gw_effect_object.CM("gw_effect_object"):Init(99999, effectRoot.transform, newEffectParams, newEffectBaseParams, function()
                if self.AlertEffect then
                    self.AlertEffect:SetVisible(isShowWarningEffect)
                end
            end)
        end
    else
        if self.AlertEffect then
            self.AlertEffect:SetVisible(false)
        end
    end
end

function mgr:AddWarningItem(type,pos,id)
    local data = {  
        type = type,
        targetPos = pos,
        id = id
    }
    --local event = require "event"
    local heroList = self.sceneMgr.playerCtrl:GetCHeroList()
    if #heroList~=0 then
        data.character = heroList[1].character
        KingShot_Define.event.Trigger(KingShot_Define.event.WARNING_CREATE, data)
    end
end

function mgr:UpdateWaningView()
    if not self._lastCheck or bc_Time.time - self._lastCheck > self._check_interval then
        self._lastCheck = bc_Time.time
        KingShot_Define.event.Trigger(KingShot_Define.event.WARNING_UPDATE)
    end
end

function mgr:ShowTips(tips)
    if tips then
        self:KillTween_Tips()
        self.DataSrc.TipsRect.gameObject:SetActive(true)
        self.DataSrc.TipsText.text = self.langMgr:GetLangById(tips)
        self.tween_Tips =  KingShot_Define.DOTween.Sequence()
        self.tween_Tips:AppendInterval(1.5)
        self.tween_Tips:OnComplete(function()
            self.DataSrc.TipsRect.gameObject:SetActive(false)
        end)
    end
end

function mgr:ShowTut(show)
    self:KillTween_Tut()
    self.DataSrc.TutRect.gameObject:SetActive(show)
    if show then
        self.DataSrc.TutImg.transform.localScale = KingShot_Define.CacheVector3.One
        local tarScale = KingShot_Define.CS.Vector3(1.1, 1.1, 1)
        self.tween_Tut = self.DataSrc.TutImg.transform:DOScale(tarScale, 0.5):SetEase(
                KingShot_Define.TweenEase.InSine):SetLoops(-1, KingShot_Define.TweenLoopType.Yoyo)
    end
end

function mgr:ShowTask(show)
    self:KillTween_Task()
    self.DataSrc.TaskCG.gameObject:SetActive(show)
    if show then
        self.DataSrc.TaskCG.alpha = 0
        self.DataSrc.TaskCG.transform.localScale = { x = 1.8, y = 1.8, z = 1 }
        self.tween_Task = KingShot_Define.DOTween.Sequence()
        self.tween_Task:Append(self.DataSrc.TaskCG.transform:DOScale(KingShot_Define.CacheVector3.One, 0.2):SetEase(
                KingShot_Define.TweenEase.Linear))
        self.tween_Task:Insert(0, KingShot_Define.DOVirtual.Float(0, 1, 0.2, function(value)
            self.DataSrc.TaskCG.alpha = value
        end):SetEase(KingShot_Define.TweenEase.OutSine))
        self.tween_Task:AppendInterval(1.5)
        self.tween_Task:Append(KingShot_Define.DOVirtual.Float(1, 0, 0.3, function(value)
            self.DataSrc.TaskCG.alpha = value
        end):SetEase(KingShot_Define.TweenEase.InSine))
        self.tween_Task:OnComplete(function()
            self.DataSrc.TaskCG.gameObject:SetActive(false)
        end)
    end
end

function mgr:ShowBossAttent(show)
    if self.attentFlag then
        return
    end
    self:KillTween_BossAttent()
    self.DataSrc.BossAttentCG.gameObject:SetActive(show)
    if show then
        self.attentFlag = true
        self.DataSrc.TextBossAttent.transform.localScale = { x = 1, y = 1, z = 1 }
        local tarScale = { x = 1.3, y = 1.3, z = 1 }
        self.DataSrc.BossAttentCG.alpha = 0
        self.tween_BossAttent = KingShot_Define.DOTween.Sequence()
        self.tween_BossAttent:Append(KingShot_Define.DOVirtual.Float(0, 1, 0.3, function(value)
            self.DataSrc.BossAttentCG.alpha = value
        end):SetEase(KingShot_Define.TweenEase.OutSine))
        self.tween_BossAttent:OnComplete(function()
            self.tween_BossAttent = KingShot_Define.DOTween.Sequence()
            self.tween_BossAttent:Append(self.DataSrc.TextBossAttent.transform:DOScale(tarScale, 0.2)
                                             :SetEase(KingShot_Define.TweenEase.InOutQuad)
                                             :SetLoops(2, KingShot_Define.TweenLoopType.Yoyo))
            self.tween_BossAttent:AppendInterval(0.15)
            self.tween_BossAttent:SetLoops(3, KingShot_Define.TweenLoopType.Restart)
            self.tween_BossAttent:OnComplete(function()
                self.tween_BossAttent = KingShot_Define.DOVirtual.Float(1, 0, 0.3, function(value)
                    self.DataSrc.BossAttentCG.alpha = value
                end):SetEase(KingShot_Define.TweenEase.InSine)
                self.tween_BossAttent:OnComplete(function()
                    self.DataSrc.BossAttentCG.gameObject:SetActive(false)
                end)
            end)
        end)
    end
end

function mgr:ShowInfoRoot(show)
    self.DataSrc.InfoRoot.gameObject:SetActive(show)
end

function mgr:ShowJoy(value)
    self.DataSrc.Joystick.gameObject:SetActive(value)
end

function mgr:ShowCtrlPanel(value)
    self.DataSrc.CtrlEventTrigger.gameObject:SetActive(value)
end

function mgr:OnPointerDown(eventData)
    if not self.readyFlag or not self.sceneMgr.BattleFlagBind.value then
        return
    end
    if not self.touchStartFlag then
        self:ShowTut(false)
        self.touchStartFlag = true
    end
    self.DataSrc.Joystick.anchoredPosition = self.RealCanvaSize * (eventData.position / self.ScreenSize)
    self:ShowJoy(true)
end

function mgr:OnPointerUp(eventData)
    if not self.touchStartFlag then
        return
    end
    self.DataSrc.JoyHandle.anchoredPosition = KingShot_Define.CacheVector2.Zero
    self.DragEvent:Invoke(KingShot_Define.CacheVector2.Zero)
    self:ShowJoy(false)
end

function mgr:OnDrag(eventData)
    if not self.touchStartFlag then
        return
    end
    local curPos = self.RealCanvaSize * (eventData.position / self.ScreenSize)
    local off = curPos - self.DataSrc.Joystick.anchoredPosition

    -- 摇杆偏移量限制
    if (off.magnitude > self.joySize.x) then
        -- JoyHandle停在边缘，但继续发送归一化的方向
        self.DataSrc.JoyHandle.anchoredPosition = off.normalized * self.joySize.x
        self.DragEvent:Invoke(off.normalized)
    else
        -- JoyHandle跟随手指在摇杆范围内移动
        self.DataSrc.JoyHandle.anchoredPosition = off
        self.DragEvent:Invoke(off.normalized)
    end

    -- 移除底部摇杆跟随逻辑，保持Joystick位置固定
    -- 注释掉原来的bgPos移动代码
end

function mgr:WorldPos2CanvasVec3(wPos)
    local anchorPos = self.sceneMgr.cameraCtrl:WorldToViewportPoint(wPos)
    anchorPos.x = self.RealCanvaSize.x * (anchorPos.x - 0.5)
    anchorPos.y = self.RealCanvaSize.y * (anchorPos.y - 0.5)
    return { x = anchorPos.x, y = anchorPos.y, z = 0 }
end

function mgr:KillTween_Tips()
    if self.tween_Tips ~= nil then
        self.tween_Tips:Kill()
        self.tween_Tips = nil
    end
end

function mgr:KillTween_Tut()
    if self.tween_Tut ~= nil then
        self.tween_Tut:Kill()
        self.tween_Tut = nil
    end
end

function mgr:KillTween_Task()
    if self.tween_Task ~= nil then
        self.tween_Task:Kill()
        self.tween_Task = nil
    end
end

function mgr:KillTween_BossAttent()
    if self.tween_BossAttent ~= nil then
        self.tween_BossAttent:Kill()
        self.tween_BossAttent = nil
    end
end

function mgr:__delete()
    self:KillTween_Tips()
    self:KillTween_Tut()
    self:KillTween_Task()
    self:KillTween_BossAttent()

    -- 清理所有延迟血条控制器
    if self.delayedHpSliderControllers then
        for _, ctrl in pairs(self.delayedHpSliderControllers) do
            if ctrl then
                ctrl:Dispose()
            end
        end
        self.delayedHpSliderControllers = nil
    end

    -- 清理所有攻击锁定图标
    if self.attackLockIcons and self.attackLockPool then
        for targetGo, iconGo in pairs(self.attackLockIcons) do
            if iconGo then
                self.attackLockPool:PushOne(iconGo)
            end
        end
        self.attackLockIcons = nil
    end

    -- 清理攻击锁定图标池
    if self.attackLockPool then
        self.attackLockPool:Delete()
        self.attackLockPool = nil
    end

    -- 清理延迟血条池
    if self.delayedHpSliderPool then
        self.delayedHpSliderPool:Delete()
        self.delayedHpSliderPool = nil
    end

    if self.DragEvent ~= nil then
        self.DragEvent:Delete()
        self.DragEvent = nil
    end
    if self._ui_lst  then
        for k,v in pairs(self._ui_lst) do
            self:HideUIModule(k)
        end
    end
    if self.AlertEffect then
        self.AlertEffect:Dispose()
        self.AlertEffect = nil
    end

    if self.bossLogoArray ~= nil then
        for i, v in ipairs(self.bossLogoArray) do
            v:dispose()
        end
        self.bossLogoArray = nil
    end

    if self.bossGuideCtrl ~= nil then
        self.bossGuideCtrl:dispose()
        self.bossGuideCtrl = nil
    end
end

function mgr:AutoAdaptLocalScale(trans, pZ, oriLocalScale)
end

function mgr:ClearCamZ()
end

function mgr:UpdateBossHpSlider(HpSliderImage, MaxHp, curHp)
    local fillAmount = 0
    local num = 0
    local xNum = 250
    curHp = math.floor(curHp)
    if MaxHp > xNum * 20 then
        xNum = MaxHp / 20
    end
    if MaxHp > xNum then
        num = math.floor(curHp / xNum) + 1
        local amount = curHp % xNum
        num = math.floor(curHp / xNum)
        fillAmount = amount / xNum
    else
        fillAmount = curHp / MaxHp
    end
    HpSliderImage.value = fillAmount
    local dataSrc = self.sliderDataWithBossSlider[HpSliderImage]
    if dataSrc == nil then
        dataSrc = {
            HpText = HpSliderImage.transform:GetChild(2):GetComponent(KingShot_Define.TypeOf.UIText),
            NumText = HpSliderImage.transform:GetChild(3):GetComponent(KingShot_Define.TypeOf.UIText),
            BackgroundImage = HpSliderImage.transform:GetChild(1):GetComponent(KingShot_Define.TypeOf.UIImage),
            fillImage = HpSliderImage.transform:GetChild(1):GetChild(0):GetChild(0):GetComponent(KingShot_Define
                .TypeOf.UIImage)
        }
        self.sliderDataWithBossSlider[HpSliderImage] = dataSrc
    end
    if num == 0 then
        dataSrc.BackgroundImage.color = KingShot_Define.CacheColor.Black
        dataSrc.fillImage.color = KingShot_Define.CacheColor.Red
    elseif num % 2 == 0 then
        dataSrc.BackgroundImage.color = KingShot_Define.CacheColor.White
        dataSrc.fillImage.color = KingShot_Define.CacheColor.Red
    else
        dataSrc.BackgroundImage.color = KingShot_Define.CacheColor.Red
        dataSrc.fillImage.color = KingShot_Define.CacheColor.White
    end

    -- FD1784  292525
    dataSrc.HpText.text = tostring(curHp)
    dataSrc.NumText.text = tostring("x" .. num)
end

function mgr:UpdateHpSliderXYZ(HpSliderImage, curHp, MaxHp, pX, pY, pZ, isBoss, parent, lastPosX, lastPosZ)
    local tmpParent = parent or HpSliderImage.transform.parent
    local canvasPos = self:WorldPos2CanvasVec3({ x = pX, y = pY, z = pZ })
    tmpParent.localPosition = canvasPos

    -- 检查是否是延迟血条
    local delayedCtrl = self.delayedHpSliderControllers[HpSliderImage]
    if delayedCtrl then
        if isBoss then
            delayedCtrl:UpdateBossHpSlider(MaxHp, curHp)
        else
            local amount = curHp / MaxHp
            if amount < 1 then
                delayedCtrl:UpdateHpSlider(curHp, MaxHp, amount)
            end
        end
        return
    end

    -- 原有的血条更新逻辑
    if isBoss then
        self:UpdateBossHpSlider(HpSliderImage, MaxHp, curHp)
        return
    end
    local amount = curHp / MaxHp
    if amount >= 1 then
        return
    end
    HpSliderImage.value = amount
end

function mgr:DestoryHpSlider(HpSliderImage)
    -- 清理延迟血条控制器
    local delayedCtrl = self.delayedHpSliderControllers[HpSliderImage]
    if delayedCtrl then
        delayedCtrl:Dispose()
        self.delayedHpSliderControllers[HpSliderImage] = nil
    end

    -- 获取血条GameObject
    local hpSliderGO = HpSliderImage.transform.parent.gameObject

    -- 判断是否是延迟血条（使用新的池系统）
    if self:_IsDelayedHpSlider(hpSliderGO) then
        -- 使用新的fusion_gopool回收
        self.delayedHpSliderPool:PushOne(hpSliderGO)
    else
        -- 使用原来的回收方式
        KingShot_Define.CS.NeeGame.ReturnObject(hpSliderGO)
    end
end

-- 判断是否是延迟血条
function mgr:_IsDelayedHpSlider(gameObject)
    local name = gameObject.name
    return string.find(name, "DelayedHpSlider") ~= nil
end

function mgr:SpawnHpSlider(cb, playerType, pos, isBoss)
    local useDelayedHp = KingShot_Define.Params.UseDelayedHp

    if useDelayedHp then
        -- 使用新的fusion_gopool系统创建延迟血条
        self:_SpawnDelayedHpSliderWithPool(cb, playerType, pos, isBoss)
    else
        -- 使用原来的系统创建传统血条
        self:_SpawnTraditionalHpSlider(cb, playerType, pos, isBoss)
    end
end

-- 使用新的fusion_gopool系统创建延迟血条
function mgr:_SpawnDelayedHpSliderWithPool(cb, playerType, pos, isBoss)
    local hpSlider = self.delayedHpSliderPool:PopOne()
    if not hpSlider then
        Fusion.Error("Failed to get DelayedHpSlider from pool")
        return
    end

    local isMonster = not playerType  -- playerType为nil/false时是怪物
    self:_SetupHpSlider(hpSlider, pos, false, isMonster, cb, true)
end

-- 使用原来的系统创建传统血条（保留原逻辑）
function mgr:_SpawnTraditionalHpSlider(cb, playerType, pos, isBoss)
    local name = cysoldierssortie_PoolObjectName.MonsterHpSlider
    local isPlayer = false
    local isMonster = false

    -- 确定血条类型
    if isBoss then
        name = cysoldierssortie_PoolObjectName.BossHpSlider
    elseif playerType then
        if playerType == 1 then
            name = cysoldierssortie_PoolObjectName.PlayerHpSlider
        else
            name = cysoldierssortie_PoolObjectName.PlayerHpHero
            isPlayer = true
        end
    else
        isMonster = true
    end

    local function callback(HpSlider)
        self:_SetupHpSlider(HpSlider, pos, isPlayer, isMonster, cb, false)
    end

    if isAsync then
        KingShot_Define.CS.NeeGame.PoolObjectAsync(name, callback, self.sceneMgr.DataSrc.HpSliderParent)
    else
        local HpSlider = KingShot_Define.CS.NeeGame.PoolObject(name, self.sceneMgr.DataSrc.HpSliderParent)
        callback(HpSlider)
    end
end

-- 设置血条的通用逻辑
function mgr:_SetupHpSlider(hpSlider, pos, isPlayer, isMonster, cb, isDelayedHp)
    -- 如果是从新池系统获取的，需要设置父容器和激活
    if isDelayedHp then
        hpSlider.transform:SetParent(self.sceneMgr.DataSrc.HpSliderParent)
        hpSlider:SetActive(true)
    end

    -- 获取Slider组件
    local imgTransf = hpSlider.transform:Find("Slider")
    local hpSliderImage = imgTransf:GetComponent(KingShot_Define.TypeOf.UISlider)

    -- 重置血条状态
    hpSliderImage.value = 1

    -- 设置变换
    KingShot_Define.SetTransformLocalScale(imgTransf, 0.5, 0.5, 1)
    KingShot_Define.SetTransformLocalPositionAndLocalRotation(hpSlider.transform, 0, 0, 0, 0, 0, 0)
    KingShot_Define.SetTransformLocalPositionAndLocalRotation(imgTransf, 0, 0, 0, 0, 0, 0)

    -- 设置玩家头像（如果需要）
    if isPlayer then
        local avatar = hpSlider.transform:Find("Avatar")
        if avatar then
            KingShot_Define.SetTransformLocalScale(avatar, 1, 1, 1)
            KingShot_Define.SetTransformLocalPositionAndLocalRotation(avatar, -40, 0, 0, 0, 0, 0)
        end
    end

    -- 设置位置和缩放
    hpSlider.transform.position = pos
    KingShot_Define.SetTransformLocalScale(hpSlider.transform, 1.5, 1.5, 1.5)

    -- 如果是延迟血条，创建控制器
    if isDelayedHp then
        local delayedCtrl = require("kingshot_delayed_hpslider_ctrl").New(hpSlider, isMonster, isPlayer and 2 or nil)
        self.delayedHpSliderControllers[hpSliderImage] = delayedCtrl
    end

    -- 执行回调
    if cb then
        cb(hpSliderImage)
    end
end

-- 创建延迟血条的便捷函数（已废弃，现在统一使用SpawnHpSlider）
function mgr:SpawnDelayedHpSlider(cb, playerType, pos, isBoss)
    -- 直接调用SpawnHpSlider，配置会自动处理延迟血条
    return self:SpawnHpSlider(cb, playerType, pos, isBoss)
end

function mgr:SpawnHpNumAniTextXYZ(attack, pX, pY, pZ, isCritical, skillView)
    isCritical = isCritical or false
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local HpNumAni = nil
    local HpTrans = nil
    local parent = self.sceneMgr.DataSrc.NumTextParent

    local function callback(HpNumAni, HpTrans)
        HpTrans.localRotation = KingShot_Define.CacheQuaternion.Identity
        KingShot_Define.SetTransformPositionXYZ(HpTrans, pX, pY, pZ)
        local HpNumAniText = nil
        if poolMgr then
            HpNumAniText = poolMgr:GetHpAnimText(HpNumAni)
        end
        if isCritical then
            KingShot_Define.SetTransformLocalScale(HpTrans, 1, 1, 1)
            if HpNumAniText then
                KingShot_Define.SetTransformLocalScale(HpNumAniText.transform, 0.7, 0.7, 1)
            end
        else
            KingShot_Define.SetTransformLocalScale(HpTrans, 0.6, 0.6, 1)
        end
        if HpNumAniText then
            KingShot_Define.SetTransformLocalPositionAndLocalRotation(HpNumAniText.transform, 0, 0, 0, 0, 0, 0)
            if isCritical then
                cysoldierssortie_lua_util:Append("C")
                cysoldierssortie_lua_util:Append("-")
                cysoldierssortie_lua_util:Append(math.floor(attack))
                HpNumAniText.text = cysoldierssortie_lua_util:ToString()
            else
                cysoldierssortie_lua_util:Append("-")
                cysoldierssortie_lua_util:Append(math.floor(attack))
                HpNumAniText.text = cysoldierssortie_lua_util:ToString()
            end
        end
        local sequenceType = 1
        if isCritical then
            sequenceType = 2
        end
        cysoldierssortie_ui_hud_anim_config:PlaySequence(HpNumAni, HpTrans, pY, 1, sequenceType)
    end

    local HpNumName
    if skillView then
        HpNumName = cysoldierssortie_PoolObjectName.SkillHpNumAniText
    elseif isCritical then
        HpNumName = cysoldierssortie_PoolObjectName.CriticalHpNumAniText
    else
        HpNumName = cysoldierssortie_PoolObjectName.HpNumAniText
    end

    if isAsync then
        NeeGame.PoolObjectAsync(HpNumName,
                function(HpNumAni)
                    local HpTrans = poolMgr:GetTransform(HpNumAni)
                    self:AutoAdaptLocalScale(HpTrans, pZ, 0.5)
                    callback(HpNumAni, HpTrans)
                end, parent)
    else
        local HpNumAni = NeeGame.PoolObject(HpNumName, parent)
        local HpTrans = poolMgr:GetTransform(HpNumAni)
        self:AutoAdaptLocalScale(HpTrans, pZ, 0.5)
        callback(HpNumAni, HpTrans)
    end
    
    --if cysoldierssortie_KingShot_HOME then --
    --    if skillView then
    --        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.SkillHpNumAniText_kingshot, parent)
    --        HpTrans = poolMgr:GetTransform(HpNumAni)
    --    elseif isCritical then
    --        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.CriticalHpNumAniText_kingshot, parent)
    --        HpTrans = poolMgr:GetTransform(HpNumAni)
    --    else
    --        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.HpNumAniText_kingshot, parent)
    --        HpTrans = poolMgr:GetTransform(HpNumAni)
    --    end
    --else
    --    if skillView then
    --        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.SkillHpNumAniText, parent)
    --        HpTrans = poolMgr:GetTransform(HpNumAni)
    --    elseif isCritical then
    --        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.CriticalHpNumAniText, parent)
    --        HpTrans = poolMgr:GetTransform(HpNumAni)
    --    else
    --        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.HpNumAniText, parent)
    --        HpTrans = poolMgr:GetTransform(HpNumAni)
    --    end
    --    local util = require "util"
    --    util.SetLayer(HpNumAni, 0)
    --end
    --
    --KingShot_Define.SetTransformPositionAndRotation(HpTrans, pX, pY, pZ,41,0,4)
    --
    --local HpNumAniText = nil
    --if poolMgr then
    --    HpNumAniText = poolMgr:GetHpAnimTextMesh(HpNumAni)
    --end
    --if HpNumAniText == nil then
    --    HpNumAniText = poolMgr:GetHpAnimText(HpNumAni)
    --end
    --if isCritical then
    --    KingShot_Define.SetTransformLocalScale(HpTrans, 1, 1, 1)
    --    if HpNumAniText then
    --        KingShot_Define.SetTransformLocalScale(HpNumAniText.transform, 0.7, 0.7, 1)
    --    end
    --else
    --    KingShot_Define.SetTransformLocalScale(HpTrans, 0.6, 0.6, 1)
    --end
    --if HpNumAniText then
    --    if isCritical then
    --        if not cysoldierssortie_KingShot_HOME then
    --            cysoldierssortie_lua_util:Append("C")
    --        end
    --        cysoldierssortie_lua_util:Append("-")
    --        cysoldierssortie_lua_util:Append(math.floor(attack))
    --        HpNumAniText.text = cysoldierssortie_lua_util:ToString()
    --    else
    --        cysoldierssortie_lua_util:Append("-")
    --        cysoldierssortie_lua_util:Append(math.floor(attack))
    --        HpNumAniText.text = cysoldierssortie_lua_util:ToString()
    --    end
    --end
    --
    --local sequenceType = 1
    --if skillView then
    --    if isCritical then
    --        sequenceType = 4
    --    else
    --        sequenceType = 3
    --    end
    --elseif isCritical then
    --    sequenceType = 2
    --end
    --cysoldierssortie_ui_hud_anim_config:PlaySequence(HpNumAni, HpTrans, pY, 1, sequenceType)
end

function mgr:SpawnCoinRewardAniTextXYZ( pX, pY, pZ,num)
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local HpNumAni = nil
    local HpTrans = nil
    local parent = self.sceneMgr.DataSrc.CoinTextParent

    HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.GetRewardAniText, parent)
    HpTrans = poolMgr:GetTransform(HpNumAni)

    KingShot_Define.SetTransformPositionXYZ(HpTrans, pX, pY, pZ)

    local numText = HpNumAni:GetComponentInChildren(typeof(CS.UnityEngine.UI.Text))
    numText.fontSize = 70
    numText.text = "金币+"..num

    if cysoldierssortie_KingShot_HOME then --
        local util = require "util"
        util.SetLayer(HpNumAni, 12)
    else
        local util = require "util"
        util.SetLayer(HpNumAni, 0)
    end

    
    local sequenceType = 1
    cysoldierssortie_ui_hud_anim_config:PlaySequence(HpNumAni, HpTrans, pY, 1, sequenceType)
end


function mgr:ShowUIModule(uiName)
    if self._ui_lst and self._ui_lst[uiName] then
        return
    end
    local ui_base_item =  require(uiName)
    if ui_base_item then
        local data = {uiRoot = self.DataSrc.InfoRoot}
        local ui_base_item_new = ui_base_item.New(data)
        ui_base_item_new:Init(self.sceneMgr)
        ui_base_item_new:Show()
        ui_base_item_new:OnShow()
        self._ui_lst = self._ui_lst or {}
        self._ui_lst[uiName] = ui_base_item_new
    end
end

function mgr:IsShowUIModule(uiName)
    if not self._ui_lst then
        return false
    end
    return self._ui_lst[uiName] and true or false
end

function mgr:HideUIModule(uiName)
    if not self._ui_lst then
        return
    end

    local ui_base_item = self._ui_lst[uiName]
    if ui_base_item then
        ui_base_item:OnHide()
        ui_base_item.Delete(ui_base_item)
        self._ui_lst[uiName] = nil
    end
end

-- 攻击锁定图标管理方法
function mgr:ShowAttackLockIcon(targetGo)
    if not targetGo or not self.readyFlag then
        return
    end

    -- 检查是否为敌方单位
    --if not self:IsEnemyUnit(targetGo) then
    --    return
    --end

    -- 如果已经有图标了，直接返回
    if self.attackLockIcons[targetGo] then
        return
    end

    -- 创建攻击锁定图标
    local parent = self.DataSrc.AttackLockParent  -- 使用InfoRoot作为父容器，确保层级最高
    local iconGo = self.attackLockPool:PopOne()
    if iconGo then
        -- 设置父容器
        iconGo.transform:SetParent(parent)
        --调整下大小把
        KingShot_Define.SetTransformLocalScale(iconGo.transform, 0.05, 0.05, 0.05)
        -- 设置图标层级最高
        iconGo.transform:SetAsLastSibling()
        -- 激活对象
        iconGo:SetActive(true)

        -- 存储映射关系
        self.attackLockIcons[targetGo] = iconGo

        -- 初始位置更新
        self:UpdateAttackLockIconPosition(targetGo, iconGo)
    end
end

function mgr:HideAttackLockIcon(targetGo)
    if not targetGo or not self.attackLockIcons then
        return
    end

    local iconGo = self.attackLockIcons[targetGo]
    if iconGo then
        -- 回收到对象池
        self.attackLockPool:PushOne(iconGo)
        -- 移除映射关系
        self.attackLockIcons[targetGo] = nil
    end
end

function mgr:UpdateAttackLockIconPosition(targetGo, iconGo)
    if not targetGo or not iconGo then
        return
    end
    -- 获取目标的世界坐标
    local targetPos = targetGo.transform.position
    -- 稍微抬高图标位置，避免与模型重叠
    local iconWorldPos = { x = targetPos.x, y = targetPos.y, z = targetPos.z }

    -- 转换为UI坐标
    local canvasPos = self:WorldPos2CanvasVec3(iconWorldPos)

    -- 设置图标位置
    iconGo.transform.localPosition = KingShot_Define.CS.Vector3(canvasPos.x, canvasPos.y, canvasPos.z)
end

function mgr:UpdateAllAttackLockIcons()
    if not self.attackLockIcons then
        return
    end

    -- 更新所有攻击锁定图标的位置
    for targetGo, iconGo in pairs(self.attackLockIcons) do
        if targetGo and iconGo and not bc_IsNull(targetGo) and not bc_IsNull(iconGo) then
            self:UpdateAttackLockIconPosition(targetGo, iconGo)
        else
            -- 清理无效的映射
            self.attackLockIcons[targetGo] = nil
        end
    end
end

function mgr:ShowFullScreenEffect(fullScreenTime)
    if fullScreenTime then
        isShowFullScreenEffect = true
        if self.FullScreenEffect then
            self.FullScreenEffect:SetVisible(false)
            self.FullScreenEffect:SetVisible(true)
        else
            local effectRoot = KingShot_Define.CS.GameObject.Find("UIRoot/CanvasScreenEffect")
            local gw_sand_effect_const = require "gw_sand_effect_const"
            local gw_sand_effect_param = require "gw_sand_effect_param"
            local gw_effect_object = require "gw_effect_object"

            local newEffectParams = gw_sand_effect_param.NewEffectParam(gw_sand_effect_const.EffectName.KingShotSkillEffect, {showTime = fullScreenTime })
            local newEffectBaseParams = gw_sand_effect_param.NewEffectBaseParam(gw_sand_effect_const.EffectName.KingShotSkillEffect, nil)
            self.FullScreenEffect = gw_effect_object.CM("gw_effect_object"):Init(8888, effectRoot.transform, newEffectParams, newEffectBaseParams, function()
                if self.FullScreenEffect then
                    self.FullScreenEffect:SetVisible(isShowFullScreenEffect)
                end
            end)
        end
    end
end


return mgr
