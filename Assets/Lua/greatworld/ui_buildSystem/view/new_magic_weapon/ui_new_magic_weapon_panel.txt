--region FileHead
--- ui_new_magic_weapon_panel.txt
-- author:  农勇智
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local print = print
local require = require
local type = type
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table
local dump = dump
local log = log

local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local enum_define = require "enum_define"
local event = require "event"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local Common_Util = CS.Common_Util.UIUtil
local e_handler_mgr = require "e_handler_mgr"
local game_scheme = require "game_scheme"
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI
local RawImage                  = CS.UnityEngine.UI.RawImage
local model_item = require "model_item"
local util = require"util"
local weapon_data = require "weapon_data"
local Quaternion         = CS.UnityEngine.Quaternion
local overlay_item = require "overlay_item"
local Input = CS.UnityEngine.Input
local screen_util = require "screen_util"
local card_sprite_asset = require "card_sprite_asset"
local ui_growup_number      = require "ui_growup_number"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local red_const = require "red_const"
local RectTransform = CS.UnityEngine.RectTransform
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local RectTransformUtility  = CS.UnityEngine.RectTransformUtility
local Camera 				= CS.UnityEngine.Camera
local math = math
local cfg_util = require "cfg_util"
local tonumber = tonumber
local GWG = GWG
local Outline       = CS.UnityEngine.UI.Outline

local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local player_mgr = require "player_mgr"
local ImageGray = CS.War.UI.ImageGray
local LeanTween         = CS.LeanTween
local Time          = CS.UnityEngine.Time
local Mathf = require "Mathf"
local ui_window_mgr = require "ui_window_mgr"
local Vector3    = CS.UnityEngine.Vector3
local base_game_object = require "base_game_object"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local flow_text = require "flow_text"
local Animation = CS.UnityEngine.Animation
local ui_new_magic_weapon_panel_controller = require "ui_new_magic_weapon_panel_controller"
local pageShowTypeEnum = ui_new_magic_weapon_panel_controller.pageShowTypeEnum
local new_weapon_combat_crystal_define = require "new_weapon_combat_crystal_define"
local StageTitleColor = new_weapon_combat_crystal_define.StageTitleColor
--endregion 

--region ModuleDeclare
module("ui_new_magic_weapon_panel")
local ui_path = "ui/prefabs/gw/buildsystem/uinewmagicweaponpanel.prefab"
local window = nil
local UIView = {}

local ignoreAttribute =
{
    [29] = true,
    [101] = true,
    [30] = true,
    [102] = true,
    [31] = true,
    [103] = true,
    [27] = true,
}

--endregion 

--region WidgetTable
UIView.widget_table = {
    modelRt = {path = "bgParent/Bg/model", type = RawImage},
    closeBtn = {path = "closeBtn", type = "Button",event_name = "OnBtn_closeBtnClickedProxy"},
    powerText = {path = "Info/power/text", type = TextMeshProUGUI},

    nameTxt = {path = "Name",type = "Text"},
    attributeToggle = {path = "pageToggle/Toggle1",type = "Toggle",value_changed_event = "ShowAttribute"},
    attributeToggleRedPoint = {path = "pageToggle/Toggle1/RedDot",type = "RectTransform"},
    equipmentToggle = {path = "pageToggle/Toggle2",type = "Toggle",value_changed_event = "ShowEquipment"},
    equipmentToggleRedPoint = {path = "pageToggle/Toggle2/RedDot",type = "RectTransform"},
    equipmentToggleGray = {path = "pageToggle/Toggle2/Background",type = ImageGray},
    equipmentToggleLockBtn = {path = "pageToggle/Toggle2/lock",type = "Button",event_name = "EquipmentLock"},

    --战斗进阶
    combatToggle = {path = "pageToggle/Toggle3",type = "Toggle",value_changed_event = "ShowCombat"},
    combatToggleRedPoint = {path = "pageToggle/Toggle3/RedDot",type = "RectTransform"},
    Info = {path = "Info",type = "RectTransform"},
    combatPage = {path = "Info/combatPage",type = "RectTransform"},
    stageBg = {path = "Info/combatPage/Bg/stageBg",type = SpriteSwitcher},
    curStageText = {path = "Info/combatPage/Bg/stageBg/curStageText",type = "Text"},
    curStageOutLine = {path = "Info/combatPage/Bg/stageBg/curStageText",type = Outline},
    nextCombatStageLevel = {path = "Info/combatPage/Bg/nextCombatStageLevel",type = "Text"},
    curCombatLevel = {path = "Info/combatPage/Bg/curCombatLevel",type = "Text"},
    combatDetail = {path = "Info/combatPage/Bg/combatDetail",type = "Button",event_name = "ShowCombatDetail"},
    combatExpSlider = {path = "Info/combatPage/Bg/combatExpSlider",type = "Slider"},
    combatExp = {path = "Info/combatPage/Bg/combatExpSlider/combatExp",type = "Text"},
    combatBtn = {path = "Info/combatPage/Bg/combatBtn",type = "Button",event_name = "ClickCombatEvent"},
    combatBtnGray = {path = "Info/combatPage/Bg/combatBtn",type = ImageGray},
    combatBtnGrayText = {path = "Info/combatPage/Bg/combatBtn/combatBtnGrayText",type = "Text"},
    addHpValue = {path = "Info/combatPage/Bg/attributeList/addHp/addHpValue",type = "Text"},
    addAtkValue = {path = "Info/combatPage/Bg/attributeList/addAtk/addAtkValue",type = "Text"},
    addDefValue = {path = "Info/combatPage/Bg/attributeList/addDef/addDefValue",type = "Text"},
    addSkillRateValue = {path = "Info/combatPage/Bg/attributeList/addSkillRate/addSkillRateValue",type = "Text"},    
    btn_crtstalBox = {path = "rtf_crtstalBoxBg/btn_crtstalBox",type = "Button",event_name = "ClickCrystalBox"},
    rtf_crtstalBoxBg = {path = "rtf_crtstalBoxBg",type = "RectTransform"},
    crystalToggle = {path = "pageToggle/Toggle4",type = "Toggle",value_changed_event = "ShowCrystal"},
    crystalToggleRedPoint = {path = "pageToggle/Toggle4/RedDot",type = "RectTransform"},
    crystalPage = {path = "crystalPage",type = "RectTransform"},
    btn_tips = {path = "btn_tips",type = "Button",event_name = "OnBtnTipsClickedProxy"},
    
    attributePanel = {path = "Info/Attribute",type = "RectTransform"},
    atkBtn = {path = "Info/Attribute/Attack",type = "Button",event_name = "ShowAtkTips"},
    hpBtn = {path = "Info/Attribute/Hp",type = "Button",event_name = "ShowHpTips"},
    defBtn = {path = "Info/Attribute/Def",type = "Button",event_name = "ShowDefTips"},
    atkRateBtn = {path = "Info/Attribute/AttributeRate/AtkRateBtn",type = "Button",event_name = "ShowAtkRateTips"},
    hpRateBtn = {path = "Info/Attribute/AttributeRate/HpRateBtn",type = "Button",event_name = "ShowHpRateTips"},
    defRateBtn = {path = "Info/Attribute/AttributeRate/DefRateBtn",type = "Button",event_name = "ShowDefRateTips"},

    atkValue = {path = "Info/Attribute/Attack/Value",type = "Text"},
    hpValue = {path = "Info/Attribute/Hp/Value",type = "Text"},
    defValue = {path = "Info/Attribute/Def/Value",type = "Text"},
    skillName = {path = "Info/Attribute/Skill/Name",type = "Text"},
    skillIcon = {path = "Info/Attribute/Skill/Skill/mask/icon",type = "Image"},
    skillBtn = {path = "Info/Attribute/Skill/Skill",type = "Button",event_name = "ShowSkillTips"},
    skillStar = {path = "Info/Attribute/Skill/star_root",type = "RectTransform"},
    skillStar_1 = {path = "Info/Attribute/Skill/star_root/star_1",type = "RectTransform"},
    skillStar_2 = {path = "Info/Attribute/Skill/star_root/star_2",type = "RectTransform"},
    skillStar_3 = {path = "Info/Attribute/Skill/star_root/star_3",type = "RectTransform"},
    skillStar_4 = {path = "Info/Attribute/Skill/star_root/star_4",type = "RectTransform"},
    skillStar_5 = {path = "Info/Attribute/Skill/star_root/star_5",type = "RectTransform"},

    atkRate = {path = "Info/Attribute/AttributeRate/Value",type = "Text"},
    hpRate = {path = "Info/Attribute/AttributeRate/Value_1",type = "Text"},
    defRate = {path = "Info/Attribute/AttributeRate/Value_2",type = "Text"},

    detailBtn = {path = "Info/Attribute/detail",type = "Button",event_name = "ShowBigAttributeTips"},
    normalUpgradeObj = {path = "Info/Attribute/NormalUpgrade",type = "RectTransform"},
    specialUpgradeObj = {path = "Info/Attribute/SpecialUpgrade",type = "RectTransform"},

    normalLevelText = {path = "Info/Attribute/NormalUpgrade/LevelBg/Text",type = "Text"},
    normalExpSlider = {path = "Info/Attribute/NormalUpgrade/ExpSlider",type = "Slider"},
    normalExpValue = {path = "Info/Attribute/NormalUpgrade/ExpValue",type = "Text"},
    normalUpgradeBtn = {path = "Info/Attribute/NormalUpgrade/Upgrade",type = "Button",event_name = "ShowUpgradeTips"},
    normalBaseItemIcon = {path = "Info/Attribute/NormalUpgrade/baseItem",type = "Image"},
    normalBaseItemIconBtn = {path = "Info/Attribute/NormalUpgrade/baseItem",type = "Button",event_name = "ShowNormalUpgradeItemTips"},
    normalBaseItemCount = {path = "Info/Attribute/NormalUpgrade/baseItem/Count",type = "Text"},
    normalSpecialItemIcon = {path = "Info/Attribute/NormalUpgrade/SpecialItem",type = "Image"},
    normalSpecialItemIconBtn = {path = "Info/Attribute/NormalUpgrade/SpecialItem",type = "Button",event_name = "ShowSpecialUpgradeItemTips"},
    normalSpecialItemCount = {path = "Info/Attribute/NormalUpgrade/SpecialItem/Count",type = "Text"},

    specialLevelText = {path = "Info/Attribute/SpecialUpgrade/LevelBg/Text",type = "Text"},
    specialExpSliderObj_1 = {path = "Info/Attribute/SpecialUpgrade/ExpSlider/Item_1/Fill",type = "RectTransform"},
    specialExpSliderObj_2 = {path = "Info/Attribute/SpecialUpgrade/ExpSlider/Item_2/Fill",type = "RectTransform"},
    specialExpSliderObj_3 = {path = "Info/Attribute/SpecialUpgrade/ExpSlider/Item_3/Fill",type = "RectTransform"},
    specialExpSliderObj_4 = {path = "Info/Attribute/SpecialUpgrade/ExpSlider/Item_4/Fill",type = "RectTransform"},
    specialExpSliderObj_5 = {path = "Info/Attribute/SpecialUpgrade/ExpSlider/Item_5/Fill",type = "RectTransform"},
    specialExpValue = {path = "Info/Attribute/SpecialUpgrade/ExpValue",type = "Text"},
    specialUpgradeBtn = {path = "Info/Attribute/SpecialUpgrade/Upgrade",type = "Button",event_name = "ShowUpgradeTips"},
    specialBaseItemIcon = {path = "Info/Attribute/SpecialUpgrade/baseItem",type = "Image"},
    specialBaseItemIconBtn = {path = "Info/Attribute/SpecialUpgrade/baseItem",type = "Button",event_name = "ShowNormalUpgradeItemTips"},
    specialBaseItemCount = {path = "Info/Attribute/SpecialUpgrade/baseItem/Count",type = "Text"},
    specialSpecialItemIcon = {path = "Info/Attribute/SpecialUpgrade/SpecialItem",type = "Image"},
    specialSpecialItemIconBtn = {path = "Info/Attribute/SpecialUpgrade/SpecialItem",type = "Button",event_name = "ShowSpecialUpgradeItemTips"},
    specialSpecialItemCount = {path = "Info/Attribute/SpecialUpgrade/SpecialItem/Count",type = "Text"},

    tipsMask = {path = "TipsMask",type = "Button",event_name = "HideAllTips"},
    tipsObj = {path = "TipsObj",type = "RectTransform"},
    tipsParent = {path = "Info/Attribute",type = "RectTransform"},
    attributeTips = {path = "Info/Attribute/AttributeTips",type = "RectTransform"},
    attributeTipsText = {path = "Info/Attribute/AttributeTips/Text",type = "Text"},
    skillTips = {path = "TipsObj/SkillTips",type = "RectTransform"},
    skillTipsIcon = {path = "TipsObj/SkillTips/SkillIcon/mask/icon",type = "Image"},
    skillTipsName = {path = "TipsObj/SkillTips/Name",type = "Text"},
    skillTipsStar = {path = "TipsObj/SkillTips/star_root",type = "RectTransform"},
    skillTipsStarObj_1 = {path = "TipsObj/SkillTips/star_root/star_1",type = "RectTransform"},
    skillTipsStarObj_2 = {path = "TipsObj/SkillTips/star_root/star_2",type = "RectTransform"},
    skillTipsStarObj_3 = {path = "TipsObj/SkillTips/star_root/star_3",type = "RectTransform"},
    skillTipsStarObj_4 = {path = "TipsObj/SkillTips/star_root/star_4",type = "RectTransform"},
    skillTipsStarObj_5 = {path = "TipsObj/SkillTips/star_root/star_5",type = "RectTransform"},
    skillTipsDesc = {path = "TipsObj/SkillTips/Desc",type = "Text"},

    skillTipsStarGroup = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star",type = "RectTransform"},

    skillTipsStarInfoObj_1 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_1",type = "RectTransform"},
    skillTipsEmptyStar_1 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_1/emptyStar",type = "RectTransform"},
    skillTipsStar_1 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_1/Star",type = "RectTransform"},
    skillTipsStarDesc_1 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_1/Text",type = "Text"},
    skillTipsStarDescRect_1 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_1/Text",type = "RectTransform"},

    skillTipsStarInfoObj_2 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_2",type = "RectTransform"},
    skillTipsEmptyStar_2 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_2/emptyStar",type = "RectTransform"},
    skillTipsStar_2 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_2/Star",type = "RectTransform"},
    skillTipsStarDesc_2 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_2/Text",type = "Text"},
    skillTipsStarDescRect_2 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_2/Text",type = "RectTransform"},

    skillTipsStarInfoObj_3 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_3",type = "RectTransform"},
    skillTipsEmptyStar_3 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_3/emptyStar",type = "RectTransform"},
    skillTipsStar_3 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_3/Star",type = "RectTransform"},
    skillTipsStarDesc_3 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_3/Text",type = "Text"},
    skillTipsStarDescRect_3 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_3/Text",type = "RectTransform"},

    skillTipsStarInfoObj_4 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_4",type = "RectTransform"},
    skillTipsEmptyStar_4 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_4/emptyStar",type = "RectTransform"},
    skillTipsStar_4 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_4/Star",type = "RectTransform"},
    skillTipsStarDesc_4 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_4/Text",type = "Text"},
    skillTipsStarDescRect_4 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_4/Text",type = "RectTransform"},

    skillTipsStarInfoObj_5 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_5",type = "RectTransform"},
    skillTipsEmptyStar_5 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_5/emptyStar",type = "RectTransform"},
    skillTipsStar_5 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_5/Star",type = "RectTransform"},
    skillTipsStarDesc_5 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_5/Text",type = "Text"},
    skillTipsStarDescRect_5 = {path = "TipsObj/SkillTips/StarSkillDescScroll/Viewport/Content/Star/StarSkill_5/Text",type = "RectTransform"},

    darkMask = {path = "TipsObj/darkMask",type = "RectTransform"},
    bigAttributeTips = {path = "TipsObj/darkMask/AttributeTips",type = "RectTransform"},
    bigHpText = {path = "TipsObj/darkMask/AttributeTips/HpGroup/HpPanel/Value",type = "Text"},
    bigHpRateText = {path = "TipsObj/darkMask/AttributeTips/HpGroup/Rate/Value",type = "Text"},
    bigHeroHpText = {path = "TipsObj/darkMask/AttributeTips/HpGroup/HeroHp/Value",type = "Text"},
    bigAtkText = {path = "TipsObj/darkMask/AttributeTips/AtkGroup/AtkPanel/Value",type = "Text"},
    bigAtkRateText = {path = "TipsObj/darkMask/AttributeTips/AtkGroup/Rate/Value",type = "Text"},
    bigHeroAtkText = {path = "TipsObj/darkMask/AttributeTips/AtkGroup/HeroAtk/Value",type = "Text"},
    bigDefText = {path = "TipsObj/darkMask/AttributeTips/DefGroup/DefPanel/Value",type = "Text"},
    bigDefRateText = {path = "TipsObj/darkMask/AttributeTips/DefGroup/Rate/Value",type = "Text"},
    bigHeroDefText = {path = "TipsObj/darkMask/AttributeTips/DefGroup/HeroDef/Value",type = "Text"},

    bigUpgradeTips = {path = "TipsObj/darkMask/UpgradeTips",type = "RectTransform"},
    bigUpgradeAtkOldValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atk/ValueParent/OldValue",type = "Text"},
    bigUpgradeAtkNewValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atk/ValueParent/NewValue",type = "Text"},
    bigUpgradeHpOldValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hp/ValueParent/OldValue",type = "Text"},
    bigUpgradeHpNewValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hp/ValueParent/NewValue",type = "Text"},
    bigUpgradeDefOldValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_def/ValueParent/OldValue",type = "Text"},
    bigUpgradeDefNewValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_def/ValueParent/NewValue",type = "Text"},
    bigUpgradeHpRateObj = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hpRate",type = "RectTransform"},
    bigUpgradeAtkRateObj = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atkRate",type = "RectTransform"},
    bigUpgradeDefRateObj = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_defRate",type = "RectTransform"},
    bigUpgradeHpRateOldValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hpRate/ValueParent/OldValue",type = "Text"},
    bigUpgradeHpRateNewValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hpRate/ValueParent/NewValue",type = "Text"},
    bigUpgradeAtkRateOldValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atkRate/ValueParent/OldValue",type = "Text"},
    bigUpgradeAtkRateNewValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atkRate/ValueParent/NewValue",type = "Text"},
    bigUpgradeDefRateOldValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_defRate/ValueParent/OldValue",type = "Text"},
    bigUpgradeDefRateNewValue = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_defRate/ValueParent/NewValue",type = "Text"},

    bigUpgradeAtkValueParent = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atk/ValueParent",type = "RectTransform"},
    bigUpgradeHpValueParent = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hp/ValueParent",type = "RectTransform"},
    bigUpgradeDefValueParent = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_def/ValueParent",type = "RectTransform"},
    bigUpgradeAtkRateValueParent = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atkRate/ValueParent",type = "RectTransform"},
    bigUpgradeHpRateValueParent = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hpRate/ValueParent",type = "RectTransform"},
    bigUpgradeDefRateValueParent = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_defRate/ValueParent",type = "RectTransform"},

    bigUpgradeSkillTipsIcon = {path = "TipsObj/darkMask/UpgradeTips/SkillIcon/mask/icon",type = "Image"},
    bigUpgradeSkillTipsStar = {path = "TipsObj/darkMask/UpgradeTips/star_root",type = "RectTransform"},
    bigUpgradeSkillTipsStarObj_1 = {path = "TipsObj/darkMask/UpgradeTips/star_root/star_1",type = "RectTransform"},
    bigUpgradeSkillTipsStarObj_2 = {path = "TipsObj/darkMask/UpgradeTips/star_root/star_2",type = "RectTransform"},
    bigUpgradeSkillTipsStarObj_3 = {path = "TipsObj/darkMask/UpgradeTips/star_root/star_3",type = "RectTransform"},
    bigUpgradeSkillTipsStarObj_4 = {path = "TipsObj/darkMask/UpgradeTips/star_root/star_4",type = "RectTransform"},
    bigUpgradeSkillTipsStarObj_5 = {path = "TipsObj/darkMask/UpgradeTips/star_root/star_5",type = "RectTransform"},
    bigUpgradeSkillTipsDesc = {path = "TipsObj/darkMask/UpgradeTips/SkillDesc",type = "Text"},

    bigUpgradeSkillTipsStarGroup = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star",type = "RectTransform"},

    bigUpgradeSkillTipsStarInfoObj_1 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_1",type = "RectTransform"},
    bigUpgradeSkillTipsEmptyStar_1 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_1/emptyStar",type = "RectTransform"},
    bigUpgradeSkillTipsStar_1 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_1/Star",type = "RectTransform"},
    bigUpgradeSkillTipsStarDesc_1 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_1/Text",type = "Text"},
    bigUpgradeSkillTipsStarDescRect_1 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_1/Text",type = "RectTransform"},

    bigUpgradeSkillTipsStarInfoObj_2 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_2",type = "RectTransform"},
    bigUpgradeSkillTipsEmptyStar_2 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_2/emptyStar",type = "RectTransform"},
    bigUpgradeSkillTipsStar_2 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_2/Star",type = "RectTransform"},
    bigUpgradeSkillTipsStarDesc_2 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_2/Text",type = "Text"},
    bigUpgradeSkillTipsStarDescRect_2 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_2/Text",type = "RectTransform"},

    bigUpgradeSkillTipsStarInfoObj_3 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_3",type = "RectTransform"},
    bigUpgradeSkillTipsEmptyStar_3 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_3/emptyStar",type = "RectTransform"},
    bigUpgradeSkillTipsStar_3 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_3/Star",type = "RectTransform"},
    bigUpgradeSkillTipsStarDesc_3 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_3/Text",type = "Text"},
    bigUpgradeSkillTipsStarDescRect_3 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_3/Text",type = "RectTransform"},

    bigUpgradeSkillTipsStarInfoObj_4 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_4",type = "RectTransform"},
    bigUpgradeSkillTipsEmptyStar_4 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_4/emptyStar",type = "RectTransform"},
    bigUpgradeSkillTipsStar_4 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_4/Star",type = "RectTransform"},
    bigUpgradeSkillTipsStarDesc_4 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_4/Text",type = "Text"},
    bigUpgradeSkillTipsStarDescRect_4 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_4/Text",type = "RectTransform"},

    bigUpgradeSkillTipsStarInfoObj_5 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_5",type = "RectTransform"},
    bigUpgradeSkillTipsEmptyStar_5 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_5/emptyStar",type = "RectTransform"},
    bigUpgradeSkillTipsStar_5 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_5/Star",type = "RectTransform"},
    bigUpgradeSkillTipsStarDesc_5 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_5/Text",type = "Text"},
    bigUpgradeSkillTipsStarDescRect_5 = {path = "TipsObj/darkMask/UpgradeTips/DescScrollView/Viewport/Content/Star/StarSkill_5/Text",type = "RectTransform"},

    bigUpgradeNormalUpgradeObj = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade",type = "RectTransform"},
    bigUpgradeSpecialUpgradeObj = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade",type = "RectTransform"},

    bigUpgradeNormalLevelText = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/LevelBg/Text",type = "Text"},
    bigUpgradeNormalExpSlider = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/ExpSlider",type = "Slider"},
    bigUpgradeNormalExpValueDenominator = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/ExpValue",type = "Text"},
    bigUpgradeNormalExpValue = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/ExpValue/ExpValue",type = "Text"},
    bigUpgradeNormalUpgradeBtn = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/Upgrade",type = "Button",event_name = "UpgradeWeapon"},--,interval_event = {"normalUpgradeWeapon",0.5}},--event_name = "UpgradeWeapon"},
    bigUpgradeNormalBaseItemIcon = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/baseItem",type = "Image"},
    bigUpgradeNormalBaseItemIconBtn = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/baseItem",type = "Button",event_name = "ShowNormalUpgradeItemTips"},
    bigUpgradeNormalBaseItemCount = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/baseItem/Count",type = "Text"},
    bigUpgradeNormalSpecialItemIcon = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/SpecialItem",type = "Image"},
    bigUpgradeNormalSpecialItemIconBtn = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/SpecialItem",type = "Button",event_name = "ShowSpecialUpgradeItemTips"},
    bigUpgradeNormalSpecialItemCount = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/SpecialItem/Count",type = "Text"},

    bigUpgradeSpecialLevelText = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/LevelBg/Text",type = "Text"},
    bigUpgradeSpecialExpSliderObj_1 = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpSlider/Item_1/Fill",type = "Image"},
    bigUpgradeSpecialExpSliderObj_2 = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpSlider/Item_2/Fill",type = "Image"},
    bigUpgradeSpecialExpSliderObj_3 = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpSlider/Item_3/Fill",type = "Image"},
    bigUpgradeSpecialExpSliderObj_4 = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpSlider/Item_4/Fill",type = "Image"},
    bigUpgradeSpecialExpSliderObj_5 = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpSlider/Item_5/Fill",type = "Image"},
    bigUpgradeSpecialExpValue = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpValue",type = "Text"},
    bigUpgradeSpecialUpgradeBtn = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/Upgrade",type = "Button",event_name ="UpgradeWeapon" },--,interval_event = {"specialUpgradeWeapon",1.2}},--event_name = "UpgradeWeapon"},
    bigUpgradeSpecialBaseItemIcon = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ItemGroup/baseItemTrans/baseItem",type = "Image"},
    bigUpgradeSpecialBaseItemIconBtn = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ItemGroup/baseItemTrans/baseItem",type = "Button",event_name = "ShowNormalUpgradeItemTips"},
    bigUpgradeSpecialBaseItemCount = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ItemGroup/baseItemTrans/Count",type = "Text"},
    bigUpgradeSpecialSpecialItemIcon = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ItemGroup/SpecialItemTrans/SpecialItem",type = "Image"},
    bigUpgradeSpecialSpecialItemIconBtn = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ItemGroup/SpecialItemTrans/SpecialItem",type = "Button",event_name = "ShowSpecialUpgradeItemTips"},
    bigUpgradeSpecialSpecialItemCount = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ItemGroup/SpecialItemTrans/Count",type = "Text"},

    equipSlotObj = {path = "Info/EquipSlot",type = "RectTransform"},

    equipSlot1Quality = {path = "Info/EquipSlot/Bg/Slot_1",type = SpriteSwitcher},
    equipSlot1Btn = {path = "Info/EquipSlot/Bg/Slot_1",type = "Button",event_name = "OnEquipFirstSlotClick"},
    equipSlot1Lv = {path = "Info/EquipSlot/Bg/Slot_1/LvBg/Text",type = "Text"},
    equipSlot1Icon = {path = "Info/EquipSlot/Bg/Slot_1/Icon",type = "Image"},

    equipSlot2Quality = {path = "Info/EquipSlot/Bg/Slot_2",type = SpriteSwitcher},
    equipSlot2Btn = {path = "Info/EquipSlot/Bg/Slot_2",type = "Button",event_name = "OnEquipSecondSlotClick"},
    equipSlot2Lv = {path = "Info/EquipSlot/Bg/Slot_2/LvBg/Text",type = "Text"},
    equipSlot2Icon = {path = "Info/EquipSlot/Bg/Slot_2/Icon",type = "Image"},

    equipSlot3Quality = {path = "Info/EquipSlot/Bg/Slot_3",type = SpriteSwitcher},
    equipSlot3Btn = {path = "Info/EquipSlot/Bg/Slot_3",type = "Button",event_name = "OnEquipThirdSlotClick"},
    equipSlot3Lv = {path = "Info/EquipSlot/Bg/Slot_3/LvBg/Text",type = "Text"},
    equipSlot3Icon = {path = "Info/EquipSlot/Bg/Slot_3/Icon",type = "Image"},

    equipSlot4Quality = {path = "Info/EquipSlot/Bg_1/Slot_4",type = SpriteSwitcher},
    equipSlot4Btn = {path = "Info/EquipSlot/Bg_1/Slot_4",type = "Button",event_name = "OnEquipFourthSlotClick"},
    equipSlot4Lv = {path = "Info/EquipSlot/Bg_1/Slot_4/LvBg/Text",type = "Text"},
    equipSlot4Icon = {path = "Info/EquipSlot/Bg_1/Slot_4/Icon",type = "Image"},

    equipSlot5Quality = {path = "Info/EquipSlot/Bg_1/Slot_5",type = SpriteSwitcher},
    equipSlot5Btn = {path = "Info/EquipSlot/Bg_1/Slot_5",type = "Button",event_name = "OnEquipFifthSlotOneClick"},
    equipSlot5Lv = {path = "Info/EquipSlot/Bg_1/Slot_5/LvBg/Text",type = "Text"},
    equipSlot5Icon = {path = "Info/EquipSlot/Bg_1/Slot_5/Icon",type = "Image"},

    equipSlot6Quality = {path = "Info/EquipSlot/Bg_1/Slot_6",type = SpriteSwitcher},
    equipSlot6Btn = {path = "Info/EquipSlot/Bg_1/Slot_6",type = "Button",event_name = "OnEquipSixthSlotClick"},
    equipSlot6Lv = {path = "Info/EquipSlot/Bg_1/Slot_6/LvBg/Text",type = "Text"},
    equipSlot6Icon = {path = "Info/EquipSlot/Bg_1/Slot_6/Icon",type = "Image"},

    equipmentAttributeObj = {path = "Info/Equipment",type = "RectTransform"},

    equipmentWeaponHpValue = {path = "Info/Equipment/Bg/attributeList/weaponHp/Value",type = "Text"},
    equipmentWeaponAtkValue = {path = "Info/Equipment/Bg/attributeList/weaponAtk/Value",type = "Text"},
    equipmentWeaponDefValue = {path = "Info/Equipment/Bg/attributeList/weaponDef/Value",type = "Text"},
    equipmentHeroHpValue = {path = "Info/Equipment/Bg/attributeList/heroHp/Value",type = "Text"},
    equipmentHeroAtkValue = {path = "Info/Equipment/Bg/attributeList/heroAtk/Value",type = "Text"},
    equipmentHeroDefValue = {path = "Info/Equipment/Bg/attributeList/heroDef/Value",type = "Text"},
    equipmentDamageRateValue = {path = "Info/Equipment/Bg/attributeList/damageRate/Value",type = "Text"},

    upgradeRedDot = {path = "Info/Attribute/NormalUpgrade/Upgrade/RedDot",type = "RectTransform"},
    upgradeRedDot_2 = {path = "Info/Attribute/SpecialUpgrade/Upgrade/RedDot",type = "RectTransform"},
    upgradeRedDot_3 = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/Upgrade/RedDot",type = "RectTransform"},
    upgradeRedDot_4 = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/Upgrade/RedDot",type = "RectTransform"},

    maxLevelObj = {path = "Info/Attribute/MaxLevel",type = "RectTransform"},
    maxLevelText = {path = "Info/Attribute/MaxLevel/LevelBg/Text",type = "Text"},

    equipmentSlotRedDot_1 = {path = "Info/EquipSlot/Bg/Slot_1/RedDot",type = "RectTransform"},
    equipmentSlotRedDot_2 = {path = "Info/EquipSlot/Bg/Slot_2/RedDot",type = "RectTransform"},
    equipmentSlotRedDot_3 = {path = "Info/EquipSlot/Bg/Slot_3/RedDot",type = "RectTransform"},
    equipmentSlotRedDot_4 = {path = "Info/EquipSlot/Bg_1/Slot_4/RedDot",type = "RectTransform"},
    equipmentSlotRedDot_5 = {path = "Info/EquipSlot/Bg_1/Slot_5/RedDot",type = "RectTransform"},
    equipmentSlotRedDot_6 = {path = "Info/EquipSlot/Bg_1/Slot_6/RedDot",type = "RectTransform"},

    modelBgParent = {path = "bgParent",type = "RectTransform"},
    attributeTipsArrow = {path = "Info/Attribute/AttributeTips/arrow",type = "RectTransform"},

    specialExpUpObj = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpValue/expUp",type = "RectTransform"},
    specialExpUpText = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/ExpValue/expUp/Text",type = "Text"},

    expObjTrans = {path = "TipsObj/darkMask/UpgradeTips/ExpUpTipsParent",type = "RectTransform"},
    normalExpUpObj = {path = "TipsObj/darkMask/UpgradeTips/ExpUpTipsParent/ExpUpTips",type = Animation},

    normalCriticalExpUpTips = {path = "TipsObj/darkMask/UpgradeTips/ExpUpTipsParent/CriticalTips",type = Animation},
    equipSlotAnim = {path = "Info/EquipSlot",type = Animation},

    attributeObj_atk_particle = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atk/Effect_UI_zhunbeiqianghua_citiao",type = "RectTransform"},
    attributeObj_hp_particle = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hp/Effect_UI_zhunbeiqianghua_citiao",type = "RectTransform"},
    attributeObj_def_particle = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_def/Effect_UI_zhunbeiqianghua_citiao",type = "RectTransform"},
    attributeObj_atkRate_particle = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_atkRate/Effect_UI_zhunbeiqianghua_citiao",type = "RectTransform"},
    attributeObj_hpRate_particle = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_hpRate/Effect_UI_zhunbeiqianghua_citiao",type = "RectTransform"},
    attributeObj_defRate_particle = {path = "TipsObj/darkMask/UpgradeTips/AttributeObj_defRate/Effect_UI_zhunbeiqianghua_citiao",type = "RectTransform"},

    normalUpgradeNormalTxt = {path = "Info/Attribute/NormalUpgrade/Upgrade/Text",type = "RectTransform"},
    normalUpgradeGrayTxt = {path = "Info/Attribute/NormalUpgrade/Upgrade/TextGray",type = "RectTransform"},
    specialUpgradeNormalTxt = {path = "Info/Attribute/SpecialUpgrade/Upgrade/Text",type = "RectTransform"},
    specialUpgradeGrayTxt = {path = "Info/Attribute/SpecialUpgrade/Upgrade/GrayText",type = "RectTransform"},
    normalBigUpgradeNormalTxt = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/Upgrade/Text",type = "RectTransform"},
    normalBigUpgradeGrayTxt = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/Upgrade/TextGray",type = "RectTransform"},
    specialBigUpgradeNormalTxt = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/Upgrade/Text",type = "RectTransform"},
    specialBigUpgradeGrayTxt = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/Upgrade/TextGray",type = "RectTransform"},

    normalUpgradeGray = {path = "Info/Attribute/NormalUpgrade/Upgrade",type = ImageGray},
    specialUpgradeGray = {path = "Info/Attribute/SpecialUpgrade/Upgrade",type = ImageGray},
    normalBigUpgradeGray = {path = "TipsObj/darkMask/UpgradeTips/NormalUpgrade/Upgrade",type = ImageGray},
    specialBigUpgradeGray = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/Upgrade",type = ImageGray},

    specialUpgradeObjTran = {path = "Info/Attribute/SpecialUpgrade/CostItemTrans",type = "RectTransform"},
    bigSpecialUpgradeObjTran = {path = "TipsObj/darkMask/UpgradeTips/SpecialUpgrade/CostItemTrans",type = "RectTransform"},

    changeOutlookBtn = {path = "ChangeOutlook",type = "Button",event_name = "OnChangeOutlookClick"},
    attributeObj = {path = "Info/Equipment/Bg/attributeList/attributeObj",type = GameObject},
    attributeTrans = {path = "Info/Equipment/Bg/attributeList",type = "RectTransform"},
}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self.toggleList = {
        [pageShowTypeEnum.Attribute] = self.attributeToggle,
        [pageShowTypeEnum.Equipment] = self.equipmentToggle,
        [pageShowTypeEnum.Combat] = self.combatToggle,
        [pageShowTypeEnum.Crystal] = self.crystalToggle,
    }
    
    self.attributeObjList = {} --生成的属性物体
    
    self:SubscribeEvents()
    self.cardSpriteAsset = card_sprite_asset.CreateSpriteAsset()
    self.cutExp = 0 --存储当前的经验值，用来计算本次涨了多少
    self.cutStar = 0

    self.costItemList = {}
    self.costItemObjList = {}
    self.layoutItemList = {}

    self.upgradeObjList = {}
    self.emptyUpgradeObjList = {}
    self.upgradeObjCount = 0
    self.normalCriticalObjList = {}
    self.emptyNormalCriticalObjList = {}
    self.normalCriticalObjCount = 0

    self.isUpgrade = false

    self.isOnAnim = false

    self.heroAddAtk = 0;
    self.heroAddHp = 0;
    self.heroAddDef = 0;

    self.baseScale = Vector3(1,1,1)
    self.bigScale = Vector3(1.3,1.3,1.3)
    self.totalScaleTime = 0.25
    self.startScale = 0.15
    self.finishScale = 0.05

    --self.tweenId = -1 --储存leantween的Id，默认为-1，表示没有。
    --self.tween = nil --测试用
    --self.equipmentSpriteAsset = card_sprite_asset.CreateDroneEquipmentAsset()

    self.TimeTicker = nil --计时器，不用leantween了
    self.scaleTimeTicker = nil;--文字放大缩小计时器
    self.expTimeTicker = nil;

    self.uiCamera = GameObject.Find("UIRoot/UICamera"):GetComponent(typeof(Camera))

    local droneId = GWG.GWHomeMgr.droneData.GetDroneId()
    local baseCfg = game_scheme:MagicWeapon_0(droneId,1,1,0);
    if baseCfg then
        local item = game_scheme:Item_0(baseCfg.costGoods.data[0])
        if item then
            self.cardSpriteAsset:GetSprite(item.icon,function(sprite)
                self.specialBaseItemIcon.sprite = sprite
                self.normalBaseItemIcon.sprite = sprite
                self.bigUpgradeNormalBaseItemIcon.sprite = sprite
                self.bigUpgradeSpecialBaseItemIcon.sprite = sprite
            end)
        end
    end

    local specialCfg = game_scheme:MagicWeapon_0(droneId,1,5,0);
    if specialCfg then
        local item2 = game_scheme:Item_0(specialCfg.costGoods.data[1])
        if item2 then
            self.cardSpriteAsset:GetSprite(item2.icon,function(sprite)
                self.specialSpecialItemIcon.sprite = sprite
                self.normalSpecialItemIcon.sprite = sprite
                self.bigUpgradeNormalSpecialItemIcon.sprite = sprite
                self.bigUpgradeSpecialSpecialItemIcon.sprite = sprite
            end)
        end
    end

    self.specialExpSliderObj =
    {
        [1] = self.specialExpSliderObj_1,
        [2] = self.specialExpSliderObj_2,
        [3] = self.specialExpSliderObj_3,
        [4] = self.specialExpSliderObj_4,
        [5] = self.specialExpSliderObj_5
    }

    self.bigTipsSpecialExpSliderObj =
    {
        [1] = self.bigUpgradeSpecialExpSliderObj_1,
        [2] = self.bigUpgradeSpecialExpSliderObj_2,
        [3] = self.bigUpgradeSpecialExpSliderObj_3,
        [4] = self.bigUpgradeSpecialExpSliderObj_4,
        [5] = self.bigUpgradeSpecialExpSliderObj_5
    }

    self.skillStarObj =
    {
        [1] = self.skillStar_1,
        [2] = self.skillStar_2,
        [3] = self.skillStar_3,
        [4] = self.skillStar_4,
        [5] = self.skillStar_5,
    }

    self.skillTipsStarList =
    {
        [1] = self.skillTipsStarObj_1,
        [2] = self.skillTipsStarObj_2,
        [3] = self.skillTipsStarObj_3,
        [4] = self.skillTipsStarObj_4,
        [5] = self.skillTipsStarObj_5,
    }

    self.bigSkillTipsStarList =
    {
        [1] = self.bigUpgradeSkillTipsStarObj_1,
        [2] = self.bigUpgradeSkillTipsStarObj_2,
        [3] = self.bigUpgradeSkillTipsStarObj_3,
        [4] = self.bigUpgradeSkillTipsStarObj_4,
        [5] = self.bigUpgradeSkillTipsStarObj_5,
    }

    self.skillStarTipsList =
    {
        [1] =
        {
            starObj = self.skillTipsStar_1,
            starDesc = self.skillTipsStarDesc_1,
            starDescTran = self.skillTipsStarDescRect_1
        },
        [2] =
        {
            starObj = self.skillTipsStar_2,
            starDesc = self.skillTipsStarDesc_2,
            starDescTran = self.skillTipsStarDescRect_2
        },
        [3] =
        {
            starObj = self.skillTipsStar_3,
            starDesc = self.skillTipsStarDesc_3,
            starDescTran = self.skillTipsStarDescRect_3
        },
        [4] =
        {
            starObj = self.skillTipsStar_4,
            starDesc = self.skillTipsStarDesc_4,
            starDescTran = self.skillTipsStarDescRect_4
        },
        [5] =
        {
            starObj = self.skillTipsStar_5,
            starDesc = self.skillTipsStarDesc_5,
            starDescTran = self.skillTipsStarDescRect_5
        },
    }

    self.bigSkillStarTipsList =
    {
        [1] =
        {
            starObj = self.bigUpgradeSkillTipsStar_1,
            starDesc = self.bigUpgradeSkillTipsStarDesc_1,
            starDescTran = self.bigUpgradeSkillTipsStarDescRect_1
        },
        [2] =
        {
            starObj = self.bigUpgradeSkillTipsStar_2,
            starDesc = self.bigUpgradeSkillTipsStarDesc_2,
            starDescTran = self.bigUpgradeSkillTipsStarDescRect_2
        },
        [3] =
        {
            starObj = self.bigUpgradeSkillTipsStar_3,
            starDesc = self.bigUpgradeSkillTipsStarDesc_3,
            starDescTran = self.bigUpgradeSkillTipsStarDescRect_3
        },
        [4] =
        {
            starObj = self.bigUpgradeSkillTipsStar_4,
            starDesc = self.bigUpgradeSkillTipsStarDesc_4,
            starDescTran = self.bigUpgradeSkillTipsStarDescRect_4
        },
        [5] =
        {
            starObj = self.bigUpgradeSkillTipsStar_5,
            starDesc = self.bigUpgradeSkillTipsStarDesc_5,
            starDescTran = self.bigUpgradeSkillTipsStarDescRect_5
        },
    }

    self.slotList =
    {
        [1] =
        {
            quality = self.equipSlot1Quality,
            lv = self.equipSlot1Lv,
            icon = self.equipSlot1Icon,
            redDot = self.equipmentSlotRedDot_1,
        },
        [2] =
        {
            quality = self.equipSlot2Quality,
            lv = self.equipSlot2Lv,
            icon = self.equipSlot2Icon,
            redDot = self.equipmentSlotRedDot_2,
        },
        [3] =
        {
            quality = self.equipSlot3Quality,
            lv = self.equipSlot3Lv,
            icon = self.equipSlot3Icon,
            redDot = self.equipmentSlotRedDot_3,
        },
        [4] =
        {
            quality = self.equipSlot4Quality,
            lv = self.equipSlot4Lv,
            icon = self.equipSlot4Icon,
            redDot = self.equipmentSlotRedDot_4,
        },
        [5] =
        {
            quality = self.equipSlot5Quality,
            lv = self.equipSlot5Lv,
            icon = self.equipSlot5Icon,
            redDot = self.equipmentSlotRedDot_5,
        },
        [6] =
        {
            quality = self.equipSlot6Quality,
            lv = self.equipSlot6Lv,
            icon = self.equipSlot6Icon,
            redDot = self.equipmentSlotRedDot_6,
        },
    }
    self.upgradeParticleList =
    {
        [1] = self.attributeObj_atk_particle,
        [2] = self.attributeObj_hp_particle,
        [3] = self.attributeObj_def_particle,
        [4] = self.attributeObj_hpRate_particle,
        [5] = self.attributeObj_atkRate_particle,
        [6] = self.attributeObj_defRate_particle,
    };
    self.upgradeValueParent =
    {
        [1] = self.bigUpgradeAtkValueParent,
        [2] = self.bigUpgradeHpValueParent,
        [3] = self.bigUpgradeDefValueParent,
        [4] = self.bigUpgradeHpRateValueParent,
        [5] = self.bigUpgradeAtkRateValueParent,
        [6] = self.bigUpgradeDefRateValueParent,
    };
    local offset = 0
    local maxOffset = 600
    local flag = util.Flag_ExternScreen()
    if flag then
        offset = -60
    end
    local screenSize = tonumber(string.format("%.4f", screen_util.width / screen_util.height));
    local offsetX = (screenSize - 0.4615) * 2971.47
    if offsetX > maxOffset then
        offsetX = maxOffset
    end
    Common_Util.SetLocalPos(self.modelBgParent, 0, offsetX - offset)
    Common_Util.SetLocalPos(self.equipSlotObj,0,-offset)
    self.modelBgParent.offsetMin = {x = 0 , y = 0} --把底部拉回来，以防止露馅
    self.equipSlotObj.offsetMin = {x = 0 , y = 0}
    self.changeAttribute = {}
    self.saveAttribute = {}
    self.expGrowUp = ui_growup_number.Init(self.bigUpgradeNormalExpValue, true, "ui_new_magic_weapon", false,self.bigUpgradeNormalExpValueDenominator)

    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
    self:BindUIRed(self.crystalToggle.transform, red_const.Enum.NewMagicWeaponCrystalTabRed,nil,{redPath = red_const.Type.Default,pos = { x = 36 ,y = 44 }})
    self:BindUIRed(self.combatToggle.transform, red_const.Enum.NewMagicWeaponCombatTabRed,nil,{redPath = red_const.Type.Default,pos = { x = 36 ,y = 44 }})
    self:BindUIRed(self.btn_crtstalBox.transform, red_const.Enum.NewMagicWeaponCrystalBoxRed,nil,{redPath = red_const.Type.Default,pos = { x = 32 ,y = 32 }})
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    if self.cardSpriteAsset then
        self.cardSpriteAsset:Dispose()
        self.cardSpriteAsset = nil
    end
    if self.expGrowUp then
        self.expGrowUp:Destroy()
        self.expGrowUp = nil
    end
    if self.scaleTimeTicker then
        util.RemoveDelayCall(self.scaleTimeTicker)
        self.scaleTimeTicker = nil
    end
    if self.TimeTicker then
        util.RemoveDelayCall(self.TimeTicker)
        self.TimeTicker = nil
    end
    if self.expTimeTicker then
        util.RemoveDelayCall(self.expTimeTicker)
        self.expTimeTicker = nil
    end
    self.isOnAnim = false
    if self.model then
        self.model:Dispose()
    end
    for i,v in ipairs(self.costItemList) do
        v:Dispose()
        v = nil;
    end
    for i,v in ipairs(self.costItemObjList) do
        if not util.IsObjNull(v) then
            v.onClick:RemoveAllListeners()
        end
        v = nil;
    end
    self.layoutItemList = {}
    for k,v in pairs(self.upgradeObjList) do
        GameObject.Destroy(v.obj)
        if v.timer then
            util.RemoveDelayCall(v.timer)
            v.timer = nil
        end
        self.upgradeObjList[k] = nil
    end
    for k,v in pairs(self.normalCriticalObjList) do
        GameObject.Destroy(v.obj)
        if v.timer then
            util.RemoveDelayCall(v.timer)
            v.timer = nil
        end
        self.normalCriticalObjList[k] = nil
    end
	if self.OverLayItem then 
        self.OverLayItem:Close()
        self.OverLayItem = nil
    end
    self.__base:Close()
    self:UnsubscribeEvents()
    window = nil
    --if self.equipmentSpriteAsset then
    --    self.equipmentSpriteAsset:Dispose()
    --    self.equipmentSpriteAsset = nil
    --end
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了   

    --self:RegisterEvent(event.UPDATE_MAGIC_WEAPON,self.updatePanel)
    self.normalUpgradeWeapon = function()
        if self.isOnAnim then
            --TODO 没有lang
            --flow_text.Add("放轻松指挥官，我已经执行过这个命令。（点击频繁提示，缺少lang）")--飘字提示
        else
            e_handler_mgr.TriggerHandler(self.controller_name,"UpgradeWeapon");
        end
    end
    self.specialUpgradeWeapon = function()
        e_handler_mgr.TriggerHandler(self.controller_name,"UpgradeWeapon");
    end
end
function UIView:UnsubscribeEvents()

end

--endregion

--region 功能函数区
---********************功能函数区**********---

function UIView:OnUpdateItem()
    local showEquipmentRedDot = false
    if not equipmentLock then
        local mEquipList = GWG.GWHomeMgr.droneData.OnGetEquipLevelList()
        for i,v in ipairs(mEquipList) do
            self.slotList[i].lv.text = "Lv."..v.lv
            local quality = v.lv;
            if quality >= 7 then
                if quality < 9 then
                    quality = 7
                else
                    quality = 8
                end
            end
            Common_Util.SetActive(self.slotList[i].redDot,v.redDot)
            if v.redDot then
                showEquipmentRedDot = true
            end
            --self.slotList[i].quality:Switch(quality)
            local equipCfg = GWG.GWHomeMgr.droneData.GetEquipCfgData(i)
            local index = i
            --local iconName = index.."_"..quality
            self.cardSpriteAsset:GetSprite(equipCfg.icon,function(sprite)
                self.slotList[index].icon.sprite = sprite
            end)
        end
        
        local attributeList = GWG.GWHomeMgr.droneData.OnGetAllEquipAttributeValue()
        for i,v in pairs(attributeList) do
            if not ignoreAttribute[i] then
                local cfg = game_scheme:ProToLang_0(i)
                if cfg then
                    if not self.attributeObjList[i] then
                        local go1 = GameObject.Instantiate(self.attributeObj,self.attributeTrans)
                        self.attributeObjList[i] = Common_Util.GetComponent(go1.transform,typeof(ScrollRectItem),"")
                    end
                    Common_Util.SetActive(self.attributeObjList[i],true)
                    local Title = self.attributeObjList[i]:Get("Title")
                    local Value = self.attributeObjList[i]:Get("Value")
                    Title.text = lang.Get(cfg.iLangId)
                    if cfg.ProType == 2 then
                        Value.text = string.format("%g%%",v / 100);
                    elseif cfg.ProType == 3 then
                        Value.text = v / 10000;
                    elseif cfg.ProType == 4 then
                        local time_util = require "time_util"
                        Value.text = time_util.GetTimeStrBySecond(v)
                    else
                        Value.text = v
                    end
                end
            end
        end
        for i,v in pairs(self.attributeObjList) do
            if ignoreAttribute[i] then
                Common_Util.SetActive(v,false)
            end
            if not attributeList[i] then
                Common_Util.SetActive(v,false)
            end
        end
        --1 2 3依次为血量、攻击、防御。如果后续有变化再改
        self.equipmentWeaponHpValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(29)
        self.equipmentHeroHpValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(101)
        self.equipmentWeaponAtkValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(30)
        self.equipmentHeroAtkValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(102)
        self.equipmentWeaponDefValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(31)
        self.equipmentHeroDefValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(103)
        self.equipmentDamageRateValue.text = GWG.GWHomeMgr.droneData.OnGetEquipAttributeValue(27) / 100 .."%"

        LayoutRebuilder.ForceRebuildLayoutImmediate(self.attributeTrans)
    end

    Common_Util.SetActive(self.equipmentToggleRedPoint,showEquipmentRedDot)
end

function UIView:OnUpdatePanel(isInit)
    self.weaponId = GWG.GWHomeMgr.droneData.GetDroneId()
    self.weaponLevel = GWG.GWHomeMgr.droneData.OnGetDroneLv()
    self.weaponUpgradeStage = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_UPGRADESTAGE)
    self.weaponCfg = GWG.GWHomeMgr.droneData.OnGetDroneCfgData()--game_scheme:MagicWeapon_0(self.weaponId,1,self.weaponLevel,self.weaponUpgradeStage)
    self.weaponSkin = GWG.GWHomeMgr.droneData.GetCurAdroneSkin()
    self.weaponSkinName = GWG.GWHomeMgr.droneData.GetCurAdroneSkinName()
    --神兽之地解锁条件
    local functionOpen = game_scheme:FunctionOpen_0(1202)
    local buildInfo = cfg_util.StringToArray(functionOpen.iBuildingID, "#")
    --local buildingCfg = game_scheme:BuildingType_0(tonumber(buildInfo[1])/1000)
    local buildingInfo = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(tonumber(buildInfo[1]))
    local equipmentLock = false
    if buildingInfo then
        equipmentLock = buildingInfo.nLevel < tonumber(buildInfo[2])
    else
        equipmentLock = true
    end
    local mEquipList = GWG.GWHomeMgr.droneData.OnGetEquipLevelList()
    if not equipmentLock then
        for i=1,6 do
            if not mEquipList[i] then
                equipmentLock = true
                break;
            end
        end
    end

    --神兽装扮解锁
    -- local functionOpenExterior = game_scheme:FunctionOpen_0(1203)
    -- local buildInfoExterior = cfg_util.StringToArray(functionOpenExterior.iBuildingID, "#")
    -- local buildingInfoExterior = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(tonumber(buildInfoExterior[1]))
    -- local lockExterior =false
    -- if buildingInfoExterior then
    --     print("buildingInfoExterior") 
    --     dump(buildingInfoExterior)
    --     lockExterior = buildingInfoExterior.nLevel <= tonumber(buildInfoExterior[2])
    -- else    
    --     lockExterior =true
    -- end
    -- self:SetActive(self.changeOutlookBtn,lockExterior) 

    -- local functionOpenExterior = game_scheme:FunctionOpen_0(1203)
    -- local buildInfoExterior = cfg_util.StringToArray(functionOpenExterior.iBuildingID, "#")
    -- local buildingInfoExterior = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(tonumber(buildInfoExterior[1]))
    -- local lockExterior =false
    -- print("IsOpenAnimal")
    -- dump(buildInfoExterior)
    -- dump(buildingInfoExterior.nLevel)


    Common_Util.SetActive(self.equipmentToggleLockBtn,equipmentLock)
    self.equipmentToggleGray:SetEnable(equipmentLock)
    self.equipmentToggle.interactable = not equipmentLock

    local upgradeType = self.weaponCfg.UpgradeType --为1表示非突破升级

    local nextLevelWeaponCfg = GWG.GWHomeMgr.droneData.OnGetNextDroneCfgData()
    local preLevelCfg = GWG.GWHomeMgr.droneData.GetPreDroneCfgData() --获取上一级的等级

    local showRedDot = GWG.GWHomeMgr.droneData.CheckUpgradeRedDot()
    Common_Util.SetActive(self.upgradeRedDot,showRedDot)
    Common_Util.SetActive(self.upgradeRedDot_2,showRedDot)
    Common_Util.SetActive(self.upgradeRedDot_3,showRedDot)
    Common_Util.SetActive(self.upgradeRedDot_4,showRedDot)

    Common_Util.SetActive(self.normalUpgradeNormalTxt,showRedDot)
    Common_Util.SetActive(self.specialUpgradeNormalTxt,showRedDot)
    Common_Util.SetActive(self.normalBigUpgradeNormalTxt,showRedDot)
    Common_Util.SetActive(self.specialBigUpgradeNormalTxt,showRedDot)

    Common_Util.SetActive(self.normalUpgradeGrayTxt,not showRedDot)
    Common_Util.SetActive(self.specialUpgradeGrayTxt,not showRedDot)
    Common_Util.SetActive(self.normalBigUpgradeGrayTxt,not showRedDot)
    Common_Util.SetActive(self.specialBigUpgradeGrayTxt,not showRedDot)

    self.normalUpgradeGray:SetEnable(not showRedDot)
    self.specialUpgradeGray:SetEnable(not showRedDot)
    self.normalBigUpgradeGray:SetEnable(not showRedDot)
    self.specialBigUpgradeGray:SetEnable(not showRedDot)

    Common_Util.SetActive(self.attributeToggleRedPoint,showRedDot)

    Common_Util.SetActive(self.normalUpgradeObj,upgradeType == 1)
    Common_Util.SetActive(self.specialUpgradeObj,upgradeType == 2)

    self.nameTxt.text = lang.Get(self.weaponSkinName or self.weaponCfg.weaponNameID)

    self.skillName.text = lang.Get(self.weaponCfg.skillNameID)
    self.skillTipsName.text = lang.Get(self.weaponCfg.skillNameID)
    self.skillTipsDesc.text = lang.Get(self.weaponCfg.skillDecID)--.."\n"..lang.Get(self.weaponCfg.skillAddNameID)
    self.bigUpgradeSkillTipsDesc.text = lang.Get(self.weaponCfg.skillDecID)

    self.powerText.text = GWG.GWHomeMgr.droneData.GetDronePower()

    --现在暂时不花钱，所以隐藏起来
    --self.normalSpecialItemCount.text = util.NumberWithUnit2(tonumber(self.weaponCfg.costGold))
    --self.specialSpecialItemCount.text = util.NumberWithUnit2(tonumber(self.weaponCfg.costGold))
    --self.bigUpgradeNormalSpecialItemCount.text = util.NumberWithUnit2(tonumber(self.weaponCfg.costGold))
    --self.bigUpgradeSpecialSpecialItemCount.text = util.NumberWithUnit2(tonumber(self.weaponCfg.costGold))

    self.bigAtkRateText.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACKRATE) / 100 .."%"
    self.atkRate.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACKRATE) / 100 .."%"
    self.bigHpRateText.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HPRATE) / 100 .."%"
    self.hpRate.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HPRATE) / 100 .."%"
    self.bigDefRateText.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCERATE) / 100 .."%"
    self.defRate.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCERATE) / 100 .."%"

    local attributeList = cfg_util.StringToArray(self.weaponCfg.strParam2,";","#")

    self.atkValue.text =  GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACK)
    self.bigAtkText.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACK)
    self.hpValue.text =  GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HP)
    self.bigHpText.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HP)
    self.defValue.text =  GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCE)
    self.bigDefText.text = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCE)

    self.heroAddHp = math.floor(GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HP) * GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HPRATE) / 10000)
    self.heroAddAtk = math.floor(GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACK) * GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACKRATE) / 10000)
    self.heroAddDef = math.floor(GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCE) * GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCERATE) / 10000)

    self.bigHeroAtkText.text = self.heroAddAtk
    self.bigHeroHpText.text = self.heroAddHp
    self.bigHeroDefText.text = self.heroAddDef

    self.bigUpgradeHpOldValue.text = tonumber(attributeList[1][2])
    self.bigUpgradeAtkOldValue.text = tonumber(attributeList[2][2])
    self.bigUpgradeDefOldValue.text = tonumber(attributeList[3][2])

    local LevelStrings = {"Lv.",self.weaponLevel}

    self.normalLevelText.text = table.concat(LevelStrings);
    self.specialLevelText.text = table.concat(LevelStrings);
    self.bigUpgradeNormalLevelText.text = table.concat(LevelStrings);
    self.bigUpgradeSpecialLevelText.text = table.concat(LevelStrings);
    self.maxLevelText.text = table.concat(LevelStrings);

    --local skillData = game_scheme:Skill_0(GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_SKILLID))
    --log.Error(GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_SKILLID))
    self.cardSpriteAsset:GetSprite(self.weaponCfg.icon, function(sprite)
        self.skillIcon.sprite = sprite
        self.skillTipsIcon.sprite = sprite
        self.bigUpgradeSkillTipsIcon.sprite = sprite
    end)

    local starSkillDesc = GWG.GWHomeMgr.droneData.GetStarSkillDesc()

    self.star = self.weaponCfg.skillStars

    for i,v in ipairs(self.skillStarObj) do
        Common_Util.SetActive(v,i<=self.star)
    end

    for i,v in ipairs(self.skillTipsStarList) do
        Common_Util.SetActive(v,i<=self.star)
    end

    for i,v in ipairs(self.bigSkillTipsStarList) do
        Common_Util.SetActive(v,i<=self.star)
    end

    for i,v in ipairs(self.skillStarTipsList) do
        Common_Util.SetActive(v.starObj,i<=self.star)
        v.starDesc.text = lang.Get(starSkillDesc[i].desc)
        if i<= self.star then
            Common_Util.SetColor(v.starDesc,"#94FF33")
        else
            Common_Util.SetColor(v.starDesc,"#A4A6A9") --未解锁
            v.starDesc.text = lang.Get(starSkillDesc[i].desc) .. "<color=#FF4D2A>"..string.format2(lang.Get(607006),starSkillDesc[i].lv).."</color>"
        end
        LayoutRebuilder.ForceRebuildLayoutImmediate(v.starDescTran)
    end
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.skillTipsStarGroup)
    for i,v in ipairs(self.bigSkillStarTipsList) do
        Common_Util.SetActive(v.starObj,i<=self.star)
        v.starDesc.text = lang.Get(starSkillDesc[i].desc)
        if i<= self.star then
            Common_Util.SetColor(v.starDesc,"#319F38")
        else
            Common_Util.SetColor(v.starDesc,"#4D6676")
            v.starDesc.text = lang.Get(starSkillDesc[i].desc) .. "<color=#FF4D2A>"..string.format2(lang.Get(607006),starSkillDesc[i].lv).."</color>"
        end
        LayoutRebuilder.ForceRebuildLayoutImmediate(v.starDescTran)
    end
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.bigUpgradeSkillTipsStarGroup)
    if not nextLevelWeaponCfg or self.weaponLevel == self.weaponCfg.maxLv then
        Common_Util.SetActive(self.normalUpgradeObj,false)
        Common_Util.SetActive(self.specialUpgradeObj,false)
        Common_Util.SetActive(self.maxLevelObj,true)
        if Common_Util.GetActive(self.bigUpgradeTips) then
            if Common_Util.GetActive(self.bigUpgradeNormalUpgradeObj) then
                if self.TimeTicker then
                    util.RemoveDelayCall(self.TimeTicker)
                    self.TimeTicker = nil
                end
                local timeLeft = 0.375;
                local baseValue = self.bigUpgradeNormalExpSlider.value;
                local eachTime = Time.deltaTime; --每帧间隔
                local eachValue = (1 - baseValue) * eachTime / 0.375 --每帧走多少

                local fullExp = preLevelCfg.costGoodsNum.data[0]
                self.expGrowUp:SetDurationTime(0.375)
                self.expGrowUp:SetNumber(fullExp,false,true)
                self:ShowUpgradeParticle();
                local timeLeft2 = self.totalScaleTime;
                self.scaleTimeTicker = util.IntervalCall(eachTime,function()
                    timeLeft2 = timeLeft2 - eachTime;
                    if timeLeft2 > self.startScale then
                        local t = (self.totalScaleTime - timeLeft2) / 0.1
                        local scale = Vector3.Lerp(self.baseScale,self.bigScale,t)
                        for j,k in ipairs(self.saveAttribute) do
                            Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                        end
                    elseif timeLeft2 < self.finishScale then
                        local t = (self.finishScale - timeLeft2) / 0.1--timeLeft2 / 0.1
                        local scale = Vector3.Lerp(self.bigScale,self.baseScale,t)
                        for j,k in ipairs(self.saveAttribute) do
                            Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                        end
                    end
                    if timeLeft2 <= 0 then
                        util.RemoveDelayCall(self.scaleTimeTicker)
                        self.scaleTimeTicker = nil
                        for j,k in ipairs(self.upgradeValueParent) do
                            Common_Util.SetLocalScale(k,self.baseScale.x,self.baseScale.y,self.baseScale.z)
                        end
                        for i,v in ipairs(self.upgradeParticleList) do
                            Common_Util.SetActive(v,false)
                        end
                    end
                end)
                self.TimeTicker = util.IntervalCall(eachTime,function()
                    timeLeft = timeLeft - eachTime
                    self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value + eachValue
                    if timeLeft <= 0 then
                        util.RemoveDelayCall(self.TimeTicker)
                        self.TimeTicker = nil
                        self:HideAllTips()
                        for i,v in ipairs(self.upgradeParticleList) do
                            Common_Util.SetActive(v,false)
                        end
                    end
                end)
            end
        end
    else
        Common_Util.SetActive(self.maxLevelObj,false)
        if self.weaponCfg.UpgradeType == 1 then
            local itemCount = self.weaponCfg.costGoods.data[0] and player_mgr.GetPlayerOwnNum(self.weaponCfg.costGoods.data[0]) or 0
            local color = "<color=#319F38>"
            local value = self.weaponCfg.SingleCost.data[0] and tonumber(self.weaponCfg.SingleCost.data[0]) or 0
            if itemCount < value then
                color = "<color=#FF4D2A>"
            end
            local strings = {color,util.NumberWithUnit2(itemCount),"</color>","/",util.NumberWithUnit2(tonumber(self.weaponCfg.SingleCost.data[0]))}
            self.normalBaseItemCount.text = table.concat(strings)
            self.bigUpgradeNormalBaseItemCount.text = table.concat(strings)
        else
            for i,v in ipairs(self.costItemList) do
                v:Dispose()
                v = nil;
            end
            for i,v in ipairs(self.costItemObjList) do
                if not util.IsObjNull(v) then
                    v.onClick:RemoveAllListeners()
                end
                v = nil;
            end
            self.layoutItemList = {}
            for i = 0,self.weaponCfg.costGoods.count - 1 do
                local tempObj = base_game_object()
                tempObj:LoadResource("ui/prefabs/gw/buildsystem/magicweaponitemcostobj.prefab","",function(obj)
                    if obj then
                        local icon = Common_Util.GetComponent(obj.transform,typeof(Image),"root/Icon")
                        local number = Common_Util.GetComponent(obj.transform,typeof(Text),"Count")
                        local btn = Common_Util.GetComponent(obj.transform,typeof(Button),"root/Icon")
                        if self.weaponCfg.costGoods.data[i] then
                            local itemIcon = game_scheme:Item_0(self.weaponCfg.costGoods.data[i]).icon
                            local itemCount = player_mgr.GetPlayerOwnNum(self.weaponCfg.costGoods.data[i])
                            local color = "<color=#319F38>"
                            if itemCount < tonumber(self.weaponCfg.SingleCost.data[i]) then
                                color = "<color=#FF4D2A>"
                            end
                            local strings = {color,util.NumberWithUnit2(itemCount),"</color>","/",util.NumberWithUnit2(tonumber(self.weaponCfg.SingleCost.data[i]))}
                            number.text = table.concat(strings)
                            self.cardSpriteAsset:GetSprite(itemIcon,function(sprite)
                                icon.sprite = sprite
                            end)
                        end
                        --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                        --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                        --LayoutRebuilder.ForceRebuildLayoutImmediate(self.specialUpgradeObjTran)
                        if btn then
                            btn.onClick:AddListener(function()
                                if self.weaponCfg.costGoods.data[i] then
                                    iui_item_detail.Show(self.weaponCfg.costGoods.data[i], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                                end
                            end)
                            table.insert(self.costItemObjList,btn) --存起来，后面释放掉
                        end
                    end
                end,false,self.specialUpgradeObjTran)
                table.insert(self.costItemList,tempObj)

                local tempObj1 = base_game_object()
                tempObj1:LoadResource("ui/prefabs/gw/buildsystem/magicweaponitemcostobj.prefab","",function(obj)
                    if obj then
                        local icon = Common_Util.GetComponent(obj.transform,typeof(Image),"root/Icon")
                        local number = Common_Util.GetComponent(obj.transform,typeof(Text),"Count")
                        local btn = Common_Util.GetComponent(obj.transform,typeof(Button),"root/Icon")
                        if self.weaponCfg.costGoods.data[i] then
                            local itemIcon = game_scheme:Item_0(self.weaponCfg.costGoods.data[i]).icon
                            local itemCount = player_mgr.GetPlayerOwnNum(self.weaponCfg.costGoods.data[i])
                            local color = "<color=#319F38>"
                            local singleCost = self.weaponCfg.SingleCost.data[i] and tonumber(self.weaponCfg.SingleCost.data[i]) or 0
                            if itemCount < singleCost then
                                color = "<color=#FF4D2A>"
                            end
                            local strings = {color,util.NumberWithUnit2(itemCount),"</color>","/",util.NumberWithUnit2(tonumber(self.weaponCfg.SingleCost.data[i]))}
                            number.text = table.concat(strings)
                            self.cardSpriteAsset:GetSprite(itemIcon,function(sprite)
                                icon.sprite = sprite
                            end)
                        end

                        --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                        --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                        --LayoutRebuilder.ForceRebuildLayoutImmediate(self.bigSpecialUpgradeObjTran)
                        if btn then
                            btn.onClick:AddListener(function()
                                if self.weaponCfg.costGoods.data[i] then
                                    iui_item_detail.Show(self.weaponCfg.costGoods.data[i], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                                end
                            end)
                            table.insert(self.costItemObjList,btn) --存起来，后面释放掉
                        end
                        --if isInit then
                        --    table.insert(self.layoutItemList,Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                        --    table.insert(self.layoutItemList,Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                        --else
                        --    LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                        --    LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                        --    LayoutRebuilder.ForceRebuildLayoutImmediate(self.bigSpecialUpgradeObjTran)
                        --end
                    end
                end,false,self.bigSpecialUpgradeObjTran)
                table.insert(self.costItemList,tempObj1)
            end
            --self.specialBaseItemCount.text = table.concat(strings)
            --self.bigUpgradeSpecialBaseItemCount.text = table.concat(strings)
        end

        local newAttributeList = cfg_util.StringToArray(nextLevelWeaponCfg.strParam2,";","#")
        self.bigUpgradeHpNewValue.text = tonumber(newAttributeList[1][2])
        self.bigUpgradeAtkNewValue.text = tonumber(newAttributeList[2][2])
        self.bigUpgradeDefNewValue.text = tonumber(newAttributeList[3][2])

        --这里要求是哪个有变化就显示哪个
        --先全部隐藏
        Common_Util.SetActive(self.bigUpgradeHpRateObj,false)
        Common_Util.SetActive(self.bigUpgradeAtkRateObj,false)
        Common_Util.SetActive(self.bigUpgradeDefRateObj,false)
        if self.weaponCfg.ConversionRate.data[0] ~= nextLevelWeaponCfg.ConversionRate.data[0] then
            Common_Util.SetActive(self.bigUpgradeHpRateObj,true)
            self.bigUpgradeHpRateOldValue.text = self.weaponCfg.ConversionRate.data[0] / 100 .."%"
            self.bigUpgradeHpRateNewValue.text = nextLevelWeaponCfg.ConversionRate.data[0] / 100 .."%"
            table.insert(self.changeAttribute,4)
        elseif self.weaponCfg.ConversionRate.data[1] ~= nextLevelWeaponCfg.ConversionRate.data[1] then
            Common_Util.SetActive(self.bigUpgradeAtkRateObj,true)
            self.bigUpgradeAtkRateOldValue.text = self.weaponCfg.ConversionRate.data[1] / 100 .."%"
            self.bigUpgradeAtkRateNewValue.text = nextLevelWeaponCfg.ConversionRate.data[1] / 100 .."%"
            table.insert(self.changeAttribute,5)
        elseif self.weaponCfg.ConversionRate.data[2] ~= nextLevelWeaponCfg.ConversionRate.data[2] then
            Common_Util.SetActive(self.bigUpgradeDefRateObj,true)
            self.bigUpgradeDefRateOldValue.text = self.weaponCfg.ConversionRate.data[2] / 100 .."%"
            self.bigUpgradeDefRateNewValue.text = nextLevelWeaponCfg.ConversionRate.data[2] / 100 .."%"
            table.insert(self.changeAttribute,6)
        end

        --for i,v in ipairs(self.upgradeParticleList) do
        --    Common_Util.SetActive(v,false)
        --end

        if isInit then
            self:OnUpdateItem()
            Common_Util.SetActive(self.equipmentAttributeObj,false)
            self:InitUpgradeParticle()
            upgradeType = self.weaponCfg.UpgradeType --为1表示非突破升级
            Common_Util.SetActive(self.bigUpgradeNormalUpgradeObj,upgradeType == 1)
            Common_Util.SetActive(self.bigUpgradeSpecialUpgradeObj,upgradeType == 2)
            for j,k in ipairs(self.bigTipsSpecialExpSliderObj) do
                k.fillAmount = j<= self.weaponUpgradeStage and 1 or 0
            end
            for j,v in ipairs(self.specialExpSliderObj) do
                Common_Util.SetActive(v,j<= self.weaponUpgradeStage)
            end
            local exp = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP)
            self.cutExp = exp;
            local totalExp = self.weaponCfg.costGoodsNum.data[0]
            self.bigUpgradeNormalExpSlider.value = exp / totalExp;
            self.normalExpSlider.value = exp / totalExp;
            self.normalExpValue.text = "<color=#92D757>"..exp.."</color>".."/"..totalExp
            self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
            self.bigUpgradeNormalExpValue.text = exp
            self.specialExpValue.text = lang.Get(607040).."<color=#92D757>"..self.weaponUpgradeStage.."</color>".."/5"
            self.bigUpgradeSpecialExpValue.text = lang.Get(607040).."<color=#319F38>"..self.weaponUpgradeStage.."</color>".."/5"
        else
            if Common_Util.GetActive(self.bigUpgradeTips) then
                if Common_Util.GetActive(self.bigUpgradeSpecialUpgradeObj) then
                    if self.weaponUpgradeStage > 0 then
                        self.specialExpValue.text = lang.Get(607040).."<color=#92D757>"..self.weaponUpgradeStage.."</color>".."/5"
                        for i,v in ipairs(self.specialExpSliderObj) do
                            Common_Util.SetActive(v,i<= self.weaponUpgradeStage)
                        end
                        for i,v in ipairs(self.bigTipsSpecialExpSliderObj) do
                            if i < self.weaponUpgradeStage then
                                v.fillAmount = 1
                            elseif i == self.weaponUpgradeStage then
                                v.fillAmount = 0
                                --if self.tweenId > 0 then
                                --    LeanTween.cancel(self.tweenId);
                                --    self.tweenId = -1
                                --end
                                if self.TimeTicker then
                                    util.RemoveDelayCall(self.TimeTicker)
                                    self.TimeTicker = nil
                                end
                                if self.scaleTimeTicker then
                                    util.RemoveDelayCall(self.scaleTimeTicker)
                                    self.scaleTimeTicker = nil
                                end
                                local timeLeft = 0.175;
                                local eachTime = Time.deltaTime; --每帧间隔
                                local eachValue = 1 * eachTime / 0.175 --每帧走多少
                                self:ShowUpgradeParticle();
                                local timeLeft2 = self.totalScaleTime;
                                self.scaleTimeTicker = util.IntervalCall(eachTime,function()
                                    timeLeft2 = timeLeft2 - eachTime;
                                    if timeLeft2 > self.startScale then
                                        local t = (self.totalScaleTime - timeLeft2) / 0.1
                                        local scale = Vector3.Lerp(self.baseScale,self.bigScale,t)
                                        for j,k in ipairs(self.saveAttribute) do
                                            Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                        end
                                    elseif timeLeft2 < self.finishScale then
                                        local t = (self.finishScale - timeLeft2) / 0.1--timeLeft2 / 0.1
                                        local scale = Vector3.Lerp(self.bigScale,self.baseScale,t)
                                        for j,k in ipairs(self.saveAttribute) do
                                            Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                        end
                                    end
                                    if timeLeft2 <= 0 then
                                        util.RemoveDelayCall(self.scaleTimeTicker)
                                        self.scaleTimeTicker = nil
                                        for j,k in ipairs(self.upgradeValueParent) do
                                            Common_Util.SetLocalScale(k,self.baseScale.x,self.baseScale.y,self.baseScale.z)
                                        end
                                        self:SaveUpgradeParticle()
                                    end
                                end)
                                self.TimeTicker = util.IntervalCall(eachTime,function()
                                    timeLeft = timeLeft - eachTime;
                                    local t = (0.125 - timeLeft) / 0.125
                                    v.fillAmount = Mathf.Lerp(0,1,t)--v.fillAmount + eachValue;

                                    if timeLeft <= 0 then
                                        util.RemoveDelayCall(self.TimeTicker)
                                        self.TimeTicker = nil
                                        v.fillAmount = 1;

                                    end
                                end)
                                --self.tweenId = LeanTween.value(0,1,0.75):setOnUpdate(function(val)
                                --    v.fillAmount = val
                                --    --log.Error(val)
                                --end):setOnComplete(function()
                                --    self.tweenId = -1
                                --end).id
                            else
                                v.fillAmount = 0
                            end
                        end
                        self.bigUpgradeSpecialExpValue.text = lang.Get(607040).."<color=#319F38>"..self.weaponUpgradeStage.."</color>".."/5"
                    else
                        for i=1,5 do
                            if i < 5 then
                                self.bigTipsSpecialExpSliderObj[i].fillAmount = 1
                            else
                                self.bigTipsSpecialExpSliderObj[i].fillAmount = 0
                                self.specialExpValue.text = lang.Get(607040).."<color=#92D757>5</color>".."/5"
                                if self.TimeTicker then
                                    util.RemoveDelayCall(self.TimeTicker)
                                    self.TimeTicker = nil
                                end
                                if self.scaleTimeTicker then
                                    util.RemoveDelayCall(self.scaleTimeTicker)
                                    self.scaleTimeTicker = nil
                                end
                                local timeLeft = 0.125;
                                local eachTime = Time.deltaTime; --每帧间隔
                                local eachValue = 1 * eachTime / 0.125 --每帧走多少
                                local index = i
                                --self.expGrowUp:SetDurationTime(timeLeft)
                                self:ShowUpgradeParticle();
                                local timeLeft2 = self.totalScaleTime;
                                self.scaleTimeTicker = util.IntervalCall(eachTime,function()
                                    timeLeft2 = timeLeft2 - eachTime;
                                    if timeLeft2 > self.startScale then
                                        local t = (self.totalScaleTime - timeLeft2) / 0.1
                                        local scale = Vector3.Lerp(self.baseScale,self.bigScale,t)
                                        for j,k in ipairs(self.saveAttribute) do
                                            Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                        end
                                    elseif timeLeft2 < self.finishScale then
                                        local t = (self.finishScale - timeLeft2) / 0.1--timeLeft2 / 0.1
                                        local scale = Vector3.Lerp(self.bigScale,self.baseScale,t)
                                        for j,k in ipairs(self.saveAttribute) do
                                            Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                        end
                                    end
                                    if timeLeft2 <= 0 then
                                        util.RemoveDelayCall(self.scaleTimeTicker)
                                        self.scaleTimeTicker = nil
                                        for j,k in ipairs(self.upgradeValueParent) do
                                            Common_Util.SetLocalScale(k,self.baseScale.x,self.baseScale.y,self.baseScale.z)
                                        end
                                        self:SaveUpgradeParticle()
                                    end
                                end)
                                self.TimeTicker = util.IntervalCall(eachTime,function()
                                    timeLeft = timeLeft - eachTime;
                                    local t = (0.125 - timeLeft) / 0.125

                                    self.bigTipsSpecialExpSliderObj[index].fillAmount = Mathf.Lerp(0,1,t)--self.bigTipsSpecialExpSliderObj[index].fillAmount + eachValue--;
                                    if timeLeft <= 0 then
                                        util.RemoveDelayCall(self.TimeTicker)
                                        self.TimeTicker = nil
                                        self.bigTipsSpecialExpSliderObj[index].fillAmount = 1
                                        upgradeType = self.weaponCfg.UpgradeType --为1表示非突破升级
                                        Common_Util.SetActive(self.bigUpgradeNormalUpgradeObj,upgradeType == 1)
                                        Common_Util.SetActive(self.bigUpgradeSpecialUpgradeObj,upgradeType == 2)
                                        for j,k in ipairs(self.bigTipsSpecialExpSliderObj) do
                                            k.fillAmount = 0
                                        end
                                        for j,v in ipairs(self.specialExpSliderObj) do
                                            Common_Util.SetActive(v,j<= self.weaponUpgradeStage)
                                        end
                                        local exp = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP)
                                        local totalExp = self.weaponCfg.costGoodsNum.data[0]
                                        self.cutExp = exp;
                                        local targetValue = exp / totalExp
                                        self.bigUpgradeNormalExpSlider.value = 0;
                                        self.normalExpSlider.value = targetValue;
                                        self.normalExpValue.text = "<color=#92D757>"..exp.."</color>".."/"..totalExp
                                        self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
                                        self.bigUpgradeNormalExpValue.text = exp--.."/"..totalExp
                                        self.specialExpValue.text = lang.Get(607040).."<color=#92D757>"..self.weaponUpgradeStage.."</color>".."/5"
                                        self.bigUpgradeSpecialExpValue.text = lang.Get(607040).."<color=#319F38>"..self.weaponUpgradeStage.."</color>".."/5"

                                        --local itemCost = preLevelCfg.costGoodsNum.data[0];
                                        --self.specialExpUpText.text = "+"..itemCost
                                        --Common_Util.SetActive(self.specialExpUpObj,false)

                                        local timeLeft1 = 0.25;
                                        local eachTime1 = Time.deltaTime; --每帧间隔
                                        local eachValue1 = targetValue * eachTime1 / 0.25 --每帧走多少

                                        if preLevelCfg.skillStars ~= self.star and not isInit then
                                            local skillData = {}
                                            skillData["isUnlock"] = true
                                            skillData["lv"] = 1
                                            skillData["nameID"] = self.weaponCfg.skillNameID
                                            skillData["descID"] = self.weaponCfg.skillStarsDes
                                            skillData["icon"] = self.weaponCfg.icon
                                            skillData["star"] = self.star
                                            local win = ui_window_mgr:ShowModule("ui_hero_skill_tips")
                                            if win then
                                                win:SetInputParam(skillData)
                                            end
                                        end

                                        self.TimeTicker = util.IntervalCall(eachTime1,function()
                                            timeLeft1 = timeLeft1 - eachTime1
                                            self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value + eachValue1
                                            if timeLeft1 <= 0 then
                                                util.RemoveDelayCall(self.TimeTicker)
                                                self.TimeTicker = nil
                                                Common_Util.SetActive(self.specialExpUpObj,false)
                                                self.bigUpgradeNormalExpSlider.value = targetValue
                                            end
                                        end)
                                        --self.tweenId = -1
                                        --self.tweenId = LeanTween.value(0,targetValue,0.5):setOnUpdate(function(val)
                                        --    --log.Error(val)
                                        --    self.bigUpgradeNormalExpSlider.value = val
                                        --end):setOnComplete(function()
                                        --    Common_Util.SetActive(self.specialExpUpObj,false)
                                        --    self.tweenId = -1
                                        --end).id

                                    end
                                end)
                                --[[
                                if self.tweenId > 0 then
                                    LeanTween.cancel(self.tweenId);
                                    self.tweenId = -1
                                end
                                self.tweenId = LeanTween.value(0,1,0.25):setOnUpdate(function(val)
                                    --log.Error(val)
                                    self.bigTipsSpecialExpSliderObj[i].fillAmount = val
                                end):setOnComplete(function()
                                    upgradeType = self.weaponCfg.UpgradeType --为1表示非突破升级
                                    Common_Util.SetActive(self.bigUpgradeNormalUpgradeObj,upgradeType == 1)
                                    Common_Util.SetActive(self.bigUpgradeSpecialUpgradeObj,upgradeType == 2)
                                    for j,k in ipairs(self.bigTipsSpecialExpSliderObj) do
                                        k.fillAmount = 0
                                    end
                                    for j,v in ipairs(self.specialExpSliderObj) do
                                        Common_Util.SetActive(v,j<= self.weaponUpgradeStage)
                                    end
                                    local exp = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP)
                                    local totalExp = self.weaponCfg.costGoodsNum
                                    self.cutExp = exp;
                                    local targetValue = exp / totalExp
                                    self.bigUpgradeNormalExpSlider.value = 0;
                                    self.normalExpSlider.value = targetValue;
                                    self.normalExpValue.text = "<color=#92D757>"..exp.."</color>".."/"..totalExp
                                    self.bigUpgradeNormalExpValue.text = "<color=#319F38>"..exp.."</color>".."/"..totalExp
                                    self.specialExpValue.text = "<color=#92D757>"..self.weaponUpgradeStage.."</color>".."/5"
                                    self.bigUpgradeSpecialExpValue.text = "<color=#319F38>"..self.weaponUpgradeStage.."</color>".."/5"

                                    local preLevelCfg = GWG.GWHomeMgr.droneData.GetPreDroneCfgData() --获取上一级的等级
                                    local itemCost = preLevelCfg.costGoodsNum;
                                    self.specialExpUpText.text = "+"..itemCost
                                    Common_Util.SetActive(self.specialExpUpObj,false)
                                    self.tweenId = -1
                                    self.tweenId = LeanTween.value(0,targetValue,0.5):setOnUpdate(function(val)
                                        --log.Error(val)
                                        self.bigUpgradeNormalExpSlider.value = val
                                    end):setOnComplete(function()
                                        Common_Util.SetActive(self.specialExpUpObj,false)
                                        self.tweenId = -1
                                    end).id
                                end).id
                                ]]
                            end
                        end
                    end
                else
                    local totalExp = self.weaponCfg.costGoodsNum.data[0]
                    local singleCost = self.weaponCfg.SingleCost.data[0];
                    local remainExp = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP)
                    local exp = remainExp--totalExp - remainExp
                    local baseValue = self.bigUpgradeNormalExpSlider.value;
                    local targetValue = exp / totalExp;
                    if upgradeType == 1 then
                        if baseValue < targetValue and targetValue <= 1 then
                            local itemCost = remainExp - self.cutExp

                            self.cutExp = remainExp
                            if itemCost < 0 then
                                --log.Error("?")
                            end
                            if itemCost > singleCost then
                                local index2 = table.remove(self.emptyNormalCriticalObjList,1)
                                if index2 == nil then
                                    index2 = self.normalCriticalObjCount;
                                    self.normalCriticalObjCount = self.normalCriticalObjCount + 1
                                end
                                local go1 = nil
                                if self.normalCriticalObjList[index2] then
                                    go1 = self.normalCriticalObjList[index2].obj;
                                    if self.normalCriticalObjList[index2].timer then
                                        util.RemoveDelayCall(self.normalCriticalObjList[index2].timer)
                                        self.normalCriticalObjList[index2].timer = nil
                                    end
                                else
                                    go1 = GameObject.Instantiate(self.normalCriticalExpUpTips,self.expObjTrans)
                                    self.normalCriticalObjList[index2] =
                                    {
                                        obj = go1;
                                        timer = nil;
                                    }
                                end
                                --go1.transform:SetParent(self.expObjTrans)
                                Common_Util.SetLocalScale(go1,1,1,1)
                                --go1.transform.localScale = {x=1,y=1,z=1}
                                Common_Util.SetActive(go1,true)
                                Common_Util.GetComponent(go1.transform,typeof(Animation)):Play()
                                self.normalCriticalObjList[index2].timer = util.DelayCallOnce(1,function()
                                    Common_Util.SetActive(go1,false)
                                    table.insert(self.emptyNormalCriticalObjList,index)
                                    self.normalCriticalObjList[index2].timer = nil
                                end)
                            end

                            local index = table.remove(self.emptyUpgradeObjList,1)
                            if index == nil then
                                index = self.upgradeObjCount;
                                self.upgradeObjCount = self.upgradeObjCount + 1
                            end
                            local go = nil
                            if self.upgradeObjList[index] then
                                go = self.upgradeObjList[index].obj;
                                if self.upgradeObjList[index].timer then
                                    util.RemoveDelayCall(self.upgradeObjList[index].timer)
                                    self.upgradeObjList[index].timer = nil
                                end
                            else
                                go = GameObject.Instantiate(self.normalExpUpObj,self.expObjTrans)
                                self.upgradeObjList[index] =
                                {
                                    obj = go;
                                    timer = nil;
                                }
                            end
                            --go.transform:SetParent(self.expObjTrans)
                            Common_Util.SetLocalScale(go,1,1,1)
                            --go.transform.localScale = {x=1,y=1,z=1}
                            Common_Util.SetActive(go,true)
                            Common_Util.GetComponent(go.transform,typeof(Animation)):Play()
                            Common_Util.GetComponent(go.transform,typeof(Text),"expUp/Text").text = string.format2("+{%s1}",itemCost)
                            --go.transform:GetChild(0):GetComponent(typeof(Text)).text = "+"..itemCost
                            self.upgradeObjList[index].timer = util.DelayCallOnce(1,function()
                                Common_Util.SetActive(go,false)
                                table.insert(self.emptyUpgradeObjList,index)
                                self.upgradeObjList[index].timer = nil
                            end)

                            if self.TimeTicker then
                                util.RemoveDelayCall(self.TimeTicker)
                                self.TimeTicker = nil
                            end
                            local timeLeft = 0.375;
                            local eachTime = Time.deltaTime; --每帧间隔
                            local eachValue = (targetValue - baseValue) * eachTime / 0.375 --每帧走多少
                            self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
                            self.expGrowUp:SetDurationTime(0.375)
                            self.expGrowUp:SetNumber(exp)
                            self.TimeTicker = util.IntervalCall(eachTime,function()
                                timeLeft = timeLeft - eachTime
                                self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value + eachValue
                                if timeLeft <= 0 then
                                    util.RemoveDelayCall(self.TimeTicker)
                                    self.TimeTicker = nil
                                    self.bigUpgradeNormalExpSlider.value = targetValue
                                end
                            end)
                        else
                            local itemCost = preLevelCfg.costGoodsNum.data[0] - self.cutExp + remainExp;
                            if itemCost < 0 then
                                --log.Error("?")
                            end

                            if itemCost > singleCost then
                                local index2 = table.remove(self.emptyNormalCriticalObjList,1)
                                if index2 == nil then
                                    index2 = self.normalCriticalObjCount;
                                    self.normalCriticalObjCount = self.normalCriticalObjCount + 1
                                end
                                local go1 = nil
                                if self.normalCriticalObjList[index2] then
                                    go1 = self.normalCriticalObjList[index2].obj;
                                    if self.normalCriticalObjList[index2].timer then
                                        util.RemoveDelayCall(self.normalCriticalObjList[index2].timer)
                                        self.normalCriticalObjList[index2].timer = nil
                                    end
                                else
                                    go1 = GameObject.Instantiate(self.normalCriticalExpUpTips,self.expObjTrans)
                                    self.normalCriticalObjList[index2] =
                                    {
                                        obj = go1;
                                        timer = nil;
                                    }
                                end
                                --go1.transform:SetParent(self.expObjTrans)
                                Common_Util.SetLocalScale(go1,1,1,1)
                                --go1.transform.localScale = {x=1,y=1,z=1}
                                Common_Util.SetActive(go1,true)
                                Common_Util.GetComponent(go1.transform,typeof(Animation)):Play()
                                self.normalCriticalObjList[index2].timer = util.DelayCallOnce(1,function()
                                    Common_Util.SetActive(go1,false)
                                    table.insert(self.emptyNormalCriticalObjList,index)
                                    self.normalCriticalObjList[index2].timer = nil
                                end)
                            end

                            local index = table.remove(self.emptyUpgradeObjList,1)
                            if index == nil then
                                index = self.upgradeObjCount;
                                self.upgradeObjCount = self.upgradeObjCount + 1
                            end
                            local go = nil
                            if self.upgradeObjList[index] then
                                go = self.upgradeObjList[index].obj;
                                if self.upgradeObjList[index].timer then
                                    util.RemoveDelayCall(self.upgradeObjList[index].timer)
                                    self.upgradeObjList[index].timer = nil
                                end
                            else
                                go = GameObject.Instantiate(self.normalExpUpObj,self.expObjTrans)
                                self.upgradeObjList[index] =
                                {
                                    obj = go;
                                    timer = nil;
                                }
                            end
                            --go.transform:SetParent(self.expObjTrans)
                            Common_Util.SetLocalScale(go,1,1,1)
                            --go.transform.localScale = {x=1,y=1,z=1}
                            Common_Util.SetActive(go,true)
                            Common_Util.GetComponent(go.transform,typeof(Animation)):Play()

                            Common_Util.GetComponent(go.transform,typeof(Text),"expUp/Text").text = string.format2("+{%s1}",itemCost)
                            self.upgradeObjList[index].timer = util.DelayCallOnce(1,function()
                                Common_Util.SetActive(go,false)
                                table.insert(self.emptyUpgradeObjList,index)
                                self.upgradeObjList[index].timer = nil
                            end)

                            self.cutExp = remainExp
                            if self.TimeTicker then
                                util.RemoveDelayCall(self.TimeTicker)
                                self.TimeTicker = nil
                            end
                            if self.scaleTimeTicker then
                                util.RemoveDelayCall(self.scaleTimeTicker)
                                self.scaleTimeTicker = nil
                            end
                            local timeLeft = 0.125;
                            local eachTime = Time.deltaTime; --每帧间隔
                            local eachValue = (1 - baseValue) * eachTime / 0.125 --每帧走多少

                            local fullExp = preLevelCfg.costGoodsNum.data[0]
                            self.expGrowUp:SetDurationTime(0.375)
                            self.expGrowUp:SetNumber(fullExp,false,true)
                            self:ShowUpgradeParticle();
                            local timeLeft2 = self.totalScaleTime;

                            self.scaleTimeTicker = util.IntervalCall(eachTime,function()
                                timeLeft2 = timeLeft2 - eachTime;
                                if timeLeft2 > self.startScale then
                                    local t = (self.totalScaleTime - timeLeft2) / 0.1
                                    local scale = Vector3.Lerp(self.baseScale,self.bigScale,t)
                                    for j,k in ipairs(self.saveAttribute) do
                                        Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                    end
                                elseif timeLeft2 < self.finishScale then
                                    local t = (self.finishScale - timeLeft2) / 0.1--timeLeft2 / 0.1
                                    local scale = Vector3.Lerp(self.bigScale,self.baseScale,t)
                                    for j,k in ipairs(self.saveAttribute) do
                                        Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                    end
                                end
                                if timeLeft2 <= 0 then
                                    util.RemoveDelayCall(self.scaleTimeTicker)
                                    self.scaleTimeTicker = nil
                                    for j,k in ipairs(self.upgradeValueParent) do
                                        Common_Util.SetLocalScale(k,self.baseScale.x,self.baseScale.y,self.baseScale.z)
                                    end
                                    self:SaveUpgradeParticle()
                                end
                            end)
                            self.TimeTicker = util.IntervalCall(eachTime,function()
                                timeLeft = timeLeft - eachTime
                                self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value + eachValue
                                if timeLeft <= 0 then
                                    util.RemoveDelayCall(self.TimeTicker)
                                    self.TimeTicker = nil
                                    self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
                                    self.bigUpgradeNormalExpSlider.value = 1
                                    self.bigUpgradeNormalExpValue.text = "0"
                                    local timeLeft1 = 0.125;
                                    local eachTime1 = Time.deltaTime; --每帧间隔
                                    local eachValue1 = 1 * eachTime1 / 0.125 --每帧走多少
                                    self.TimeTicker = util.IntervalCall(eachTime1,function()
                                        timeLeft1 = timeLeft1 - eachTime1
                                        self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value - eachValue1
                                        if timeLeft1 <= 0 then
                                            util.RemoveDelayCall(self.TimeTicker)
                                            self.TimeTicker = nil
                                            self.bigUpgradeNormalExpSlider.value = 0
                                            if targetValue > 1 then
                                                targetValue = targetValue - 1
                                            end

                                            local timeLeft2 = 0.125;
                                            local eachTime2 = Time.deltaTime; --每帧间隔
                                            local eachValue2 = targetValue * eachTime2 / 0.125 --每帧走多少

                                            self.expGrowUp:SetDurationTime(0.125)
                                            self.expGrowUp:SetNumber(exp,false)

                                            if preLevelCfg.skillStars ~= self.star and not isInit then
                                                local skillData = {}
                                                skillData["isUnlock"] = true
                                                skillData["lv"] = 1
                                                skillData["nameID"] = self.weaponCfg.skillNameID
                                                skillData["descID"] = self.weaponCfg.skillStarsDes
                                                skillData["icon"] = self.weaponCfg.icon
                                                skillData["star"] = self.star
                                                local win = ui_window_mgr:ShowModule("ui_hero_skill_tips")
                                                if win then
                                                    win:SetInputParam(skillData)
                                                end
                                            end

                                            self.TimeTicker = util.IntervalCall(eachTime2,function()
                                                timeLeft2 = timeLeft2 - eachTime2
                                                self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value + eachValue2
                                                if timeLeft2 <= 0 then
                                                    util.RemoveDelayCall(self.TimeTicker)
                                                    self.TimeTicker = nil
                                                end
                                            end)

                                        end
                                    end)
                                end
                            end)
                        end
                    else

                        if self.TimeTicker then
                            util.RemoveDelayCall(self.TimeTicker)
                            self.TimeTicker = nil
                        end
                        if self.scaleTimeTicker then
                            util.RemoveDelayCall(self.scaleTimeTicker)
                            self.scaleTimeTicker = nil
                        end
                        if self.expTimeTicker then
                            util.RemoveDelayCall(self.expTimeTicker)
                            self.expTimeTicker = nil
                        end
                        local timeLeft = 0.375;
                        local eachTime = Time.deltaTime; --每帧间隔
                        local eachValue = (1 - baseValue) * eachTime / 0.375 --每帧走多少
                        local fullExp = preLevelCfg.costGoodsNum.data[0]
                        self.expGrowUp:SetDurationTime(0.375)
                        self.expGrowUp:SetNumber(fullExp,false,true)
                        self:ShowUpgradeParticle();

                        local itemCost = preLevelCfg.costGoodsNum.data[0] - self.cutExp + remainExp;
                        self.cutExp = remainExp

                        local index = table.remove(self.emptyUpgradeObjList,1)
                        if index == nil then
                            index = self.upgradeObjCount;
                            self.upgradeObjCount = self.upgradeObjCount + 1
                        end
                        local go = nil
                        if self.upgradeObjList[index] then
                            go = self.upgradeObjList[index].obj;
                            if self.upgradeObjList[index].timer then
                                util.RemoveDelayCall(self.upgradeObjList[index].timer)
                                self.upgradeObjList[index].timer = nil
                            end
                        else
                            go = GameObject.Instantiate(self.normalExpUpObj,self.expObjTrans)
                            self.upgradeObjList[index] =
                            {
                                obj = go;
                                timer = nil;
                            }
                        end
                        --go.transform:SetParent(self.expObjTrans)
                        Common_Util.SetLocalScale(go,1,1,1)
                        --go.transform.localScale = {x=1,y=1,z=1}
                        Common_Util.SetActive(go,true)
                        Common_Util.GetComponent(go.transform,typeof(Animation)):Play()
                        Common_Util.GetComponent(go.transform,typeof(Text),"expUp/Text").text = string.format2("+{%s1}",itemCost)
                        self.upgradeObjList[index].timer = util.DelayCallOnce(1,function()
                            Common_Util.SetActive(go,false)
                            table.insert(self.emptyUpgradeObjList,index)
                            self.upgradeObjList[index].timer = nil
                        end)

                        local timeLeft2 = self.totalScaleTime;

                        self.scaleTimeTicker = util.IntervalCall(eachTime,function()
                            timeLeft2 = timeLeft2 - eachTime;
                            if timeLeft2 > self.startScale then
                                local t = (self.totalScaleTime - timeLeft2) / 0.1
                                local scale = Vector3.Lerp(self.baseScale,self.bigScale,t)
                                for j,k in ipairs(self.saveAttribute) do
                                    Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                end
                            elseif timeLeft2 < self.finishScale then
                                local t = (self.finishScale - timeLeft2) / 0.1--timeLeft2 / 0.1
                                local scale = Vector3.Lerp(self.bigScale,self.baseScale,t)
                                for j,k in ipairs(self.saveAttribute) do
                                    Common_Util.SetLocalScale(self.upgradeValueParent[k],scale.x,scale.y,scale.z)
                                end
                            end
                            if timeLeft2 <= 0 then
                                util.RemoveDelayCall(self.scaleTimeTicker)
                                self.scaleTimeTicker = nil
                                for j,k in ipairs(self.upgradeValueParent) do
                                    Common_Util.SetLocalScale(k,self.baseScale.x,self.baseScale.y,self.baseScale.z)
                                end
                                self:SaveUpgradeParticle()
                            end
                        end)
                        self.TimeTicker = util.IntervalCall(eachTime,function()
                            timeLeft = timeLeft - eachTime
                            self.bigUpgradeNormalExpSlider.value = self.bigUpgradeNormalExpSlider.value + eachValue
                            if timeLeft <= 0 then
                                util.RemoveDelayCall(self.TimeTicker)
                                self.TimeTicker = nil
                                self.cutExp = 0
                                self.bigUpgradeNormalExpSlider.value = 0
                                self.normalExpSlider.value = 0
                                self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
                                self.bigUpgradeNormalExpValue.text = exp--.."/"..totalExp
                                if preLevelCfg.skillStars ~= self.star and not isInit then
                                    local skillData = {}
                                    skillData["isUnlock"] = true
                                    skillData["lv"] = 1
                                    skillData["nameID"] = self.weaponCfg.skillNameID
                                    skillData["descID"] = self.weaponCfg.skillStarsDes
                                    skillData["icon"] = self.weaponCfg.icon
                                    skillData["star"] = self.star
                                    local win = ui_window_mgr:ShowModule("ui_hero_skill_tips")
                                    if win then
                                        win:SetInputParam(skillData)
                                    end
                                end
                                Common_Util.SetActive(self.bigUpgradeNormalUpgradeObj,false)
                                Common_Util.SetActive(self.bigUpgradeSpecialUpgradeObj,true)
                            end
                        end)

                        for j,k in ipairs(self.bigTipsSpecialExpSliderObj) do
                            k.fillAmount = 0
                        end
                        for i,v in ipairs(self.specialExpSliderObj) do
                            Common_Util.SetActive(v,i<= self.weaponUpgradeStage)
                        end
                    end

                    --self.bigUpgradeNormalExpSlider.value = exp / totalExp;
                    self.normalExpSlider.value = exp / totalExp;
                    self.normalExpValue.text = "<color=#92D757>"..exp.."</color>".."/"..totalExp
                    --self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
                    --self.bigUpgradeNormalExpValue.text = exp--.."/"..totalExp
                    self.specialExpValue.text = lang.Get(607040).."<color=#92D757>"..self.weaponUpgradeStage.."</color>".."/5"
                    self.bigUpgradeSpecialExpValue.text = lang.Get(607040).."<color=#319F38>"..self.weaponUpgradeStage.."</color>".."/5"
                end
            else
                if upgradeType ~= 1 then
                    self.specialExpValue.text = lang.Get(607040).."<color=#92D757>"..self.weaponUpgradeStage.."</color>".."/5"
                    for i,v in ipairs(self.specialExpSliderObj) do
                        Common_Util.SetActive(v,i<= self.weaponUpgradeStage)
                    end
                    for i,v in ipairs(self.bigTipsSpecialExpSliderObj) do
                        v.fillAmount = i<= self.weaponUpgradeStage and 1 or 0
                        --Common_Util.SetActive(v,i<= self.weaponUpgradeStage)
                    end
                    self.bigUpgradeSpecialExpValue.text = lang.Get(607040).."<color=#319F38>"..self.weaponUpgradeStage.."</color>".."/5"
                else
                    local totalExp = self.weaponCfg.costGoodsNum.data[0]
                    local remainExp = GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP)
                    local exp = remainExp--totalExp - remainExp
                    self.bigUpgradeNormalExpSlider.value = exp / totalExp;
                    self.normalExpSlider.value = exp / totalExp;
                    self.normalExpValue.text = "<color=#92D757>"..exp.."</color>".."/"..totalExp
                    self.bigUpgradeNormalExpValueDenominator.text = "/"..totalExp
                    self.bigUpgradeNormalExpValue.text = exp--.."/"..totalExp
                end
            end
            self:ResetItemLayout()
        end
    end
end

function UIView:ResetItemLayout()
    --for i,v in ipairs(self.layoutItemList) do
    --    LayoutRebuilder.ForceRebuildLayoutImmediate(v)
    --end
    --LayoutRebuilder.ForceRebuildLayoutImmediate(self.specialUpgradeObjTran)
    --LayoutRebuilder.ForceRebuildLayoutImmediate(self.bigSpecialUpgradeObjTran)
end

function UIView:UpdateMagicWeaponItem(isInit)
    if self.pageType ~= pageShowTypeEnum.Equipment then
        Common_Util.SetActive(self.equipmentAttributeObj,true)
    end
    self:OnUpdateItem()
    if self.pageType ~= pageShowTypeEnum.Equipment then
        Common_Util.SetActive(self.equipmentAttributeObj,false)
    end
    local nextLevelWeaponCfg = GWG.GWHomeMgr.droneData.OnGetNextDroneCfgData()
    if nextLevelWeaponCfg then
        if self.weaponCfg.UpgradeType == 1 then
            local itemCount = self.weaponCfg.costGoods.data[0] and player_mgr.GetPlayerOwnNum(self.weaponCfg.costGoods.data[0]) or 0
            local color = "<color=#319F38>"
            if itemCount < tonumber(self.weaponCfg.SingleCost.data[0]) then
                color = "<color=#FF4D2A>"
            end
            local strings = {color,util.NumberWithUnit2(itemCount),"</color>","/",util.NumberWithUnit2(tonumber(self.weaponCfg.SingleCost.data[0]))}
            self.normalBaseItemCount.text = table.concat(strings)
            self.bigUpgradeNormalBaseItemCount.text = table.concat(strings)
        else
            for i,v in ipairs(self.costItemList) do
                v:Dispose()
                v = nil;
            end
            for i,v in ipairs(self.costItemObjList) do
                if not util.IsObjNull(v) then
                    v.onClick:RemoveAllListeners()
                end
                v = nil;
            end
            self.layoutItemList = {}
            for i = 0,self.weaponCfg.costGoods.count - 1 do
                local itemCfg = game_scheme:Item_0(self.weaponCfg.costGoods.data[i])
                if itemCfg then
                    local tempObj = base_game_object()
                    tempObj:LoadResource("ui/prefabs/gw/buildsystem/magicweaponitemcostobj.prefab","",function(obj)
                        if obj then
                            local icon = Common_Util.GetComponent(obj.transform,typeof(Image),"root/Icon")
                            local number = Common_Util.GetComponent(obj.transform,typeof(Text),"Count")
                            local btn = Common_Util.GetComponent(obj.transform,typeof(Button),"root/Icon")
                            local itemIcon = itemCfg.icon
                            local itemCount = self.weaponCfg.costGoods.data[i] and player_mgr.GetPlayerOwnNum(self.weaponCfg.costGoods.data[i]) or 0
                            local color = "<color=#319F38>"
                            if self.weaponCfg.SingleCost.data[i] then
                                if itemCount < tonumber(self.weaponCfg.SingleCost.data[i]) then
                                    color = "<color=#FF4D2A>"
                                end
                            end
                            local strings = {color,util.NumberWithUnit2(itemCount),"</color>","/",util.NumberWithUnit2(tonumber(self.weaponCfg.SingleCost.data[i]))}
                            number.text = table.concat(strings)
                            self.cardSpriteAsset:GetSprite(itemIcon,function(sprite)
                                icon.sprite = sprite
                            end)
                            --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                            --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                            --LayoutRebuilder.ForceRebuildLayoutImmediate(self.specialUpgradeObjTran)
                            if btn then
                                btn.onClick:AddListener(function()
                                    if self.weaponCfg.costGoods.data[i] then
                                        iui_item_detail.Show(self.weaponCfg.costGoods.data[i], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                                    end
                                end)
                                table.insert(self.costItemObjList,btn) --存起来，后面释放掉
                            end
                            --table.insert(self.layoutItemList,Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                            --table.insert(self.layoutItemList,Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                        end
                    end,false,self.specialUpgradeObjTran)
                    table.insert(self.costItemList,tempObj)

                    local tempObj1 = base_game_object()
                    tempObj1:LoadResource("ui/prefabs/gw/buildsystem/magicweaponitemcostobj.prefab","",function(obj)
                        if obj then
                            local icon = Common_Util.GetComponent(obj.transform,typeof(Image),"root/Icon")
                            local number = Common_Util.GetComponent(obj.transform,typeof(Text),"Count")
                            local btn = Common_Util.GetComponent(obj.transform,typeof(Button),"root/Icon")
                            local itemIcon = itemCfg.icon
                            local itemCount = self.weaponCfg.costGoods.data[i] and player_mgr.GetPlayerOwnNum(self.weaponCfg.costGoods.data[i]) or 0
                            local color = "<color=#319F38>"
                            if self.weaponCfg.SingleCost.data[i] then
                                if itemCount < tonumber(self.weaponCfg.SingleCost.data[i]) then
                                    color = "<color=#FF4D2A>"
                                end
                            end
                            local strings = {color,util.NumberWithUnit2(itemCount),"</color>","/",util.NumberWithUnit2(tonumber(self.weaponCfg.SingleCost.data[i]))}
                            number.text = table.concat(strings)
                            self.cardSpriteAsset:GetSprite(itemIcon,function(sprite)
                                icon.sprite = sprite
                            end)
                            --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                            --LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                            --LayoutRebuilder.ForceRebuildLayoutImmediate(self.bigSpecialUpgradeObjTran)
                            if btn then
                                btn.onClick:AddListener(function()
                                    if self.weaponCfg.costGoods.data[i] then
                                        iui_item_detail.Show(self.weaponCfg.costGoods.data[i], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                                    end
                                end)
                                table.insert(self.costItemObjList,btn) --存起来，后面释放掉
                            end
                            --if isInit then
                            table.insert(self.layoutItemList,Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                            table.insert(self.layoutItemList,Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                            --else
                            --    LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(number),"RectTransform",""))
                            --    LayoutRebuilder.ForceRebuildLayoutImmediate(Common_Util.GetComponent(Common_Util.GetTrans(obj),"RectTransform",""))
                            --    LayoutRebuilder.ForceRebuildLayoutImmediate(self.bigSpecialUpgradeObjTran)
                            --end
                        end
                    end,false,self.bigSpecialUpgradeObjTran)
                    table.insert(self.costItemList,tempObj1)
                end

            end
            --self.specialBaseItemCount.text = table.concat(strings)
            --self.bigUpgradeSpecialBaseItemCount.text = table.concat(strings)
        end

    end
    local showRedDot = GWG.GWHomeMgr.droneData.CheckUpgradeRedDot()
    Common_Util.SetActive(self.upgradeRedDot,showRedDot)
    Common_Util.SetActive(self.upgradeRedDot_2,showRedDot)
    Common_Util.SetActive(self.upgradeRedDot_3,showRedDot)
    Common_Util.SetActive(self.upgradeRedDot_4,showRedDot)

    Common_Util.SetActive(self.normalUpgradeNormalTxt,showRedDot)
    Common_Util.SetActive(self.specialUpgradeNormalTxt,showRedDot)
    Common_Util.SetActive(self.normalBigUpgradeNormalTxt,showRedDot)
    Common_Util.SetActive(self.specialBigUpgradeNormalTxt,showRedDot)

    Common_Util.SetActive(self.normalUpgradeGrayTxt,not showRedDot)
    Common_Util.SetActive(self.specialUpgradeGrayTxt,not showRedDot)
    Common_Util.SetActive(self.normalBigUpgradeGrayTxt,not showRedDot)
    Common_Util.SetActive(self.specialBigUpgradeGrayTxt,not showRedDot)

    self.normalUpgradeGray:SetEnable(not showRedDot)
    self.specialUpgradeGray:SetEnable(not showRedDot)
    self.normalBigUpgradeGray:SetEnable(not showRedDot)
    self.specialBigUpgradeGray:SetEnable(not showRedDot)

    Common_Util.SetActive(self.attributeToggleRedPoint,showRedDot)
end

function UIView:ShowUpgradeParticle()
    for i,v in ipairs(self.saveAttribute) do
        Common_Util.SetActive(self.upgradeParticleList[v],true)
    end
end

function UIView:InitUpgradeParticle()
    local nextLevelWeaponCfg = GWG.GWHomeMgr.droneData.OnGetNextDroneCfgData()
    if nextLevelWeaponCfg then
        local newAttributeList = cfg_util.StringToArray(nextLevelWeaponCfg.strParam2,";","#")
        local attributeList = cfg_util.StringToArray(self.weaponCfg.strParam2,";","#")
        if tonumber(newAttributeList[2][2]) > tonumber(attributeList[2][2]) then
            table.insert(self.changeAttribute,1)
        end
        if tonumber(newAttributeList[1][2]) > tonumber(attributeList[1][2]) then
            table.insert(self.changeAttribute,2)
        end
        if tonumber(newAttributeList[3][2]) > tonumber(attributeList[3][2]) then
            table.insert(self.changeAttribute,3)
        end
        if self.weaponCfg.ConversionRate.data[0] ~= nextLevelWeaponCfg.ConversionRate.data[0] then
            table.insert(self.changeAttribute,4)
        elseif self.weaponCfg.ConversionRate.data[1] ~= nextLevelWeaponCfg.ConversionRate.data[1] then
            table.insert(self.changeAttribute,5)
        elseif self.weaponCfg.ConversionRate.data[2] ~= nextLevelWeaponCfg.ConversionRate.data[2] then
            table.insert(self.changeAttribute,6)
        end
    end

    for i,v in ipairs(self.changeAttribute) do
        table.insert(self.saveAttribute,v)
    end
    self.changeAttribute = {}
    for i,v in ipairs(self.upgradeParticleList) do
        Common_Util.SetActive(v,false)
    end
end

function UIView:SaveUpgradeParticle()
    self.saveAttribute = {}
    for i,v in ipairs(self.changeAttribute) do
        table.insert(self.saveAttribute,v)
    end
    self.changeAttribute = {}
    local nextLevelWeaponCfg = GWG.GWHomeMgr.droneData.OnGetNextDroneCfgData()
    if nextLevelWeaponCfg then
        local newAttributeList = cfg_util.StringToArray(nextLevelWeaponCfg.strParam2,";","#")
        local attributeList = cfg_util.StringToArray(self.weaponCfg.strParam2,";","#")
        if tonumber(newAttributeList[2][2]) > tonumber(attributeList[2][2]) then
            table.insert(self.changeAttribute,1)
        end
        if tonumber(newAttributeList[1][2]) > tonumber(attributeList[1][2]) then
            table.insert(self.changeAttribute,2)
        end
        if tonumber(newAttributeList[3][2]) > tonumber(attributeList[3][2]) then
            table.insert(self.changeAttribute,3)
        end
        if self.weaponCfg.ConversionRate.data[0] ~= nextLevelWeaponCfg.ConversionRate.data[0] then
            table.insert(self.changeAttribute,4)
        elseif self.weaponCfg.ConversionRate.data[1] ~= nextLevelWeaponCfg.ConversionRate.data[1] then
            table.insert(self.changeAttribute,5)
        elseif self.weaponCfg.ConversionRate.data[2] ~= nextLevelWeaponCfg.ConversionRate.data[2] then
            table.insert(self.changeAttribute,6)
        end
    end

    for i,v in ipairs(self.upgradeParticleList) do
        Common_Util.SetActive(v,false)
    end
end

function UIView:ShowAttributePanel()
    Common_Util.SetActive(self.attributePanel,true)
    Common_Util.SetActive(self.equipmentAttributeObj,false)
    Common_Util.SetActive(self.equipSlotObj,false)
    Common_Util.SetActive(self.changeOutlookBtn,true)
end

function UIView:UpdateUIPage(pageType)
    self.pageType = pageType
    for k,v in pairs(self.toggleList) do
        if self.toggleList[pageType] then
            self.toggleList[pageType].isOn = true
        end
    end
    self:SetActive(self.Info,pageType ~= pageShowTypeEnum.Crystal)
    self:SetActive(self.crystalPage,pageType == pageShowTypeEnum.Crystal)
    if pageType ~= pageShowTypeEnum.Crystal then
        self:SetActive(self.attributePanel,pageType == pageShowTypeEnum.Attribute)
        self:SetActive(self.changeOutlookBtn,pageType == pageShowTypeEnum.Attribute)
        self:SetActive(self.equipmentAttributeObj,pageType == pageShowTypeEnum.Equipment)
        self:SetActive(self.equipSlotObj,pageType == pageShowTypeEnum.Equipment)
        self:SetActive(self.combatPage,pageType == pageShowTypeEnum.Combat)
    end
        self:SetActive(self.rtf_crtstalBoxBg,pageType == pageShowTypeEnum.Combat or pageType == pageShowTypeEnum.Crystal)

    if pageType == pageShowTypeEnum.Equipment then
        self.equipSlotAnim:Play()
    end
end

function UIView:UpdateCombat(data)
    local cfg = data.curLevelCfg
    self.addHpValue.text = string.format("+%d",cfg.weaponHP)
    self.addAtkValue.text = string.format("+%d",cfg.weaponATK)
    self.addDefValue.text = string.format("+%d",cfg.weaponDEF)
    self.addSkillRateValue.text = string.format("+%d",cfg.AmplificationLevel)
    self.curCombatLevel.text = string.format2("Lv.{%s1}",cfg.stageLevel)
    if not data.nextStageLevelCfg then
        self.nextCombatStageLevel.text = lang.Get(607049)
    else
        self.nextCombatStageLevel.text = string.format2(lang.Get(607048),data.nextStageLevelCfg.stageLevel)
    end
    self.stageBg:Switch(cfg.stage)
    self.curStageText.text = string.format2(lang.Get(607047),cfg.stage)
    self.curStageOutLine.effectColor = StageTitleColor[cfg.stage] or StageTitleColor[1]
    if data.nextLevelCfg then
        self.combatExp.text = string.format("<color=#92D757>%d</color>/%d",data.curLevelExp,cfg.exp)
        self.combatExpSlider.value = data.curLevelExp / cfg.exp
    end
    local isMaxLevel = cfg.stageLevel >= data.maxCombatLevel
    self:SetActive(self.combatExpSlider,not isMaxLevel)
    self:SetActive(self.combatBtnGrayText,isMaxLevel)
    self.combatBtnGray:SetEnable(isMaxLevel)
end

function UIView:UpdateCombatAndCrystalToggleUI(data)    
    self:SetActive(self.combatToggle,data.combatIsUnlock)
    self:SetActive(self.crystalToggle,data.crystalIsUnlock)
end

function UIView:InitPanelData()
    self:OnUpdatePanel(true)
    self:UpdateMagicWeaponItem(true)
    self:UpdateUIPage(pageShowTypeEnum.Attribute)
    for i,v in ipairs(self.bigSkillStarTipsList) do
        LayoutRebuilder.ForceRebuildLayoutImmediate(v.starDescTran)
    end
    local path=nil
    local helper_personalInfo = require "helper_personalInfo"
    if self.weaponSkin and self.weaponSkin == 0 then
        local data_personalInfo = require "data_personalInfo"
        self.weaponSkin = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID)
    end
    if self.weaponSkin and self.weaponSkin ~= 0 then
        path = helper_personalInfo.GetAnimalPrefabPath(self.weaponSkin)
    else
        local defaultAnimalCfg = game_scheme:InitBattleProp_0(8234)
        if defaultAnimalCfg and defaultAnimalCfg.szParam and defaultAnimalCfg.szParam.data then
            path = helper_personalInfo.GetAnimalPrefabPath(defaultAnimalCfg.szParam.data[0])
        end
    end
    if self.model then
        self.model:Dispose()
        self.model = nil
    end
    self.model = model_item.CModelItem():InitSingle(path, function(_rt)
        if self:IsValid() then
            self.modelRt.texture = _rt
            util.DelayCall(0.1,function()
                if self and self:IsValid() and self.modelRt and not util.IsObjNull(self.modelRt) then
                    self.modelRt.gameObject:SetActive(true)
                end
            end)
            self.modelRt:SetNativeSize()

            local cameraData = weapon_data.weaponCameraData[self.weaponId]

            self.model.actorNode.gameObject.transform.parent.localRotation = Quaternion.Euler(0,cameraData.rotate,0)
            self.model:MoveCamera(cameraData.pos)
            self.model:LookAtModel()
            self.modelRt.transform.anchoredPosition = cameraData.imagePos
        end
    end,false,{x=1024,y=1400})

    -- if modulCfg.showChildModel and modulCfg.showChildModel == 1 then
    --     self.model:SetShowOtherModel()
    -- end
    self.YieldFrame = function()

        if self.OverLayItem then
            self.OverLayItem:Close()
            self.OverLayItem = nil
        end

        --- OverLayItem
        self.OverLayItem = overlay_item.Create()
        self.OverLayItem.CheckOverLay = function (screenpoint)
            local IsCanDrag = function ()
                local obj,moduleName = ui_window_mgr:GetTopUI()
                local condition1 = moduleName == "ui_new_magic_weapon_panel"
                local condition2 = Input.mousePosition.x > screen_util.width/2 - 300 * screen_util.width / 720 and Input.mousePosition.x < screen_util.width/2 + 300 * screen_util.width / 720 and Input.mousePosition.y > screen_util.height/2 - 200 * screen_util.height / 1280 and Input.mousePosition.y < screen_util.height/2 + 300 * screen_util.height / 1280
                return condition1 and condition2
            end
            if IsCanDrag()then
                if self.model and self.model.actorNode then
                    self.modelRotationY = self.model.actorNode.angleY_Rotate
                end
                local onDragHeroLayout =  self:OnOverHeroLayout(screenpoint)
                return function ( sp )
                    if onDragHeroLayout then onDragHeroLayout(sp) end
                end
            end
        end
        self.OverLayItem:Start()
    end
    util.YieldFrame(self.YieldFrame)
    self:HideAllTips()
end

function UIView:ShowUpgradeTips()
    Common_Util.SetActive(self.tipsMask,true)
    Common_Util.SetActive(self.tipsObj,true)
    Common_Util.SetActive(self.darkMask,true)
    Common_Util.SetActive(self.bigUpgradeTips,true)
    self:ResetItemLayout()
    local upgradeType = self.weaponCfg.UpgradeType --为1表示非突破升级
    Common_Util.SetActive(self.bigUpgradeNormalUpgradeObj,upgradeType == 1)
    Common_Util.SetActive(self.bigUpgradeSpecialUpgradeObj,upgradeType == 2)
end

function UIView:ShowBigAttributeTips()
    Common_Util.SetActive(self.tipsMask,true)
    Common_Util.SetActive(self.tipsObj,true)
    Common_Util.SetActive(self.darkMask,true)
    Common_Util.SetActive(self.bigAttributeTips,true)
end

function UIView:HideAllTips()
    Common_Util.SetActive(self.tipsMask,false)
    Common_Util.SetActive(self.tipsObj,false)
    Common_Util.SetActive(self.attributeTips,false)
    Common_Util.SetActive(self.skillTips,false)
    Common_Util.SetActive(self.darkMask,false)
    Common_Util.SetActive(self.bigAttributeTips,false)
    Common_Util.SetActive(self.bigUpgradeTips,false)
end

function UIView:ShowAtkTips()
    local redVec2 = RectTransformUtility.WorldToScreenPoint(self.uiCamera, self.atkBtn.transform.position)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle( self.tipsParent , redVec2, self.uiCamera)
    self:ShowAttributeTips(string.format2(lang.Get(607029),lang.Get(607017),"<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACK).."</color>",
            "<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACKRATE)/100 .. "%</color>",lang.Get(603006),
            "<color=#3CF493>"..self.heroAddAtk.."</color>"),localpoint);
end

function UIView:ShowHpTips()
    local redVec2 = RectTransformUtility.WorldToScreenPoint(self.uiCamera, self.hpBtn.transform.position)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle( self.tipsParent , redVec2, self.uiCamera)
    self:ShowAttributeTips(string.format2(lang.Get(607029),lang.Get(607015),"<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HP).."</color>",
            "<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HPRATE)/100 .. "%</color>",lang.Get(603005),
            "<color=#3CF493>"..self.heroAddHp.."</color>"),localpoint);
end

function UIView:ShowDefTips()
    local redVec2 = RectTransformUtility.WorldToScreenPoint(self.uiCamera, self.defBtn.transform.position)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.tipsParent , redVec2, self.uiCamera)
    self:ShowAttributeTips(string.format2(lang.Get(607029),lang.Get(607018),"<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCE).."</color>",
            "<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCERATE)/100 .. "%</color>",lang.Get(603007),
            "<color=#3CF493>"..self.heroAddDef.."</color>"),localpoint);
end

function UIView:ShowAtkRateTips()
    local redVec2 = RectTransformUtility.WorldToScreenPoint(self.uiCamera, self.atkRateBtn.transform.position)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle( self.tipsParent , redVec2, self.uiCamera)
    self:ShowAttributeTips(string.format2(lang.Get(607029),lang.Get(607017),"<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACK).."</color>",
            "<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACKRATE)/100 .. "%</color>",lang.Get(603006),
            "<color=#3CF493>"..self.heroAddAtk.."</color>"),localpoint);
end

function UIView:ShowHpRateTips()
    local redVec2 = RectTransformUtility.WorldToScreenPoint(self.uiCamera, self.hpRateBtn.transform.position)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle( self.tipsParent , redVec2, self.uiCamera)
    self:ShowAttributeTips(string.format2(lang.Get(607029),lang.Get(607015),"<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HP).."</color>",
            "<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_HPRATE)/100 .. "%</color>",lang.Get(603005),
            "<color=#3CF493>"..self.heroAddHp.."</color>"),localpoint);
end

function UIView:ShowDefRateTips()
    local redVec2 = RectTransformUtility.WorldToScreenPoint(self.uiCamera, self.defRateBtn.transform.position)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.tipsParent , redVec2, self.uiCamera)
    self:ShowAttributeTips(string.format2(lang.Get(607029),lang.Get(607018),"<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCE).."</color>",
            "<color=#3CF493>"..GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_DEFENCERATE)/100 .. "%</color>",lang.Get(603007),
            "<color=#3CF493>"..self.heroAddDef.."</color>"),localpoint);
end

function UIView:ShowAttributeTips(text,pos)
    Common_Util.SetActive(self.tipsMask,true)
    Common_Util.SetActive(self.tipsObj,true)
    Common_Util.SetActive(self.attributeTips,true)
    self.attributeTipsText.text = text
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.attributeTips)
    Common_Util.SetLocalPos(self.attributeTips, math.max(pos.x,-137), pos.y)
    if pos.x < -137 then
        Common_Util.SetLocalPos(self.attributeTipsArrow, -118, -2.5)
    else
        Common_Util.SetLocalPos(self.attributeTipsArrow, 0, -2.5)
    end
end

function UIView:ShowSkillTips()
    Common_Util.SetActive(self.tipsMask,true)
    Common_Util.SetActive(self.tipsObj,true)
    Common_Util.SetActive(self.skillTips,true)
end

function UIView:OnOverHeroLayout(screenpoint)
    return function ( sp )
        if self.model and self.model.actorNode then
            if not self.modelRotationY then
                self.modelRotationY = self.model.actorNode.angleY_Rotate
            end
            local moveLength = (screenpoint.x - Input.mousePosition.x) / 1.5 + self.modelRotationY
            self.model.actorNode.angleY_Rotate = moveLength
        end
    end
end

function GetAssetPath(assetPath)
    local model_res = require "model_res"
    return model_res.GetResPath(assetPath)
end
---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if  data  and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil,nil,true)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
