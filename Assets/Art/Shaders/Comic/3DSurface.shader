// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

// Upgrade NOTE: upgraded instancing buffer 'InstanceProperties' to new syntax.

// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

//Base on Offical Shader ------->"Mobile/Bumped Specular" 

Shader "Comic/3DSurface"
{
    Properties
    {
        _Shininess ("Shininess", Range (0.03, 1)) = 0.13 //�߹�ǿ��
        [HDR]_BaseColor("Color", Color) = (1,1,1,1)
        [NoScaleOffset] _MainTex ("Base (RGB) Gloss (A)", 2D) = "white" {}
        [NoScaleOffset] _BumpMap ("Normal Map", 2D) = "bump" {}

        [Toggle(_ENABLE_CUBEMAP)]_EnableCubemap("EnableCubemap",Float) = 0
        _Cubemap("Cubemap",Cube) = "_Skybox" {}
        _ReflectionIntensity("Reflection Intensity",Range(0,5)) = 1
        _FresnelPower("Fresnel Power",Range(0,10)) = 5
        _FresnelScale("Fresnel Scale",Range(0,1)) = 0.5
        _FresnelColor("Fresnel Color",Color) = (1,1,1,1)

        //�Է�����ͼ
        [Header(Emission)][Space(5)]
        [Toggle(EMOSSION_ON)] _EnableEmossion("Emission", Float) = 0
        [HideDisabled(EMOSSION_ON)] [NoScaleOffset] _EmissionMap ("Emission Texture", 2D) = "bump" {}
        [HideDisabled(EMOSSION_ON)] _EmissionPower("Emission Power",Range(0,5)) = 2

        //�ܽ�
        [Header(Dissolve)][Space(5)]
        [Toggle(DISSOLVE_ON)]_EnableDissolve("Use Dissolve?", Float) = 0
        [HideDisabled(DISSOLVE_ON)]_DissolveTex("Dissolve Texture(RGB)", 2D) = "white" {}//�ܽ�Noise��ͼ
        [HideDisabled(DISSOLVE_ON)]_SliceAmount("Dissolve Amount", Range(0.0, 1.0)) = 0//�ܽ�̶�
        //  ���ܽ�ı�Ե
        [HideDisabled(DISSOLVE_ON)][HDR]_DissolveFirstColor("Dissolve First Color", Color) = (0.9,0.9,0.9,1)//HDR����ɫ��֧��hdr��Ⱦ�������з���Ч��
        [HideDisabled(DISSOLVE_ON)][HDR]_DissolveSecondColor("Dissolve Second Color", Color) = (1,1.13,1.13,1)
        [HideDisabled(DISSOLVE_ON)]_LineWidth("Dissolve Edge Size", Range(0.0, 0.2)) = 0.1 //�ܽ�ı�Ե����
        //  �����ܽ�
        [Space(5)]
        [Toggle(_DISSOLVE_WORLDPOS_ON)]_EnableDissolveWorldPos("Use Dissolve WorldPos?", Float) = 0
        [HideDisabled(_DISSOLVE_WORLDPOS_ON)]_WorldDissolveAmount("WorldDissolve Amount", Range(-3,3)) = 0 //�����ܽ�ĳ̶�
		[HideDisabled(_DISSOLVE_WORLDPOS_ON)]_WorldDissolveDirection("WorldDissolve Direction", Vector) = (0,1,0,0) //�����ܽⷽ��
        
        //�û�
        [Header(Gray)][Space(5)]
        [Toggle(GRAY_ON)] _EnableGray("Use Gray?", Float) = 0
        [HideDisabled(GRAY_ON)]_Grayness("Grayness", Range(0, 1)) = 0
        [HideDisabled(GRAY_ON)]_GrayDotColor("Gray dot Color",Color) = (0.227, 0, 0)

        //�Է���
        [Header(SelfLight)][Space(5)]
        [Toggle] _EnableSelfLight("Enable self light?", Float) = 0
        [HDR]_SelfLightColor ("Color", Color) = (1.74,2.8,3.38,1)
        _SelfLightness ("Lightness", Range(0, 1)) = 0

        //��Ե��
        [Header(Rim)][Space(5)]
        _RimColor("Rim Color", Color) = (1,1,1,1)
        _RimLightRotation("Rim Light Rotation", Range(0, 360)) = 0
        _RimLightSoft("Rim Light Soft", Range(0, 0.7)) = 0 //����������Ե��С��soft��Ϊ1��, soft<rim<threshold
        _RimLightThreshold("Rim Light Threshold", Range(0.8, 1)) = 1 //rim<threshold���б�Ե�⣨����threshold��Ϊ0��
        _RimPower ("Rim Power", Float) = 1
        _RimBias ("Rim Bias", Float) = 0
        _RimScale ("Rim Scale", Float) = 0
        
        _CenterOffset("Center Offset", Float) = 0

        [Space(5)]
        [Header(Engine Height Fog)]
        [Toggle]_CloseHeightFog("Close Height Fog",Float) = 0
        
         // AnimatedBoneMatrices 
        [Header(GPU)][Space(5)]
        [NoScaleOffset]_AnimatedBoneMatrices("AnimatedBoneMatrices", 2D) = "white" {}
        [Toggle(GPU_ANIMATION_ON)]_EnableGPUAnimation("GPUAnimation", Float) = 0
        _AnimationState("AnimationState", Vector) = (0,0,0,0)
        
         _Color32("Color32", Float) = 0
         _SpeedFactor("SpeedFactor", Float) = 1
        //������
        [Header(Stencil)]
        _Stencil ("Stencil ID", Float) = 2
        _StencilReadMask ("Stencil Read Mask", Float) = 1
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp ("Stencil Comparison", Float) = 8
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp ("Stencil Operation", Float) = 2
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOpFail ("Stencil Fail Operation", Float) = 0
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOpZFail ("Stencil Z-Fail Operation", Float) = 0
    }
    SubShader
    {
        Tags
        {
            "RenderType"="Opaque" "Queue"="Geometry" "RenderPipeline"="UniversalPipeline"
        }
        LOD 250

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            ReadMask [_StencilReadMask]
            Pass [_StencilOp]
        }

        HLSLINCLUDE
        #undef UNITY_INSTANCED_SH
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

        CBUFFER_START(UnityPerMaterial)
            float4 _GrayDotColor;
            float4 _SelfLightColor;
            half4 _DissolveFirstColor;
            half4 _DissolveSecondColor;

            float4 _MainTex_ST;
            float4 _BumpMap_ST;
            float4 _DissolveTex_ST;
            float4 _EmissionMap_ST;

            half4 _BaseColor;

            half4 _RimColor;


	        half4 _WorldDissolveDirection;

        	float4 _AnimationState;

            float _Color32;
            float _SpeedFactor;
            half _Shininess;
            
            float _EnableGray;
            float _Grayness;
            
            float _SelfLightness;
            float _EnableSelfLight;

            
            half _EmissionPower;

            
            float _SliceAmount;
            float _LineWidth;
            // #if defined(_DISSOLVE_WORLDPOS_ON)
	        half _WorldDissolveAmount;
            // #endif


            half _RimLightRotation;
            half _RimLightSoft;
            half _RimLightThreshold;
            half _RimPower;
            half _RimBias;
            half _RimScale;

            half _CenterOffset;

            half _CloseHeightFog;

            half _ReflectionIntensity;
            half _FresnelPower;
            half _FresnelScale;
            half4 _FresnelColor;


        CBUFFER_END
        ENDHLSL
        Pass
        {
            Name "FORWARD"
            Tags
            {
                "LightMode"="UniversalForward"
            }

            //    Cull Back

            HLSLPROGRAM
            #undef UNITY_INSTANCED_SH
            // #include "AutoLight.cginc"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/BuiltinFuns.hlsl"
            #include "Assets/cysoldierssortie/Shader/EngineFog/EngineHeightFog.cginc"
            

            #pragma multi_compile _ DISSOLVE_ON
            #pragma multi_compile _ EMOSSION_ON
            #pragma multi_compile _ _FOG_ENABLED
            // #pragma multi_compile_fwdbase noshadowmask nodynlightmap nolightmap
            //#pragma multi_compile_fog
            #pragma shader_feature_local _ _DISSOLVE_WORLDPOS_ON
            #pragma shader_feature_local_fragment _ENABLE_CUBEMAP
            #pragma multi_compile_local _ GPU_ANIMATION_ON
            #pragma multi_compile_instancing
            #pragma multi_compile _ _ADDITIONAL_LIGHTS
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS

            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"
            #if defined(GPU_ANIMATION_ON)
                #include "GpuEcsAnimator.cginc"
            #endif

            
            #pragma enable_d3d11_debug_symbols

            #pragma vertex vert
            #pragma fragment frag

            sampler2D _MainTex;
            sampler2D _BumpMap;
			sampler2D _EmissionMap;
			sampler2D _DissolveTex;
			Texture2D<float4> _AnimatedBoneMatrices;

			samplerCUBE _Cubemap;			
            // struct a2v{
            //     float4 vertex:POSITION;
            //     float3 normal:NORMAL;
            //     float4 tangent:TANGENT;
            //     float4 texcoord:TEXCOORD0;
            // };
			struct appdata_full {
				float4 vertex : POSITION;
				float4 tangent : TANGENT;
				float3 normal : NORMAL;
				float4 texcoord : TEXCOORD0;
				float4 texcoord1 : TEXCOORD1;
				float4 texcoord2 : TEXCOORD2;
				float4 texcoord3 : TEXCOORD3;
				half4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
            
            struct v2f
            {
                float4 pos:SV_POSITION;
                float4 uv:TEXCOORD0;
                float3 worldViewDir:TEXCOORD1;
                float3 vertexLighting : TEXCOORD2;
                float4 Ttow0 : TEXCOORD3;
                float4 Ttow1 : TEXCOORD4;
                float4 Ttow2 : TEXCOORD5;
                UNITY_LIGHTING_COORDS(6, 7)
                #ifndef GPU_ANIMATION_ON
                float4 shadowCoord : TEXCOORD8;
                #endif
                half4 selfLightColor : TEXCOORD9;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            v2f vert(appdata_full v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_TRANSFER_INSTANCE_ID(v, o);
                 UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
             
                o.uv.xy = TRANSFORM_TEX(v.texcoord, _MainTex);


                float3 vertex = v.vertex;
                half3 normal = v.normal;
                half3 tangent = v.tangent;
                float4x4 localToWorld = UNITY_MATRIX_M;
#ifdef GPU_ANIMATION_ON
                float4 boneWeights = v.texcoord1;

                float4 animationState;

                #ifdef UNITY_DOTS_INSTANCING_ENABLED
                    animationState = PlayAnimationState(_AnimationState, _SpeedFactor);
                #else
                UNITY_BRANCH
                    if (asuint(localToWorld[3].y) > 0)
                    {
                        animationState = PlayAnimationState(localToWorld[3]);
                        localToWorld[3] = float4(0, 0, 0, 1);
                    }
                    else
                        animationState = _AnimationState;
                #endif

                AnimateBlend_half(v.vertex, v.normal, v.tangent, boneWeights, _AnimatedBoneMatrices, animationState, vertex, normal, tangent);
#endif
                float4 worldPos = mul(localToWorld, float4(vertex, 1.0));
                o.pos = mul(UNITY_MATRIX_VP, worldPos);
                float3 worldNormal = normalize(mul(localToWorld, normal)); // UNITY_ASSUME_UNIFORM_SCALING
                float3 worldTangent = normalize(mul(localToWorld, tangent.xyz));
                float3 worldBinormal = cross(worldNormal, worldTangent) * v.tangent.w;
            
                o.Ttow0 = float4(worldTangent.x, worldBinormal.x, worldNormal.x, worldPos.x);
                o.Ttow1 = float4(worldTangent.y, worldBinormal.y, worldNormal.y, worldPos.y);
                o.Ttow2 = float4(worldTangent.z, worldBinormal.z, worldNormal.z, worldPos.z);

                o.vertexLighting = 0;
                float3 viewDirForLight = _WorldSpaceCameraPos.xyz - worldPos;
                float3 worldLightDir = UnityWorldSpaceLightDir(worldPos);
                o.worldViewDir = normalize(normalize(viewDirForLight) + normalize(worldLightDir));

                o.uv.z = clamp(worldPos.y+_CenterOffset, 0.0, 1.0);//rim area

                //�����ܽ�
                #if defined(_DISSOLVE_WORLDPOS_ON)
                float3 rootPos = float3(localToWorld[0].w, localToWorld[1].w, localToWorld[2].w);
			    float3 root2WorldDir = worldPos - rootPos;
                // float3 root2WorldDir = (worldPos - rootPos) / _DissolveAmount;
			    // root2WorldDir = mul(Rotation(half4(root2WorldDir, 1.0)), v.vertex);//����root����ת�������ʱ����
                //  �������ܽⷽ���ϵ�ͶӰ!!!
                float worldFactor = dot(_WorldDissolveDirection, root2WorldDir);
                o.uv.w = worldFactor;
                #endif

                    #if defined(UNIVERSAL_PIPELINE_CORE_INCLUDED) || (UNITY_SHOULD_SAMPLE_SH && !UNITY_SAMPLE_FULL_SH_PER_PIXEL)
                        float3 shLight = ShadeSH9(float4(worldNormal,1.0));
                        o.vertexLighting = shLight;
                    #else
                        o.vertexLighting = 0.0;
                    #endif
#ifndef GPU_ANIMATION_ON
                float4 shadowCoord = TransformWorldToShadowCoord(worldPos.xyz);
                o.shadowCoord = shadowCoord;
                o.selfLightColor = half4(_SelfLightColor.xyz,_SelfLightness);
#else
                 half4 selfLightColor = 0;
                #ifdef UNITY_DOTS_INSTANCING_ENABLED
                   uint raw = asuint(_Color32);
                    selfLightColor.a = (raw & 0xFF) / 127.0;
                    selfLightColor.b = ((raw >> 8) & 0xFF) / 127.0;
                    selfLightColor.g = ((raw >> 16) & 0xFF) / 127.0;
                    selfLightColor.r = ((raw >> 24) & 0xFF) / 127.0;
                #else
                    if (asuint(UNITY_MATRIX_M[3].y) > 0)
                    {
                        uint raw = asuint(UNITY_MATRIX_M[3].w);
                        selfLightColor.a = (raw & 0xFF) / 127.0;
                        selfLightColor.b = ((raw >> 8) & 0xFF) / 127.0;
                        selfLightColor.g = ((raw >> 16) & 0xFF) / 127.0;
                        selfLightColor.r = ((raw >> 24) & 0xFF) / 127.0;
                    }
                #endif
                o.selfLightColor = selfLightColor;
#endif

                return o;
            }

            inline float3 RotateAroundYInDegrees(float3 vertex, float degrees)
	        {
		        float alpha = degrees * UNITY_PI / 180.0;
		        float sina, cosa;
		        sincos(alpha, sina, cosa);
		        float2x2 m = float2x2(cosa, -sina, sina, cosa);
                //TODO Why ?
		        // return float3(mul(m, vertex.xy), vertex.z).xzy;
                return float3(mul(m, vertex.yz), vertex.x).xzy;
	        }

            inline void Reflection(float3 normalWS,float3 viewDirWS, inout half3 reflectColor)
            {
                #if defined(_ENABLE_CUBEMAP)
                      float3 reflectVec = reflect(-viewDirWS,normalWS);

                    half4 cubemap = texCUBE(_Cubemap,reflectVec);
                    cubemap *= _ReflectionIntensity;

                    float fresnel = _FresnelScale + (1-_FresnelScale) * pow(1-saturate(dot(normalWS,viewDirWS)),_FresnelPower);
                    half4 fresnelColor = fresnel * _FresnelColor;

                    reflectColor = fresnelColor * cubemap;
                #endif
            }

            inline void AdditionalLightDiffuse(float3 normalWS,half3 albedo,float3 posWS,inout half3 additionalLight)
            {
                #ifdef _ADDITIONAL_LIGHTS
                        uint pixelLightCount =  GetAdditionalLightsCount();
                        for (uint lightIndex = 0u; lightIndex < pixelLightCount; ++lightIndex)
                        {
                            Light light = GetAdditionalLight(lightIndex, posWS);
                            half3 attenuatedLightColor = light.color * (light.distanceAttenuation * light.shadowAttenuation);
                        
                            half3 lightDir = normalize(light.direction);
                            half3 additionalDiffuse = attenuatedLightColor * albedo * max(0, dot(normalWS, lightDir));                
                        
                            additionalLight += additionalDiffuse;
                        }
                #endif
            }

            half4 frag(v2f i):SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
                float3 worldPos = float3(i.Ttow0.w, i.Ttow1.w, i.Ttow2.w);
                
#ifdef GPU_ANIMATION_ON
                Light mainLight = GetMainLight();
#else
                Light mainLight = GetMainLight(i.shadowCoord);
#endif
                #ifndef USING_DIRECTIONAL_LIGHT
                    half3 lightDir = UnityWorldSpaceLightDir(worldPos);
                #else
                    half3 lightDir = mainLight.direction;
                #endif

                half3 worldLightDir = normalize(lightDir);

                half4 packedNormal = tex2D(_BumpMap, i.uv);
                half rimArea = packedNormal.a;
                half3 tangentNormal = UnpackNormal(packedNormal);

                half3 worldNormal = normalize(half3(dot(i.Ttow0.xyz, tangentNormal), dot(i.Ttow1.xyz, tangentNormal),
                                                      dot(i.Ttow2.xyz, tangentNormal)));

                half4 tex = tex2D(_MainTex, i.uv) * _BaseColor;
                half3 albedo = tex.rgb;

                half3 grayColor = dot(albedo.rgb, _GrayDotColor);
                {
                    albedo.rgb = lerp(albedo.rgb, grayColor, _Grayness);
                }

#ifndef UNITY_DOTS_INSTANCING_ENABLED
                albedo.rgb = lerp(albedo.rgb, _SelfLightColor.xyz, _SelfLightness);
#else
                albedo.rgb = lerp(albedo.rgb, i.selfLightColor.xyz, i.selfLightColor.a);
#endif

#ifndef GPU_ANIMATION_ON // DOTS_INSTANCING 默认关溶解，减少dicard 引起的overdraw
                    #ifdef DISSOLVE_ON
                        //ԭ���ܽ�
                        half burnFactor = tex2D(_DissolveTex, i.uv).r;
                        burnFactor = burnFactor - _SliceAmount;//�����ܽ�̶�
                
                        //�����ܽ�
                        float posW = 0;
                        #if defined(_DISSOLVE_WORLDPOS_ON)
                        half worldFactor = i.uv.w;
                        // return worldFactor;//fortest
                        worldFactor = worldFactor - _WorldDissolveAmount;
                        burnFactor -= worldFactor;
                        #endif
                
                        clip(burnFactor); 
                        half edge = 1 - smoothstep(0.0, _LineWidth, burnFactor);//��Ե����
                        half4 dissolveColor = lerp(_DissolveFirstColor, _DissolveSecondColor, edge);
                        dissolveColor = pow(max(0.001,dissolveColor), 10);
                        //�����ϱ�Ե��ɫ���Ǳ�Ե��ԭ��albedo��
                        //ע��_SliceAmount�������0������Ӷ����ܽ�ʱ��û�б�ԵЧ��
                        albedo = lerp(albedo.rgb, dissolveColor, edge * step(0.0001, _SliceAmount));
                    #endif
#endif


                half4 finalColor = half4(0,0,0,1);
    
                    half3 vertexLighting = i.vertexLighting * albedo;
                    half3 additionalLightDiffuse = (half3)0;
                    AdditionalLightDiffuse(worldNormal,albedo,worldPos,additionalLightDiffuse);
                    vertexLighting += additionalLightDiffuse;
                    
                    finalColor.rgb += vertexLighting;
                    half attenu = mainLight.shadowAttenuation*mainLight.distanceAttenuation;
                    half3 diffuse = mainLight.color * albedo * max(0, dot(worldNormal, worldLightDir))*attenu;
                    half3 specular = mainLight.color * pow(max(0.001, dot(worldNormal, i.worldViewDir)), 128 * _Shininess) * tex.a*attenu;
#ifndef GPU_ANIMATION_ON
                    finalColor.rgb += half3((diffuse + specular));
#else
                    finalColor.rgb += half3((diffuse + specular));
#endif
                //#endif

                //emission
                #ifdef EMOSSION_ON
                    half4 em = tex2D(_EmissionMap, i.uv);
                    em.rgb = lerp(em.rgb, grayColor, _Grayness);
                    finalColor.rgb += em * _EmissionPower;             
                #endif

                //Rim
                //float fresnel = saturate(1 - saturate(dot(worldNormal, i.worldViewDir)));//�����viewDir���Ȳ���
                //float fresnel = saturate(dot(worldNormal, UnityWorldSpaceViewDir(worldPos)));
                /*half3 customLightDir = RotateAroundYInDegrees(worldLightDir, _RimLightRotation);
                half fresnel = max(0, dot(customLightDir, worldNormal));
                
                fresnel = 1 - fresnel;
                fresnel = smoothstep(_RimLightThreshold, _RimLightSoft, fresnel);
                fresnel = (pow(max(0.001,fresnel), _RimPower) + _RimBias);
                fresnel *= _RimScale * i.uv.z;
                fresnel *= rimArea;
                //return fresnel.xxxx;
                
                finalColor.rgb = lerp(finalColor.rgb, finalColor.rgb+_RimColor.rgb, fresnel);*/

                half3 reflectColor = (half3)0;
                float3 viewDirWS =  UnityWorldSpaceViewDir(worldPos);
                viewDirWS = normalize(viewDirWS);
                Reflection(worldNormal,viewDirWS,reflectColor);
                finalColor.rgb += reflectColor;

                UNITY_BRANCH
                if(_CloseHeightFog==0)
                {
                    MixFog(finalColor,worldPos,worldLightDir);
                }
 
                return finalColor;
            }
            ENDHLSL
        }


        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            Cull Back
            ZWrite On
            ZTest LEqual

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_shadowcaster
            #pragma multi_compile_local _ GPU_ANIMATION_ON
            #pragma multi_compile_instancing
            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"
            //#pragma multi_compile _ MESH_BATCH_GROUP_ON

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "GpuEcsAnimator.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float4 texcoord1 : TEXCOORD1;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            Texture2D<float4> _AnimatedBoneMatrices;

            v2f vert(appdata v)
            {
                UNITY_SETUP_INSTANCE_ID(v);
                v2f o;
                float3 vertex = v.vertex;
                float4x4 localToWorld = unity_ObjectToWorld;
#ifdef GPU_ANIMATION_ON
                float4 boneWeights = v.texcoord1;
                //float blendFactor = _AnimationState[0];
                 //float  frameIndex = _AnimationState[1];
                //half4 weights = frac(boneWeights) * (1.0 / 0.9);
                //int4 boneIndexs = boneWeights;
                //GetSimpleSkin3(v.vertex, v.normal, v.tangent, _AnimatedBoneMatrices, weights, boneIndexs, _FrameIndex, vertex, normal, tangent);
                float4 animationState;
                UNITY_BRANCH
                    if (asuint(localToWorld[3].y) > 0)
                    {
                        animationState = PlayAnimationState(localToWorld[3]);
                        localToWorld[3] = float4(0, 0, 0, 1);
                    }
                    else
                        animationState = _AnimationState;

                half3 normal;
                half3 tangent;
                AnimateBlend_half(v.vertex, normal, tangent, boneWeights, _AnimatedBoneMatrices, animationState, vertex, normal, tangent);
#endif
                float4 worldPos = mul(localToWorld, float4(vertex, 1.0));
                o.pos = mul(UNITY_MATRIX_VP, worldPos);
                return o;
            }

            float frag(v2f i) : SV_Target
            {
                return 0; // No color output needed
            }
            ENDHLSL
        }

    }
	SubShader
    {
        Tags
        {
            "RenderType"="Opaque" "Queue"="Geometry"
        }
        LOD 250

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            ReadMask [_StencilReadMask]
            Pass [_StencilOp]
        }

        Pass
        {
            Name "FORWARD"
            Tags
            {
                "LightMode"="ForwardBase"
            }

            //    Cull Back

            CGPROGRAM
            #undef UNITY_INSTANCED_SH
            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"
            #include "GpuEcsAnimator.cginc"
            #include "Assets/cysoldierssortie/Shader/EngineFog/EngineHeightFog.cginc"
            

            #pragma multi_compile _ DISSOLVE_ON
            #pragma multi_compile _ EMOSSION_ON
            #pragma multi_compile _ _FOG_ENABLED
            #pragma multi_compile_fwdbase noshadowmask nodynlightmap nolightmap
            //#pragma multi_compile_fog
            #pragma shader_feature_local _ _DISSOLVE_WORLDPOS_ON
            #pragma shader_feature_local_fragment _ENABLE_CUBEMAP
            #pragma multi_compile_local _ GPU_ANIMATION_ON
            #pragma multi_compile_instancing

            //#pragma multi_compile _ MESH_BATCH_GROUP_ON
            
            //#pragma enable_d3d11_debug_symbols

            #pragma vertex vert
            #pragma fragment frag

            sampler2D _MainTex;
            sampler2D _BumpMap;
            half _Shininess;
            
            uniform float _EnableGray;
            uniform float _Grayness;
            float4 _GrayDotColor;
            
            uniform float4 _SelfLightColor;
            uniform float _SelfLightness;
            uniform float _EnableSelfLight;

            sampler2D _EmissionMap;
            fixed _EmissionPower;

            sampler2D _DissolveTex;
            float _SliceAmount;
            fixed4 _DissolveFirstColor;
            fixed4 _DissolveSecondColor;
            float _LineWidth;
            #if defined(_DISSOLVE_WORLDPOS_ON)
	            half _WorldDissolveAmount;
	            half4 _WorldDissolveDirection;
            #endif

            Texture2D<float4> _AnimatedBoneMatrices;
            
            float4 _MainTex_ST;
            float4 _BumpMap_ST;
            float4 _DissolveTex_ST;
            float4 _EmissionMap_ST;

            half4 _BaseColor;

            half4 _RimColor;
            half _RimLightRotation;
            half _RimLightSoft;
            half _RimLightThreshold;
            half _RimPower;
            half _RimBias;
            half _RimScale;

            half _CenterOffset;

            half _CloseHeightFog;

            samplerCUBE _Cubemap;
            half _ReflectionIntensity;
            half _FresnelPower;
            half _FresnelScale;
            half4 _FresnelColor;
            
            // struct a2v{
            //     float4 vertex:POSITION;
            //     float3 normal:NORMAL;
            //     float4 tangent:TANGENT;
            //     float4 texcoord:TEXCOORD0;
            // };
            
            struct v2f
            {
                float4 pos:SV_POSITION;
                float2 uv:TEXCOORD0;
                float3 worldViewDir:TEXCOORD1;
                float3 vertexLighting : TEXCOORD2;
                float4 Ttow0 : TEXCOORD3;
                float4 Ttow1 : TEXCOORD4;
                float4 Ttow2 : TEXCOORD5;
                float2 addition : TEXCOORD6;
                UNITY_LIGHTING_COORDS(7, 8)
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            



            float4 _AnimationState;

            v2f vert(appdata_full v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_TRANSFER_INSTANCE_ID(v, o);
             
                o.uv = TRANSFORM_TEX(v.texcoord, _MainTex);


                float3 vertex = v.vertex;
                half3 normal = v.normal;
                half3 tangent = v.tangent;
                float4x4 localToWorld = unity_ObjectToWorld;
#ifdef GPU_ANIMATION_ON
                float4 boneWeights = v.texcoord1;
                //float blendFactor = _AnimationState[0];
                 //float  frameIndex = _AnimationState[1];
                //half4 weights = frac(boneWeights) * (1.0 / 0.9);
                //int4 boneIndexs = boneWeights;
                //GetSimpleSkin3(v.vertex, v.normal, v.tangent, _AnimatedBoneMatrices, weights, boneIndexs, _FrameIndex, vertex, normal, tangent);
                float4 animationState;
                UNITY_BRANCH
                if (asuint(localToWorld[3].y) > 0)
                {
                    animationState = PlayAnimationState(localToWorld[3]);
                    localToWorld[3] = float4(0, 0, 0, 1);
                }
                else
                    animationState = _AnimationState;
                AnimateBlend_half(v.vertex, v.normal, v.tangent, boneWeights, _AnimatedBoneMatrices, animationState, vertex, normal, tangent);
#endif
                float4 worldPos = mul(localToWorld, float4(vertex, 1.0));
                o.pos = mul(UNITY_MATRIX_VP, worldPos);
                float3 worldNormal = normalize(mul(localToWorld, normal)); // UNITY_ASSUME_UNIFORM_SCALING
                float3 worldTangent = normalize(mul(localToWorld, tangent.xyz));
                float3 worldBinormal = cross(worldNormal, worldTangent) * v.tangent.w;
            
                o.Ttow0 = float4(worldTangent.x, worldBinormal.x, worldNormal.x, worldPos.x);
                o.Ttow1 = float4(worldTangent.y, worldBinormal.y, worldNormal.y, worldPos.y);
                o.Ttow2 = float4(worldTangent.z, worldBinormal.z, worldNormal.z, worldPos.z);

                o.vertexLighting = 0;
                float3 viewDirForLight = UnityWorldSpaceViewDir(worldPos);
                float3 worldLightDir = UnityWorldSpaceLightDir(worldPos);
                o.worldViewDir = normalize(normalize(viewDirForLight) + normalize(worldLightDir));

                o.addition.x = clamp(worldPos.y+_CenterOffset, 0.0, 1.0);//rim area

                //�����ܽ�
                #if defined(_DISSOLVE_WORLDPOS_ON)
                float3 rootPos = float3(localToWorld[0].w, localToWorld[1].w, localToWorld[2].w);
			    float3 root2WorldDir = worldPos - rootPos;
                // float3 root2WorldDir = (worldPos - rootPos) / _DissolveAmount;
			    // root2WorldDir = mul(Rotation(half4(root2WorldDir, 1.0)), v.vertex);//����root����ת�������ʱ����
                //  �������ܽⷽ���ϵ�ͶӰ!!!
                float worldFactor = dot(_WorldDissolveDirection, root2WorldDir);
                o.addition.y = worldFactor;
                #endif


                //#ifdef LIGHTMAP_OFF
                    #if UNITY_SHOULD_SAMPLE_SH && !UNITY_SAMPLE_FULL_SH_PER_PIXEL
                        float3 shLight = ShadeSH9(float4(worldNormal,1.0));
                        o.vertexLighting = shLight;
                    #else
                        o.vertexLighting = 0.0;
                    #endif
        
                    #ifdef VERTEXLIGHT_ON
                        float3 vertexLight = Shade4PointLights(unity_4LightPosX0,unity_4LightPosY0,unity_4LightPosZ0,
                        unity_LightColor[0].rgb,unity_LightColor[1].rgb,unity_LightColor[2].rgb,unity_LightColor[3].rgb,
                        unity_4LightAtten0,worldPos,worldNormal);
                        o.vertexLighting+=vertexLight;
                    #endif
                //#endif

                TRANSFER_VERTEX_TO_FRAGMENT(o);
                //UNITY_TRANSFER_FOG(o,o.pos);
                return o;
            }

            inline float3 RotateAroundYInDegrees(float3 vertex, float degrees)
	        {
		        float alpha = degrees * UNITY_PI / 180.0;
		        float sina, cosa;
		        sincos(alpha, sina, cosa);
		        float2x2 m = float2x2(cosa, -sina, sina, cosa);
                //TODO Why ?
		        // return float3(mul(m, vertex.xy), vertex.z).xzy;
                return float3(mul(m, vertex.yz), vertex.x).xzy;
	        }

            inline void Reflection(float3 normalWS,float3 viewDirWS, inout half3 reflectColor)
            {
                #if defined(_ENABLE_CUBEMAP)
                      float3 reflectVec = reflect(-viewDirWS,normalWS);

                    half4 cubemap = texCUBE(_Cubemap,reflectVec);
                    cubemap *= _ReflectionIntensity;

                    float fresnel = _FresnelScale + (1-_FresnelScale) * pow(1-saturate(dot(normalWS,viewDirWS)),_FresnelPower);
                    half4 fresnelColor = fresnel * _FresnelColor;

                    reflectColor = fresnelColor * cubemap;
                #endif
            }

            half4 frag(v2f i):SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                float3 worldPos = float3(i.Ttow0.w, i.Ttow1.w, i.Ttow2.w);

                #ifndef USING_DIRECTIONAL_LIGHT
                    half3 lightDir = UnityWorldSpaceLightDir(worldPos);
                #else
                    half3 lightDir = _WorldSpaceLightPos0.xyz;
                #endif

                half3 worldLightDir = normalize(lightDir);

                half4 packedNormal = tex2D(_BumpMap, i.uv);
                half rimArea = packedNormal.a;
                half3 tangentNormal = UnpackNormal(packedNormal);

                half3 worldNormal = normalize(half3(dot(i.Ttow0.xyz, tangentNormal), dot(i.Ttow1.xyz, tangentNormal),
                                                      dot(i.Ttow2.xyz, tangentNormal)));

                fixed4 tex = tex2D(_MainTex, i.uv) * _BaseColor;
                fixed3 albedo = tex.rgb;

                fixed3 grayColor = dot(albedo.rgb, _GrayDotColor);
                {
                    albedo.rgb = lerp(albedo.rgb, grayColor, _Grayness);
                }
                
                if (asuint(unity_ObjectToWorld[3].y) > 0)
                {
                    uint raw = asuint(unity_ObjectToWorld[3].w);
                    float4 selfLightColor;
                    selfLightColor.a = (raw & 0xFF) / 127.0;
                    selfLightColor.b = ((raw >> 8) & 0xFF) / 127.0;
                    selfLightColor.g = ((raw >> 16) & 0xFF) / 127.0;
                    selfLightColor.r = ((raw >> 24) & 0xFF) / 127.0;

                    albedo.rgb = lerp(albedo.rgb, selfLightColor.xyz, selfLightColor.a);
                }
                else
                    albedo.rgb = lerp(albedo.rgb, _SelfLightColor, _SelfLightness);

            #ifndef GPU_ANIMATION_ON
                #ifdef DISSOLVE_ON
                    //ԭ���ܽ�
                    half burnFactor = tex2D(_DissolveTex, i.uv).r;
                    burnFactor = burnFactor - _SliceAmount;//�����ܽ�̶�
                
                    //�����ܽ�
                    float posW = 0;
                    #if defined(_DISSOLVE_WORLDPOS_ON)
                    half worldFactor = i.addition.y;
                    // return worldFactor;//fortest
                    worldFactor = worldFactor - _WorldDissolveAmount;
                    burnFactor -= worldFactor;
                    #endif
                
                    clip(burnFactor); 
                    half edge = 1 - smoothstep(0.0, _LineWidth, burnFactor);//��Ե����
                    half4 dissolveColor = lerp(_DissolveFirstColor, _DissolveSecondColor, edge);
                    dissolveColor = pow(max(0.001,dissolveColor), 10);
                    //�����ϱ�Ե��ɫ���Ǳ�Ե��ԭ��albedo��
                    //ע��_SliceAmount�������0������Ӷ����ܽ�ʱ��û�б�ԵЧ��
                    albedo = lerp(albedo.rgb, dissolveColor, edge * step(0.0001, _SliceAmount));
                #endif
            #endif


                fixed4 finalColor = 0;
                //#ifdef LIGHTMAP_OFF
                    // float3 shLight = ShadeSH9(float4(worldNormal,1.0));
                    // finalColor.rgb += shLight * albedo;

                    //SH VertexLight
                    fixed3 vertexLighting = i.vertexLighting * albedo;
                    finalColor.rgb += vertexLighting;

                    fixed3 diffuse = _LightColor0.rgb * albedo * max(0, dot(worldNormal, worldLightDir));
                    fixed3 specular = _LightColor0.rgb * pow(max(0.001, dot(worldNormal, i.worldViewDir)), 128 * _Shininess) * tex.a;
                    diffuse = max(0,diffuse);
                    specular = max(0,specular);
#ifndef GPU_ANIMATION_ON
                    UNITY_LIGHT_ATTENUATION(atten, i, worldPos);
                    finalColor.rgb += fixed3((diffuse + specular) * atten);
#else
                    finalColor.rgb += fixed3((diffuse + specular));
#endif
                //#endif

                //emission
                #ifdef EMOSSION_ON
                    fixed4 em = tex2D(_EmissionMap, i.uv);
                    em.rgb = lerp(em.rgb, grayColor, _Grayness);
                    finalColor.rgb += em * _EmissionPower;             
                #endif

                //Rim
                //float fresnel = saturate(1 - saturate(dot(worldNormal, i.worldViewDir)));//�����viewDir���Ȳ���
                //float fresnel = saturate(dot(worldNormal, UnityWorldSpaceViewDir(worldPos)));
                /*half3 customLightDir = RotateAroundYInDegrees(worldLightDir, _RimLightRotation);
                half fresnel = max(0, dot(customLightDir, worldNormal));
                
                fresnel = 1 - fresnel;
                fresnel = smoothstep(_RimLightThreshold, _RimLightSoft, fresnel);
                fresnel = (pow(max(0.001,fresnel), _RimPower) + _RimBias);
                fresnel *= _RimScale * i.addition.x;
                fresnel *= rimArea;
                //return fresnel.xxxx;
                
                finalColor.rgb = lerp(finalColor.rgb, finalColor.rgb+_RimColor.rgb, fresnel);*/

                half3 reflectColor = (half3)0;
                float3 viewDirWS =  UnityWorldSpaceViewDir(worldPos);
                viewDirWS = normalize(viewDirWS);
                Reflection(worldNormal,viewDirWS,reflectColor);
                finalColor.rgb += reflectColor;

                UNITY_OPAQUE_ALPHA(finalColor.a);
                //UNITY_APPLY_FOG(i.fogCoord,finalColor.rgb);
                //return half4(worldPos.xyz*0.02,1);

                UNITY_BRANCH
                if(_CloseHeightFog==0)
                {
                    MixFog(finalColor,worldPos,worldLightDir);
                }
 
                return finalColor;
            }
            ENDCG
        }


        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            Cull Back
            ZWrite On
            ZTest LEqual

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_shadowcaster
            #pragma multi_compile_local _ GPU_ANIMATION_ON
            #pragma multi_compile_instancing

            #include "UnityCG.cginc"
            #include "GpuEcsAnimator.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float4 texcoord1 : TEXCOORD1;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };
            float4 _AnimationState;
            Texture2D<float4> _AnimatedBoneMatrices;
            v2f vert(appdata v)
            {
                UNITY_SETUP_INSTANCE_ID(v);
                v2f o;
                float3 vertex = v.vertex;
                float4x4 localToWorld = unity_ObjectToWorld;
#ifdef GPU_ANIMATION_ON
                float4 boneWeights = v.texcoord1;
                //float blendFactor = _AnimationState[0];
                 //float  frameIndex = _AnimationState[1];
                //half4 weights = frac(boneWeights) * (1.0 / 0.9);
                //int4 boneIndexs = boneWeights;
                //GetSimpleSkin3(v.vertex, v.normal, v.tangent, _AnimatedBoneMatrices, weights, boneIndexs, _FrameIndex, vertex, normal, tangent);
                float4 animationState;
                UNITY_BRANCH
                    if (asuint(localToWorld[3].y) > 0)
                    {
                        animationState = PlayAnimationState(localToWorld[3]);
                        localToWorld[3] = float4(0, 0, 0, 1);
                    }
                    else
                        animationState = _AnimationState;

                half3 normal;
                half3 tangent;

                AnimateBlend_half(v.vertex, normal, tangent, boneWeights, _AnimatedBoneMatrices, animationState, vertex, normal, tangent);
#endif
                float4 worldPos = mul(localToWorld, float4(vertex, 1.0));
                o.pos = mul(UNITY_MATRIX_VP, worldPos);
                return o;
            }

            float frag(v2f i) : SV_Target
            {
                return 0; // No color output needed
            }
            ENDCG
        }

    }

    FallBack "Mobile/Bumped Diffuse"
}