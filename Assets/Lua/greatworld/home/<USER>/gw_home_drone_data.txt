--- Created by nyz.
--- DateTime: 2024/8/29 09:59
--- Des:召唤兽数据类

local require = require
local pairs = pairs
local ipairs = ipairs
local table = table
local math = math
local gw_const = require "gw_const"
local data_mgr = require "data_mgr"
local game_scheme = require "game_scheme"
local GWG  = GWG
local string = string
local tonumber = tonumber
local player_mgr = require "player_mgr"
local cfg_util = require "cfg_util"
local event         = require "event"

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -


---@class GWHomeDroneData
module("gw_home_drone_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("gw_home_drone_data")
---非服务器数据存储
local mc = _d.mde.const
local msg_item_match_funs =
{
    ["TPropData"] = function(item1,item2)
        return item1.partType == item2.partType
    end,
}

local DEFAULT_UPGRADE_STAGE = 0 --默认的升级类型

local isInit = false --初始化标记

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@see 初始化
function M.Init()
    if isInit then
        return
    end
    isInit = true
    mc.droneData = {}
    mc.weapon_cfg_cache = {} --缓存武器cfg
    
    mc.itemSynthesisList = {} --记录道具合成用，仅在初始化时读取一次
    local tempCount = game_scheme:Prescription_nums()
    for i=0,tempCount-1 do
        local tempData = game_scheme:Prescription(i)
        if tempData then
            if tempData.type == 4 then
                if mc.itemSynthesisList[tempData.target] then
                    mc.itemSynthesisList[tempData.target].decomposeCount = tempData.costnum.data[0];
                    mc.itemSynthesisList[tempData.target].decomposeTargetId = tempData.cost.data[0];
                    mc.itemSynthesisList[tempData.target].decomposeCfgId = tempData.prescriptionID;
                else
                    mc.itemSynthesisList[tempData.target] =
                    {
                        id = tempData.target,
                        decomposeCount = tempData.costnum.data[0],
                        decomposeTargetId = tempData.cost.data[0],
                        composeCfgId = 0,
                        composeCount = 0,
                        composeTargetId = nil,
                        decomposeCfgId = tempData.prescriptionID,
                    }
                end
                --饰品合成分解都只有一个
                
                local costKey = tempData.cost.data[0]
                
                if mc.itemSynthesisList[costKey] then
                    mc.itemSynthesisList[costKey].composeCount = tempData.costnum.data[0];
                    mc.itemSynthesisList[costKey].composeTargetId = tempData.target;
                    mc.itemSynthesisList[costKey].composeCfgId = tempData.prescriptionID;
                else
                    mc.itemSynthesisList[costKey] =
                    {
                        id = tempData.cost.data[0],
                        decomposeCount = 0,
                        decomposeTargetId = nil,
                        decomposeCfgId = 0,
                        composeCount = tempData.costnum.data[0],
                        composeTargetId = tempData.target,
                        composeCfgId = tempData.prescriptionID,
                    }
                end
            end
        end
    end
    
    mc.itemSynthesisChain = {} --道具合成与分解的链条，不同链上的道具不能转化
    mc.lookupTable = {} --查找表
    local visited = {} --用来存储已经访问过的点
    local groupId = 0
    for i,v in pairs(mc.itemSynthesisList) do
        if not visited[v.id] then
            groupId = groupId + 1
            mc.itemSynthesisChain[groupId] = {}
            mc.itemSynthesisChain[groupId].chain = {}
            local queue = {v.id}
            while #queue > 0 do
                local node = table.remove(queue,1)
                if not visited[node] then
                    visited[node] = true --确保该点已经被访问过，从而避免死循环
                    mc.lookupTable[node] = groupId --记录当前属于哪个链条
                    mc.itemSynthesisChain[groupId].chain[node] = mc.itemSynthesisList[node]
                    local nextNodes = {}
                    if mc.itemSynthesisList[node].composeTargetId then
                        table.insert(nextNodes,mc.itemSynthesisList[node].composeTargetId)
                    else
                        mc.itemSynthesisChain[groupId].endIndex = node --该物品不能再合成，说明是链条的尽头
                    end
                    if mc.itemSynthesisList[node].decomposeTargetId then
                        table.insert(nextNodes,mc.itemSynthesisList[node].decomposeTargetId)
                    else
                        mc.itemSynthesisChain[groupId].beginIndex = node --该物品不能再分解，说明是链条的起点
                    end
                    for _,nextNode in pairs(nextNodes) do
                        if not visited[nextNode] then
                            table.insert(queue,nextNode)
                        end
                    end
                end
            end
        end
    end
    
    --下面这俩很常用，所以存储起来
    mc.weaponCfg = nil
    mc.nextLevelWeaponCfg = nil

    mc.CurSkin = 0 --缓存皮肤ID
    mc.CurSkinName = 0 --缓存皮肤名字 langID
end

function M.GetWeaponCfg(droneId,slot,lv,upgradeType)
    local keyStr = {droneId,"_",slot,"_",lv,"_",upgradeType}
    local key = table.concat(keyStr)
    if mc.weapon_cfg_cache and not mc.weapon_cfg_cache[key] then
        mc.weapon_cfg_cache[key] = game_scheme:MagicWeapon_0(droneId,slot,lv,upgradeType)
    end
    return mc.weapon_cfg_cache[key]
end

---region 无人机相关
--初始化无人机数据，根据获取到的无人机数据进行赋值
--由于只有一个无人机，因此每次NTF更新都是全量更新
function M.OnSetDroneData(partData)
    mc.droneData = {}
    mc.droneData.mEquipList = {} --装备插槽
    mc.droneData.attribute = {} --属性List
    mc.droneData.equipAttributeList = {} --装备的总加成
    mc.weapon_cfg_cache = {} --缓存武器cfg
    local DroneCenter_pb = require "DroneCenter_pb"
    local data = DroneCenter_pb.TDroneDataPart()
    data:ParseFromString(partData)
    --目前只有一个无人机，所以数组里只有1个元素
    if not data.droneInfo[1] then
        return;
    end
    mc.droneData.nDroneId = data.droneInfo[1].nDroneId;
    mc.droneData.bActive = data.droneInfo[1].bActive;
    
    for i,v in ipairs(data.droneInfo[1].part) do
        mc.droneData.mEquipList[v.partType - 1] =
        {lv = v.nLv,redDot = false,result = {}}; --装备插槽从2开始，所以key要-1。但其实不减1好像也可以……不过不减1的话就不能ipairs遍历了。
    end
    for i,v in ipairs(data.droneInfo[1].props) do
        mc.droneData.attribute[v.propid] = v.propvalue;
    end
    mc.droneData.nLv = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_LV]
    for i,v in ipairs(mc.droneData.mEquipList) do
        local equipCfg = self.GetWeaponCfg(mc.droneData.nDroneId,i+1,v.lv,DEFAULT_UPGRADE_STAGE)
        local temp = cfg_util.StringToArray(equipCfg.strParam1,";","#")
        for j,k in ipairs(temp) do
            if not mc.droneData.equipAttributeList[tonumber(k[1])] then
                mc.droneData.equipAttributeList[tonumber(k[1])] = 0
            end
            mc.droneData.equipAttributeList[tonumber(k[1])] = mc.droneData.equipAttributeList[tonumber(k[1])] + k[2]
        end
        local nextLevelCfg = self.GetWeaponCfg(mc.droneData.nDroneId,i+1,v.lv + 1,DEFAULT_UPGRADE_STAGE)
        if nextLevelCfg then
            local checkList = self.GetSynthesisItemResult(equipCfg.costGoods.data[0],equipCfg.costGoodsNum.data[0])
            if checkList then
                v.redDot = checkList.canSynthesis or checkList.isEnough --isEnough
            end
            v.result = checkList
        else
            v.redDot = false
            v.result = {}
        end

    end

    mc.weaponCfg = self.GetWeaponCfg(mc.droneData.nDroneId,1,mc.droneData.nLv,mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_UPGRADESTAGE])
    if mc.weaponCfg then
        local upgradeType = mc.weaponCfg.UpgradeType --为1表示非突破升级
        local weaponUpgradeStage = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_UPGRADESTAGE]

        local isNormalUpgrade = upgradeType == 1
        local isMaxBreakStage = weaponUpgradeStage == 4

        local nextLevelWeaponUpgradeStage = (isNormalUpgrade or (not isNormalUpgrade and isMaxBreakStage)) and 0 or weaponUpgradeStage + 1
        local nextLevel = (isNormalUpgrade or (not isNormalUpgrade and isMaxBreakStage)) and mc.droneData.nLv+1 or mc.droneData.nLv
        mc.nextLevelWeaponCfg = self.GetWeaponCfg(mc.droneData.nDroneId,1,nextLevel,nextLevelWeaponUpgradeStage)
    end

end

function M.OnUpdateDroneData(data)
    local oldData =
    {
        droneData = mc.droneData,
        weaponCfg = mc.weaponCfg,
    } --备份用，拿来做打点处理
    local changeEquip = false
    local changeEquipData = {}
    
    mc.droneData = {}
    mc.droneData.mEquipList = {} --装备插槽
    mc.droneData.attribute = {} --属性List
    mc.droneData.equipAttributeList = {} --装备的总加成

    --目前只有一个无人机，所以数组里只有1个元素
    if not data.droneInfo[1] then
        return;
    end
    mc.droneData.nDroneId = data.droneInfo[1].nDroneId;
    mc.droneData.bActive = data.droneInfo[1].bActive;
    
    for i,v in ipairs(data.droneInfo[1].part) do
        mc.droneData.mEquipList[v.partType - 1] =
        {lv = v.nLv,redDot = false,result = {}}; --装备插槽从2开始，所以key要-1。但其实不减1好像也可以……不过不减1的话就不能ipairs遍历了。
    end
    for i,v in ipairs(data.droneInfo[1].props) do
        mc.droneData.attribute[v.propid] = v.propvalue;
    end
    mc.droneData.nLv = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_LV]
    for i,v in ipairs(mc.droneData.mEquipList) do
        local equipCfg = self.GetWeaponCfg(mc.droneData.nDroneId,i+1,v.lv,DEFAULT_UPGRADE_STAGE)
        local temp = cfg_util.StringToArray(equipCfg.strParam1,";","#")
        for j,k in ipairs(temp) do
            if not mc.droneData.equipAttributeList[tonumber(k[1])] then
                mc.droneData.equipAttributeList[tonumber(k[1])] = 0
            end
            mc.droneData.equipAttributeList[tonumber(k[1])] = mc.droneData.equipAttributeList[tonumber(k[1])] + k[2]
        end
        if oldData and oldData.droneData.mEquipList[i] and v.lv ~= oldData.droneData.mEquipList[i].lv and not changeEquip then
            changeEquip = true;
            local preCfg = self.GetWeaponCfg(oldData.droneData.nDroneId,i+1,oldData.droneData.mEquipList[i].lv,DEFAULT_UPGRADE_STAGE)
            local count = player_mgr.GetPlayerOwnNum(preCfg.costGoods)
            changeEquipData =
            {
                imprintID = i+1,
                levelup = v.lv,
                cost = preCfg.costGoods.data[0],
                costNum = preCfg.costGoodsNum.data[0],
                remainder = count,
            }
        end
        --等级变化也会带来物品变化，所以这里不再执行
        --local checkList = self.GetSynthesisItemResult(equipCfg.costGoods,equipCfg.costGoodsNum)
        --if checkList then
        --    v.redDot = checkList.isEnough
        --end
        --v.result = checkList
    end
    mc.weaponCfg = self.GetWeaponCfg(mc.droneData.nDroneId,1,mc.droneData.nLv,mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_UPGRADESTAGE])
    if mc.weaponCfg then
        local upgradeType = mc.weaponCfg.UpgradeType --为1表示非突破升级
        local weaponUpgradeStage = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_UPGRADESTAGE]

        local isNormalUpgrade = upgradeType == 1
        local isMaxBreakStage = weaponUpgradeStage == 4

        local nextLevelWeaponUpgradeStage = (isNormalUpgrade or (not isNormalUpgrade and isMaxBreakStage)) and 0 or weaponUpgradeStage + 1
        local nextLevel = (isNormalUpgrade or (not isNormalUpgrade and isMaxBreakStage)) and mc.droneData.nLv+1 or mc.droneData.nLv
        mc.nextLevelWeaponCfg = self.GetWeaponCfg(mc.droneData.nDroneId,1,nextLevel,nextLevelWeaponUpgradeStage)
        event.Trigger(event.UPDATE_MAGIC_WEAPON)

        if oldData and oldData.droneData and oldData.droneData.attribute and oldData.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP] then
            local expLeft = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP] - oldData.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP]
            if expLeft ~= 0 or (oldData.weaponCfg.UpgradeType == 2 and mc.weaponCfg.UpgradeType == 2 and mc.weaponCfg.UpgradeStage ~= oldData.weaponCfg.UpgradeStage) then
                local temp = {}
                temp.levelup = string.format2("{%s1}#{%s2}",oldData.droneData.nLv,mc.droneData.nLv)
                local costStr = {}
                local costNumStr = {}
                local itemLeftStr = {}
                for i = 0, oldData.weaponCfg.costGoods.count - 1 do
                    table.insert(costStr,oldData.weaponCfg.costGoods.data[i])
                    table.insert(costNumStr,oldData.weaponCfg.costGoodsNum.data[i])
                    table.insert(itemLeftStr,player_mgr.GetPlayerOwnNum(oldData.weaponCfg.costGoods.data[i]))
                    if i < oldData.weaponCfg.costGoods.count - 1 then
                        table.insert(costStr,"#")
                        table.insert(costNumStr,"#")
                        table.insert(itemLeftStr,"#")
                    end
                end
                temp.cost = table.concat(costStr);
                temp.costNum = table.concat(costNumStr);
                temp.remainder = table.concat(itemLeftStr)
                if oldData.weaponCfg.UpgradeType == 2 then --为2表示特殊升级
                    temp.breakLevel = mc.weaponCfg.UpgradeStage
                    event.Trigger(event.GAME_EVENT_REPORT, "MagicWeapon_break", temp)
                else
                    if expLeft < 0 then
                        expLeft = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP] + (oldData.weaponCfg.costGoodsNum.data[0] - oldData.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_REMAINEXP])
                    end
                    temp.exp = expLeft
                    temp.Critical = expLeft > oldData.weaponCfg.SingleCost.data[0] and 1 or 0
                    event.Trigger(event.GAME_EVENT_REPORT, "MagicWeapon_levelup", temp)
                end
            elseif changeEquip then
                event.Trigger(event.GAME_EVENT_REPORT, "MagicWeapon_imprintUp", changeEquipData)
            end
        end

    end
end

function M.OnGetAllEquipAttributeValue()
    return mc.droneData.equipAttributeList
end

function M.OnGetEquipAttributeValue(_type)
    return mc.droneData.equipAttributeList[_type] or 0
end

function M.OnGetDroneCfgData()
    return mc.weaponCfg
end

function M.OnGetNextDroneCfgData()
    return mc.nextLevelWeaponCfg
end

function M.GetEquipGridAndLevelByIndex(id)
    if not mc.equipLvAndSlot then
        self.GetEquipDataAndSkillData()
    end
    return mc.equipLvAndSlot[id]
end

function M.GetEquipDataAndSkillData()
    mc.droneStarSkillDesc = {} --获取所有星级的技能描述lang表id，因为UI需要显示所有星级，而目前每一条数据仅能读取到当前星级的lang
    mc.equipLvAndSlot = {} --缓存当前的装备等级与部位
    mc.equipMaxLvSlot = {} --缓存当前装备部分最高级的属性
    local droneLength = game_scheme:MagicWeapon_nums()
    for i = 0,droneLength - 1 do
        local temp = game_scheme:MagicWeapon(i)
        if temp then
            if temp.weaponID >= 4 and temp.weaponType >= 2 then
                mc.equipLvAndSlot[temp.costGoods.data[0]] =
                {
                    level = temp.weaponLv,
                    slot = temp.weaponType,
                }
                if not mc.equipMaxLvSlot[temp.weaponType - 1] or mc.equipMaxLvSlot[temp.weaponType - 1].weaponLv < temp.weaponLv then
                    mc.equipMaxLvSlot[temp.weaponType - 1] = temp
                end
            end
            if temp.skillStars and temp.skillStars > 0 then
                if not mc.droneStarSkillDesc[temp.skillStars] then
                    mc.droneStarSkillDesc[temp.skillStars] =
                    {
                        desc = temp.skillStarsDes,
                        lv = temp.weaponLv,
                    }
                end
            end
        end
    end
end

function M.GetMaxLvEquipCfg(weaponType)
    if not mc.equipMaxLvSlot then
        self.GetEquipDataAndSkillData()
    end
    return mc.equipMaxLvSlot[weaponType]
end

function M.GetPreDroneCfgData()
    local upgradeType = mc.weaponCfg.UpgradeType --为1表示非突破升级
    local weaponUpgradeStage = mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_UPGRADESTAGE]
    local preLevelWeaponUpgradeStage = (upgradeType == 1 or (upgradeType ~= 1 and weaponUpgradeStage == 0)) and 0 or weaponUpgradeStage - 1
    local preLevel = (upgradeType == 1 or (upgradeType ~= 1 and weaponUpgradeStage == 0)) and mc.droneData.nLv-1 or mc.droneData.nLv
    local item = self.GetWeaponCfg(mc.droneData.nDroneId,1,preLevel,preLevelWeaponUpgradeStage)
    if item == nil then
        --log.Error("???")
    end
    return self.GetWeaponCfg(mc.droneData.nDroneId,1,preLevel,preLevelWeaponUpgradeStage)
end

function M.GetDroneId()
    return mc.droneData.nDroneId;
end

---@public 利用已有数据重新刷新表现
function M.RefreshShowByData()
    --如有表现事件要抛出，需要对应处理
end

---检测建筑红点，可升级时返回红点
function M.CheckBuildingRedDot()
    if self.CheckUpgradeRedDot() then
        return true
    end
    if mc.droneData and mc.droneData.mEquipList then
        for i,v in pairs(mc.droneData.mEquipList) do
            if v.redDot then
                return true
            end
        end
    end
    return false
end

function M.GetDronePower()
    if mc.droneData and mc.droneData.attribute then
        return mc.droneData.attribute[GWG.GWConst.enDroneProp.DRONE_PROP_POWER] or 0
    end
    return 0;
end

function M.GetCombatLevel()
    if mc.droneData and mc.droneData.attribute then
        local DroneCenter_pb = require "DroneCenter_pb"
        return mc.droneData.attribute[DroneCenter_pb.DRONE_PROP_ADVANCE_LV] or 1
    end
    return 0;
end

function M.SetCombatLevel(level)
    if mc.droneData and mc.droneData.attribute then
        local DroneCenter_pb = require "DroneCenter_pb"
        mc.droneData.attribute[DroneCenter_pb.DRONE_PROP_ADVANCE_LV] = level
    end
end

function M.GetCombatLevelRemainExp()
    if mc.droneData and mc.droneData.attribute then
        local DroneCenter_pb = require "DroneCenter_pb"
        return mc.droneData.attribute[DroneCenter_pb.DRONE_PROP_ADVANCE_REMAINEXP] or 0
    end
    return 0;
end

function M.SetCombatLevelRemainExp(exp)
    if mc.droneData and mc.droneData.attribute then
        local DroneCenter_pb = require "DroneCenter_pb"
        mc.droneData.attribute[DroneCenter_pb.DRONE_PROP_ADVANCE_REMAINEXP] = exp
    end
    return 0;
end

function M.GetDroneLocalPower()
    if mc.weaponCfg then
        return mc.weaponCfg.iSword
    end
    return 0
end

--这里传入的是gwconst里的enDroneProp
function M.GetDroneAttribute(attributeKey)
    if mc.droneData and mc.droneData.attribute then
        return mc.droneData.attribute[attributeKey] or 0
    end
    return 0
end

function M.OnGetDroneLv()
    return mc.droneData.nLv
end

function M.OnGetEquipLevelList()
    return mc.droneData.mEquipList
end

function M.GetEquipmentPower()
    local power = 0
    for i,v in ipairs(mc.droneData.mEquipList) do
        local temp = self.GetEquipCfgData(i)
        if temp then
            power = power + temp.iSword
        end
    end
    return power
end

function M.GetDroneBasePower()
    return mc.weaponCfg.iSword
end

---@public 获取无人机属性战斗力
function M.GetDroneAttributePower()

    local hpRatio, atkRatio, defRatio
    local hpProCfg = game_scheme:ProToLang_0(gw_const.ATTR_TYPE.DroneHP)
    hpRatio = hpProCfg and hpProCfg.Power or 0
    local atkProCfg = game_scheme:ProToLang_0(gw_const.ATTR_TYPE.DroneAtk)
    atkRatio = atkProCfg and atkProCfg.Power or 0
    local defProCfg = game_scheme:ProToLang_0(gw_const.ATTR_TYPE.DroneDef)
    defRatio = defProCfg and defProCfg.Power or 0

    local attarPower = 0

    if mc.droneData and mc.droneData.attribute then
        local hpValue = mc.droneData.attribute[gw_const.enDroneProp.DRONE_PROP_HP] or 0
        local atkValue = mc.droneData.attribute[gw_const.enDroneProp.DRONE_PROP_ATTACK] or 0
        local defValue = mc.droneData.attribute[gw_const.enDroneProp.DRONE_PROP_DEFENCE] or 0

        attarPower = attarPower + hpValue * hpRatio / 100 + atkValue * atkRatio/100 + defValue * defRatio/100
    end

    return attarPower
end


function M.OnGetDroneState()
    return mc.droneData.bActive
end

function M.GetEquipCfgData(slot)
    local cfg = self.GetWeaponCfg(mc.droneData.nDroneId,slot + 1,mc.droneData.mEquipList[slot].lv,DEFAULT_UPGRADE_STAGE)
    return cfg
end

--检测升级与突破的红点状态
function M.CheckUpgradeRedDot()
    local gw_home_building_data = require "gw_home_building_data"
    local buildingId = 29000
    local building = gw_home_building_data.GetMaxLevelBuildingDataByBuildingID(buildingId)
    if not building then
        return false
    end
    if building.nLevel < 1 then
        return false
    end
    if not mc.nextLevelWeaponCfg then
        return false
    end
    for i=0,mc.weaponCfg.SingleCost.count-1 do
        local needCount = mc.weaponCfg.SingleCost.data[i]
        local itemCount = player_mgr.GetPlayerOwnNum(mc.weaponCfg.costGoods.data[i])
        if needCount > itemCount then
            return false
        end
    end
    return true
end

function M.UpdateSynthesisItemResult()
    if not mc.droneData or not mc.droneData.mEquipList then
        return
    end
    for i,v in ipairs(mc.droneData.mEquipList) do
        local equipCfg = self.GetWeaponCfg(mc.droneData.nDroneId,i+1,v.lv,DEFAULT_UPGRADE_STAGE)
        local nextLevelCfg = self.GetWeaponCfg(mc.droneData.nDroneId,i+1,v.lv + 1,DEFAULT_UPGRADE_STAGE)
        if nextLevelCfg then
            local checkList = self.GetSynthesisItemResult(equipCfg.costGoods.data[0],equipCfg.costGoodsNum.data[0])
            if checkList then
                v.redDot = checkList.canSynthesis or checkList.isEnough--isEnough
            end
            v.result = checkList
        else
            v.redDot = false
            v.result = {}
        end

    end
    event.Trigger(event.UPDATE_MAGIC_WEAPON_ITEM)
end

function M.GetSynthesisItemResult(itemIndex,itemCount)
    local hold = player_mgr.GetPlayerOwnNum(itemIndex)
    if hold >= itemCount then
        return {isEnough = true,canSynthesis = false,resultList = {},getItemCount = 0,isDecompose = false} --能满足，则不走下面的逻辑
    end
    local chainIndex = mc.lookupTable[itemIndex] -- 确定当前属于哪个链条
    if not chainIndex then
        --log.Error("配置表有误！目前没有任何道具可以转化为该道具，id"..itemIndex)
        return nil
    end
    local chain = mc.itemSynthesisChain[chainIndex]
    if not chain then
        --log.Error("配置表有误！目前没有任何道具可以转化为该道具，id"..itemIndex)
        return nil;
    else
        local beginIndex = chain.beginIndex --合成起始道具
        local endIndex = chain.endIndex   --分解起始道具
        local maxCount = 0 --链条长度，用来限制while循环避免死循环

        for i,v in pairs(chain.chain) do
            maxCount = maxCount + 1
            v.hold = player_mgr.GetPlayerOwnNum(v.id)
        end

        --local temp = maxCount
        local result = {isEnough = false,canSynthesis = false,resultList = { },getItemCount = 0,isDecompose = false}--记录已经消耗了多少道具
        local needItem = {}--记录还需要多少道具
        local composeList = {} --记录合成出来的道具数量

        local currentId = itemIndex

        result.resultList[itemIndex] = {id = itemIndex,count = 0}--math.min(itemCount,chain.chain[itemIndex].hold)}
        --composeList[itemIndex] = result.resultList[itemIndex].count

        if chain.chain[itemIndex].hold >= itemCount then
            result.isEnough = true
            return result
        end

        local currentQty = itemCount - chain.chain[itemIndex].hold --还需要多少个？
        needItem[itemIndex] = currentQty
        composeList[itemIndex] = chain.chain[itemIndex].hold
        while chain.chain[currentId].decomposeTargetId and currentQty > 0 do --取自己可以拆解的道具的id，即可以合成自己的道具
            local tempId = chain.chain[currentId].decomposeTargetId
            local item = chain.chain[tempId]
            local tempCount = item.hold
            local maxCanCompose = math.floor(tempCount / item.composeCount) --获得最大可合成数
            if maxCanCompose >= currentQty then
                result.resultList[tempId] = {id = tempId,count = currentQty * item.composeCount,prescriptionID = 0,exchangeCount = 0,nType = 0}
                if result.resultList[tempId].count > 0 and tempId == itemIndex then
                    --result.canSynthesis = true
                end
                currentQty = 0
                break;
            else
                local needCount = currentQty * item.composeCount
                currentQty = needCount - tempCount
                result.resultList[tempId] = {id = tempId,count = tempCount,prescriptionID = 0,exchangeCount = 0,nType = 0}
                if result.resultList[tempId].count > 0 and maxCanCompose > 0 and tempId == itemIndex then
                    --result.canSynthesis = true
                end
                currentId = tempId
            end
        end

        currentId = itemIndex
        local index = beginIndex

        --log.Error("target"..itemIndex..",needCount"..itemCount)
        --for i,v in pairs(chain.chain) do
        --    log.Error("index:"..i..",count:"..v.hold)
        --end
        while index ~= itemIndex do
            local item = chain.chain[index]
            if result.resultList[index] then
                if not composeList[index] then
                    composeList[index] = 0
                end
                if not composeList[item.composeTargetId] then
                    composeList[item.composeTargetId] = 0
                end
                local useCount = result.resultList[index].count + composeList[index] --要用掉多少？
                --log.Error(index)
                --log.Error(useCount)
                composeList[index] = useCount % item.composeCount --取余，避免额外使用
                result.resultList[index].count = result.resultList[index].count - composeList[index]
                result.resultList[index].prescriptionID = item.composeCfgId; --转换的配方id
                result.resultList[index].exchangeCount = math.floor(useCount / item.composeCount); --转换的次数
                result.resultList[index].nType = 1 --合成
                composeList[item.composeTargetId] = composeList[item.composeTargetId] + math.floor(useCount / item.composeCount) --上一轮获得的数量存起来
                --log.Error("--------------")
            end
            index = item.composeTargetId
        end

        --for i,v in pairs(result.resultList) do
        --    if v.exchangeCount then
        --        log.Error(i..":"..v.count..","..v.prescriptionID..","..v.exchangeCount)
        --        log.Error(composeList[i])
        --        log.Error("--------------______")
        --    end
        --end
        
        result.getItemCount = composeList[itemIndex] - chain.chain[itemIndex].hold
        if result.getItemCount > 0 then
            result.canSynthesis = true
        end
        if composeList[itemIndex] >= itemCount then
            result.isEnough = true
            result.isDecompose = false
            return result
        end
        currentQty = itemCount - composeList[itemIndex] - result.resultList[itemIndex].count --还需要多少个？
        while chain.chain[currentId].composeTargetId and currentQty > 0 do
            local tempId = chain.chain[currentId].composeTargetId
            local item = chain.chain[tempId]
            local tempCount = item.hold
            local maxCanDecompose = tempCount * item.decomposeCount --获得最大可分解数
            if maxCanDecompose >= currentQty then
                result.resultList[tempId] = {id = tempId,count = math.ceil(currentQty / item.decomposeCount)}
                --composeList[tempId] = composeList[tempId] + result.resultList[tempId].count
                if result.resultList[tempId].count > 0 then
                    result.canSynthesis = true
                end
                currentQty = 0
                break;
            else
                local needCount = math.ceil(currentQty / item.decomposeCount)
                currentQty = needCount - tempCount
                result.resultList[tempId] = {id = tempId,count = tempCount}
                --composeList[tempId] = composeList[tempId] + result.resultList[tempId].count
                if result.resultList[tempId].count > 0 and maxCanDecompose > 0 then
                    result.canSynthesis = true
                end
                currentId = tempId
            end
        end
        index = endIndex

        while index ~= itemIndex do
            local item = chain.chain[index]
            if result.resultList[index] then
                if not composeList[index] then
                    composeList[index] = 0
                end
                if not composeList[item.decomposeTargetId] then
                    composeList[item.decomposeTargetId] = 0
                end
                local useCount = result.resultList[index].count + composeList[index]  --要用掉多少？
                result.resultList[index].prescriptionID = item.decomposeCfgId
                result.resultList[index].exchangeCount = useCount
                result.resultList[index].nType = 2 --分解
                composeList[item.decomposeTargetId] = composeList[item.decomposeTargetId] + useCount * item.decomposeCount
            end
            index = item.decomposeTargetId
        end

        if composeList[itemIndex] >= itemCount then
            result.isEnough = true
            result.isDecompose = true
        else
            result.isEnough = false
        end
        result.getItemCount = composeList[itemIndex] - chain.chain[itemIndex].hold
        return result
    end

end

function M.GetStarSkillDesc()
    if not mc.droneStarSkillDesc then
        self.GetEquipDataAndSkillData()
    end
    return mc.droneStarSkillDesc
end

function M.SetCurAdroneSkin(value) 
    mc.CurSkin = value
end

function M.GetCurAdroneSkin() 
    return mc.CurSkin
end

function M.GetCurAdroneSkinName() 
    if mc.CurSkin ~= 0 then 
        local cfg= game_scheme:AccessoryAdorn_0(mc.CurSkin)
        if cfg then 
            mc.CurSkinName = cfg.NameID
        end
    end
    return mc.CurSkinName
end

--获取神兽进阶功能是否开启
function M.GetCombatIsOpen() 
    local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
    local moduleOpen = new_weapon_combat_crystal_data.GetModuleIsOpen()
    if not moduleOpen then
        return false
    end
    local function_open_mgr = require "function_open_mgr"
    return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.DronCombat)
end

--获取神兽结晶功能是否开启
function M.GetCrystalIsOpen() 
    local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
    local moduleOpen = new_weapon_combat_crystal_data.GetModuleIsOpen()
    if not moduleOpen then
        return false
    end
    local curStage = new_weapon_combat_crystal_data.GetCurrentCombatStage()
    return curStage > 0
end
--endregion

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--- 数据清理
function M.Dispose()
    local table_util = require "table_util"
    table_util.ClearTable(mc.itemSynthesisChain)
    table_util.ClearTable(mc.lookupTable)
    isInit = false
    _d.mde:clear()
    mc = _d.mde.const
end
return M