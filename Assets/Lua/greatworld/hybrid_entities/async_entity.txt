local AsyncEntity = {}

local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local require = require
local event = require 'event'
local table  = table
local asset_loader = require "asset_loader"
local xpcall = xpcall
local pairs  = pairs
local log = require "log"
local LRU = require "lru"
local max = math.max
local floor = math.floor
local BoxCollider = CS.UnityEngine.BoxCollider

-- ============================================================================
-- reqId 编码：把 path 与 layer 一并编码进 reqId，可通过 reqId 反查 path/layer
-- 格式：reqId = ((pathIdx * 32 + layer) * 2^32) + serial
-- ============================================================================
local pathIndices = {}   -- path -> idx
local pathByIdx   = {}   -- idx  -> path
local nextPathIdx = 1

local REQ_SERIAL_BITS = 32
local REQ_SERIAL_BASE = 2 ^ REQ_SERIAL_BITS  -- 4294967296
local REQ_LAYER_BITS  = 5
local REQ_LAYER_BASE  = 2 ^ REQ_LAYER_BITS   -- 32
local REQ_PATH_BITS   = 53 - REQ_SERIAL_BITS - REQ_LAYER_BITS
local PATH_MAX = (2 ^ REQ_PATH_BITS) - 1

local function clampLayer(l)
    l = l or 0
    if l < 0 then return 0 end
    if l > 31 then return 31 end
    return l
end

local function getPathIdx(path)
    local idx = pathIndices[path]
    if not idx then
        idx = nextPathIdx
        nextPathIdx = idx + 1
        if idx > PATH_MAX then
            log.Error("AsyncEntity: path index overflow")
            idx = PATH_MAX
            nextPathIdx = idx -- 停在上限，避免继续增长
        end
        pathIndices[path] = idx
        pathByIdx[idx] = path
    end
    return idx
end

-- 生成 reqId（将 path 与 layer 一并编码）
local function makeReqId(path, layer, serial)
    return ((getPathIdx(path) * REQ_LAYER_BASE + layer) * REQ_SERIAL_BASE) + serial
end

-- 从 reqId 反解出 path 与 layer
local function reqIdToPathLayer(requestId)
    local tmp   = floor(requestId / REQ_SERIAL_BASE)
    local layer = tmp % REQ_LAYER_BASE
    local pidx  = floor(tmp / REQ_LAYER_BASE)
    return pathByIdx[pidx], layer
end
-- ============================================================================

-- 对象池
local entryPool = {}
local infoPool  = {}
local metaPool  = {}

-- 单元素缓存池：每个 path 只存 1 个 entity + 其 layer 键
local entityPool    = {}  -- entityPool[path] = entity
local entityPoolKey = {}  -- entityPoolKey[path] = layer(0..31)

-- 前置声明：loader 释放
local DisposeLoader

-- LRU 淘汰回调：根据 pathIdx 找到 path，销毁池内 entity 并清理 loader
local function onLRUEvict(evictIdx)
    local p = pathByIdx[evictIdx]
    if not p then return end
    local e = entityPool[p]
    if e ~= nil then
        local instanceID = EntityHybridUtility.DestroyEntity(e)
        if instanceID ~= 0 then
            DisposeLoader(instanceID)
        end
    end
    entityPool[p]    = nil
    entityPoolKey[p] = nil
end

-- 实例化一个 LRU
local lru = LRU.new(64, onLRUEvict)

function AsyncEntity.SetPoolCapacity(n)
    lru:setCapacity(n or 0)
end
function AsyncEntity.GetPoolStats()
    return lru.size, lru.cap
end
-- =======================================================

-- 已创建实体：requestId -> entity
local createdMap = {}

-- 分配一个 entry，直接作为列表使用
local function allocEntry()
    return table.remove(entryPool) or {}
end

-- 回收 entry，清空列表
local function freeEntry(entry)
    for i = #entry, 1, -1 do entry[i] = nil end
    table.insert(entryPool, entry)
end

-- 分配 info 对象
local function allocInfo() return table.remove(infoPool) or {} end

-- 清理并回收 info（只清必要字段）
local function freeInfo(info)
    info.onComplete = nil
    table.insert(infoPool, info)
end

-- 分配/回收 meta
local function allocMeta() return table.remove(metaPool) or {} end
local function freeMeta(meta)
    meta.entry = nil
    meta.index = nil
    table.insert(metaPool, meta)
end

-- 请求 serial（reqId = encode(path, layer, serial)）
local nextSerial = 1

-- 请求元数据映射（pending 阶段）：reqId -> { entry, index }
local reqMap = {}

-- pendingRequests[path] = entry 列表
local pendingRequests = {}
-- prefab 缓存、加载状态
local prefabCache = {}
local loadingRequests = {}

local asset_loader_cache = {}
local loader_path_map = {}

local ColliderExtentsCache = {}

-- 异步加载
local function startLoad(path)
    loadingRequests[path] = true
    local loader = asset_loader(path, "async_entity")
    loader:load(function(prefabGameObject)
        if not prefabGameObject or not loadingRequests[path] then
            loadingRequests[path] = nil
            loader:Dispose()
            return
        end
        loadingRequests[path] = nil
        if prefabGameObject.asset then
            prefabCache[path] = prefabGameObject.asset
            local instanceId = prefabGameObject.asset:GetInstanceID()
            asset_loader_cache[instanceId] = loader
            loader_path_map[instanceId] = path
        end
    end)
end

--- 提交异步 Instantiate 请求，返回 reqId
-- @param path
-- @param onComplete
-- @param sortingOrder 可选
-- @param zTestAlways 可选
-- @param layer 可选（0..31），会 clamp
function AsyncEntity.RequestInstantiate(path, onComplete, sortingOrder, zTestAlways, layer)
    -- clamp layer 在请求阶段完成
    local cl = clampLayer(layer)

    local entry = pendingRequests[path]
    if not entry then
        entry = allocEntry()
        pendingRequests[path] = entry
    end
    local list = entry

    -- 生成 reqId（编码 path+layer+serial）
    local serial = nextSerial
    local id     = makeReqId(path, cl, serial)
    nextSerial   = serial + 1

    local info = allocInfo()
    info.id           = id
    info.onComplete   = onComplete
    info.layer        = cl
    info.sortingOrder = sortingOrder or 0
    info.zTestAlways  = zTestAlways or false

    list[#list+1] = info
    local index = #list

    -- 保存请求元数据
    local meta = allocMeta()
    meta.entry = entry
    meta.index = index
    reqMap[id] = meta

    -- 启动加载
    if not prefabCache[path] and not loadingRequests[path] then
        startLoad(path)
    end
    return id
end

-- 前置声明在上面
function DisposeLoader(instanceId)
    local loader = asset_loader_cache[instanceId]
    if loader then
        loader:Dispose()
        local path = loader_path_map[instanceId]
        if path then
            prefabCache[path] = nil
            loadingRequests[path] = nil
            loader_path_map[instanceId] = nil
        end
        asset_loader_cache[instanceId] = nil
    end
end

--- 取消并销毁请求（含按 path 的单元素池 + LRU）
function AsyncEntity.Dispose(requestId)
    -- 1) 取消 pending
    local meta = reqMap[requestId]
    if meta then
        local entry = meta.entry
        local list  = entry
        local idx   = meta.index
        local info  = list[idx]
        if info then
            if idx < #list then
                local last = list[#list]
                list[idx] = last
                local movedMeta = reqMap[last.id]
                if movedMeta then movedMeta.index = idx end
            end
            list[#list] = nil
            reqMap[requestId] = nil
            freeMeta(meta)
            freeInfo(info)

            if #list == 0 then
                local path = reqIdToPathLayer(requestId)  -- 只取 path
                if path then pendingRequests[path] = nil end
                freeEntry(entry)
            end
        else
            reqMap[requestId] = nil
            freeMeta(meta)
        end
    end

    -- 2) 销毁/入池已创建实体（按 path，单槽 + LRU）
    local created = createdMap[requestId]
    if created then
        local path, layer = reqIdToPathLayer(requestId)
        if path and entityPool[path] == nil and (lru.cap or 0) > 0 then
            entityPool[path]    = created
            entityPoolKey[path] = layer
            EntityHybridUtility.SetActive(created, false)
            lru:attach(getPathIdx(path))  -- 入队尾（最新），内部会自动驱逐
        else
            local instanceID = EntityHybridUtility.DestroyEntity(created)
            if instanceID ~= 0 then
                DisposeLoader(instanceID)
            end
        end
        createdMap[requestId] = nil
    end
end

local outputEntities = {}
local function ExceptionsHander(err) log.Error(err) end

--- 每帧：处理 pending（#list == 1 命中 path 池且 layer 匹配则复用）
function AsyncEntity.Update()
    local k = next(pendingRequests)
    while k do
        local path = k
        k = next(pendingRequests, path)

        local entry  = pendingRequests[path]
        local prefab = prefabCache[path]
        if prefab and entry then
            pendingRequests[path] = nil
            local list = entry
            local n = #list
            if n > 0 then
                local first = list[1]
                if n == 1 then
                    local wantLayer = first.layer
                    local pooled    = entityPool[path]
                    if pooled ~= nil and entityPoolKey[path] == wantLayer then
                        EntityHybridUtility.SetActive(pooled, true)
                        entityPool[path]    = nil
                        entityPoolKey[path] = nil
                        lru:detach(getPathIdx(path)) -- 从 LRU 移除

                        createdMap[first.id] = pooled
                        xpcall(first.onComplete, ExceptionsHander, pooled)

                        local m = reqMap[first.id]
                        reqMap[first.id] = nil
                        freeMeta(m)
                        freeInfo(first)
                    else
                        local e = EntityHybridUtility.Instantiate(
                            prefab, first.layer, first.sortingOrder, first.zTestAlways
                        )
                        createdMap[first.id] = e
                        xpcall(first.onComplete, ExceptionsHander, e)

                        local m = reqMap[first.id]
                        reqMap[first.id] = nil
                        freeMeta(m)
                        freeInfo(first)
                    end
                else
                    -- Batch：不走池
                    local count = EntityHybridUtility.Instantiate(
                        prefab, n, first.layer, first.sortingOrder, first.zTestAlways, outputEntities
                    ) or 0
                    for i = 1, n do
                        local info = list[i]
                        local e = outputEntities[i]
                        createdMap[info.id] = e
                        xpcall(info.onComplete, ExceptionsHander, e)
                        local m = reqMap[info.id]
                        reqMap[info.id] = nil
                        freeMeta(m)
                        freeInfo(info)
                    end
                    for i = count, 1, -1 do
                        outputEntities[i] = nil
                    end
                end
            end
            freeEntry(entry)
        end
    end
end

function AsyncEntity.GetPrefabFromCache(path)
    return prefabCache[path]
end

function AsyncEntity.GetColliderIntSize(entity, path, gameobject, volatileSize)
    local extent = ColliderExtentsCache[path]
    if extent then
        if volatileSize then
            return type(extent) == "table" and extent[1] * volatileSize / extent[2]
        end
        return extent
    else
        if entity then
            local x, y, z = EntityHybridUtility.GetColliderSize(entity)
            if x and z then
                extent = floor(max(x, z) * 0.5 + 0.5)
            end
        elseif gameobject then
            local collider = gameobject:GetComponentInChildren(typeof(BoxCollider), true)
            if not collider then
                extent = 0
            else
                local size = collider.bounds.size
                extent = floor(max(size.x, size.z) * 0.5 + 0.5)
            end
        end
        if not volatileSize then
            ColliderExtentsCache[path] = extent
        else
            ColliderExtentsCache[path] = { extent, volatileSize }
        end
        return extent
    end
end

event.Register(event.CSUpdate, AsyncEntity.Update)

return AsyncEntity
