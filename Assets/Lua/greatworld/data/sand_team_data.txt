local ipairs = ipairs
local pairs = pairs
local table = table
local dump = dump
local common_pb = require "common_pb"
local log = require "log"
local event = require "event"
local sandbox_pb = require "sandbox_pb"
local gw_home_common_data = require "gw_home_common_data"
local game_scheme = require "game_scheme"
local player_mgr = require"player_mgr"
local gw_power_mgr = require "gw_power_mgr"
local gw_const = require "gw_const"
local sand_attack_const = require "sand_attack_const"
local gw_ed = require "gw_ed"
local gw_hero_define = require "gw_hero_define"
local Edump = Edump
local TeamData = {}

local normal_team_Data = {}
local scout_team_data = {}
local team_data_map = {}
local troopIsIdle = {} --队伍的空闲状态，所有其他地方都从这里取，不再单独维护

--编队和一键派遣会使用的数据。
--TODO 后续应当把数据进行整合，编队界面直接读取这里的数据，避免直接在UI层读取
local TabData = {}


--region 内部方法

local moduleName = "[sand_team_data]"
local logSwitch = true
local function LogWarning(...)
    if logSwitch then
        local log = require "log"
        log.Warning(moduleName, ...)
    end
end
local function Log(...)
    if logSwitch then
        local log = require "log"
        log.LogFormat(moduleName, ...)
    end
end

local special_teams = {
    [sandbox_pb.enSandboxLineType_Spy] = gw_const.TEAM_LINE_TYPE.Scouting
}

local isInitSandTeamData = false        --沙盘队伍数据是否初始化
local waitTimer = nil                   --等待队伍数据初始化


--endregion


--region 对外方法
--写死总队伍数
TeamData.totalTeamCount = 4
local RowColumnConfig = {
    [1] = { row = 1, column = 1 },
    [2] = { row = 2, column = 1 },
    [3] = { row = 1, column = 2 },
    [4] = { row = 2, column = 2 },
    [5] = { row = 3, column = 2 },
}
--根据索引获取英雄配置行列
function TeamData.Index2RowColumn(index)
    --弃用
    return RowColumnConfig[index].row, RowColumnConfig[index].column
end

--根据索引及英雄id获取英雄配置
function TeamData.GetBattlePalInfoByIndexAndHeroId(index, heroId)
    --弃用
    local battlePalInfo = {}
    battlePalInfo.row, battlePalInfo.col = TeamData.Index2RowColumn(index)
    battlePalInfo.palId = heroId
    return battlePalInfo
end

function TeamData.UpdateTroopState(_,msg)
    troopIsIdle[msg.teamIndex] = msg.bIdle
    event.Trigger(event.UPDATE_TROOP_STATE)
end

--获取对应阵容的所有选中英雄
local function GetAllLineUpHeroDic(sandType)
    local result = {}
    local lines = TeamData.GetLines(sandType)
    if lines then
        for v=1,5 do
            local lineUpTabData = lines[v]
            --todo 获取所有编队信息
            if lineUpTabData and lineUpTabData.team.pals then
                for p, h in ipairs(lineUpTabData.team.pals) do
                    if h ~= nil then
                        result[h.heroSid] = v
                    end
                end
            end
        end
    end
    return result
end

--获取是否有未上阵的新英雄
function TeamData.HasUnselectHero(index, isScout,sandType)
    local allHeroList = TeamData.GetFilteredHero(sandType)
    local selectHeroList = GetAllLineUpHeroDic(sandType)
    local selectHeroNum = 0
    for k, v in pairs(selectHeroList) do
        if v then
            selectHeroNum = selectHeroNum + 1
        end
    end
    if #allHeroList > selectHeroNum then
        --有未上阵的新英雄，判断队伍是否已满
        local heroList = TeamData.GetHeroListByIndex(index,sandType)
        if heroList then
            return #heroList < 5
        end
    end
    return false
end


function TeamData.GetUnselectHerodata(sandType)
    local tempData = TeamData.GetFilteredHero(sandType)
    local heroListData = {}
    local allLineUpHero = GetAllLineUpHeroDic(sandType)
    if tempData ~= nil then
        for sid, hero in pairs(tempData) do
            if allLineUpHero[hero.heroSid] then
                hero.teamIndex = allLineUpHero[hero.heroSid]
            end
            table.insert(heroListData, hero)
        end
    end
    return heroListData
end

---@deprecated 获取玩家的所有英雄
function TeamData.GetFilteredHero(sandType)
    local gw_hero_data = require "gw_hero_data"
    local t = {}
    -- 增加 validHero 表，以避开带 metatable 的包含 hero_entity 的表格进行排序，以提高性能
    local validHero = {}

    local heroPart = gw_hero_data.GetOwnedHeroIDList() or {}
    
    for i, sid in pairs(heroPart) do
        local hero = gw_hero_data.GetHeroCfgId2Entity(sid)
        local heroID = hero.heroID
        local heroCfg = game_scheme:Hero_0(heroID)
        if heroCfg then
            local hero_trial_mgr = require"hero_trial_mgr"
            local isTrial = hero_trial_mgr.isTrialHeroBySid(sid)
            local otmHero = {}--self:BuildOptimizeHero(hero)
            --性能优化项，如果每次都给hero设置新key，消耗量较大
            otmHero.numProp =
            {
                lv = hero.numProp.lv,
                starLv = hero.numProp.starLv
            }
            otmHero.rarityType = hero.rarityType
            otmHero.type = hero.type
            otmHero.heroID = hero.heroID
            otmHero.heroSid = hero.heroSid
            otmHero.hasFinish = hero.hasFinish
            otmHero.isTrial = isTrial and 1 or 0
            otmHero.type = heroCfg.type
            otmHero.teamIndex = nil
            otmHero.hasTeamIndex = false
            otmHero.battleProp =
            {
                power = gw_power_mgr.GetHeroPowerByCfgId(hero.heroID)
            }
            table.insert(validHero, otmHero )
        end
    end
    
    -- local statistic = 0
    --新的排序规则为：战力 → ID→SID
    local SortRule = function (t1, t2)
        -- statistic = statistic + 1

        local battleProp1,battleProp2 = t1.battleProp,t2.battleProp
        if battleProp1["power"] ~= battleProp2["power"] then
            return battleProp1["power"] > battleProp2["power"]
        end

        if t1.heroID ~= t2.heroID then
            return t1.heroID < t2.heroID
        end
        return t1.heroSid < t2.heroSid
    end

    table.sort(validHero, SortRule)
    t = {}
    local i,v
    for i,v in ipairs(validHero) do
        table.insert(t, v)
    end
    
    return t
end

--获取队伍到达时间,默认最后一个节点
function TeamData.GetArriveTimestampByIndex(index, lineIndex, lineType,sandType)
    --local teamData = isScout and scout_team_data or normal_team_Data
    lineType = lineType or gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]

    if line then


        local timeStamp = #line.dotList_txy>=3 and  line.dotList_txy[lineIndex and (lineIndex*3-2) or (#line.dotList_txy-2)]
        return timeStamp  or 0
    end
    return 0
end

--获取队伍是否是回程
function TeamData.GetIsBackByIndex(index, lineType,sandType)
    lineType = lineType or gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    return line and line.isBack or false
end

--获取队伍数据TSimpleTeam
function TeamData.GetTeamByIndex(index, lineType,sandType)
    lineType = lineType or gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    return lines and lines[index] and lines[index].team
    --[[ local list = gw_home_common_data.GetSquadDataByIndex(index)
     return list]]
end

--获取队伍作业结束时间
function TeamData.GetTeamEndTimeByIndex(index, isScout,sandType)
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
  
    local line = lines and lines[index]
    if line and line.endTime then
        return line.endTime
    end
    return 0
end

--获取队伍作业开始时间
function TeamData.GetTeamStartTimeByIndex(index, isScout,sandType)
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line and line.startTime then
        return line.startTime
    end
    return 0
end

---获取可带领士兵百分比
function TeamData.GetTeamSoldierPrgByIndex(index, isScout)
    --local teamData = isScout and scout_team_data or normal_team_Data
    local curSoldierNum ,maxSoldierNum = TeamData.GetSoldierNumByIndex(index, isScout)
    if maxSoldierNum ~= 0 then
        return curSoldierNum / maxSoldierNum
    end
    return 0
end

---获取可带领士兵百分比,传入Team的数据
function TeamData.GetTeamSoldierPrgByPals(pals)
    if pals and #pals > 0  then
        local curSoldierNum = 0
        local maxSoldierNum = 0
        for _, pal in ipairs(pals) do
            curSoldierNum = curSoldierNum + pal.soldierValid        --可用士兵总量
            maxSoldierNum = maxSoldierNum + pal.soldierCapacity     --兵力上限
        end
        if maxSoldierNum ~= 0 then
            return curSoldierNum / maxSoldierNum
        end
    end
    return 0
end

---获取当前队伍的目标实体sid
function TeamData.GetTeamTargetSidByIndex(index, isScout,sandType)
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        return line:GetTargetSid() or 0
    end
    return 0
end

--根据目标实体id获取队伍下标
function TeamData.GetTeamIndexBySid(sid, isScout,sandType)
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    if lines then
        for _, line in pairs(lines) do
            if line and line:GetTargetSid() == sid then
                local index = line.team and line.team.index
                if index then
                    return index
                end
            end
        end
    end
    log.Error("没有找到队伍下标,targetSid:", sid)

    return 0
end

---获取当前队伍的目标实体的战斗状态
function TeamData.GetTeamBattleState(index,sandType)
    local lines = TeamData.GetLines(sandType,gw_const.TEAM_LINE_TYPE.Normal)
    if index and lines then
        for _, line in pairs(lines) do
            if line and line.team and line.team.index == index then
                return line.props.battleState or 0
            end
        end
    end
    return 0
end

--根据目标实体id获取队伍战力最高英雄
function TeamData.GetTeamPowerHeroByTargetSid(targetSid, isScout, sid,sandType)
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    if lines then
        local power = 0
        local powerHero
        for _, line in pairs(lines) do
            if line and (line:GetTargetSid() == targetSid or line.sid == sid) then
                if line.team then
                    for k, v in ipairs(line.team.pals) do
                        if v.heroPower and v.heroPower > power then
                            power = v.heroPower
                            powerHero = v
                        end
                    end
                end
            end
        end
        return powerHero
    end
end

--根据队伍索引获取队伍英雄总战力,包括
function TeamData.GetTeamTotalPowerByIndex(index,sandType)
    local _power =0 --总战力
    local heroPower = 0

    local soldierPower = 0
    local maxSoldierLevel = GWG.GWHomeMgr.soldierData.GetCampusSoldierLevel()
    local gw_hero_data = require "gw_hero_data"
    local lines = TeamData.GetLines(sandType,gw_const.TEAM_LINE_TYPE.Normal)
    
    if lines then
        local power = 0
        for _, line in pairs(lines) do
            if line and line.team and line.team.index == index then
                if line.team then
                    for k, v in ipairs(line.team.pals) do
                        if v.heroID then

                            heroPower = heroPower +  gw_power_mgr.GetHeroPowerByCfgId(v.heroID)
                            local entity = gw_hero_data.GetHeroEntityByCfgId(v.heroID)
                            local soldierCapacity = entity:GetCarrySoldierNum()
                            soldierPower = soldierPower + GWG.GWHomeMgr.soldierData.GetSoldierAttributeByLevel(soldierCapacity,maxSoldierLevel).power
                        end
                    end
                end
            end
        end
        
    end
    local weaponPower = GWG.GWHomeMgr.droneData.GetDronePower()
    _power = soldierPower + weaponPower + heroPower
    
    
   return _power
end


---@deprecated 获取第一个空闲的队伍
function TeamData.GetFreeTeam(attackMarchType, actionType, sandType)
    local teamCount = 4
    local selectIndex = 1
    local isIdle = false
    local canAttack
    local firstSelect = 0
    local sand_attack_mgr = require "sand_attack_mgr"
    for i = 1, teamCount do
        --遍历队伍,查找第一个空闲的队伍
        if TeamData.GetIsUnlockByIndex(i) then
            canAttack, isIdle = sand_attack_mgr.CanChangeAttackTarget(i, attackMarchType, actionType)
            if canAttack then
                selectIndex = i
            end
            if firstSelect == 0 then
                firstSelect = i
            end
            if isIdle then
                break
            end
        end
    end
    if not isIdle then
        if firstSelect ~= 0 then
            return firstSelect
        else
            return 1
        end
    else
        return selectIndex 
    end
end

--取当前战力最高的队伍，返回它的战力
function TeamData.GetMaxPowerTeam(sandType)
    local teamCount = 4
    local maxPower = 0
    for i = 1, teamCount do
        --遍历队伍,查找第一个空闲的队伍
        local power = TeamData.GetTeamTotalPowerByIndex(i,false,sandType)
        if maxPower < power then
            maxPower = power
        end
    end
    return maxPower
end

--[[--根据index获取当前队伍目标实体类型获取实体角色id
function TeamData.GetTargetRoleIdByIndex(index, isScout)
    local sand_ui_data = require "sand_ui_data"
    local sid = TeamData.GetTeamTargetSidByIndex(index,isScout)
    local entity = sand_ui_data.GetEntityBySid(sid)
    return entity:GetRoleId()
end]]

---获取当前队伍的实体线sid
function TeamData.GetTeamSidByIndex(index, isScout,sandType)
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        --entity使用sid
        return line.sid or 0
        --return line.lineSid or 0
    end
    return 0
end

--获取最高战力英雄
function TeamData.GetMaxPowerPalByIndex(index,sandType)
    local heroList = TeamData.GetHeroListByIndex(index,sandType)
    local maxHero = nil
    if heroList then
        for _, pal in ipairs(heroList) do
            if pal.heroPower >= (maxHero and maxHero.heroPower or 0) then
                maxHero = pal
            end
        end
    end

    return maxHero

end

function TeamData.GetTeamTargetPosByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        return #line.dotList_txy>=3 and  {x = line.dotList_txy[#line.dotList_txy-1]/10000, y = line.dotList_txy[#line.dotList_txy]/10000}
    end
    return nil
end

--连线类型，目标类型
function TeamData.GetTeamTargetTypeByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        return line.type
    end
    return 0
end


--获取队伍跳转sid,如果有lineSid，则返回lineSid，如果有followLineSid，返回followLineSid，没有返回0
function TeamData.GetTeamJumpSidByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then

        local sid = TeamData.GetTeamSidByIndex(index, isScout,sandType)
        if sid == 0 then
            sid = line:GetFollowLineSid()
        end
        return sid or 0
    end
    return 0
end

--获取队伍状态
function TeamData.GetTeamStateByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        if line.state == 0 then
            --LogWarning("team state = 0", index)
        end
        return line.state or 1
    else
        --LogWarning("teamData is nil", index)
    end
    
    return 0

    --[[local list = gw_home_common_data.GetSquadDataByIndex(index)
    if list then
        return list.state or 1
    end
    return 0]]
end

--检查队伍是否在作业(沙漠风暴和沙盘通用)
function TeamData.CheckTeamIsBusy(index)
    if troopIsIdle[index] and troopIsIdle[index] ~= 1 then --1空闲0不空闲
        return true
    end
    local lineType = gw_const.TEAM_LINE_TYPE.Normal
    for sandType,v in pairs(team_data_map or {}) do
        local lines = v[lineType]
        local line = lines and lines[index]
        if line  and line.state then
            if line.state ~= common_pb.enSandboxTeamState_Idle then
                return true
            end
        end
    end
    if index ~= 4 then
        local result = gw_home_common_data.GetSquadState(index,2,false);
        if not result then
            return true
        end
    else
        local privilege_data = require "privilege_data"
        local privilege_define  = require "privilege_define"
        local value = gw_home_common_data.GetSquadState(4,2,false) and privilege_data.GetPrivilegeIsOpenByID(privilege_define.MONTH_FOUR_TEAM);--TabData[4] ~= nil;
        if not value then
            return true 
        end
    end
    return false
end

function TeamData.GetTeamTargetRoleIdByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        --LogWarning("line.targetdbid: ")
        --return line.targetdbid or 0
        return line:GetTargetSid() or 0
    end
    return 0
end

--获取增援目标的dbid
function TeamData.GetTeamTargetDbidByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        --LogWarning("line.targetdbid: ")
        --return line.targetdbid or 0
        return line:GetTargetDbid() or 0
    end
    return 0
end

--获取队伍目标roleName
function TeamData.GetTeamTargetRoleNameByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        --return line.targetName or 0
        return line:GetTargetName() or ""
    end
    return ""
end

--获取队伍对应的状态开始时间
function TeamData.GetStateBeginTimeByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        return line.startTime or 0
    end
    return 0
end

--获取队伍对应的状态结束时间
function TeamData.GetStateEndTimeByIndex(index, isScout,sandType)

    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    local line = lines and lines[index]
    if line then
        return line.endTime or 0
    end
    return 0
end


--获取队伍英雄列表,THeroBaseInfo
function TeamData.GetHeroListByIndex(index,sandType)

    local lines = TeamData.GetLines(sandType,gw_const.TEAM_LINE_TYPE.Normal)
    local line = lines and lines[index]
    if line and line.team then
        return line.team.pals
    end

    --[[  local list = gw_home_common_data.GetSquadDataByIndex(index)
      if list then
          return list.heroList
      end]]
end

--队伍英雄士兵信息
function TeamData.GetSoldierInfo(index, isScout,sandType)

    local lines = TeamData.GetLines(sandType,gw_const.TEAM_LINE_TYPE.Normal)
    local line = lines and lines[index]
    if line and line.team then
        return line.team.soldierInfo
    end
    return nil
end

--计算队伍每个等级的士兵数量
function TeamData.GetSoldierLevelNumByIndex(index, isScout,sandType)
    local soldierInfo = TeamData.GetSoldierInfo(index, isScout,sandType)
    local data = {}
    if soldierInfo then
        local soldierNum = soldierInfo.soldierNum or {}
        local soldierWound = soldierInfo.soldierWound or {}
        local soldierInjury = soldierInfo.soldierInjury or {}
        local soldierDead = soldierInfo.soldierDead or {}
        for level, num in ipairs(soldierNum) do
            local cur = num - soldierWound[level] - soldierInjury[level] - soldierDead[level]
            local soldierCfg = game_scheme:Soldier_1(level)
            if not soldierCfg then
                LogWarning("cannot find soldierCfg by level: ",level)
            end
            local soldierId = soldierCfg and soldierCfg.soldierID or 0
            if cur > 0 then
                data[soldierId] = cur
            end
        end
    end
    return data
end

--计算队伍总带兵数量
function TeamData.GetSoldierNumByIndex(index, isScout,sandType)
    local soldierInfo = TeamData.GetSoldierInfo(index, isScout,sandType)
    return TeamData.GetSoldierNumBySoldierInfo(soldierInfo)
end

--根据队伍士兵数据获取队伍士兵信息
function TeamData.GetSoldierNumBySoldierInfo(soldierInfo)
    local teamCur = 0
    local teamMax = 0
    if soldierInfo then
        --计算士兵数量
        local palSoldierValid = soldierInfo.palSoldierValid or {}
        local palSoldierMax = soldierInfo.palSoldierMax or {}
        local palSoldierNum = soldierInfo.palSoldierNum or {}
        for k, _ in ipairs(palSoldierNum) do
            local cur = palSoldierValid[k]
            local max = palSoldierMax[k]
            if cur > 0 then
                teamCur = teamCur + cur
            end
            if max > 0 then
                teamMax = teamMax + max
            end
        end
    end
    return teamCur, teamMax
end

--获取队伍士兵列表
function TeamData.GetSoldierDataByIndex(index, isScout,sandType)
    --[[local soldierData = {
        max = 0,--最大值
        cur = 0,--当前值
        ratio = 0,--权重比例
        data = {
            --[id] = count,
        }
        
    }]]
    local soldierData = {
        max = 0,--最大值
        cur = 0,--当前值
        data = {
            --[id] = count,
        }
    }
    soldierData.data = TeamData.GetSoldierLevelNumByIndex(index, isScout,sandType)
    soldierData.cur, soldierData.max = TeamData.GetSoldierNumByIndex(index, isScout,sandType)
    return soldierData
end

--获取队伍英雄带兵数
function TeamData.GetHeroSoldierNum(index, heroIndex, isScout,sandType)
    local soldierInfo = TeamData.GetSoldierInfo(index, isScout,sandType)
    local data = {
        cur = 0,
        max = 0   
    }
    if soldierInfo then
        if soldierInfo.palSoldierMax then
            data.max = soldierInfo.palSoldierMax[heroIndex] or 0
        end
        if soldierInfo.palSoldierMax then
            data.cur = soldierInfo.palSoldierValid[heroIndex] or 0
        end
    end
    return data
end


--获取队伍英雄总战力
function TeamData.GetTotalHeroPowerByIndex(index,sandType)
    local heroList = TeamData.GetHeroListByIndex(index,sandType)
    local totalPower = 0
    if heroList then
        for _, hero in ipairs(heroList) do
            if hero and hero.heroID then
                local heroPower = gw_power_mgr.GetHeroPowerByCfgId(hero.heroID) or 0
                totalPower = totalPower + heroPower
            end
        end
    end
    return totalPower
end



---获取队伍总战力
function TeamData.GetTotalPowerByIndex(teamIndex, soldierData,sandType)
    local totalPower = 0
    local soldierPower = 0
    local heroTotalPower = TeamData.GetTotalHeroPowerByIndex(teamIndex,sandType) or 0
    if not soldierData then
        soldierData = TeamData.GetSoldierDataByIndex(teamIndex,false,sandType)
    end
    
    for id,num in pairs(soldierData.data) do
        soldierPower = soldierPower + gw_power_mgr.GetSoliderForceInQueue(num, id)
    end
    
    local soldierPercentage = soldierData.max == 0 and 0 or soldierData.cur / soldierData.max
    totalPower = gw_power_mgr.GetOutBattlePower(heroTotalPower, soldierPower, soldierPercentage)
    local petPower = GWG.GWHomeMgr.droneData.GetDroneLocalPower() or 0
    --TODO 技能晶片战力
    local equipmentPower = 0
    totalPower = totalPower + petPower*soldierPercentage + equipmentPower*soldierPercentage
    --return myTeamPower
    --最后这一步再向下取整
    totalPower = math.floor(totalPower)
    return totalPower
end

--获取队伍战力（模拟满配士兵）
function TeamData.GetTeamPowerByIndexByAllSoldier(teamIndex, isScout,sandType)
    local totalPower = 0
    local gw_home_soldier_data = require "gw_home_soldier_data"
    local maxSoldierLevel = gw_home_soldier_data.GetCampusSoldierLevel()
    local maxLevelSoldierCfg = game_scheme:Soldier_1(maxSoldierLevel)
    local heroList = TeamData.GetHeroListByIndex(teamIndex,sandType)
    if heroList then
        local gw_hero_data = require "gw_hero_data"
        for _, hero in ipairs(heroList) do
            local entity = gw_hero_data.GetHeroCfgId2Entity(hero.heroID)
            local carrySoldierNum = entity and entity:GetHeroSoldierNum() or 0
            local heroPower = gw_power_mgr.GetHeroPowerByCfgId(hero.heroID) or 0
            local soldierPower = maxLevelSoldierCfg and gw_power_mgr.GetSoliderForceInQueue(carrySoldierNum,maxLevelSoldierCfg.soldierID) or 0
            totalPower = totalPower + heroPower + soldierPower
        end
        if totalPower ~= 0 then
            local gw_home_drone_data = require "gw_home_drone_data"
            totalPower = totalPower + gw_home_drone_data.GetDroneLocalPower()
        end
    end
    return totalPower
end

---获取解锁的队伍数量
function TeamData.GetUnlockTeamCount(isScout,sandType)
    local count = 0
    local maxCount = isScout and gw_home_common_data.GetMaxAircraftCount() or TeamData.totalTeamCount
    for i = 1, maxCount do
        if TeamData.GetIsUnlockByIndex(i, isScout) then
            count = count + 1
        end
    end
    return count
end


---获取空闲的队伍数量
function TeamData.GetIdleTeamCount(isScout,sandType)
    local count = TeamData.GetUnlockTeamCount(isScout,sandType)
    local idleCount =0
    for i = 1, count do
        if TeamData.GetTeamStateByIndex(i, isScout,sandType) == common_pb.enSandboxTeamState_Idle then
            idleCount = idleCount + 1
        end
    end
   
    return idleCount
end

--根据Index获取解锁状态
function TeamData.GetIsUnlockByIndex(index, isScout)
    local type = isScout and gw_home_common_data.unlockItemType.Aircraft or gw_home_common_data.unlockItemType.Squad
    local isUnlock = gw_home_common_data.GetSquadState(index, type)
    if index == 4 then
        local privilege_data = require("privilege_data")
        local privilege_define = require("privilege_define")
        return isUnlock and privilege_data.GetPrivilegeIsOpenByID(privilege_define.MONTH_FOUR_TEAM)
    end
    return gw_home_common_data.GetSquadState(index, type)
end

---@description 获取所有队伍
---@param isScout 是否为侦查队伍,默认为普通队伍
function TeamData.GetAllTeamEntityList(isScout,sandType)
    
    local lineType = isScout and gw_const.TEAM_LINE_TYPE.Scouting or  gw_const.TEAM_LINE_TYPE.Normal
    local lines = TeamData.GetLines(sandType,lineType)
    return lines
end
--endregion

---@deprecated 获取队伍状态图标
function TeamData.GetStateIconByIndex(index, isScout,sandType)
    local attackTargetType = TeamData.GetTeamTargetTypeByIndex(index,sandType)
    local state = TeamData.GetTeamStateByIndex(index,isScout,sandType)
    local stateIcon = "chengJian_icon_daiJi"    --默认为在家休息的图标
    if state == common_pb.enSandboxTeamState_Idle then
        return stateIcon
    end
    local marchCfg = game_scheme:SandMapMarch_0(attackTargetType)
    if not marchCfg then

        log.Error("cannot find marchCfg by attackTargetType: ",attackTargetType)
        return stateIcon
    end
    --local attackType = sand_team_data.GetTeamTargetTypeByIndex(i, isScout)
    if state == common_pb.enSandboxTeamState_Going or state == common_pb.enSandboxTeamState_Massing then
        --出发阶段
        stateIcon = marchCfg.advanceicon
    elseif state == common_pb.enSandboxTeamState_Backing then
        --返回阶段
        stateIcon = marchCfg.backicon
    elseif state == common_pb.enSandboxTeamState_Collecting  then
        --持续阶段
        stateIcon = marchCfg.doingicon
    elseif state == common_pb.enSandboxTeamState_MassWait then
        --集结等待阶段
        stateIcon = marchCfg.groupicon
    end
    return stateIcon
end

---@deprecated 获取队伍是否允许加速
function TeamData.GetCanSpeedByIndex(index,isScout,sandType)
    local canSpeed = true    --默认为不允许加速
    local lines = TeamData.GetLines(sandType,gw_const.TEAM_LINE_TYPE.Normal)
    local line = lines and lines[index]
    if not line then
        return false
    end
    local isMassMember = line:GetFollowLineSid() ~= 0
    local marchCfg = game_scheme:SandMapMarch_0(line.type)
    if not marchCfg then
        return false
    end
    local state = TeamData.GetTeamStateByIndex(index,isScout,sandType)
    local marchCompareName = sand_attack_const.MarchParamsNameByState[state]
    if not marchCompareName or not marchCfg[marchCompareName] or marchCfg[marchCompareName].count == 0 then
        return false
    end
    if isMassMember then
        return false
    end
    return marchCfg[marchCompareName].data[0] == 1
end

--region  数据初始化及更新
--TSandboxMarch
--[[message TSandboxMarch
{
    required uint64 lineSid = 1;	    		//行军对象SID
required enSandboxLineType type = 2;		//行军类型
required emTeamState state = 3;				//行军状态
repeated TSandboxLineDot dotList = 4;		// 连线上的节点
optional TSandboxLineDot changePos = 5;		// 中途变更节点
optional TSimpleTeam team = 6;				// 携带的队伍
optional uint32 startTime = 7;				// 开始时间
optional uint32 endTime = 8;				// 结束时间
repeated TSandBoxProp prop = 9;				// 行军int属性
repeated TSandBoxPropStr strProp = 10;		// 行军str属性
}]]

function TeamData.UpdateEntityData(data, entity)
    local index = data.team and data.team.index
    local gw_march_entity = require "gw_march_entity"
    if not entity then
        entity = gw_march_entity:CreateInstance()
        entity:Create(data,true)
    else
        --队伍数据每次都是全量发送,所以需要清除prop数据
        entity:ClearPropData()
        entity:UpdateData(data)
    end
    --dump(entity)
    return entity
end

function TeamData.UpdateLine(line,sandTeamType,dataType)
    local sandType = sandTeamType or gw_const.SandTeamType.Sand
    dataType = dataType or gw_const.TEAM_DATA_TYPE.ALL
    --log.Error("UpdateLine dataType = "..dataType)
    --dataType = gw_const.TEAM_DATA_TYPE.ALL
    --local gw_storm_mgr = require "gw_storm_mgr"
    -- if not gw_storm_mgr.CheckStormOpen() then
    --     dataType = gw_const.TEAM_DATA_TYPE.ALL
    -- end
    if not team_data_map[sandType] then
        team_data_map[sandType] = {}
    end
    if line then
        
        local lineType = (line.type and special_teams[line.type]) and special_teams[line.type] or gw_const.TEAM_LINE_TYPE.Normal

        if not team_data_map[sandType][lineType] then
            team_data_map[sandType][lineType] = {}
        end
        if line.team and line.team.index then
            local index = line.team.index
            local teamEntity = team_data_map[sandType][lineType][index]
           

            if dataType then
                if  dataType == gw_const.TEAM_DATA_TYPE.Formation then
                    if  teamEntity then
                        for sandType,tempMap in pairs(team_data_map) do
                            if tempMap[lineType] and tempMap[lineType][index] then
                                tempMap[lineType][index]:UpdateFormation(line)
                            end
                        end
                    end
                elseif dataType == gw_const.TEAM_DATA_TYPE.ALL then
                    if not teamEntity then
                        local gw_march_entity = require "gw_march_entity"
                        teamEntity = gw_march_entity:CreateInstance()
                        teamEntity:Create(line,dataType)
                    else
                         --队伍数据每次都是全量发送,所以需要清除prop数据
                        teamEntity:ClearPropData()
                        teamEntity:UpdateData(line,dataType)
                    end
                end
            end
            team_data_map[sandType][lineType][line.team.index] = teamEntity
        end
    end

    if sandType == gw_const.SandTeamType.Sand then
        --是沙盘的普通队伍
        if line and line.type and not special_teams[line.type] and line.team and line.team.index and #line.team.pals ~= 0 then
            local player_prefs=require "player_prefs"
            --存
            local str = string.format("%s%d",sand_attack_const.HAS_SET_HERO_TEAM, line.team.index)
            player_prefs.SetCacheData(str,true)
        end
        if not isInitSandTeamData then
            if waitTimer then
                return
            end
            local util = require "util"
            waitTimer = util.DelayCallOnce(1,function()
                isInitSandTeamData = true

            end)
        end
    end
end


function TeamData.GetSandTeamIsInit()
    return isInitSandTeamData
end

function TeamData.GetLines(sandType,lineType)
    sandType = sandType or TeamData.GetSandType()
    --sandType = sandType or gw_const.SandType[gw_const.ESceneType.Sand]
    lineType = lineType or gw_const.TEAM_LINE_TYPE.Normal
    if team_data_map[sandType]  and team_data_map[sandType][lineType] then
        return team_data_map[sandType][lineType]
    end
    return nil
end

--更新队伍列表
function TeamData.UpdateTeamList(lineUp,sandType,dataType)
    if lineUp then
        TeamData.UpdateLine(lineUp,sandType,dataType)

        --LogWarning("更新队伍列表", Edump({
        --    count = normal_team_Data.lines and #normal_team_Data.lines or 0,
        --    --teams = GetHeroListData(),
        --    scoutCount = scout_team_data.lines and #scout_team_data.lines,
        --}))
        local sand_ui_event = require "sand_ui_event_define"
        local event = require "event"
        event.DelayTrigger(sand_ui_event.GW_TEAM_DATA_CHANGE)
    end
end

function TeamData.GetSandType()
    local gw_mgr = require "gw_mgr"
    local sceneId =  gw_mgr.GetFsmCurScene() or gw_const.ESceneType.Sand
    return TeamData.GetSandTeamTypeBySceneId(sceneId)
end

function TeamData.GetSandTeamTypeBySandType(sandType)
    local sandTeamType = gw_const.SandTeamType.Sand
    if  sandType == gw_const.SandType[gw_const.ESceneType.Storm] then
        sandTeamType = gw_const.SandTeamType.Storm
    end
    return sandTeamType
end

function TeamData.GetSandTeamTypeBySceneId(sceneId)
    local sandTeamType = gw_const.SandTeamType.Sand
    if  sceneId == gw_const.ESceneType.Storm then
        sandTeamType = gw_const.SandTeamType.Storm
    end
    return sandTeamType
end
function TeamData.ClearData()
    team_data_map = {}
    isInitSandTeamData = false
    waitTimer = nil
end

function TeamData.ClearDataByType(sandType)
    if sandType == nil then
        return
    end
    team_data_map[sandType] = nil
end

function TeamData.OnQuickLineUp()
    local function_open = require "function_open"
    local functionOpenCfg = function_open.CheckFunctionIsOpen(1533,false,false)
    if functionOpenCfg then
        local new_hero_select_mgr = require "new_hero_select_mgr"
        new_hero_select_mgr.QuickSelectHero()
    end
end
--endregion

event.Register(gw_hero_define.GET_NEW_HERO,TeamData.OnQuickLineUp)
event.Register(event.USER_DATA_RESET, TeamData.ClearData)
event.Register(gw_ed.GW_GET_TROOP_IS_IDLE, TeamData.UpdateTroopState)


return TeamData