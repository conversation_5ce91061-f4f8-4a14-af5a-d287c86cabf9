-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local mq_common_pb=require("mq_common_pb")
local common_new_pb=require("common_new_pb")
local challenge_pb=require("challenge_pb")
module('chat_pb')


V1M=V(4,"Channel_World",0,1)
V2M=V(4,"Channel_Guild",1,2)
V3M=V(4,"Channel_Territory",2,4)
V4M=V(4,"Channel_Recruit",3,8)
V5M=V(4,"Channel_WMTOPRACE",4,32)
V6M=V(4,"Channel_Language",5,64)
V7M=V(4,"Channel_AllianceR4R5",6,128)
V8M=V(4,"Channel_Private",7,256)
E1M=E(3,"tChannelType",".CSMsg.tChannelType")
V9M=V(4,"enSign_Translate",0,1)
V10M=V(4,"enSign_GetLangInfo",1,2)
E2M=E(3,"tChatSignType",".CSMsg.tChatSignType")
F1D=F(2,"channel",".CSMsg.TMSG_CHAT_SPEAK_REQ.channel",1,0,2,false,0,5,1)
F2D=F(2,"sType",".CSMsg.TMSG_CHAT_SPEAK_REQ.sType",2,1,2,false,nil,14,8)
F3D=F(2,"context",".CSMsg.TMSG_CHAT_SPEAK_REQ.context",3,2,1,false,"",9,9)
F4D=F(2,"hero",".CSMsg.TMSG_CHAT_SPEAK_REQ.hero",4,3,1,false,nil,11,10)
F5D=F(2,"goods",".CSMsg.TMSG_CHAT_SPEAK_REQ.goods",5,4,1,false,nil,11,10)
F6D=F(2,"langText",".CSMsg.TMSG_CHAT_SPEAK_REQ.langText",6,5,1,false,nil,11,10)
F7D=F(2,"topHero",".CSMsg.TMSG_CHAT_SPEAK_REQ.topHero",7,6,3,false,{},11,10)
F8D=F(2,"shareLottery",".CSMsg.TMSG_CHAT_SPEAK_REQ.shareLottery",8,7,1,false,nil,11,10)
F9D=F(2,"voidArenaID",".CSMsg.TMSG_CHAT_SPEAK_REQ.voidArenaID",9,8,1,false,0,5,1)
F10D=F(2,"leagueRecruit",".CSMsg.TMSG_CHAT_SPEAK_REQ.leagueRecruit",10,9,1,false,nil,11,10)
F11D=F(2,"extendinfo",".CSMsg.TMSG_CHAT_SPEAK_REQ.extendinfo",11,10,1,false,"",9,9)
F12D=F(2,"farmLottery",".CSMsg.TMSG_CHAT_SPEAK_REQ.farmLottery",12,11,1,false,nil,11,10)
F13D=F(2,"roomID",".CSMsg.TMSG_CHAT_SPEAK_REQ.roomID",13,12,1,false,0,13,3)
F14D=F(2,"shareInfo",".CSMsg.TMSG_CHAT_SPEAK_REQ.shareInfo",14,13,1,false,nil,11,10)
F15D=F(2,"langtype",".CSMsg.TMSG_CHAT_SPEAK_REQ.langtype",15,14,1,false,0,5,1)
F16D=F(2,"exchangeInfo",".CSMsg.TMSG_CHAT_SPEAK_REQ.exchangeInfo",16,15,1,false,nil,11,10)
F17D=F(2,"picktherouteData",".CSMsg.TMSG_CHAT_SPEAK_REQ.picktherouteData",17,16,1,false,nil,11,10)
F18D=F(2,"nShareState",".CSMsg.TMSG_CHAT_SPEAK_REQ.nShareState",18,17,1,false,0,5,1)
F19D=F(2,"pumpkinData",".CSMsg.TMSG_CHAT_SPEAK_REQ.pumpkinData",19,18,1,false,nil,11,10)
F20D=F(2,"sandboxmarkData",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxmarkData",20,19,1,false,nil,11,10)
F21D=F(2,"sandboxmassData",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxmassData",21,20,1,false,nil,11,10)
F22D=F(2,"sandboxTreasureData",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxTreasureData",22,21,1,false,nil,11,10)
F23D=F(2,"sandboxTreasureFinishData",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxTreasureFinishData",23,22,1,false,nil,11,10)
F24D=F(2,"sandboxKastenboxMultipleRewardData",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxKastenboxMultipleRewardData",24,23,1,false,nil,11,10)
F25D=F(2,"leagueAchievementData",".CSMsg.TMSG_CHAT_SPEAK_REQ.leagueAchievementData",25,24,1,false,nil,11,10)
F26D=F(2,"sandboxCarriageTruckShare",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxCarriageTruckShare",26,25,1,false,nil,11,10)
F27D=F(2,"sandboxNCOccupied",".CSMsg.TMSG_CHAT_SPEAK_REQ.sandboxNCOccupied",27,26,1,false,nil,11,10)
F28D=F(2,"extendinfopb",".CSMsg.TMSG_CHAT_SPEAK_REQ.extendinfopb",28,27,1,false,"",12,9)
F29D=F(2,"deviceLangType",".CSMsg.TMSG_CHAT_SPEAK_REQ.deviceLangType",29,28,1,false,0,5,1)
F30D=F(2,"goldenEggsChat",".CSMsg.TMSG_CHAT_SPEAK_REQ.goldenEggsChat",30,29,1,false,nil,11,10)
F31D=F(2,"isSystemMsg",".CSMsg.TMSG_CHAT_SPEAK_REQ.isSystemMsg",31,30,1,false,false,8,7)
M1G=D(1,"TMSG_CHAT_SPEAK_REQ",".CSMsg.TMSG_CHAT_SPEAK_REQ",false,{},{},nil,{})
F32D=F(2,"err",".CSMsg.TMSG_CHAT_SPEAK_RSP.err",1,0,2,false,nil,14,8)
F33D=F(2,"sType",".CSMsg.TMSG_CHAT_SPEAK_RSP.sType",2,1,1,false,nil,14,8)
M23G=D(1,"TMSG_CHAT_SPEAK_RSP",".CSMsg.TMSG_CHAT_SPEAK_RSP",false,{},{},nil,{})
F34D=F(2,"msg",".CSMsg.TMSG_CHAT_NEW_MSG_NTF.msg",1,0,2,false,nil,11,10)
F35D=F(2,"channel",".CSMsg.TMSG_CHAT_NEW_MSG_NTF.channel",2,1,2,false,0,5,1)
M25G=D(1,"TMSG_CHAT_NEW_MSG_NTF",".CSMsg.TMSG_CHAT_NEW_MSG_NTF",false,{},{},nil,{})
F36D=F(2,"bHideVip",".CSMsg.tBlockCtrl.bHideVip",1,0,2,false,false,8,7)
F37D=F(2,"blockChannel",".CSMsg.tBlockCtrl.blockChannel",2,1,2,false,0,5,1)
F38D=F(2,"bRecruitMsg",".CSMsg.tBlockCtrl.bRecruitMsg",3,2,1,false,false,8,7)
M27G=D(1,"tBlockCtrl",".CSMsg.tBlockCtrl",false,{},{},nil,{})
F39D=F(2,"roleid",".CSMsg.TChatPrivateMsg.roleid",1,0,1,false,0,13,3)
F40D=F(2,"chatData",".CSMsg.TChatPrivateMsg.chatData",2,1,3,false,{},11,10)
M28G=D(1,"TChatPrivateMsg",".CSMsg.TChatPrivateMsg",false,{},{},nil,{})
F41D=F(2,"blockCtrl",".CSMsg.TMSG_CHAT_MSG_NTF.blockCtrl",1,0,2,false,nil,11,10)
F42D=F(2,"worldMsg",".CSMsg.TMSG_CHAT_MSG_NTF.worldMsg",2,1,3,false,{},11,10)
F43D=F(2,"GuildMsg",".CSMsg.TMSG_CHAT_MSG_NTF.GuildMsg",3,2,3,false,{},11,10)
F44D=F(2,"territoryMsg",".CSMsg.TMSG_CHAT_MSG_NTF.territoryMsg",4,3,3,false,{},11,10)
F45D=F(2,"recruitMsg",".CSMsg.TMSG_CHAT_MSG_NTF.recruitMsg",5,4,3,false,{},11,10)
F46D=F(2,"blockPlayer",".CSMsg.TMSG_CHAT_MSG_NTF.blockPlayer",6,5,3,false,{},5,1)
F47D=F(2,"muteEndTime",".CSMsg.TMSG_CHAT_MSG_NTF.muteEndTime",7,6,2,false,0,5,1)
F48D=F(2,"WMTopRaceMsg",".CSMsg.TMSG_CHAT_MSG_NTF.WMTopRaceMsg",8,7,3,false,{},11,10)
F49D=F(2,"languageMsg",".CSMsg.TMSG_CHAT_MSG_NTF.languageMsg",9,8,3,false,{},11,10)
F50D=F(2,"r4r5Msg",".CSMsg.TMSG_CHAT_MSG_NTF.r4r5Msg",10,9,3,false,{},11,10)
F51D=F(2,"urgentAnnounceNum",".CSMsg.TMSG_CHAT_MSG_NTF.urgentAnnounceNum",11,10,1,false,0,5,1)
F52D=F(2,"urgentAnnounceTime",".CSMsg.TMSG_CHAT_MSG_NTF.urgentAnnounceTime",12,11,1,false,0,13,3)
F53D=F(2,"privateMsg",".CSMsg.TMSG_CHAT_MSG_NTF.privateMsg",13,12,3,false,{},11,10)
M30G=D(1,"TMSG_CHAT_MSG_NTF",".CSMsg.TMSG_CHAT_MSG_NTF",false,{},{},nil,{})
F54D=F(2,"languageType",".CSMsg.TMSG_CHAT_LANGUAGE_MSG_REQ.languageType",1,0,1,false,0,5,1)
M31G=D(1,"TMSG_CHAT_LANGUAGE_MSG_REQ",".CSMsg.TMSG_CHAT_LANGUAGE_MSG_REQ",false,{},{},nil,{})
F55D=F(2,"errorCode",".CSMsg.TMSG_CHAT_LANGUAGE_MSG_RSP.errorCode",1,0,2,false,0,5,1)
F56D=F(2,"languageType",".CSMsg.TMSG_CHAT_LANGUAGE_MSG_RSP.languageType",2,1,1,false,0,5,1)
F57D=F(2,"languageMsg",".CSMsg.TMSG_CHAT_LANGUAGE_MSG_RSP.languageMsg",3,2,3,false,{},11,10)
M32G=D(1,"TMSG_CHAT_LANGUAGE_MSG_RSP",".CSMsg.TMSG_CHAT_LANGUAGE_MSG_RSP",false,{},{},nil,{})
F58D=F(2,"roleIds",".CSMsg.TBlockRoleList.roleIds",1,0,3,false,{},13,3)
M33G=D(1,"TBlockRoleList",".CSMsg.TBlockRoleList",false,{},{},nil,{})
F59D=F(2,"bBlock",".CSMsg.TMSG_CHAT_BLOCK_REQ.bBlock",1,0,2,false,false,8,7)
F60D=F(2,"roleid",".CSMsg.TMSG_CHAT_BLOCK_REQ.roleid",2,1,2,false,0,5,1)
M34G=D(1,"TMSG_CHAT_BLOCK_REQ",".CSMsg.TMSG_CHAT_BLOCK_REQ",false,{},{},nil,{})
F61D=F(2,"err",".CSMsg.TMSG_CHAT_BLOCK_RSP.err",1,0,2,false,nil,14,8)
F62D=F(2,"roleid",".CSMsg.TMSG_CHAT_BLOCK_RSP.roleid",2,1,2,false,0,5,1)
F63D=F(2,"bBlock",".CSMsg.TMSG_CHAT_BLOCK_RSP.bBlock",3,2,2,false,false,8,7)
M35G=D(1,"TMSG_CHAT_BLOCK_RSP",".CSMsg.TMSG_CHAT_BLOCK_RSP",false,{},{},nil,{})
M36G=D(1,"TMSG_CHAT_BLOCK_LIST_REQ",".CSMsg.TMSG_CHAT_BLOCK_LIST_REQ",false,{},{},{},{})
F64D=F(2,"shieldInfo",".CSMsg.TMSG_CHAT_BLOCK_LIST_RSP.shieldInfo",1,0,3,false,{},11,10)
M37G=D(1,"TMSG_CHAT_BLOCK_LIST_RSP",".CSMsg.TMSG_CHAT_BLOCK_LIST_RSP",false,{},{},nil,{})
F65D=F(2,"shieldInfo",".CSMsg.TMSG_CHAT_BLOCK_LIST_NTF.shieldInfo",1,0,3,false,{},11,10)
M39G=D(1,"TMSG_CHAT_BLOCK_LIST_NTF",".CSMsg.TMSG_CHAT_BLOCK_LIST_NTF",false,{},{},nil,{})
F66D=F(2,"roleid",".CSMsg.TMSG_CHAT_SEND_MAIL_REQ.roleid",1,0,2,false,0,5,1)
F67D=F(2,"context",".CSMsg.TMSG_CHAT_SEND_MAIL_REQ.context",2,1,2,false,"",9,9)
M40G=D(1,"TMSG_CHAT_SEND_MAIL_REQ",".CSMsg.TMSG_CHAT_SEND_MAIL_REQ",false,{},{},nil,{})
F68D=F(2,"err",".CSMsg.TMSG_CHAT_SEND_MAIL_RSP.err",1,0,2,false,nil,14,8)
M41G=D(1,"TMSG_CHAT_SEND_MAIL_RSP",".CSMsg.TMSG_CHAT_SEND_MAIL_RSP",false,{},{},nil,{})
F69D=F(2,"bHideVip",".CSMsg.TMSG_CHAT_BLOCK_CHANNEL_REQ.bHideVip",1,0,2,false,false,8,7)
F70D=F(2,"channel",".CSMsg.TMSG_CHAT_BLOCK_CHANNEL_REQ.channel",2,1,2,false,0,5,1)
F71D=F(2,"bRecruitMsg",".CSMsg.TMSG_CHAT_BLOCK_CHANNEL_REQ.bRecruitMsg",3,2,1,false,false,8,7)
M42G=D(1,"TMSG_CHAT_BLOCK_CHANNEL_REQ",".CSMsg.TMSG_CHAT_BLOCK_CHANNEL_REQ",false,{},{},nil,{})
F72D=F(2,"err",".CSMsg.TMSG_CHAT_BLOCK_CHANNEL_RSP.err",1,0,2,false,nil,14,8)
M43G=D(1,"TMSG_CHAT_BLOCK_CHANNEL_RSP",".CSMsg.TMSG_CHAT_BLOCK_CHANNEL_RSP",false,{},{},nil,{})
M44G=D(1,"TMSG_CHAT_LEAVE_LEAGUE_NTF",".CSMsg.TMSG_CHAT_LEAVE_LEAGUE_NTF",false,{},{},{},{})
F73D=F(2,"channel",".CSMsg.TMSG_CHAT_RECALL_MSG_REQ.channel",1,0,1,false,0,5,1)
F74D=F(2,"languageType",".CSMsg.TMSG_CHAT_RECALL_MSG_REQ.languageType",2,1,1,false,0,5,1)
F75D=F(2,"szChatID",".CSMsg.TMSG_CHAT_RECALL_MSG_REQ.szChatID",3,2,1,false,"",9,9)
M45G=D(1,"TMSG_CHAT_RECALL_MSG_REQ",".CSMsg.TMSG_CHAT_RECALL_MSG_REQ",false,{},{},nil,{})
F76D=F(2,"errorCode",".CSMsg.TMSG_CHAT_RECALL_MSG_RSP.errorCode",1,0,1,false,0,5,1)
F77D=F(2,"channel",".CSMsg.TMSG_CHAT_RECALL_MSG_RSP.channel",2,1,1,false,0,5,1)
F78D=F(2,"languageType",".CSMsg.TMSG_CHAT_RECALL_MSG_RSP.languageType",3,2,1,false,0,5,1)
F79D=F(2,"szChatID",".CSMsg.TMSG_CHAT_RECALL_MSG_RSP.szChatID",4,3,1,false,"",9,9)
M46G=D(1,"TMSG_CHAT_RECALL_MSG_RSP",".CSMsg.TMSG_CHAT_RECALL_MSG_RSP",false,{},{},nil,{})
F80D=F(2,"beReportid",".CSMsg.TMSG_CHAT_REPORT_REQ.beReportid",1,0,2,false,0,5,1)
F81D=F(2,"context",".CSMsg.TMSG_CHAT_REPORT_REQ.context",2,1,2,false,"",9,9)
F82D=F(2,"reportType",".CSMsg.TMSG_CHAT_REPORT_REQ.reportType",3,2,2,false,nil,14,8)
F83D=F(2,"desc",".CSMsg.TMSG_CHAT_REPORT_REQ.desc",4,3,2,false,"",9,9)
M47G=D(1,"TMSG_CHAT_REPORT_REQ",".CSMsg.TMSG_CHAT_REPORT_REQ",false,{},{},nil,{})
F84D=F(2,"err",".CSMsg.TMSG_CHAT_REPORT_RSP.err",1,0,2,false,nil,14,8)
M49G=D(1,"TMSG_CHAT_REPORT_RSP",".CSMsg.TMSG_CHAT_REPORT_RSP",false,{},{},nil,{})
F85D=F(2,"roleid",".CSMsg.TMSG_CHAT_BAN_NTF.roleid",1,0,2,false,0,5,1)
F86D=F(2,"endTime",".CSMsg.TMSG_CHAT_BAN_NTF.endTime",2,1,2,false,0,5,1)
F87D=F(2,"reason",".CSMsg.TMSG_CHAT_BAN_NTF.reason",3,2,2,false,"",9,9)
F88D=F(2,"commandType",".CSMsg.TMSG_CHAT_BAN_NTF.commandType",4,3,2,false,nil,14,8)
F89D=F(2,"channel",".CSMsg.TMSG_CHAT_BAN_NTF.channel",5,4,1,false,0,13,3)
M50G=D(1,"TMSG_CHAT_BAN_NTF",".CSMsg.TMSG_CHAT_BAN_NTF",false,{},{},nil,{})
F90D=F(2,"roleid",".CSMsg.TMSG_CHAT_BAN_REMOVE_NTF.roleid",1,0,2,false,0,5,1)
F91D=F(2,"commandType",".CSMsg.TMSG_CHAT_BAN_REMOVE_NTF.commandType",2,1,2,false,nil,14,8)
F92D=F(2,"reason",".CSMsg.TMSG_CHAT_BAN_REMOVE_NTF.reason",3,2,2,false,"",9,9)
F93D=F(2,"channel",".CSMsg.TMSG_CHAT_BAN_REMOVE_NTF.channel",4,3,1,false,0,13,3)
M52G=D(1,"TMSG_CHAT_BAN_REMOVE_NTF",".CSMsg.TMSG_CHAT_BAN_REMOVE_NTF",false,{},{},nil,{})
F94D=F(2,"toRoleId",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_REQ.toRoleId",1,0,2,false,0,5,1)
F95D=F(2,"msgType",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_REQ.msgType",2,1,2,false,nil,14,8)
F96D=F(2,"context",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_REQ.context",3,2,1,false,"",9,9)
F97D=F(2,"topHero",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_REQ.topHero",7,3,3,false,{},11,10)
F98D=F(2,"toRoleName",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_REQ.toRoleName",8,4,1,false,"",9,9)
M53G=D(1,"TMSG_PRIVATE_MSG_SPEAK_REQ",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_REQ",false,{},{},nil,{})
F99D=F(2,"err",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_RSP.err",1,0,2,false,nil,14,8)
M54G=D(1,"TMSG_PRIVATE_MSG_SPEAK_RSP",".CSMsg.TMSG_PRIVATE_MSG_SPEAK_RSP",false,{},{},nil,{})
F100D=F(2,"msg",".CSMsg.TMSG_PRIVATE_MSG_NTF.msg",1,0,2,false,nil,11,10)
M55G=D(1,"TMSG_PRIVATE_MSG_NTF",".CSMsg.TMSG_PRIVATE_MSG_NTF",false,{},{},nil,{})
F101D=F(2,"msg",".CSMsg.TMSG_PRIVATE_MSG_SET_NTF.msg",1,0,3,false,{},11,10)
M56G=D(1,"TMSG_PRIVATE_MSG_SET_NTF",".CSMsg.TMSG_PRIVATE_MSG_SET_NTF",false,{},{},nil,{})
F102D=F(2,"msg",".CSMsg.TMSG_MATE_MSG_SET_NTF.msg",1,0,3,false,{},11,10)
M57G=D(1,"TMSG_MATE_MSG_SET_NTF",".CSMsg.TMSG_MATE_MSG_SET_NTF",false,{},{},nil,{})
F103D=F(2,"stype",".CSMsg.tCrossSvrChatReq.stype",1,0,2,false,nil,14,8)
F104D=F(2,"channel",".CSMsg.tCrossSvrChatReq.channel",2,1,1,false,0,5,1)
F105D=F(2,"langtype",".CSMsg.tCrossSvrChatReq.langtype",3,2,1,false,0,5,1)
F106D=F(2,"toRoleId",".CSMsg.tCrossSvrChatReq.toRoleId",4,3,1,false,0,5,1)
F107D=F(2,"toworldid",".CSMsg.tCrossSvrChatReq.toworldid",5,4,1,false,0,5,1)
F108D=F(2,"context",".CSMsg.tCrossSvrChatReq.context",6,5,1,false,"",9,9)
F109D=F(2,"hero",".CSMsg.tCrossSvrChatReq.hero",7,6,1,false,nil,11,10)
F110D=F(2,"goods",".CSMsg.tCrossSvrChatReq.goods",8,7,1,false,nil,11,10)
F111D=F(2,"extendinfo",".CSMsg.tCrossSvrChatReq.extendinfo",9,8,1,false,"",9,9)
F112D=F(2,"topHero",".CSMsg.tCrossSvrChatReq.topHero",10,9,3,false,{},11,10)
F113D=F(2,"bgm",".CSMsg.tCrossSvrChatReq.bgm",11,10,1,false,false,8,7)
F114D=F(2,"shareLottery",".CSMsg.tCrossSvrChatReq.shareLottery",12,11,1,false,nil,11,10)
F115D=F(2,"farmLottery",".CSMsg.tCrossSvrChatReq.farmLottery",13,12,1,false,nil,11,10)
F116D=F(2,"toRoleName",".CSMsg.tCrossSvrChatReq.toRoleName",14,13,1,false,"",9,9)
F117D=F(2,"shareInfo",".CSMsg.tCrossSvrChatReq.shareInfo",15,14,1,false,nil,11,10)
F118D=F(2,"exchangeInfo",".CSMsg.tCrossSvrChatReq.exchangeInfo",16,15,1,false,nil,11,10)
F119D=F(2,"picktherouteData",".CSMsg.tCrossSvrChatReq.picktherouteData",17,16,1,false,nil,11,10)
F120D=F(2,"nShareState",".CSMsg.tCrossSvrChatReq.nShareState",18,17,1,false,0,5,1)
F121D=F(2,"pumpkinData",".CSMsg.tCrossSvrChatReq.pumpkinData",19,18,1,false,nil,11,10)
F122D=F(2,"sandboxmarkData",".CSMsg.tCrossSvrChatReq.sandboxmarkData",20,19,1,false,nil,11,10)
F123D=F(2,"sandboxmassData",".CSMsg.tCrossSvrChatReq.sandboxmassData",21,20,1,false,nil,11,10)
F124D=F(2,"sandboxTreasureData",".CSMsg.tCrossSvrChatReq.sandboxTreasureData",22,21,1,false,nil,11,10)
F125D=F(2,"sandboxTreasureFinishData",".CSMsg.tCrossSvrChatReq.sandboxTreasureFinishData",23,22,1,false,nil,11,10)
F126D=F(2,"sandboxKastenboxMultipleRewardData",".CSMsg.tCrossSvrChatReq.sandboxKastenboxMultipleRewardData",24,23,1,false,nil,11,10)
F127D=F(2,"leagueAchievementData",".CSMsg.tCrossSvrChatReq.leagueAchievementData",25,24,1,false,nil,11,10)
F128D=F(2,"sandboxCarriageTruckShare",".CSMsg.tCrossSvrChatReq.sandboxCarriageTruckShare",26,25,1,false,nil,11,10)
F129D=F(2,"sandboxNCOccupied",".CSMsg.tCrossSvrChatReq.sandboxNCOccupied",27,26,1,false,nil,11,10)
F130D=F(2,"deviceLangType",".CSMsg.tCrossSvrChatReq.deviceLangType",28,27,1,false,0,5,1)
M58G=D(1,"tCrossSvrChatReq",".CSMsg.tCrossSvrChatReq",false,{},{},nil,{})
F131D=F(2,"context",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ.context",1,0,2,false,nil,11,10)
M59G=D(1,"TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ",false,{},{},nil,{})
F132D=F(2,"err",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP.err",1,0,2,false,nil,14,8)
F133D=F(2,"stype",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP.stype",2,1,1,false,nil,14,8)
M60G=D(1,"TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP",false,{},{},nil,{})
F134D=F(2,"msg",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF.msg",1,0,2,false,nil,11,10)
M61G=D(1,"TMSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF",".CSMsg.TMSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF",false,{},{},nil,{})
F135D=F(2,"context",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ.context",1,0,2,false,nil,11,10)
M62G=D(1,"TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ",false,{},{},nil,{})
F136D=F(2,"err",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP.err",1,0,2,false,nil,14,8)
F137D=F(2,"stype",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP.stype",2,1,1,false,nil,14,8)
M63G=D(1,"TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP",false,{},{},nil,{})
F138D=F(2,"msg",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF.msg",1,0,2,false,nil,11,10)
M64G=D(1,"TMSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF",".CSMsg.TMSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF",false,{},{},nil,{})
F139D=F(2,"langtype",".CSMsg.TMSG_CHAT_REPORT_CHANNEL_REQ.langtype",1,0,2,false,0,5,1)
F140D=F(2,"channelid",".CSMsg.TMSG_CHAT_REPORT_CHANNEL_REQ.channelid",2,1,2,false,0,5,1)
M65G=D(1,"TMSG_CHAT_REPORT_CHANNEL_REQ",".CSMsg.TMSG_CHAT_REPORT_CHANNEL_REQ",false,{},{},nil,{})
F141D=F(2,"err",".CSMsg.TMSG_CHAT_REPORT_CHANNEL_RSP.err",1,0,2,false,nil,14,8)
M66G=D(1,"TMSG_CHAT_REPORT_CHANNEL_RSP",".CSMsg.TMSG_CHAT_REPORT_CHANNEL_RSP",false,{},{},nil,{})
F142D=F(2,"signType",".CSMsg.TMSG_CHAT_GET_SIGN_REQ.signType",1,0,2,false,nil,14,8)
F143D=F(2,"context",".CSMsg.TMSG_CHAT_GET_SIGN_REQ.context",2,1,1,false,"",9,9)
F144D=F(2,"msgid",".CSMsg.TMSG_CHAT_GET_SIGN_REQ.msgid",3,2,1,false,"",9,9)
M67G=D(1,"TMSG_CHAT_GET_SIGN_REQ",".CSMsg.TMSG_CHAT_GET_SIGN_REQ",false,{},{},nil,{})
F145D=F(2,"err",".CSMsg.TMSG_CHAT_GET_SIGN_RSP.err",1,0,2,false,nil,14,8)
F146D=F(2,"signType",".CSMsg.TMSG_CHAT_GET_SIGN_RSP.signType",2,1,1,false,nil,14,8)
F147D=F(2,"sign1",".CSMsg.TMSG_CHAT_GET_SIGN_RSP.sign1",3,2,1,false,"",9,9)
F148D=F(2,"time1",".CSMsg.TMSG_CHAT_GET_SIGN_RSP.time1",4,3,1,false,0,13,3)
F149D=F(2,"msgid",".CSMsg.TMSG_CHAT_GET_SIGN_RSP.msgid",5,4,1,false,"",9,9)
M69G=D(1,"TMSG_CHAT_GET_SIGN_RSP",".CSMsg.TMSG_CHAT_GET_SIGN_RSP",false,{},{},nil,{})
F150D=F(2,"dbid1",".CSMsg.tChatCrossLikeMsg.dbid1",1,0,2,false,0,13,3)
F151D=F(2,"dbid2",".CSMsg.tChatCrossLikeMsg.dbid2",2,1,2,false,0,13,3)
F152D=F(2,"chatType",".CSMsg.tChatCrossLikeMsg.chatType",3,2,2,false,0,13,3)
F153D=F(2,"szChatID",".CSMsg.tChatCrossLikeMsg.szChatID",4,3,2,false,"",9,9)
M70G=D(1,"tChatCrossLikeMsg",".CSMsg.tChatCrossLikeMsg",false,{},{},nil,{})
F154D=F(2,"dbid",".CSMsg.tChatLikeMsg.dbid",1,0,2,false,0,13,3)
F155D=F(2,"szChatID",".CSMsg.tChatLikeMsg.szChatID",2,1,2,false,"",9,9)
M71G=D(1,"tChatLikeMsg",".CSMsg.tChatLikeMsg",false,{},{},nil,{})
F156D=F(2,"msg",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_NTF.msg",1,0,2,false,nil,11,10)
M72G=D(1,"TMSG_PRIVATE_CHAT_LIKE_NTF",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_NTF",false,{},{},nil,{})
F157D=F(2,"msg",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_ALL_NTF.msg",1,0,3,false,{},11,10)
M73G=D(1,"TMSG_PRIVATE_CHAT_LIKE_ALL_NTF",".CSMsg.TMSG_PRIVATE_CHAT_LIKE_ALL_NTF",false,{},{},nil,{})
F158D=F(2,"bClose",".CSMsg.TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF.bClose",1,0,2,false,false,8,7)
M74G=D(1,"TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF",".CSMsg.TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF",false,{},{},nil,{})
F159D=F(2,"roleid",".CSMsg.tGmConversationUser.roleid",1,0,2,false,0,13,3)
F160D=F(2,"gmid",".CSMsg.tGmConversationUser.gmid",2,1,2,false,0,13,3)
M75G=D(1,"tGmConversationUser",".CSMsg.tGmConversationUser",false,{},{},nil,{})
F161D=F(2,"userinfo",".CSMsg.tGmConversationUserInfo.userinfo",1,0,3,false,{},11,10)
M76G=D(1,"tGmConversationUserInfo",".CSMsg.tGmConversationUserInfo",false,{},{},nil,{})
F162D=F(2,"battleIds",".CSMsg.tShareMultiBattleMsg.battleIds",1,0,3,false,{},9,9)
F163D=F(2,"attackPlayerInfo",".CSMsg.tShareMultiBattleMsg.attackPlayerInfo",2,1,2,false,nil,11,10)
F164D=F(2,"defencePlayerInfo",".CSMsg.tShareMultiBattleMsg.defencePlayerInfo",3,2,2,false,nil,11,10)
F165D=F(2,"isVictory",".CSMsg.tShareMultiBattleMsg.isVictory",4,3,2,false,false,8,7)
F166D=F(2,"areaRsp",".CSMsg.tShareMultiBattleMsg.areaRsp",5,4,1,false,nil,11,10)
M77G=D(1,"tShareMultiBattleMsg",".CSMsg.tShareMultiBattleMsg",false,{},{},nil,{})
F167D=F(2,"bereportedID",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ.bereportedID",1,0,2,false,0,13,3)
F168D=F(2,"channel",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ.channel",2,1,2,false,0,13,3)
F169D=F(2,"reason",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ.reason",3,2,3,false,{},14,8)
F170D=F(2,"content",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ.content",4,3,2,false,"",9,9)
F171D=F(2,"reporterpid",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ.reporterpid",5,4,2,false,"",9,9)
F172D=F(2,"remark",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ.remark",6,5,1,false,"",9,9)
M79G=D(1,"TMSG_CHAT_REPORT_BACKSTAGE_REQ",".CSMsg.TMSG_CHAT_REPORT_BACKSTAGE_REQ",false,{},{},nil,{})
F173D=F(2,"channel",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_REQ.channel",1,0,2,false,nil,14,8)
F174D=F(2,"szChatID",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_REQ.szChatID",2,1,2,false,"",9,9)
M81G=D(1,"TMSG_CHAT_MSG_GETBEFORE_REQ",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_REQ",false,{},{},nil,{})
F175D=F(2,"channel",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_RSP.channel",1,0,2,false,nil,14,8)
F176D=F(2,"chatMsg",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_RSP.chatMsg",2,1,3,false,{},11,10)
F177D=F(2,"bChatOver",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_RSP.bChatOver",3,2,1,false,false,8,7)
M83G=D(1,"TMSG_CHAT_MSG_GETBEFORE_RSP",".CSMsg.TMSG_CHAT_MSG_GETBEFORE_RSP",false,{},{},nil,{})
F178D=F(2,"sType",".CSMsg.ChatShareBattleReport.sType",1,0,2,false,nil,14,8)
F179D=F(2,"sId",".CSMsg.ChatShareBattleReport.sId",2,1,2,false,0,4,4)
F180D=F(2,"subMailType",".CSMsg.ChatShareBattleReport.subMailType",3,2,2,false,0,13,3)
F181D=F(2,"bIsVic",".CSMsg.ChatShareBattleReport.bIsVic",4,3,1,false,false,8,7)
F182D=F(2,"contentLangId",".CSMsg.ChatShareBattleReport.contentLangId",5,4,2,false,0,13,3)
F183D=F(2,"nameInfo",".CSMsg.ChatShareBattleReport.nameInfo",6,5,2,false,"",9,9)
F184D=F(2,"nameInfoLangId",".CSMsg.ChatShareBattleReport.nameInfoLangId",7,6,2,false,0,13,3)
F185D=F(2,"teamCount",".CSMsg.ChatShareBattleReport.teamCount",8,7,2,false,0,13,3)
F186D=F(2,"teamPowerSum",".CSMsg.ChatShareBattleReport.teamPowerSum",9,8,2,false,0,4,4)
M84G=D(1,"ChatShareBattleReport",".CSMsg.ChatShareBattleReport",false,{},{},nil,{})
F187D=F(2,"subMailType",".CSMsg.TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ.subMailType",1,0,2,false,0,13,3)
F188D=F(2,"sId",".CSMsg.TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ.sId",2,1,2,false,0,4,4)
F189D=F(2,"data",".CSMsg.TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ.data",3,2,2,false,"",12,9)
M85G=D(1,"TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ",".CSMsg.TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ",false,{},{},nil,{})
F190D=F(2,"errorCode",".CSMsg.TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_RSP.errorCode",1,0,2,false,nil,14,8)
M86G=D(1,"TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_RSP",".CSMsg.TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_RSP",false,{},{},nil,{})
F191D=F(2,"subMailType",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_REQ.subMailType",1,0,2,false,0,13,3)
F192D=F(2,"sId",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_REQ.sId",2,1,2,false,0,4,4)
M87G=D(1,"TMSG_CHAT_MSG_GET_BATTLE_REPORT_REQ",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_REQ",false,{},{},nil,{})
F193D=F(2,"errorCode",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP.errorCode",1,0,2,false,nil,14,8)
F194D=F(2,"data",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP.data",2,1,1,false,"",12,9)
F195D=F(2,"subMailType",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP.subMailType",3,2,1,false,0,13,3)
F196D=F(2,"sId",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP.sId",4,3,1,false,0,4,4)
M88G=D(1,"TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP",".CSMsg.TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M}
E2M.values = {V9M,V10M}
F2D.enum_type=mq_common_pb.E8M
F4D.message_type=mq_common_pb.M3G
F5D.message_type=mq_common_pb.M4G
F6D.message_type=mq_common_pb.M13G
F7D.message_type=mq_common_pb.M7G
F8D.message_type=mq_common_pb.M11G
F10D.message_type=mq_common_pb.M14G
F12D.message_type=mq_common_pb.M12G
F14D.message_type=mq_common_pb.M27G
F16D.message_type=mq_common_pb.M28G
F17D.message_type=mq_common_pb.M23G
F19D.message_type=mq_common_pb.M24G
F20D.message_type=mq_common_pb.M15G
F21D.message_type=mq_common_pb.M16G
F22D.message_type=mq_common_pb.M18G
F23D.message_type=mq_common_pb.M19G
F24D.message_type=mq_common_pb.M20G
F25D.message_type=mq_common_pb.M17G
F26D.message_type=mq_common_pb.M29G
F27D.message_type=mq_common_pb.M21G
F30D.message_type=mq_common_pb.M30G
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D}
F32D.enum_type=error_code_pb.E1M
F33D.enum_type=mq_common_pb.E8M
M23G.fields={F32D, F33D}
F34D.message_type=mq_common_pb.M25G
M25G.fields={F34D, F35D}
M27G.fields={F36D, F37D, F38D}
F40D.message_type=mq_common_pb.M31G
M28G.fields={F39D, F40D}
F41D.message_type=M27G
F42D.message_type=mq_common_pb.M25G
F43D.message_type=mq_common_pb.M25G
F44D.message_type=mq_common_pb.M25G
F45D.message_type=mq_common_pb.M25G
F48D.message_type=mq_common_pb.M25G
F49D.message_type=mq_common_pb.M25G
F50D.message_type=mq_common_pb.M25G
F53D.message_type=M28G
M30G.fields={F41D, F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D, F50D, F51D, F52D, F53D}
M31G.fields={F54D}
F57D.message_type=mq_common_pb.M25G
M32G.fields={F55D, F56D, F57D}
M33G.fields={F58D}
M34G.fields={F59D, F60D}
F61D.enum_type=error_code_pb.E1M
M35G.fields={F61D, F62D, F63D}
F64D.message_type=common_new_pb.M1G
M37G.fields={F64D}
F65D.message_type=common_new_pb.M1G
M39G.fields={F65D}
M40G.fields={F66D, F67D}
F68D.enum_type=error_code_pb.E1M
M41G.fields={F68D}
M42G.fields={F69D, F70D, F71D}
F72D.enum_type=error_code_pb.E1M
M43G.fields={F72D}
M45G.fields={F73D, F74D, F75D}
M46G.fields={F76D, F77D, F78D, F79D}
F82D.enum_type=mq_common_pb.E7M
M47G.fields={F80D, F81D, F82D, F83D}
F84D.enum_type=error_code_pb.E1M
M49G.fields={F84D}
F88D.enum_type=mq_common_pb.E6M
M50G.fields={F85D, F86D, F87D, F88D, F89D}
F91D.enum_type=mq_common_pb.E6M
M52G.fields={F90D, F91D, F92D, F93D}
F95D.enum_type=mq_common_pb.E8M
F97D.message_type=mq_common_pb.M7G
M53G.fields={F94D, F95D, F96D, F97D, F98D}
F99D.enum_type=error_code_pb.E1M
M54G.fields={F99D}
F100D.message_type=mq_common_pb.M25G
M55G.fields={F100D}
F101D.message_type=mq_common_pb.M25G
M56G.fields={F101D}
F102D.message_type=mq_common_pb.M25G
M57G.fields={F102D}
F103D.enum_type=mq_common_pb.E8M
F109D.message_type=mq_common_pb.M3G
F110D.message_type=mq_common_pb.M4G
F112D.message_type=mq_common_pb.M7G
F114D.message_type=mq_common_pb.M11G
F115D.message_type=mq_common_pb.M12G
F117D.message_type=mq_common_pb.M27G
F118D.message_type=mq_common_pb.M28G
F119D.message_type=mq_common_pb.M23G
F121D.message_type=mq_common_pb.M24G
F122D.message_type=mq_common_pb.M15G
F123D.message_type=mq_common_pb.M16G
F124D.message_type=mq_common_pb.M18G
F125D.message_type=mq_common_pb.M19G
F126D.message_type=mq_common_pb.M20G
F127D.message_type=mq_common_pb.M17G
F128D.message_type=mq_common_pb.M29G
F129D.message_type=mq_common_pb.M21G
M58G.fields={F103D, F104D, F105D, F106D, F107D, F108D, F109D, F110D, F111D, F112D, F113D, F114D, F115D, F116D, F117D, F118D, F119D, F120D, F121D, F122D, F123D, F124D, F125D, F126D, F127D, F128D, F129D, F130D}
F131D.message_type=M58G
M59G.fields={F131D}
F132D.enum_type=error_code_pb.E1M
F133D.enum_type=mq_common_pb.E8M
M60G.fields={F132D, F133D}
F134D.message_type=mq_common_pb.M31G
M61G.fields={F134D}
F135D.message_type=M58G
M62G.fields={F135D}
F136D.enum_type=error_code_pb.E1M
F137D.enum_type=mq_common_pb.E8M
M63G.fields={F136D, F137D}
F138D.message_type=mq_common_pb.M31G
M64G.fields={F138D}
M65G.fields={F139D, F140D}
F141D.enum_type=error_code_pb.E1M
M66G.fields={F141D}
F142D.enum_type=M68G
M67G.fields={F142D, F143D, F144D}
F145D.enum_type=error_code_pb.E1M
F146D.enum_type=M68G
M69G.fields={F145D, F146D, F147D, F148D, F149D}
M70G.fields={F150D, F151D, F152D, F153D}
M71G.fields={F154D, F155D}
F156D.message_type=M71G
M72G.fields={F156D}
F157D.message_type=M71G
M73G.fields={F157D}
M74G.fields={F158D}
M75G.fields={F159D, F160D}
F161D.message_type=M75G
M76G.fields={F161D}
F163D.message_type=common_new_pb.M1G
F164D.message_type=common_new_pb.M1G
F166D.message_type=challenge_pb.M14G
M77G.fields={F162D, F163D, F164D, F165D, F166D}
F169D.enum_type=common_new_pb.E10M
M79G.fields={F167D, F168D, F169D, F170D, F171D, F172D}
F173D.enum_type=M82G
M81G.fields={F173D, F174D}
F175D.enum_type=M82G
F176D.message_type=mq_common_pb.M25G
M83G.fields={F175D, F176D, F177D}
F178D.enum_type=mq_common_pb.E8M
M84G.fields={F178D, F179D, F180D, F181D, F182D, F183D, F184D, F185D, F186D}
M85G.fields={F187D, F188D, F189D}
F190D.enum_type=error_code_pb.E1M
M86G.fields={F190D}
M87G.fields={F191D, F192D}
F193D.enum_type=error_code_pb.E1M
M88G.fields={F193D, F194D, F195D, F196D}

Channel_AllianceR4R5 = 128
Channel_Guild = 2
Channel_Language = 64
Channel_Private = 256
Channel_Recruit = 8
Channel_Territory = 4
Channel_WMTOPRACE = 32
Channel_World = 1
ChatShareBattleReport =M(M84G)
TBlockRoleList =M(M33G)
TChatPrivateMsg =M(M28G)
TMSG_CHAT_BAN_NTF =M(M50G)
TMSG_CHAT_BAN_REMOVE_NTF =M(M52G)
TMSG_CHAT_BLOCK_CHANNEL_REQ =M(M42G)
TMSG_CHAT_BLOCK_CHANNEL_RSP =M(M43G)
TMSG_CHAT_BLOCK_LIST_NTF =M(M39G)
TMSG_CHAT_BLOCK_LIST_REQ =M(M36G)
TMSG_CHAT_BLOCK_LIST_RSP =M(M37G)
TMSG_CHAT_BLOCK_REQ =M(M34G)
TMSG_CHAT_BLOCK_RSP =M(M35G)
TMSG_CHAT_GET_SIGN_REQ =M(M67G)
TMSG_CHAT_GET_SIGN_RSP =M(M69G)
TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF =M(M74G)
TMSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF =M(M61G)
TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ =M(M59G)
TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP =M(M60G)
TMSG_CHAT_LANGUAGE_MSG_REQ =M(M31G)
TMSG_CHAT_LANGUAGE_MSG_RSP =M(M32G)
TMSG_CHAT_LEAVE_LEAGUE_NTF =M(M44G)
TMSG_CHAT_MSG_GETBEFORE_REQ =M(M81G)
TMSG_CHAT_MSG_GETBEFORE_RSP =M(M83G)
TMSG_CHAT_MSG_GET_BATTLE_REPORT_REQ =M(M87G)
TMSG_CHAT_MSG_GET_BATTLE_REPORT_RSP =M(M88G)
TMSG_CHAT_MSG_NTF =M(M30G)
TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_REQ =M(M85G)
TMSG_CHAT_MSG_SAVE_BATTLE_REPORT_RSP =M(M86G)
TMSG_CHAT_NEW_MSG_NTF =M(M25G)
TMSG_CHAT_RECALL_MSG_REQ =M(M45G)
TMSG_CHAT_RECALL_MSG_RSP =M(M46G)
TMSG_CHAT_REPORT_BACKSTAGE_REQ =M(M79G)
TMSG_CHAT_REPORT_CHANNEL_REQ =M(M65G)
TMSG_CHAT_REPORT_CHANNEL_RSP =M(M66G)
TMSG_CHAT_REPORT_REQ =M(M47G)
TMSG_CHAT_REPORT_RSP =M(M49G)
TMSG_CHAT_SEND_MAIL_REQ =M(M40G)
TMSG_CHAT_SEND_MAIL_RSP =M(M41G)
TMSG_CHAT_SPEAK_REQ =M(M1G)
TMSG_CHAT_SPEAK_RSP =M(M23G)
TMSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF =M(M64G)
TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ =M(M62G)
TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP =M(M63G)
TMSG_MATE_MSG_SET_NTF =M(M57G)
TMSG_PRIVATE_CHAT_LIKE_ALL_NTF =M(M73G)
TMSG_PRIVATE_CHAT_LIKE_NTF =M(M72G)
TMSG_PRIVATE_MSG_NTF =M(M55G)
TMSG_PRIVATE_MSG_SET_NTF =M(M56G)
TMSG_PRIVATE_MSG_SPEAK_REQ =M(M53G)
TMSG_PRIVATE_MSG_SPEAK_RSP =M(M54G)
enSign_GetLangInfo = 2
enSign_Translate = 1
tBlockCtrl =M(M27G)
tChatCrossLikeMsg =M(M70G)
tChatLikeMsg =M(M71G)
tCrossSvrChatReq =M(M58G)
tGmConversationUser =M(M75G)
tGmConversationUserInfo =M(M76G)
tShareMultiBattleMsg =M(M77G)

