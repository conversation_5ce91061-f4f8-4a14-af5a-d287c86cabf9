--main_slg_tips_mgr


local require = require
local typeof = typeof
local pairs = pairs
local main_slg_const = require "main_slg_const"
local lang = require "lang"
local card_sprite_asset = require "card_sprite_asset"
local event = require "event"
local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local festival_activity_cfg = require "festival_activity_cfg"
local UIUtil = CS.Common_Util.UIUtil
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Common_Util = CS.Common_Util.UIUtil
local CanvasType = typeof(CS.UnityEngine.Canvas)
local SortingGroupType = typeof(CS.UnityEngine.Rendering.SortingGroup)

local util = require "util"
local ui_window_mgr = require "ui_window_mgr"
local log = require "log"
local RectTransform = CS.UnityEngine.RectTransform
local GameObject = CS.UnityEngine.GameObject
---@class main_slg_tips_mgr
local M = {}
---@field tips实体 table 
M.TipGameObjectDic = {}

function M.Init()

end

---@public 获取主界面对象
function M.GetMainWindow()
    if not M.MainSlgWindow then
        M.MainSlgWindow = ui_window_mgr:GetWindowObj("ui_main_slg")
    end
    return M.MainSlgWindow
end

---@public function 创建tip模板
function M.CreateTipTemplate(tipName, position, renderFun, offset, disTimeFun,tran,tipsData,enum_TipType,hideCallback)
    
    local wnd = M.GetMainWindow()
    if not wnd and util.IsObjNull(wnd) then
        return
    end
    
    if util.IsObjNull(wnd.rtf_Template) then
        return
    end

    if util.IsObjNull(tran) then
        return
    end

    M.TipGameObjectDic[enum_TipType] =  M.TipGameObjectDic[enum_TipType] or {}
    
    local template = UIUtil.GetComponent(wnd.rtf_Template, "RectTransform", tipName)
    --避免重复生成
    local itemGo = not util.IsObjNull(M.TipGameObjectDic[enum_TipType].itemGo) and M.TipGameObjectDic[enum_TipType].itemGo or UIUtil.AddChild(tran.gameObject, template.gameObject)
    
    --设置位置
    itemGo.transform.position = position
    
    --设置偏移
    if offset then
        local itemRect = itemGo:GetComponent(typeof(RectTransform))
        itemRect.anchoredPosition = {
            x = itemRect.anchoredPosition.x + offset.x,
            y = itemRect.anchoredPosition.y + offset.y
        }
    end
    
    local scrollItem = itemGo:GetComponent(typeof(ScrollRectItem))
    if renderFun then
        renderFun(scrollItem,tipsData)
    end
    
    local mainSlg = ui_window_mgr:GetWindowObj("ui_main_slg")
    if mainSlg then
        local curOrder = mainSlg.curOrder > 10000 and mainSlg.curOrder or 15000

        local canvas = itemGo:GetComponent(CanvasType)
        if canvas and not canvas:IsNull() then
            canvas.sortingOrder = curOrder + 50
        end

        local sortingGroup = itemGo:GetComponent(SortingGroupType)
        if sortingGroup and not sortingGroup:IsNull() then
            sortingGroup.sortingOrder = curOrder + 51
        end
    end
    
    --region 存储数据
    
    M.TipGameObjectDic[enum_TipType].itemGo = itemGo 
    M.TipGameObjectDic[enum_TipType].scrollItem = scrollItem
    M.TipGameObjectDic[enum_TipType].renderFun = renderFun
    
    if disTimeFun then
        local disTime = disTimeFun() or 0
        local callback = hideCallback
        util.DelayCallOnce(disTime, function()
            --region 释放注册的事件
            if not util.IsObjNull(scrollItem) then
                if scrollItem and scrollItem.data then
                    if scrollItem.data.tipButton then
                        scrollItem.data.tipButton.onClick:RemoveAllListeners()
                    end
                end
            end
            --endregion 
            if itemGo and not util.IsObjNull(itemGo) then
                --直接销毁物体
                GameObject.DestroyImmediate(itemGo);
            end
            --挂起之后执行回调
            if callback then
                callback()
            end
        end)
    end
end

---@public 显示tip 在需要弹出的时机调用
function M.ShowTip(enum_TipType)
    --log.Error("ShowTip enum_TipType = ", enum_TipType)
    if M.mainBtnTipConfig[enum_TipType] then
        local cfg = M.mainBtnTipConfig[enum_TipType]
        if cfg then
            local main_slg_mgr = require "main_slg_mgr"

            if cfg.buttonType then
                local btnTransform = main_slg_mgr.GetMainButtonTransformByType(cfg.buttonGroupType,cfg.buttonType,true)
                if not util.IsObjNull(btnTransform) then
                    --生成按钮到正确的位置
                    M.CreateTipTemplate(cfg.tipName,btnTransform.position,cfg.renderFun,cfg.offset,cfg.disTimeFun,btnTransform,nil,enum_TipType)
                end
            else
                local btnTransform = main_slg_mgr.GetMainActivityEntranceButtonTransform(cfg.specialType,nil,true)
                if not util.IsObjNull(btnTransform) then
                    --生成按钮到正确的位置
                    M.CreateTipTemplate(cfg.tipName,btnTransform.position,cfg.renderFun,cfg.offset,cfg.disTimeFun,btnTransform,nil,enum_TipType)
                end
            end
        end
    end
end

function M.ShowActivityTip(enum_TipType,tipsData,tf,hideCallback)
    if M.mainBtnTipConfig[enum_TipType] then
        local cfg = M.mainBtnTipConfig[enum_TipType]
        local isOpen = festival_activity_mgr.GetIsOpenByActivityID(tipsData.activityId)

        if isOpen and cfg then
            local main_slg_mgr = require "main_slg_mgr"
            local btnTransform = tf or main_slg_mgr.GetMainActivityEntranceButtonTransformByActivityId(tipsData.activityId)
            if btnTransform then
                --生成按钮到正确的位置
                M.CreateTipTemplate(cfg.tipName,btnTransform.position,cfg.renderFun,cfg.offset,cfg.disTimeFun,btnTransform,tipsData,enum_TipType,hideCallback)
            end
        end
    end
end

---@public function 隐藏tip
function M.HideTip(enum_TipType)
    --log.Error("HideTip enum_TipType = ", enum_TipType)
    if not M.TipGameObjectDic then
        return
    end
    local itemGo = M.TipGameObjectDic[enum_TipType] and M.TipGameObjectDic[enum_TipType].itemGo
    local scrollItem = M.TipGameObjectDic[enum_TipType] and M.TipGameObjectDic[enum_TipType].scrollItem
    
    --region 释放注册的事件
    if not util.IsObjNull(scrollItem) then
        if scrollItem.data then
            if scrollItem.data.tipButton then
                scrollItem.data.tipButton.onClick:RemoveAllListeners()
            end
        end
    end
    --endregion 
    if itemGo and not util.IsObjNull(itemGo) then
        --直接销毁物体
        GameObject.DestroyImmediate(itemGo);
    end
end

function M.UpdateUILayer()
    if M.TipGameObjectDic then
        local mainSlg = ui_window_mgr:GetWindowObj("ui_main_slg")
        if mainSlg then
            local curOrder = mainSlg.curOrder > 10000 and mainSlg.curOrder or 15000
            for i,v in pairs(M.TipGameObjectDic) do
                if not util.IsObjNull(v.itemGo) then
                    local itemGo = v.itemGo
                    local canvas = itemGo:GetComponent(CanvasType)
                    if canvas and not canvas:IsNull() then
                        canvas.sortingOrder = curOrder + 50
                    end
                    local sortingGroup = itemGo:GetComponent(SortingGroupType)
                    if sortingGroup and not sortingGroup:IsNull() then
                        sortingGroup.sortingOrder = curOrder + 51
                    end
                end
            end
        end

    end
end

--region 每个按钮管理自己的方法

---@public 同盟对决的显示方法
function M.DuelThemeTipsFun(scrollRectItem)
    local duelTip = scrollRectItem:Get("duelTip")
    local icon = scrollRectItem:Get("icon")
    local name = scrollRectItem:Get("name")
    local duelBtn = scrollRectItem:Get("duelBtn")
    
    local alliance_duel_data = require "alliance_duel_data"
    local themeID = alliance_duel_data.GetThemeDuelID()
    local themeCfg = game_scheme:AllianceDuelTheme_0(themeID)


    scrollRectItem.data = scrollRectItem.data or {}

    duelBtn.onClick:RemoveAllListeners()
    duelBtn.onClick:AddListener(function()
        ui_window_mgr:ShowModule("ui_alliance_duel_main")
    end)
    
    scrollRectItem.data.tipButton = duelBtn
    
    if themeCfg then
        M.duel_asset = M.duel_asset or card_sprite_asset.CreateGWAllianceDuelIcon()

        if M.duel_asset then
            --读表ID
            --log.Error("表ID：",themeCfg.ShowBubbleIcon)
            local iconID = themeCfg.ShowBubbleIcon.data[0]
            M.duel_asset:GetSprite(iconID, function(sprite)
                if sprite then
                    if not util.IsObjNull(icon) then
                        icon.sprite = sprite
                    end
                end
            end)
        end
        
        name.text = lang.Get(themeCfg.ThemeName)
    end
    
end

---@public 联盟Boss入口
function M.AllianceBossTipsFun(scrollRectItem)
    local txtTips = scrollRectItem:Get("txtTips")
    local clickBtn = scrollRectItem:Get("clickBtn")
    clickBtn.onClick:RemoveAllListeners()
    clickBtn.onClick:AddListener(function()
        --直接去打开对应的入口
    end)
    scrollRectItem.data = scrollRectItem.data or {}
    scrollRectItem.data.tipButton = clickBtn
    txtTips.text = lang.Get(653612)
end

---@public 通用活动入口
function M.ActivityTipFun(scrollRectItem,tipsData)
    local txtTips = scrollRectItem:Get("txtTips")
    local clickBtn = scrollRectItem:Get("clickBtn")
    clickBtn.onClick:RemoveAllListeners()
    if tipsData then
        clickBtn.onClick:AddListener(function()
            --直接去打开对应的入口
            if tipsData.clickFun then
                tipsData.clickFun()
            end
        end)
        Common_Util.SetActive(clickBtn,tipsData.clickFun ~= nil)
        txtTips.text = tipsData.desc
    end

    scrollRectItem.data = scrollRectItem.data or {}
    scrollRectItem.data.tipButton = clickBtn

end

---@public 待办日程入口
function M.ScheduleTipFun(scrollRectItem,tipsData)
    local txtTips = scrollRectItem:Get("txtTips")
    local todo_schedule_data = require "todo_schedule_data"
    local curBubbleData = todo_schedule_data.GetMainRedBubbleData()
    if not curBubbleData then
        M.HideTip(M.EnumTipType.ScheduleTip)
        return
    end
    txtTips.text = string.format2(lang.Get(1004225),lang.Get(curBubbleData.activityName))

    local clickBtn = scrollRectItem:Get("clickBtn")
    clickBtn.onClick:RemoveAllListeners()
    clickBtn.onClick:AddListener(function()
        --直接去打开对应的入口
        ui_window_mgr:ShowModule("ui_todo_schedule")
        todo_schedule_data.ClearMainRedPoint()
    end)
    
    --txtTips.text = string.format2(lang.Get(653612),lang.Get(655050))
end

---@public 待办日程红点
function M.ScheduleRedTipFun(scrollRectItem,tipsData)
    local todo_schedule_mgr = require "todo_schedule_mgr"
    local hasRed = todo_schedule_mgr.GetTaskRedPoint()
    if not hasRed then
        return
    end
end

---@public function 战区对决Tip
function M.WarZoneTipFun(scrollRectItem,tipsData)
    
end

---@public function 领地复兴Tip
function M.LandRevivalTipFun(scrollRectItem,tipsData)
    local clickBtn = scrollRectItem:Get("clickBtn")
    clickBtn.onClick:RemoveAllListeners()
    clickBtn.onClick:AddListener(function()
        local land_revival_data = require "land_revival_data"
        --直接去打开对应的入口    
        festival_activity_mgr.OpenActivityUIByActivityID(land_revival_data.GetActivityID())
    end)
end

--endregion

--region 配置区

M.EnumTipType = {
    DuelTip = 100,--同盟对决tip
    DuelTipBtn = 200,
    AllianceBossTip = 300,
    ActivityTip = 400, --通用活动tips
    ScheduleTip = 500, --待办日程
    ScheduleRedTip = 550, --待办日程
    WarZoneTip = 600, --战区对决Tip
    LandRevivalTip = 700, --领地复兴Tip
}

---@field 主界面按钮的tip配置
M.mainBtnTipConfig = {
    
    [M.EnumTipType.DuelTip] = {
        --按钮模版名字
        tipName = "duelTip",
        --按钮类型组
        --buttonGroupType = main_slg_const.MainButtonGroupType.LeftBottom,
        --按钮类型
        --buttonType = main_slg_const.MainButtonType.info,
        --特殊按钮类型，比如右上角活动按钮
        specialType = 1005,
        --偏移
        offset = { x = -120,y = 0 },
        --按钮显示执行方法
        renderFun = M.DuelThemeTipsFun,
        --消失时间 返回具体的时间
        disTimeFun = function()
            return 3
        end ,
    },
    [M.EnumTipType.AllianceBossTip] = {
        --按钮模版名字
        tipName = "allianceBossTip",
        --按钮类型
        --buttonType
        --特殊按钮类型，比如右上角活动按钮
        specialType = festival_activity_cfg.ActivityEntranceType.Special,
        --偏移
        offset = { x = -120,y = 0 },
        --按钮显示执行方法
        renderFun = M.AllianceBossTipsFun,
        --消失时间
        disTimeFun = function()
            return 30
        end ,
    },
    [M.EnumTipType.ActivityTip] = {
        --按钮模版名字
        tipName = "ActivityTip",
        --按钮类型
        --buttonType
        --特殊按钮类型，比如右上角活动按钮
        specialType = festival_activity_cfg.ActivityEntranceType.Special,
        --偏移
        offset = { x = -120,y = 0 },
        --按钮显示执行方法
        renderFun = M.ActivityTipFun,
        --消失时间
        disTimeFun = function()
            return 3
        end ,
    },
    [M.EnumTipType.ScheduleTip] = {
        --按钮模版名字
        tipName = "ScheduleTip",
        --按钮类型
        buttonGroupType = main_slg_const.MainButtonGroupType.LeftBottom,
        buttonType = main_slg_const.MainButtonType.schedule,
        --偏移
        offset = { x = 24,y = 0 },
        --按钮显示执行方法
        renderFun = M.ScheduleTipFun,
    },
    [M.EnumTipType.ScheduleRedTip] = {
        --按钮模版名字
        tipName = "ScheduleRedTip",
        --按钮类型
        buttonGroupType = main_slg_const.MainButtonGroupType.LeftBottom,
        buttonType = main_slg_const.MainButtonType.schedule,
        --偏移
        offset = { x = 24,y = 20 },
        --按钮显示执行方法
        renderFun = M.ScheduleRedTipFun,
    },
    [M.EnumTipType.WarZoneTip] = {
        --按钮模版名字
        tipName = "WarZoneTip",
        --按钮类型
        --buttonType
        --特殊按钮类型，比如右上角活动按钮
        specialType = festival_activity_cfg.ActivityEntranceType.WarZonePK,
        --偏移
        offset = { x = -130,y = 0 },
        --按钮显示执行方法
        renderFun = M.WarZoneTipFun,
    },
    [M.EnumTipType.LandRevivalTip] = {
        --按钮模版名字
        tipName = "LandRevivalTip",
        --按钮类型
        --buttonType
        --特殊按钮类型，比如右上角活动按钮
        specialType = festival_activity_cfg.ActivityEntranceType.LandRevival,
        --偏移
        offset = { x = -136,y = 0 },
        --按钮显示执行方法
        renderFun = M.LandRevivalTipFun,
        --消失时间
        disTimeFun = function()
            return 3
        end ,
    }
}
--endregion

---@public 默认登录时的显示tips配置
M.DefaultLoginShow = {
    [M.EnumTipType.DuelTip] = false, --先不弹了
}
---@public
M.ActivityLoginShow = {}
M.ActivityLoginReadyShow = {} --即将要显示的气泡
M.ActivityLoginHasShow = {} --已经显示过的气泡
M.CutIndex = 1

---@public 默认登录时的提示
function M.DefaultLoginTips()
    for key, v in pairs(M.DefaultLoginShow) do
        if v then
            M.ShowTip(key)
        end
    end
    --local player_mgr = require "player_mgr"
    --local showBubble = player_mgr.GetActivityBubble()
    --if showBubble then
    --    player_mgr.SetActivityBubble(false)
    --    M.ActivityLoginTips()
    --end
end

function M.CheckActivityBubble()
    local len = #M.ActivityLoginShow
    if len > 0 then
        for i,v in ipairs(M.ActivityLoginShow) do
            if not M.ActivityLoginHasShow[v.value.bubble] then
                table.insert(M.ActivityLoginReadyShow,v)
            end
        end
        M.ActivityLoginShow = {}
        local gw_popups_data = require "gw_popups_data"
        gw_popups_data.AddMessage("ui_main_slg_Bubble", function()
            gw_popups_data.ShowNextMessage(0)
            M.ActivityLoginTips()
        end, gw_popups_data.PopupsType.Normal)
    end
end

function M.ActivityLoginTips(trans,callback)
    local len = #M.ActivityLoginReadyShow
    local tf = trans
    local GetShowBubble = function(startIndex)
        if len < startIndex then
            return nil,0
        end
        for i = startIndex,len do
            if M.ActivityLoginReadyShow[i] and M.ActivityLoginReadyShow[i].checkFunc()~=nil then
                return M.ActivityLoginReadyShow[i],i
            end
        end
        return nil,0
    end
    local ShowBubble
    ShowBubble = function(temp)
        local showData =
        {
            desc = temp.checkFunc(),
            activityId = temp.value.activityID,
            clickFun = function()
                festival_activity_mgr.OpenActivityUIByActivityID(temp.value.activityID)
            end
        }
        M.ActivityLoginHasShow[temp.value.bubble] = true
        M.ShowActivityTip(M.EnumTipType.ActivityTip,showData,tf,function()
            local next,_index2 = GetShowBubble(M.CutIndex + 1)
            if next then
                M.CutIndex = _index2
                ShowBubble(next)
            else
                --已经没有气泡可以弄了
                if callback then
                    callback()
                end
                M.ActivityLoginReadyShow = {} --清空数组
            end
        end)
    end
    local temp,_index = GetShowBubble(M.CutIndex)
    if temp then
        M.CutIndex = _index
        ShowBubble(temp)
    end
end

function M.AddActivityLoginTips(value)
    if not M.ActivityLoginShow then
        M.ActivityLoginShow = {}
    end
    if M.ActivityLoginHasShow[value.bubble] then
        return
    end
    for i,v in ipairs(M.ActivityLoginShow) do
        if v.value.activityID == value.value.activityID then
            return
        end
    end
    for i,v in ipairs(M.ActivityLoginReadyShow) do
        if v.value.activityID == value.value.activityID then
            return
        end
    end
    table.insert(M.ActivityLoginShow,value)
end

---@public function 切换语言时，刷新显示
function M.RefreshChangeLang()
    for tipType, tipData in pairs(M.TipGameObjectDic) do
        if tipData.renderFun and tipData.scrollItem then
            if not util.IsObjNull(tipData.scrollItem) then
                tipData.renderFun(tipData.scrollItem)
            end
        end
    end
end

function M.Clear()
    M.MainSlgWindow = nil
    M.TipGameObjectDic = {}
    M.ActivityLoginShow = {}
    M.ActivityLoginHasShow = {}
    M.ActivityLoginReadyShow = {}

    --释放图集
    if M.duel_asset then
        M.duel_asset:Dispose()
        M.duel_asset = nil
    end
end

event.Register(event.LANGUAGE_SETTING_CHANGED, M.RefreshChangeLang)
event.Register(event.LOGIN_UI_POPUP_END, M.DefaultLoginTips)
event.Register(event.USER_DATA_RESET, M.Clear)
event.Register(event.MAIN_SLG_UPDATE_UI,M.UpdateUILayer)

return M