---注册控制台工具
---key : string 控制台工具名称,对应val.get的key, value : {控制台工具的tab最大值,默认值}
local CS = CS
local Application = CS.UnityEngine.Application
local require = require
local print = print
local toggle_print = toggle_print
local try = try
module("console_util_config")

-- table第一个是tab的个数（不包含，从0开始），第二个是默认值
local cfg = {
    sw_hook_destroy = {2, 0},
    sw_novice_chapter = {2, 0}, --新手章节开关
    sw_gameobject_pool = {2, 1},
    -- lua对象池的开关，0 使用老cs对象池，目前不会主动释放，1 使用lua对象池，3s检查释放空引用
    sw_hook_frame = { 2, 0 },
    sw_print_on = {2, 0},
    sw_empty_lang_convert_key = {2, 1},
    sw_print_val = {2, 0},
    sw_delay_asset_loader = {2, 0},
    sw_entity_opt = {2, 0},
    sw_ecs_opt1 = {2, 1},
    sw_ingame_modify_lua = { 2, 0 }, -- 游戏内修改lua文件实时生效
    sw_lpat_lua = { 2, 1 }, -- lua热修复开关
    sw_role_add = {10, 0}, --角色创建,测试账号
    sw_role_add_10 = { 10, 0 },      --角色创建,测试账号
    sw_role_clear = { 2, 0 },        --测试角色恢复默认
    sw_hook_ab_load = { 2, 0 },        --ab包加载hook
    sw_function_open = { 2, 0 },     --功能开放
    sw_equip_level_click = { 2, 0 },     --装备快速点击升级
    sw_preload_list = { 2, 0 },       --
    sw_hook_require = { 2, 0 },       --
    sw_close_preload = { 2, 0 },
    sw_hero_model_scale = { 2, 0 },--模型展示高精度
    sw_gw_globle_new = { 2, 0 },--
    sw_ui_dependencies_log = { 2, 1 },--ui依赖关系日志
    sw_require_report = { 2, 0 },--require日志
    sw_redtree_log = { 2, 0 },--红点相关日志
    sw_preload_common_export = {2, 0},
    sw_res_pool_tick_prepare = {2, 0},
    sw_home_bubble_new = {2, 1}, --城建气泡优化开关, 0:屏幕空间气泡, 1:世界空间气泡
	sw_threads_test = {2,0},
    sw_editor_res_size = {2, 0},
    sw_depend_res_size = {2, 0},
    sw_res_size_zero   = {2, 0},
    sw_req_serverData_by_Gate = {2, 0},
    sw_login_require_optimize = {2, 0}, ----开启登录Require优化
    sw_require_auto_init_optimize = {2, 0}, ----开启Require自动Init优化
    sw_require_simple_optimize = {2, 0}, ----开启Require脚本时的内部简化优化 脚本内部的require其他模块的并不会立即执行，而是等待该模块中对应的变量需要执行时才真正require
    sw_frame_task_queue_optimize = {2, 1}, ----是否开启帧任务队列优化
    sw_servermarkset = {20, 0}, --
    sw_servermarkreset = {2, 0}, --
    sw_split_init_module = {2, 0}, --
    sw_minigame_create_entity = {2, 0}, --
    sw_lua_binary = {2, 0}, --
    sw_hero_attr_log = { 2, 0 },     --英雄属性
    sw_bubble_count_down = {2, 1}, --气泡倒计时开关, 0: image实现, 1: sprite实现
    sw_red_system_perf = {2, 0},   --红点系统优化
    sw_close_fboard_data_loader = {2, 0},
    sw_hide_reward_effect = {2, 0},
    sw_select_game_type = {4, 1}, --选择游戏类型
    sw_storm_draw_disable_move = {2, 0}, --不可迁城区域画图
    sw_storm_open = {2, 0}, --沙漠风暴本地开关（一些版本不同步的兼容）
    sw_print_net_worldId= {2, 0}, --打印网络消息世界id
    sw_protobuf_new = {2, 1},      --新protobuf库
    sw_skip_client_check = {2, 0},  --跳过客户端校验
    sw_new_mini_game = {2, 0},      --新小游戏
    sw_new_ab_test_id = {12, 0},      --新ab测试id
    sw_res_preload_off = {2, 0},      --关闭资源预加载
    
 	sw_instantiate_async = {2, 1},    --异步实例化
    sw_skip_declare_check = {2, 0},  -- 跳过宣战检测
    
    sw_home_ecs_loader = {2, 1}, --城建ecs加载

    sw_sand_debug_log = {3, 0},  -- 沙盘异常调试日志
    
    sw_test_notify_url = {2, 0}, --测试通知url

    sw_show_long_worldID = {2,0} --展示名字 + worldID
}

local cache = { cfg = cfg, console_func = {} }
function Init()

    local val = require "val"
    val.set("is_editor", Application.isEditor and 1 or 0)

    local files_version_mgr = require"files_version_mgr"
    --jenkins控制sw_print_on默认值
    if not files_version_mgr.IsPrintOn() then
        if cache.cfg.sw_print_on then
            cache.cfg.sw_print_on[2] = 0
        else
            cache.cfg.sw_print_on = {2, 0}
        end
   end
    
    cache.ON_REGISTER_CONSOLE_SET = function(evname, name, st)
        -- print("ON_REGISTER_CONSOLE_SET", evname, name, st)
        if cache.console_func[name] then
            cache.console_func[name](st)
        end
    end

    local event = require "event"
    event.Register("ON_REGISTER_CONSOLE_SET", cache.ON_REGISTER_CONSOLE_SET)
    -- local util = require "util"
    -- util.DelayCallOnce(3,CommonAction)
    try{
        CommonAction,
        catch = function(err)
            print("console_util_config error",err)
        end
    }
    -- CommonAction()
end

function CommonAction()
     


    cache.console_func["sw_print_val"] = function(st)
        if st == 1 then
            local val = require "val"
            local json = require "dkjson"
            print("val", json.encode(val.get_cache()))
        end
    end

    cache.console_func["sw_ingame_modify_lua"] = function(st)
        -- print("sw_ingame_modify_lua", st)
        if st == 1 then
            if Application.isEditor then
                local lpat = require "lpat"
                lpat:Init()
            end
        end
    end
    cache.console_func["sw_role_clear"] = function(st)
        if st == 1 then
            local c = CS.UnityEngine.PlayerPrefs.GetString("login_testUserName", "")
            print("login_testUserName", c)
            local console_util = require "console_util"
            console_util.ResetKey("sw_role_clear")
            console_util.ResetKey("sw_role_add")
            console_util.ResetKey("sw_role_add_10")
        end
    end

    cache.console_func["sw_gameobject_pool"] = function(st)
        if st == 1 then
            local util = require "util"
            cache.temp = cache.temp or {}
            util.RemoveDelayCall(cache.temp.sw_gameobject_pool_ticker)
            cache.temp.sw_gameobject_pool_ticker = util.DelayCallOnce(
                4,
                function()
                    local GameObjectPool = CS.War.Base.GameObjectPool
                    GameObjectPool.Clear()
                    return 3
                end
            )
        end
    end
    cache.console_func["sw_preload_list"] = function(st)
        if st == 1 then
            local console_util = require "console_util"
            console_util.ResetKey("sw_preload_list")

            local res_preload_c = require "res_preload_c"
            res_preload_c.start_preload()
        end
    end
    toggle_print()
    cache.console_func["sw_print_on"] = function(st)
        toggle_print()
    end

    cache.console_func["sw_require_report"] = function(st)
        if st == 1 then
            local hook_require = require "hook_require"
            hook_require.set_require_report()
        -- elseif st == 2 then
        --     local console_util = require "console_util"
        --     console_util.ResetKey("sw_hook_require")
        --     local hook_require = require "hook_require"
        --     hook_require.print_hook()
        end
    end


    cache.console_func["sw_hook_require"] = function(st)
        if st == 1 then
            local hook_require = require "hook_require"
            hook_require.hook_require()
        -- elseif st == 2 then
        --     local console_util = require "console_util"
        --     console_util.ResetKey("sw_hook_require")
        --     local hook_require = require "hook_require"
        --     hook_require.print_hook()
        end
    end
    cache.console_func["sw_require_auto_init_optimize"] = function(st)
        local util = require "util"
        util.DelayCallOnce(0, function()
            if st == 1 then
                local require_optimize_mgr = require "require_optimize_mgr"
                require_optimize_mgr.ExeRequireAutoInitOptimize()
            end
        end )
        
    end
    cache.console_func["sw_require_simple_optimize"] = function(st)
        local util = require "util"
        --延后一帧；因为注册事件并不是顺序
        util.DelayCallOnce(0, function()
            if st == 1 then
                local require_optimize_mgr = require "require_optimize_mgr"
                require_optimize_mgr.ExeRequireSimpleOptimize()
            end
        end )
       
    end

    cache.console_func["sw_servermarkset"] = function(st)
        if st > 0 then
            local cb = st
            local m = "ServerMark" .. cb
            local log = require "log"
            log.Warning("ServerMarkSet", st, m)
            local player_prefs = require "player_prefs"
            player_prefs.SetString("ServerMarkSet", m)
        end
    end
    cache.console_func["sw_servermarkreset"] = function(st)
        if st > 0 then
            local player_prefs = require "player_prefs"
            player_prefs.SetString("ServerMarkSet", "")
            local console_util = require "console_util"
            console_util.ResetKey("sw_servermarkset")
            console_util.ResetKey("sw_servermarkreset")
        end
    end

    local hook_debug = require "hook_debug"
    hook_debug.init(cache)

end
-- 保留tab缩进，重载脚本会执行一遍
    Init()
function GetConsoleCfg()
    return cfg
end
function InitRequire()
    if cache.inited then
        return
    end
    cache.inited = true
    CommonAction()
end
-- return cfg