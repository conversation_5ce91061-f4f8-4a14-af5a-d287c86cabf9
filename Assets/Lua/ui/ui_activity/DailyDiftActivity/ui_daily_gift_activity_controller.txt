
--- ui_daily_gift_activity_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local newclass = newclass

local event = require "event"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local flow_text = require "flow_text"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local ui_daily_gift_activity_mgr = require "ui_daily_gift_activity_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
local net_rechargeGift_module = require "net_rechargeGift_module"
local gw_recharge_mgr = require "gw_recharge_mgr"
local event_rechargeGift_define = require "event_rechargeGift_define"
local event_define = require "event_define"
module("ui_daily_gift_activity_controller")
local controller = nil
local UIController = newclass("ui_daily_gift_activity_controller", controller_base)
local lang = require "lang"
local freeRechagerID = 900166
local red_system = require "red_system"
local red_const = require "red_const"

local activityData = nil


--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    self.InitRedDot()
    self:RefreshUI()
end

function UIController:RefreshUI()
    ui_daily_gift_activity_mgr.InitActivityData()
    self.activityCfg = game_scheme:festivalActivity_0(ui_daily_gift_activity_mgr.GetActivityID())
    self:RefreshFreeReward()
    self:SetTimeText()
    self:SetFreeRechagerState()
    self:RefreshRechagerItem()
    self:SetHeroNameText()
    self:RefreshHeroBg()
    self:RefreshChangeHeroBtn()
    self:CheckChangeHeroBtnState()
    self:CheckRedDot()
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:OnBtn_freeRewardBtnClickedProxy()
    net_rechargeGift_module.MSG_RECHARGE_GIFT_GET_FREE_REQ({ rechargeId = freeRechagerID })
    self:TriggerUIEvent("PlayEffect")
end

function  UIController:OnBtn_changeHeroBtnClickedProxy()
    windowMgr:ShowModule("ui_daily_gift_activity_hero_change")
end

function  UIController:OnBtn_TipBtnClickedProxy()
    -- 打开帮助文本
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(10048)
    --查看每日特惠帮助 打点
    event.EventReport("Daily_gift_ViewHelp", {})
end

function  UIController:OnBtn_changeHeroBtnGrayClickedProxy()
    --提示已购买
    flow_text.Add(lang.Get(1001570))
end

function  UIController:OnBtn_oneClickOtherBuyClickedProxy()
    local lang = require "lang"
    --提示一键礼包不能购买
    flow_text.Add(lang.Get(1001571))
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    self.FreshUI = function()
        self:RefreshUI()
    end
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了   
    event.Register(event_rechargeGift_define.TMSG_RECHARGE_GIFT_DATA_NTF,self.FreshUI)
    event.Register(event_define.DALITY_GIFT_REFRESHUI,self.FreshUI)
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()
    event.Unregister(event_rechargeGift_define.TMSG_RECHARGE_GIFT_DATA_NTF,self.FreshUI)
    event.Unregister(event_define.DALITY_GIFT_REFRESHUI,self.FreshUI)
end
---********************功能函数区**********---
function UIController:InitRedDot()
    red_system.RegisterRedFunc(red_const.Enum.DaliyGift, UpdateDaliyGiftRedDot)
end

function UpdateDaliyGiftRedDot()
    return gw_recharge_mgr.GetRechargeBuyCount(freeRechagerID) == 0
end

function GetTabRedDot()
    return gw_recharge_mgr.GetRechargeBuyCount(freeRechagerID) == 0 and 1 or 0 
end

--根据充值状态判断入口是否置灰 false 无法切换  true 可以切换
function UIController:CheckChangeHeroBtnState()
    local bundleIDs = game_scheme:DailySpecialGift_0(ui_daily_gift_activity_mgr.GetSelectID()).BundleID.data
    local state = true
    for i = 1, #bundleIDs do
        if not ui_daily_gift_activity_mgr.CheckRechargeState((bundleIDs[i])) then
            state = false
            break
        end
    end
    local onlyOne = #ui_daily_gift_activity_mgr.GetOpenHeroIdsList() > 1
    if not onlyOne then
        return
    end
    self:TriggerUIEvent("CheckChangeHeroBtnState",state)
end

function UIController:CheckRedDot()
    self:InitActivityData()
    local gw_event_activity_define = require "gw_event_activity_define"
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,activityData.activityID)
    red_system.TriggerRed(red_const.Enum.DaliyGift)
end

function UIController.InitActivityData()
    local festival_activity_cfg = require "festival_activity_cfg"
    activityData = festival_activity_mgr.GetActivityDataByActivityCodeType( festival_activity_cfg.ActivityCodeType.DailyGift)
end

--如果只有一个英雄不可见
function UIController:RefreshChangeHeroBtn()
    local state = #ui_daily_gift_activity_mgr.GetOpenHeroIdsList() > 1
    self:TriggerUIEvent("RefreshChangeHeroBtn",state)
end

function UIController:RefreshHeroBg()
    local textureName = game_scheme:DailySpecialGift_0(ui_daily_gift_activity_mgr.GetSelectID()).banner
    self:TriggerUIEvent("RefreshHeroBg",textureName)
end

function UIController:RefreshFreeReward()
    local active = self.activityCfg.ctnID1.data and #self.activityCfg.ctnID1.data > 0
    self:TriggerUIEvent( "RefreshFreeReward",active)
end

function UIController:SetTimeText()
    self:TriggerUIEvent("SetTimeText", gw_recharge_mgr.GetRechargeFreshTime(freeRechagerID))
end

function UIController:SetHeroNameText()
    local heroCfg = game_scheme:Hero_0(ui_daily_gift_activity_mgr.GetHeroSelectID())
    self:TriggerUIEvent("SetHeroNameText", heroCfg.HeroNameID)
end

--检查免费礼包领取状态,购买次数>0 = 已领取，关闭点击事件
function UIController:SetFreeRechagerState()
    local rechagerState = gw_recharge_mgr.GetRechargeBuyCount(freeRechagerID) == 0
    self:TriggerUIEvent("SetFreeRechagerState", rechagerState)
end

--刷新礼包信息
function UIController:RefreshRechagerItem()
    local select_id = ui_daily_gift_activity_mgr.GetSelectID()
    local DailySpecialGiftCfg = game_scheme:DailySpecialGift_0(select_id)
    if not DailySpecialGiftCfg then
        local log = require "log"
        log.Error("没有找到礼包配置",select_id)
        return
    end
    local goEvent = function(rechargeId)
        --请求服务器购买礼包
        --ui_general_trials_mgr.MSG_GENERALTRIAL_PERSONALGETPRIZE_REQ(rechargeId)
        local net_activity_module = require "net_activity_module"
        net_activity_module.Send_New_Recharge_REQ(rechargeId)
    end
    self:TriggerUIEvent("RefreshRechagerItem", DailySpecialGiftCfg.BundleID.data,goEvent)
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
