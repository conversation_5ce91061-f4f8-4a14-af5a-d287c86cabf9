return {
[310074]=[[Objective: Move away the goods with the tractor.]],
[310075]=[[Objective: Defend the goal.]],
[310076]=[[Objective: Stop trucks from running red lights.]],
[310077]=[[Objective: Catch the air conditioner with ropes.]],
[310078]=[[Objective: Stop the plane with ropes.]],
[310079]=[[The red buoy will move under force, halving its blocking effect.]],
[310080]=[[Objective: Stop the speedboats.]],
[310081]=[[Objective: Save the car from falling off a cliff.]],
[310082]=[[Objective: Save the high-speed train from falling off a cliff.]],
[310083]=[[Objective: Stop the passenger liner with ropes.]],
[310084]=[[Objective: Stop the speedboat with ropes.]],
[310085]=[[Objective: Build a bridge to help the train reach the other side.]],
[310086]=[[Objective: Build a bridge and stop the car.]],
[310087]=[[Objective: Catch the falling air conditioner.]],
[310088]=[[Objective: Move away the wrecked passenger liner.]],
[310089]=[[Objective: Avoid car crashes.]],
[310090]=[[The red roadblock will move under force, halving its blocking effect.]],
[310091]=[[Objective: Build a bridge and stop the high-speed train.]],
[310092]=[[Objective: Stop the coach with ropes.]],
[310093]=[[Rope Savior]],
[310095]='Jetpack',
[310096]=[[Jumping Power]],
[310097]='Level',
[310098]='Thrust',
[310099]=[[Tap to Start]],
[310100]=[[Stage Objective]],
[310101]=[[Tap when you reach the starting line.]],
[310102]=[[Tap to Jump]],
[310103]=[[Tap when both your feet touch the ground.]],
[310104]=[[Tap again to use the thruster.]],
[310105]=[[Hold to use the jetpack.]],
[310106]=[[Fuel Exhausted]],
[310109]=[[Jumping Boy]],
[310008]=[[Rescue the Lamb]],
[310009]=[[Tap or swipe the screen to place timber piles and prevent the lamb from getting eaten by the wolf.]],
[310010]=[[Tap the button to start the game.]],
[310062]=[[You will fail if you can't help the lamb eat grass in haystack stages.]],
[310063]=[[The lamb and the wolf drop down when they walk into a trap.]],
[310064]=[[The lamb and the wolf always move toward the rafts that are closest to them first.]],
[310065]=[[Note: Timber piles cannot be placed on stone roads.]],
[310146]='CutEscape',
[310148]=[[Hurricane Racing]],
[310149]=[[Tap and hold the screen to move.]],
[310150]=[[Tap the screen to drift.]],
[310151]='Steady!!',
[310152]=[[Drift Time]],
[310153]=[[Drift Zone Entrance]],
[310154]=[[Drift Zone Exit]],
[310160]='BlandMaster',
[310187]=[[City Defense]],
[310188]=[[Unit Deployment Cost]],
[310189]=[[Eliminate incoming enemies.]],
[310190]=[[Insufficient Energy.]],
[310191]=[[Save Dog]],
[310192]=[[Draw the line slowly to avoid any discontinuity.]],
[310193]=[[Jelly /Cubes]],
[310215]=[[Escape/the Ray]],
[310216]=[[Move items to stop the laser and reach the goal.]],
[310217]=[[Move boxes.]],
[310218]='Tip',
[310221]='The/Finishing/Touch',
[310222]=[[Draw the missing part.]],
[310299]='Bridge/Building',
[310300]='Start',
[310301]='Tip',
[310302]=[[Got it]],
[310316]=[[Sneak and Take]],
[310455]=[[Javelin Contest]],
[310469]=[[Storage
Master]],
[310470]='newballup',
[310471]=[[Long press to throw a dagger and win when the floor is full]],
[310472]=[[Be the first to reach the top before your opponent!]],
[310500]=[[Press and hold the screen to launch]],
[310501]=[[Tip: 1 dagger is awarded for every certain value accumulated in the spin game for the number of consecutive hits]],
[310502]=[[Remaining squares]],
[310503]='Score',
[310504]=[[All-time high]],
[310505]=[[New Record]],
[310495]='ink',
[310496]=[[Link the wheels and the seat to assemble the kart.]],
[310497]='Start',
[310498]=[[Tap the screen to speed it up]],
[310510]=[[Forgotten Canyon]],
[310511]='Pool/Slide',
[310512]='Tip',
[310513]=[[Swipe the screen to knock away other characters!]],
[310540]='Piece/Wanted',
[310541]='Blackhole/Strike',
[310584]=[[Surround the thief trying to escape！]],
[310586]=[[The red line is the non-drawable area, please avoid it when drawing the line]],
[310590]=[[Beach Couple]],
[310591]=[[Draw lines to protect humans from harm. Humans also take damage if they touch the drawn lines.]],
[310592]=[[Game over as the thief arrived at the exit]],
[310593]='Scaleman',
[310597]=[[Epic Escape]],
[310598]=[[The red line is the non-drawable area, please avoid it when drawing the line]],
[310599]=[[Move your finger 
to start the game]],
[310600]=[[Collect bricks and return to 
the tower for upgrades]],
[310601]=[[Cannon Charge]],
[310602]='release',
[310603]='Score',
[310604]=[[Swipe to start]],
[310605]='Tip',
[310606]=[[Unlock more solutions!]],
[310607]=[[Draw lines on the ground to separate the animals.]],
[310608]='Charges:',
[310610]='HotPursuit',
[310611]=[[Tap to start jumping]],
[310612]=[[Tap the screen to close.]],
[310613]=[[Draw a line to cut the rope.]],
[310614]=[[Make sure the vulnerable cat gets away.]],
[310615]=[[Can't clear the stage with a brown hose.]],
[310616]='Countdown:',
[310617]=[[Tap the brushes to color the tiles and reflect the image above.]],
[310618]=[[Color the tiles with blue paint first, and you will get one blue tile at the end.]],
[310619]=[[Try the coloring approach from the previous round.]],
[310620]='Shadow/Trick',
[310630]=[[Straighten the photo frame.]],
[310631]=[[Put toys inside the basket.]],
[310632]=[[The positions of chocolates in the box must match the lid.]],
[310633]=[[Adjust the radio until it can receive a signal.]],
[310634]=[[Place tableware in the appropriate position.]],
[310635]=[[Add a suitable lid to the pencil box.]],
[310636]=[[Place the cans in the right sequence.]],
[310637]=[[Put the scattered puzzle pieces together.]],
[310638]=[[The vases and shadows must match.]],
[310639]=[[Move the twinkling light spots to draw a painting.]],
[310640]=[[Fix the vase.]],
[310641]=[[Which side of the vase is the front?]],
[310642]=[[Wipe the table clean.]],
[310643]=[[Place the flowers and make sure they don't block one another.]],
[310644]=[[Stack tableware by size.]],
[310645]='Bravo!',
[310646]=[[Stack items by size.]],
[310647]=[[Cool! Smiling face graffiti!]],
[310648]=[[Align the patterns.]],
[310649]=[[Clean the crumbs and arrange biscuits from the longest to the shortest.]],
[310650]='Sunflower',
[310651]=[[Design the pet calendar following the pattern.]],
[310652]=[[Sort from the largest to the smallest.]],
[310653]=[[Place the plates following the traces.]],
[310654]=[[Organize the photos.]],
[310655]=[[Pay attention to the arc of sand in the bottle.]],
[310656]=[[Arrange the chili by size.]],
[310657]=[[Place the wine sets and make sure they don't block one another.]],
[310658]=[[Clean the crumbs and place the puzzle.]],
[310659]=[[Sort by color.]],
[310660]=[[Place the glasses in the right slots.]],
[310661]=[[Place the cosmetics in the right positions.]],
[310662]=[[The statues and shadows must match.]],
[310663]=[[Fix the vase.]],
[310664]=[[Utter Darkness]],
[310665]=[[Sort the photos in the right sequence.]],
[310666]=[[Sort the stones based on their grooves.]],
[310667]=[[Pay attention to the horizontal transition of color.]],
[310668]=[[Sort the items by putting items of the same kind together.]],
[310669]=[[Pay attention to the curves on the package.]],
[310670]=[[Match the shadows in the background with the discs.]],
[310671]=[[Move the box to connect the rope.]],
[310672]=[[Place the puzzle.]],
[310673]=[[Sort the stationery and place them in the right grooves.]],
[310674]=[[Sort items in the closet. Pay attention to the details.]],
[310675]=[[Hang the tools neatly on the wall.]],
[310676]=[[Pictures on the blackboard must match the actual items.]],
[310677]=[[Move the mirror and place the items.]],
[310681]=[[Shadow Kill]],
[310682]=[[Swipe the screen to toss out the weapon.]],
[310686]=[[Tap anywhere to teleport to the weapon.]],
[310687]=[[Toss out your weapon to defeat the enemy.]],
[310688]=[[Bridge / Master]],
[310689]=[[Pause for one second at the end of the segment to continue drawing the line.]],
[310690]=[[Tap anywhere else on the screen to close.]],
[310691]=[[Cannot draw lines in the yellow line area.]],
[310700]=[[Swipe to start]],
[310701]=[[Roll the snowball to pave the road.]],
[310702]=[[Players rolling a big snowball will knock over those with a smaller snowball.]],
[310703]=[[All snowballs used up. Return to the snowfield and gather more snowballs.]],
[310704]=[[The bigger the snowball, the greater the amount of surface you can pave.]],
[310710]=[[Dual Blade/Assault]],
[310711]='%d+',
[310714]='Superdog',
[310715]=[[Swipe to draw lines to fend off the bees]],
[310717]=[[Ultra Black Hole]],
[310757]=[[Phantom Pierce]],
[310821]=[[Synth Elimination]],
[330000]=[[Move our Warrior to Position 1]],
[330001]=[[[%s] unlocked a new stage]],
[330002]=[[Challenge Adventure mode to unlock new stages]],
[330003]=[[Collect hero shards to recruit more heroes]],
[330004]=[[Let's upgrade Mage heroes!]],
[330005]=[[Now, try the [%s] stage!]],
[330006]=[[Feel the power of Mage heroes!]],
[330007]=[[Continue challenging [%s] to earn rewards!]],
[330008]=[[Danger is approaching. Go check it out.]],
[330009]=[[Collect Summon Scrolls to recruit more heroes]],
[330010]='New',
[330011]=[[You can continue your challenge or clear Adventure Stage 2-3 to unlock the next chapter [%s].]],
[330012]=[[Challenge Adventure mode stages to unlock more [%s] stages]],
[330013]=[[Adventure Stage cleared, new [%s] unlocked. Go challenge it now!]],
[330014]=[[The enemies up ahead are very powerful. Let's level up our heroes first!]],
[330015]=[[Upgraded heroes will help us unlock more [%s] stages.]],
[330016]=[[Adventure Stage cleared, new [%s] unlocked. Go challenge it now!]],
[330017]=[[Adventure Stage completed, new [%s] stage unlocked. Go challenge it now!]],
[330018]=[[Deploy powerful Tank heroes to unlock more [%s] stages.]],
[330019]=[[Every 4 [%s] stages cleared grants 1 phase reward. Don't forget to claim it!]],
[330020]=[[Go challenge Adventure Stages now!]],
[330057]=[[Isolde enters the battle in Abyssal Form. This controllable passive skill cannot be activated while affected by CC.
In the Abyssal Form, Isolde consumes all Energy to cast Abyssal Gaze once their Energy reaches <color=#df2d52>100</color>. This effect only triggers <color=#df2d52>1</color> time per turn.
Abyssal Gaze: Has a <color=#df2d52>15%</color> chance to apply Erosion to <color=#df2d52>4</color> random enemies for <color=#df2d52>2</color> turns.
Erosion: Immobilized and DMG Reduction is permanently reduced by <color=#df2d52>10%</color> at the end of each turn.]],
[330058]=[[Lv. 2: Increases the chance of Abyssal Gaze applying Erosion to 25%.]],
[330059]=[[Lv. 3: Increases the chance of applying Erosion to the first target to 100%.]],
[330060]=[[In the "Abyss" form, after using "Abyss Gaze", Isolde enters the "Eclipse" state.
"Eclipse": When the enemy hero is about to consume energy to use an active skill, Isolde removes <color=#df2d52>50</color> points of energy from the enemy hero, and the Eclipse state is removed after the energy removal effect is triggered (only effective in PVP scenarios).]],
[330061]=[[Lv. 2: Increases removed Energy to 75.]],
[330062]=[[Lv. 3: Increases removed Energy to 100.]],
[330063]=[[Lv. 4: Removes all Energy. Isolde becomes immune to Erosion and dispels any CC she is affected by. This effect can only trigger 1 time per turn.]],
[330064]=[[This controllable passive skill cannot be activated while affected by CC.
When Isolde takes fatal damage, they become immune to that damage, dispels any CC they are affected by, and activates Skyland Form (this effect can only trigger <color=#df2d52>1</color> time per battle).
In the Skyland Form, Isolde consumes all Energy to cast Solar Radiance once their Energy reaches <color=#df2d52>100</color>. This effect triggers only <color=#df2d52>1</color> time per turn. Isolde switches back to Abyssal Form after casting Solar Radiance.
Solar Radiance: Restores the HP of all allies by <color=#df2d52>15%</color>.]],
[330065]=[[Lv. 2: Increases the healing of Solar Radiance to 20%.]],
[330066]=[[Lv. 3: Increases the healing of Solar Radiance to 25%.]],
[330067]=[[Lv. 4: Increases the healing of Solar Radiance to 30%.]],
[330068]=[[Isolde enters Skyland Form, recovering <color=#df2d52>30%</color> HP.]],
[330069]=[[Lv. 2: Recovers 45% HP.]],
[330070]=[[Lv. 3: Recovers 60% HP.]],
[330071]=[[In the Abyssal Form, Isolde gains <color=#ff9200>6%</color> Debuff Hit, while in the Skyland Form, she gains <color=#ff9200>6%</color> DMG Reduction.]],
[330072]=[[In the Abyssal Form, Isolde gains <color=#ff9200>12%</color> Debuff Hit, while in the Skyland Form, she gains <color=#ff9200>12%</color> DMG Reduction.]],
[330073]=[[In the Abyssal Form, Isolde gains <color=#ff9200>18%</color> Debuff Hit, while in the Skyland Form, she gains <color=#ff9200>18%</color> DMG Reduction.]],
[330074]=[[In the Abyssal Form, Isolde gains <color=#ff9200>24%</color> Debuff Hit, while in the Skyland Form, she gains <color=#ff9200>24%</color> DMG Reduction. <color=#ff9200>Isolde gains 50 Energy at the start of the battle and recovers 50 Energy after casting Abyssal Gaze or Solar Radiance</color>.]],
[330075]=[[In the Abyssal Form, Isolde gains <color=#ff9200>30%</color> Debuff Hit, while in the Skyland Form, she gains <color=#ff9200>30%</color> DMG Reduction. Isolde gains <color=#ff9200>50</color> Speed on the first turn.]],
[330095]=[[Porcelina casts Piercing Dragon Souls, launching <color=#df2d52>5</color> attacks at the enemy with the lowest HP, each dealing DMG equal to <color=#df2d52>80%</color> of ATK. (Prioritizes attacks on front-row enemies.)
When affected by Dragon's Wrath, the number of attacks increases to <color=#df2d52>8</color>.]],
[330096]=[[Lv. 3: Increase DMG to 100% of ATK.]],
[330097]=[[Lv. 3: Increase DMG to 120% of ATK.]],
[330098]=[[Porcelina casts Roaring Fists as her Basic Attacks, dealing DMG equal to <color=#df2d52>120%</color> of ATK to the enemy with the lowest HP. (Prioritizes attacks on front-row enemies.) Gain 1 stack of Invigoration, stacking up to <color=#df2d52>7</color> times. Each stack of Invigoration increases extra DMG of Roaring Fists by <color=#df2d52>3%</color> of the enemy's Max HP (cannot exceed <color=#df2d52>1,500%</color> of Porcelina's max ATK).
When affected by Dragon's Wrath, each stack of Invigoration increases CRIT by <color=#df2d52>10%</color> and CRIT DMG by <color=#df2d52>10%</color>.]],
[330099]=[[Lv. 2: Increase DMG to 160% of ATK.]],
[330100]=[[Lv. 3: Increase DMG to 200% of ATK. Piercing Dragon Souls and Roaring Fists pierce through Invulnerability and deal 40% of the original DMG. This effect is increased to 70% of the original DMG when targeting invulnerable, front-row enemies.]],
[330101]=[[Lv. 4: When in Dragon's Wrath, the first cast of Roaring Fists per turn grants 1 extra stack of Invigoration.]],
[330102]=[[At the start of battle, Porcelina settles down to meditate and enters the Dragon Shadow state.
Dragon Shadow: Porcelina takes <color=#df2d52>50%</color> reduced DMG and causes other heroes from both sides to deal <color=#df2d52>15%</color> reduced DMG. (Reduced DMG dealt is only active during PvP.)
When other heroes from both sides launch active attacks, Porcelina casts Roaring Fists (passive, cannot trigger if affected by CC). The effect can trigger <color=#df2d52>7</color> times per turn.
Upon accumulating <color=#df2d52>7</color> stacks of Invigoration, Porcelina completes her meditation, exiting the Dragon Shadow state before entering Dragon's Wrath.
Dragon's Wrath: Enhances the effects of Roaring Fists and Piercing Dragon Souls.]],
[330103]=[[Lv. 2: When in the Dragon Shadow state, non-Warrior heroes from both sides deal an additional 15% reduced DMG. (Reduced DMG dealt is only active during PvP.)]],
[330104]=[[Lv. 3: When in Dragon's Wrath, upon receiving an attack that deals DMG exceeding 30% of Max HP, consume 1 stack of Invigoration to block DMG equal to 15% of Max HP.]],
[330105]=[[When in Dragon's Wrath, Porcelina casts Piercing Dragon Souls (passive, cannot trigger if affected by CC). This time, Piercing Dragon Souls increases CRIT by an additional <color=#df2d52>25%</color> and CRIT DMG by an additional <color=#df2d52>25%</color>.]],
[330106]=[[Lv. 2: When in the Dragon's Wrath state, whenever a front-row enemy is defeated, Porcelina casts Piercing Dragon Souls (passive, cannot trigger if affected by CC).]],
[330107]=[[Lv. 3: When the last front-row enemy is defeated, if Porcelina is in the Dragon's Wrath state, she will cast an additional Piercing Dragon Souls.]],
[330108]=[[Lv. 4: When taking fatal damage, Porcelina becomes immune to DMG from that action, including DMG from passive abilities that can be triggered while under CC. She gains Invulnerability for 1 turn, healing herself for 800% of ATK and immediately casting Piercing Dragon Souls (passive, cannot trigger if affected by CC). Afterward, she consumes 3 stacks of Invigoration. (This effect can only trigger once per battle.)]],
[330109]=[[After each action, Porcelina's DMG increases by <color=#ff9200>5%</color> for <color=#ff9200>2</color> turns, stacking up to <color=#ff9200>4</color> times. When an allied unit defeats an enemy, this effect disappears.]],
[330110]=[[After each action, Porcelina's DMG increases by <color=#ff9200>5%</color> for <color=#ff9200>2</color> turns, stacking up to <color=#ff9200>8</color> times. When an allied unit defeats an enemy, this effect disappears.]],
[330111]=[[After each action, Porcelina's DMG increases by <color=#ff9200>5%</color> for <color=#ff9200>2</color> turns, stacking up to <color=#ff9200>12</color> times. When an allied unit defeats an enemy, this effect disappears.]],
[330112]=[[After each action, Porcelina's DMG increases by <color=#ff9200>5%</color> for <color=#ff9200>2</color> turns, stacking up to <color=#ff9200>16</color> times. When an allied unit defeats an enemy, this effect disappears. <color=#ff9200>In Dragon Shadow state, Porcelina is immune to all CC. In Dragon's Wrath state, when she is affected by CC for the first time each turn, she consumes one stack of Invigoration to remove the CC. This can only trigger once per turn.</color>]],
[330113]=[[At the start of the turn, for every <color=#ff9200>1%</color> CRIT, increases CRIT RES by <color=#ff9200>0.3%</color> for 1 turn. In Dragon's Wrath, if Porcelina casts Roaring Fists while having <color=#ff9200>7</color> stacks of Invigoration, DMG increases by <color=#ff9200>30%</color> this time.]],
[350000]=[[Configure Quest Star Level]],
[350001]='Accept',
[350002]=[[Accept All]],
[350003]=[[You haven't configured the star level of quests for Accept All]],
[350004]=[[Would you like to do so now?]],
[350005]='Deploy',
[350006]=[[The quest requirements have not been met.]],
[350007]=[[Quest Accepted]],
[350008]=[[Star Quest]],
[350009]=[[Not Selected]],
[372000]=[[Rune Exclusive]],
[372001]=[[Daily Special Offer Reward]],
[372003]=[[Public Welfare Pack]],
[372004]=[[Rune Pack]],
[372005]=[[Exclusive Pack]],
[372006]=[[Daily Special Offer]],
[372011]=[[1. You can purchase the event pack once every day during the Special Offer event.]],
[372012]=[[2. Each day at 00:00, the event pack's purchase attempts reset, allowing you to carry on with your purchases.]],
[373000]=[[[Permanent Card] Rewards]],
[373001]=[[Dear Traveler, your Permanent Card has been sent. Please check and claim!]],
[373002]=[[Permanent Card Info]],
[373003]=[[1. After purchasing the Permanent Card, you will get "Daily Rewards" for life!]],
[373004]=[[2. You must log in to have "Daily Rewards" sent to your mail. The "Daily Rewards" of a day cannot be sent if you don't log into the game.]],
[373005]=[[Purchase and Claim]],
[373006]=[[Daily Rewards]],
[373007]=[[Daily Rewards will be sent via mail]],
[373008]=[[Hot
Sale]],
[373009]=[[Buy once, benefit for life]],
[373010]=[[Two-hour hang-up bonus]],
[373011]=[[Get 2h AFK worth of Gold, Hero EXP, and Rankup Stones immediately (not affected by VIP Bonus).]],
[373021]=[[You don't need to purchase the Monthly Card separately. The card will be activated for free when you obtain a certain amount of VIP EXP. Upon activation, you can claim Monthly Card Rewards every day for 30 days.]],
[373022]=[[Obtain 250 VIP EXP to activate the Common Monthly Card or 750 VIP EXP to activate the Deluxe Monthly Card. You can own both cards at the same time and their durations are independent.]],
[373023]=[[When the Monthly Card is activated, further recharges will not refresh or extend its duration.]],
[373024]=[[When the Monthly Card expires, you can reactivate it by obtaining more VIP EXP via recharges.]],
[373025]=[[Gain VIP EXP from recharges ]],
[373026]=[[to get Monthly Cards for free]],
[373027]=[[1.You don't need to purchase the Monthly Card separately. The card will be activated for free when you obtain a certain amount of VIP EXP. Upon activation, you can claim Monthly Card Rewards every day for 30 days.]],
[373028]=[[2.Obtain 250 VIP EXP to activate the Common Monthly Card or 750 VIP EXP to activate the Deluxe Monthly Card. You can own both cards at the same time and their durations are independent.]],
[373029]=[[3.When the Monthly Card is activated, further recharges will not refresh or extend its duration.]],
[373030]=[[4.When the Monthly Card expires, you can reactivate it by obtaining more VIP EXP via recharges.]],
[373031]=[[5.Gain VIP EXP from recharges to get Monthly Cards for free.]],
[375000]=[[Community Rewards]],
[375001]=[[Tap the Join button, copy the code, and find the REWARDS channel to receive your community reward Exchange Code.]],
[375002]=[[Community Exclusive Exchange Rewards]],
[375003]='Go',
[375004]=[[Clear Adventure Stage 5-1 to unlock the Redeem Code feature]],
[375005]=[[Community Rewards]],
[380000]=[[Exclusive Avatar Frame]],
[380001]=[[Obtained through events]],
[283701]=[[Black Friday]],
[283702]=[[Discount gift pack]],
[283703]=[[Diamond Pack]],
[283704]=[[During the Black Friday event, discount gift packs are available for purchase]],
[283705]=[[During the Black Friday event, diamond gift packs are available for purchase]],
[290091]=[[<color=#ffea00><size=40>%s</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7> <size=40>6-Star Super CC Rune</size></color>.]],
[290092]=[[<color=#ffea00><size=40>%s</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7> <size=40>4-Star Super CC Rune</size></color>.]],
[290093]=[[<color=#ffea00><size=40>%s</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7> <size=40>3-Star Super CC Rune</size></color>.]],
[290094]=[[<color=#ffea00><size=40>Draw</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7><size=40>6-Star Super CC Rune</size></color>.]],
[290095]=[[<color=#ffea00><size=40>Draw</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7><size=40>4-Star Super CC Rune</size></color>.]],
[290096]=[[<color=#ffea00><size=40>Draw</size></color><color=#ecb440> <size=30>to guarantee a</size></color><color=#69e7a7><size=40>3-Star Super CC Rune</size></color>.]],
[381531]='Previous',
[381532]='Next',
[381680]=[[No sales]],
[381931]=[[The reward has not been unlocked yet, come back tomorrow~]],
[381932]=[[All rewards have been claimed]],
[384255]=[[Insufficient materials.]],
[384400]=[[Return to current stage]],
[385000]=[[6. In Manage Union, you can leave the union, search for a union, or impeach the President.]],
[385010]=[[Summon will consume diamonds, continue?]],
[385011]='Cancel',
[385012]='Confirm',
[385005]=[[Dear Traveler, your special rewards have been issued. Enjoy the game!]],
[385020]=[[Welcome to Hero Clash, Traveler. Please select a minigame to start your journey.]],
[385021]=[[Please note the following:]],
[385022]=[[1. The minigame cannot be changed once selected.]],
[385023]=[[2. More minigames will be unlocked as you progress through your journey in Hero Clash.]],
[385024]=[[Game Center]],
[385025]=[[Clear Adventure Stages to unlock more minigame stages along with new minigames.]],
[385026]=[[Clear %d more stage(s) in %s to unlock the next minigame.]],
[385027]='Unlocked',
[385028]='Locked',
[385029]=[[Stay Tuned]],
[385030]=[[Switch Game]],
[385031]='Latest',
[385032]='Current',
[385033]=[[1. Minigames not selected at the start of the game can be found in the Game Center along with other minigames.]],
[385034]=[[2. You can unlock minigames by meeting the corresponding conditions.]],
[385035]=[[3. You can switch to and play unlocked minigames via the Unlocked screen.]],
[385036]=[[4. Some minigames require you to download resources to unlock.]],
[385037]=[[Unlock conditions not met.]],
[385038]=[[Downloaded and unlocked.]],
[385039]=[[Select Game]],
[385040]=[[Are you sure you want to select %s?]],
[385041]=[[(This minigame requires you to download approximately %s resources.)]],
[385042]=[[This is the current minigame.]],
[385043]=[[Unlock conditions met. Select one of the following minigames to unlock.]],
[385044]=[[You are downloading another minigame.]],
[385045]=[[This minigame requires you to download approximately %s resources. Do you want to proceed?]],
[385046]=[[A new minigame can be unlocked.]],
[385047]=[[5. Stage diamond rewards will no longer be available after you have cleared a total of 2,000 stages.]],
[385048]=[[Reward-claiming limit reached.]],
[385049]='Switch',
[385050]=[[The Game Center is now open. Go try it out!]],
[385051]=[[You'll need Mini-Game Token to enjoy the Game Center. Each new mini-game stage requires 1 Mini-Game Token to enter.]],
[385052]=[[Mini-Game Token refresh at 00:00 daily. You can also receive extra Mini-Game Token by completing corresponding quests.]],
[385053]=[[Traveler, you've got enough Mini-Game Token to enjoy some mini-games!]],
[385054]=[[Stage Progress: %s]],
[385055]=[[Each new mini-game stage requires 1 %s to enter.]],
[385056]='Start',
[385057]='Downloading...',
[385058]=[[Note: Each new mini-game stage requires 1 %s to enter.]],
[385059]=[[Refresh Time: %s]],
[385060]=[[Mini-Game Token]],
[385061]=[[Get Mini-Game Tokens]],
[385062]=[[Reach 100 Daily Activity Points.]],
[385063]=[[Insufficient %s.]],
[385064]=[[Insufficient Mini-Game Tokens.]],
[385065]=[[Currency used in the Game Center. Spend mini-game tokens to enter stages. Tokens are replenished in full on the following day.]],
[385066]=[[Previous stage not cleared yet.]],
[385067]=[[Coming soon!]],
[385068]=[[1. Clearances of new mini-game stages will be accumulated.]],
[385069]=[[2. Reach the current cumulative progress to claim rewards for this round.]],
[385070]=[[1. Mini-games in the Game Center are available to all, but note that you'll have to download additional resources to play some of them.]],
[385071]=[[2. Stage 1 of each mini-game is unlocked by default. Clear the current stage to unlock the next.]],
[385072]=[[3. Entering an uncleared stage requires 1 mini-game token. Resets during the game do not consume extra mini-game tokens. However, if you rechallenge the stage after defeat, another token will be consumed.]],
[385073]=[[4. No mini-game tokens will be consumed when you enter cleared stages.]],
[385074]=[[5. Mini-game tokens will be replenished daily at 00:00. Travelers can also earn a certain amount of mini-game tokens by completing daily quests.]],
[385075]=[[6. Get extra rewards by clearing the required number of stages.]],
[385076]=[[Download complete. Please log in again.]],
[385077]=[[Current Phase Reward Progress]],
[385078]=[[Next Stage]],
[385079]=[[Game Center]],
[385800]='Piece/Wanted',
[385801]=[[Save the Dog]],
[385802]=[[Hero Tower Defense]],
[385803]=[[Merge /Dinosaur]],
[385804]=[[Brick Remove]],
[385805]='Snowball/Game',
[385806]='Rapid/Slashes',
[385807]=[[Stop Bees]],
[385808]='HotPursuit',
[385809]='Leave/None',
[385810]='Shadow/Trick',
[385811]=[[Beach Couple]],
[385812]='Blackhole/Strike',
[385813]='FishKing',
[385814]='Parking/King',
[385815]=[[Interlocking Rings]],
[385816]=[[No Escape]],
[385817]=[[Epic Escape]],
[385818]=[[Shadow Kill]],
[385819]=[[Clear Tiles]],
[385820]=[[Merge Thug]],
[385821]=[[Connect Dots]],
[385822]='DrawBreak',
[385823]=[[Rocket Boy]],
[385824]=[[Brick Removal]],
[393000]=[[Hero Trial]],
[393001]=[[Free Trial]],
[393002]=[[On Trial]],
[393003]='Used',
[393004]='Tutorial',
[393005]=[[Tap to claim. No need for upgrade.]],
[393006]=[[%s-Day Free Trial]],
[393007]=[[%s day(s) %s hour(s) remaining]],
[393008]=[[You can't upgrade a trial hero.]],
[393009]=[[%s day(s) %s hour(s)]],
[393010]=[[Trial Ended]],
[393011]=[[Max number of trials for this hero reached today for this feature.]],
[393012]=[[Limited Trial]],
[393013]=[[1. During the event, players can claim trial heroes for free.]],
[393014]=[[2. Trial heroes are available for trial only and will be automatically recycled once the trial period ends.]],
[393015]=[[3. During the event, players can enjoy the full trial period of a hero by claiming them at any time.]],
[393016]=[[4. The power of a trial hero is calculated based on the player's level of hero development at the moment. Once the hero is claimed, their power will remain unaffected by any changes in hero development.]],
[393017]=[[5. Players cannot upgrade or descend their trial heroes, but can change their gear and artifacts. All equipped items will be returned once the trial heroes are recycled.]],
[393018]=[[6. Trial heroes cannot be deployed in the Acorn Tavern and Forbidden Jungle. Players cannot activate their Codex or obtain tutorial rewards. However, trial heroes can still benefit from the public fixed stat bonuses.]],
[393019]=[[7. Trial heroes are available for certain features, which may have a daily limit on the number of trials. Each time the trial hero is deployed and achieves victory, it will consume one trial charge.]],
[393020]=[[You can't view a trial hero.]],
[393021]=[[Unlock limited trials]],
[393022]=[[The team already has a trial hero]],
[393023]=[[The trial hero has been claimed]],
[385999]='Used',
[383000]=[[Monthly Card activated]],
[390100]=[[Hero's Descent]],
[390101]='1',
[390102]='CRIT',
[390103]='Super',
[390104]=[[AoE Damage]],
[390105]=[[Awakening Pack]],
[390106]=[[Summon Now]],
[390107]=[[Public Welfare Pack]],
[390108]='Placeholder',
[390109]='Placeholder',
[390110]='Placeholder',
[390111]='Placeholder',
[390112]='Placeholder',
[390113]='Placeholder',
[390114]='Placeholder',
[390115]='Placeholder',
[392000]=[[Return Journey]],
[392001]=[[Dear Traveler,]],
[392002]=[[%s days have passed, and much has changed in the world. We've prepared some amazing rewards.]],
[392003]=[[We've prepared some amazing rewards.]],
[392004]=[[Log in for 8 days to obtain free avatar frames.]],
[392005]=[[2x drop in time-limited dungeons]],
[392006]=[[Retrieve the lost resources]],
[392007]=[[Limited Super Value Pack offer]],
[392008]=[[We also have the following gifts:]],
[392009]=[[Login Gift]],
[392010]=[[Log in for 8 days to obtain avatar frames for free.]],
[392011]=[[Hero Tryout]],
[392012]=[[Limited Trials]],
[392013]=[[Double Perks]],
[392014]=[[2x drop in dungeons]],
[392016]=[[Return Collection]],
[392017]=[[Return to claim big rewards.]],
[392018]='Retrieval',
[392019]=[[You've been gone for %s day(s).]],
[392020]=[[Limited resource retrieval]],
[392021]=[[Lost Reward No. %s]],
[392022]=[[Time-limited Discount on Super Value Gifts]],
[392023]=[[Recharge VIP EXP Rewards]],
[392024]=[[Return Guidance]],
[392025]=[[Return Collection Reward Reissue]],
[392026]=[[Commander, the Return Tome event has ended. The system has detected that you have unclaimed rewards, which will be reissued by mail. Please claim it promptly.]],
[392027]=[[Unlocks in %dh %sm %as!]],
[392030]=[[1. The Return Collection event lasts for 14 days and lots of amazing rewards await. Join while you can!]],
[392031]=[[2. During the event, complete Collection Quests to gain Collection EXP, which can be used to upgrade the Collection. Upgrade the Collection to obtain lucrative rewards or buy levels to obtain rewards immediately!]],
[392032]=[[3. Unclaimed EXP and items will be sent via mail when the Return Collection event ends.]],
[392033]=[[4. Unlock Advanced Collection to earn more rewards. Buy now!]],
[392034]=[[Recharge Pack]],
[392035]='New',
[392036]=[[Login Gift Reward Reissue]],
[392037]=[[Dear Traveler, the Login Gift event has ended and your unclaimed paid rewards will be sent via mail.]],
[392038]=[[Recharge Reward Reissue]],
[392039]=[[Dear Traveler, the Recharge VIP EXP Rewards event has ended and your unclaimed paid rewards will be sent via mail.]],
[392040]=[[Daily reward is locked.]],
[392051]=[[Random Scroll Chest]],
[392053]=[[Common Summon Scroll x2 Drop Rate: 50%]],
[392054]=[[Common Summon Scroll x3 Drop Rate: 38.9%]],
[392055]=[[Common Summon Scroll x6 Drop Rate: 10%]],
[392056]=[[Excellent Faction Scroll x2 Drop Rate: 1%]],
[392057]=[[Excellent Faction Scroll x5 Drop Rate: 0.1%]],
[392060]=[[Super Value Gifts
Time-limited Discount]],
[392061]=[[Super value gift]],
[392062]=[[Limited time sale]],
[392063]=[[Purchase limit (%d/%d)]],
[392064]=[[total value]],
[394001]=[[Congratulations, you obtained]],
[394002]=[[Please claim the items you redeemed for with Google Play Points.]],
[394003]='Note',
[394004]=[[Please check if you want to claim the items with this account. If not, please switch to the desired account.]],
[394005]='Claim',
[394006]=[[Switch Account]],
[402001]=[[My Friends]],
[402150]=[[My Knights]],
[402151]=[[The reward increases with the Lord's Adventure progress!]],
[402152]=[[Knights: %s/%s]],
[402153]=[[Rewards Gained]],
[402154]='Quest',
[402155]='Dismiss',
[402156]='Recruit',
[402157]=[[Recruit Knights]],
[402158]=[[Clear stage %s to unlock]],
[402159]=[[Territory Record]],
[402160]=[[Claim All]],
[402161]='Lord',
[402162]='Knight',
[402163]=[[Recruit Knights]],
[402164]='Knight',
[402165]='Lord',
[402166]='Traveler',
[402167]='Recruit',
[402168]='Used',
[402169]=[[You've been recruited by another traveler as a knight.]],
[402170]=[[Your knight has been recruited by another traveler.]],
[402171]=[[Your knight has left.]],
[402172]=[[Your rewards are full, go claim them!]],
[402173]=[[You can now recruit knights to increase your income.]],
[402174]=[[7. Cross-Server Limit: Travelers can only recruit other travelers on the same server as Knights.]],
[402175]=[[8. Automatic Separation: Partnerships between Knights and Lords will be automatically terminated 120 hours after they are forged.]],
[402176]='Knight',
[402177]=[[1. Lord Reward: Lords receive Gold rewards based on the Adventure progress of their Knights. The reward increases as their Knights progress through the Adventure.]],
[402178]=[[2. Knight Reward: Knights receive Gold rewards based on the Adventure progress of their Lords. The reward increases as their Lords progress through the Adventure.]],
[402179]=[[3. Energy: Starting a battle in this mode costs 10 Energy.]],
[402180]=[[4. Energy Regeneration: When you run out of Energy, you can wait for it to regenerate or buy some more with Diamonds.]],
[402181]=[[5. Knight Capacity: Lords can have up to 2 Knights when the mode is unlocked, gradually increasing to up to 4 Knights in their territory as they progress through the Adventure.]],
[402182]=[[6. Recruit Knights: Recruit friends as Knights by tapping the Recruit Knight button.]],
[402183]=[[No records yet.]],
[402184]=[[Territory Record]],
[402185]=[[Dear Traveler, you have defeated <color=#FF0808>%s</color> and recruited them as your Knight. From now on, the two of you will go on adventures together. Whenever one of you claims your AFK Reward, the other will receive an additional Gold reward (the AFK Reward won't change).]],
[402186]=[[Dear Traveler, you were defeated by <color=#FF0808>%s</color> and failed to make them your Knight. Raise your CP for a better chance of winning.]],
[402187]=[[Dear Traveler, you have defeated <color=#CE29C8>%s</color>'s Lord <color=#FF0808>%s</color> and recruited <color=#CE29C8>%s</color> as your Knight. From now on, the two of you will go on adventures together. Whenever your Knight claims an AFK Reward, you will receive an additional Gold reward.]],
[402188]=[[Dear Traveler, you were defeated by <color=#CE29C8>%s</color>'s Lord <color=#FF0808>%s</color> and failed to recruit <color=#CE29C8>%s</color> as your Knight. Raise your CP for a better chance of winning.]],
[402189]=[[Dear Traveler, you defeated the Lord <color=#FF0808>%s</color> and ended your partnership. <color=#FF0808>%s</color> is no longer your Lord!]],
[402190]=[[Dear Traveler, you were defeated by your Lord <color=#FF0808>%s</color>. You were unable to end your partnership early.]],
[402191]=[[Dear Traveler, you dismissed <color=#CE29C8>%s</color> and are no longer <color=#CE29C8>%s</color>'s Lord.]],
[402192]=[[Dear Traveler, congratulations on recruiting knight no. %s for the first time. The rewards have been sent to you via mail!]],
[402193]=[[Dear Traveler, your Knight <color=#CE29C8>%s</color> has ended your partnership. Your unclaimed rewards will be sent to you via mail!]],
[402194]=[[Dear Traveler, <color=#FF0808>%s</color> has recruited you and become your Lord. From now on, the two of you will go on adventures together. Whenever one of you claims your AFK Reward, the other will receive an additional Gold reward (the AFK Reward won't change).]],
[402195]=[[Dear Traveler, your Knight <color=#CE29C8>%s</color> has been adventuring with you for %s hours. Your partnership was automatically ended. <color=#CE29C8>%s</color> is no longer your Knight.]],
[402196]=[[Dear Traveler, you have been adventuring with your Lord for %s hours. Your partnership was automatically ended. <color=#CE29C8>%s</color> is no longer your Lord.]],
[402197]=[[Dear Traveler, your partnership with your Lord <color=#CE29C8>%s</color> has ended. Your unclaimed rewards will be sent to you via mail!]],
[402198]=[[Dear Traveler, <color=#FF0808>%s</color>'s attempt to recruit you failed. They were unable to become your Lord.]],
[402199]=[[Dear Traveler, <color=#CE29C8>%s</color> has defeated your former Lord <color=#CE29C8>%s</color> and become your new Lord. From now on, the two of you will go on adventures together. Whenever one of you claims their AFK Reward, the other will receive an additional Gold reward (the AFK Reward won't change).]],
[402200]=[[Dear Traveler, <color=#CE29C8>%s</color> attempted to recruit you from your Lord <color=#CE29C8>%s</color> but was defeated due to a significant difference in CP.]],
[402201]=[[Dear Traveler, <color=#FF0808>%s</color> defeated you in a challenge. You are no longer <color=#FF0808>%s</color>'s Lord, and your rewards will decrease!]],
[402202]=[[Dear Traveler, <color=#FF0808>%s</color> challenged you. You defeated them and maintained your position as Lord.]],
[402203]=[[Dear Traveler, your Lord <color=#CE29C8>%s</color> has decided to dismiss you. <color=#CE29C8>%s</color> is no longer your Lord!]],
[402204]=[[Dear Traveler, your Lord <color=#CE29C8>%s</color> has received %d Gold in rewards thanks to you.]],
[402205]='Used',
[402206]='Used',
[402207]=[[Dear Traveler, <color=#FF0808>%s</color> has defeated you and recruited your Knight <color=#CE29C8>%s</color>. Raise your CP for a better chance of winning.]],
[402208]=[[Dear Traveler, <color=#FF0808>%s</color> attempted to recruit your Knight <color=#CE29C8>%s</color> but was defeated.]],
[402209]='Used',
[402210]=[[Delete all read records? (records with unclaimed attachments will not be deleted.)]],
[402211]=[[Delete this record?]],
[402212]='Claim',
[402213]='Delete',
[402214]=[[There are no resources to claim.]],
[402215]=[[You have reached the daily purchase limit. Come again tomorrow!]],
[402216]=[[No knights available for recruitment. Go add more friends and recruit them!]],
[402217]=[[The Traveler is still struggling through stages to unlock this mode.]],
[402218]='Tips',
[402219]=[[Are you sure to dismiss this knight?]],
[402220]='Used',
[402221]=[[Claim All]],
[402222]=[[Delete All]],
[402223]=[[No claimable attachments.]],
[402224]='Used',
[402225]=[[(Recruited — %s%%)]],
[402226]='Used',
[402227]='Used',
[402228]=[[Recruitment Success]],
[402229]=[[Recruitment Failed]],
[402230]=[[Challenge Success]],
[402231]=[[Challenge Failed]],
[402232]='Used',
[402233]=[[You cannot recruit more Knights as you have reached your Knight limit.]],
[402234]='Used',
[402235]='Expected:',
[402236]='%s/day',
[402237]=[[Lord: %s]],
[402238]=[[Knights: %s]],
[402239]='Energy',
[402240]=[[Essential resource for recruiting Knights and initiating challenges. If you don't have enough, wait for it to recover.]],
[402241]=[[Lord Knight]],
[402242]='Used',
[402243]=[[Insufficient Energy]],
[402244]=[[Cost: %s]],
[402245]='Exchange',
[402246]=[[Purchased: %s/%s]],
[402247]=[[Purchase successful.]],
[402248]=[[Territory Info]],
[402249]='Used',
[402250]=[[The maximum amount of Energy is %s. Use up some Energy before buying any more.]],
[402251]='Used',
[402252]=[[Unlock by clearing stage %s.]],
[402253]='Used',
[402254]=[[This Traveler is in battle. Try challenging them later.]],
[402255]='Knight',
[402256]='Lord',
[402257]='Used',
[402258]=[[Battle Brief]],
[402259]=[[Latest Battle Brief]],
[402260]=[[My Knights]],
[402261]='Results',
[402262]=[[Resources Changes]],
[402263]='Recruited.',
[402264]=[[Left Lord.]],
[402265]='View',
[402266]='Recruit',
[402267]='%s/day',
[402268]='————',
[402269]=[[%s Knight Info]],
[402270]=[[Your Lord has not visited their territory for a long time. Your partnership has been automatically ended.]],
[402271]='Claimable',
[402272]=[[Recruitment Success]],
[402273]=[[Recruitment Success]],
[402274]=[[Knight Left]],
[402275]='Poached',
[402276]=[[Defense Successful]],
[402277]='Defeat',
[402278]='Auto-removed',
[402279]=[[Received Rewards]],
[402280]='Lord',
[402281]='Knights',
[402282]='Log',
[402283]=[[Challenge Success]],
[402284]=[[Lord Info]],
[402285]=[[Dismiss Knight]],
[402286]=[[Rewards increase with the Lord's Adventure progress]],
[402287]=[[Rewards increase with the Knight's Adventure progress]],
[402288]=[[No rewards to claim]],
[402289]=[[Obtain %s Gold every day upon recruitment.]],
[402290]=[[Occupied until 402400]],
[402400]='Used',
[402500]=[[Frame Rate:]],
[402501]=[[30 fps]],
[402502]=[[60 fps]],
[402503]=[[*Attention: A high frame rate will increase your device's energy consumption and generate heat.]],
[402600]='Loading...',
[404350]=[[Lucky Draw]],
[404351]=[[1. Lucky Draw opens on the first day after a player creates a character.]],
[404352]=[[2. Lucky Draw lasts for 15 days.]],
[404353]=[[3. During the event, players can get Lucky Draw Tickets by receiving 150 VIP EXP on Daily Pack. Lucky Draw Tickets will be recycled after the event ends.]],
[404354]=[[4. Each reward in the Lucky Draw Wheel can only be obtained once.]],
[404355]=[[Lucky Draw Ticket]],
[404356]=[[Used to join the <color=#dd3c2dFF>Lucky Draw</color> event to win precious rewards <color=#dd3c2dFF>(this item will be recycled after the event ends.)</color>
Source: <color=#dd3c2dFF>Daily Pack</color>]],
[404357]=[[Lucky Draw Ticket Recycle]],
[404358]=[[Dear Traveler, the Lucky Draw event has ended. Your remaining items will be recycled and corresponding rewards will be issued.]],
[404359]=[[Lucky Draw Ticket Compensation]],
[404360]=[[Dear Traveler, your unclaimed Lucky Draw Tickets for yesterday have been sent via mail. Make sure to claim them.]],
[404361]=[[5. When the Lucky Draw event opens, a Login Gift is granted every day (only available for the first 7 days after the event opens).]],
[404362]=[[Buy Daily Packs to accumulate 150 VIP EXP to claim]],
[404363]=[[Refreshes at 00:00 each day.]],
[404364]=[[Perform 7 draws to obtain all rewards.]],
[404365]='Draw',
[404367]=[[Purchase Daily Packs to get 1 draw each.]],
[404369]=[[Powerful Single DMG]],
[470001]='Subscribe',
[470002]='Email：',
[470003]=[[Verification code:]],
[470004]=[[Dear Lord,]],
[470005]=[[To make sure you don't miss out on any in-game events, we cordially invite you to sign up to receive our emails. Rewards will be sent to your account upon signing up.]],
[470006]=[[Please enter the email verification code.]],
[470007]=[[Please enter an email address.]],
[470008]='Subscribed!',
[470009]=[[Please enter the correct verification code.]],
[470010]=[[Resend in %ss.]],
[470011]=[[Please enter a valid email address that has not been bound to an account yet.]],
[470012]='Unsubscribe',
[470013]=[[You can tap <a href=unbind>Unsubscribe XMan</a> to cancel your email subscription.]],
[470014]=[[After unbinding your email, you will not receive game update news anymore. Are you sure to unbind?]],
[470015]=[[Get verification code]],
[470016]='Subscribe',
[470017]=[[Please use the following verification code to sign up for XMan emails:]],
[470018]=[[This code will remain valid for 24 hours.]],
[470019]=[[Do not share this code with anyone.]],
[470020]=[[If you have any questions, contact customer service on the XMan official Facebook page:]],
[470021]=[[Use the following verification code to sign up for emails:]],
[470022]='Unbound!',
[470023]=[[Failed to unbind!]],
[470024]=[[Verification Email]],
[470025]=[[Hero Clash]],
[470026]=[[Time Machine]],
[470027]=[[Your account is not bound to an email yet.]],
[470028]=[[Failed to send a verification code. The action is in cooldown.]],
[470029]=[[Failed to send a verification code. Please try again.]],
[470030]=[[The verification code has expired. Please get a new verification code.]],
[470031]=[[You have entered an incorrect verification code more than 10 times. Please get a new verification code.]],
[470032]=[[The account has been registered.]],
[470033]=[[The email address has been registered.]],
[560001]='Village',
[560002]='Mine',
[560003]='Manor',
[560004]='Factory',
[560005]='Town',
[560006]='Arsenal',
[560007]=[[Building Guild]],
[560008]='Library',
[560009]=[[Royal City]],
[560010]=[[City Ownership:]],
[560011]='Manage',
[560012]=[[Event starts in: {%s1}]],
[560013]=[[Recommended Troop CP: {%s1}]],
[560014]=[[Level {%s1}]],
[560015]=[[First Kill Reward]],
[560016]=[[Guaranteed Rewards]],
[560017]=[[Potential Rewards]],
[560018]=[[Please defeat Lv. {%s1} monsters first.]],
[560019]=[[Suggested Power: {%s1}]],
[560020]=[[Kill Reward]],
[560021]=[[Today's first kill rewards: ({%s1}/{%s2})]],
[560022]=[[No targets nearby for now.]],
[560023]=[[Collector: {%s1}]],
[560024]=[[Ends in {%s1}.]],
[560025]='CP:',
[560026]='Kills:',
[560027]='Union:',
[560028]='Like',
[560029]='Loot',
[560030]='Claimed',
[560031]=[[Unclaimed loot will disappear in {%s1}h. Please claim it promptly.]],
[560032]=[[Team Members]],
[560033]=[[Team leader]],
[560034]=[[Claim All]],
[560035]=[[Soldier Qty.]],
[560036]='March',
[560037]='Unknown',
[560038]=[[Quick Deploy]],
[560039]=[[Team up]],
[560040]=[[You haven't brought all the highest-level soldiers!]],
[560041]=[[Team formation time]],
[560042]='{%s1}min',
[560043]=[[Warning! Enemy's level is higher than your soldiers. Deployed heroes will be weakened.]],
[560044]=[[Rallying: {%s1}]],
[560045]=[[{%s1} km away]],
[560046]=[[Qty/Limit ({%s1}/{%s2})]],
[560047]=[[Team formed]],
[560048]=[[Waiting for Allies: {%s1}]],
[560049]='Attack',
[560050]='Tips',
[560051]=[[Exit the team?]],
[560052]=[[Remove the teammate?]],
[560053]=[[Unavailable. You're already in the team.]],
[560054]=[[New Team]],
[560055]='Return',
[560056]='Go',
[560057]=[[X: {%s1}]],
[560058]=[[Y: {%s1}]],
[560059]=[[Speed Up]],
[560060]='destination:',
[560061]='Teleport',
[560062]=[[Advanced Teleport: {%s1}]],
[560063]=[[You can relocate your castle anywhere.]],
[560064]=[[Location available]],
[560065]=[[Location unavailable]],
[560066]=[[Union gathering point]],
[560067]=[[Set Assembly point]],
[560068]=[[Return to home?]],
[560069]=[[This march will take over {%s1} minute(s). Confirm?]],
[560070]=[[No available teams. Recall Team 1 to the castle before marching.]],
[560071]=[[March time exceeds the target's remaining duration. No damage will be dealt. March anyway?]],
[560072]=[[Recover 1 Stamina every {%s1} seconds.]],
[560073]='Reinforce',
[560074]=[[No reinforcements.]],
[560075]=[[{%s1}'s Castle]],
[560076]=[[Reinforcements: {%s1}/{%s2}]],
[560077]=[[Total CP: {%s1}]],
[560078]=[[Reinforcement: Send troops to defend allied buildings against enemy attacks.]],
[560079]=[[1. You can reinforce your or your allies' buildings.]],
[560080]=[[2. Later-arriving reinforcements defend first.]],
[560081]=[[3. Reinforcements lose some soldiers when attacked.]],
[560082]=[[Reinforcements have arrived and started to assist you in defense.]],
[560083]=[[First Occupation Rewards]],
[560084]='Unoccupied',
[560085]='N.A.',
[560086]=[[Recover Stamina]],
[560087]='{%s1}/{%s2}',
[560088]=[[{%s1} Stamina]],
[560089]=[[Claim Cooldown: <color=#319f38>{%s1}h</color>]],
[560090]=[[Remaining Today: {%s1}/{%s2}]],
[560091]=[[Owned: {%s1}]],
[560092]='Use',
[560093]='Purchase',
[560094]=[[You want to choose?]],
[560095]=[[Queue March Time]],
[560096]=[[​Advanced March Speed Up]],
[560097]=[[Reduces the team's current march time by 50%.]],
[560098]=[[​Standard March Speed Up]],
[560099]=[[Reduces the team's current march time by 25%.]],
[560100]=[[Purchase and use]],
[560101]=[[No attempts left today.]],
[560102]=[[Purchase {%s2} Stamina with {%s1} Diamonds?]],
[560103]=[[No more claim attempts left today.]],
[560104]=[[10 Stamina]],
[560105]=[[Restores 10 of your Stamina.]],
[560106]=[[Purchase {%s2} with {%s1} Diamonds?]],
[560107]=[[Recover Stamina]],
[560108]=[[My Castle]],
[560109]='Allies',
[560110]='Leader',
[560111]=[[War Zone#]],
[560112]=[[Today's limit of 20 rewards for participating in a rally as a teammate has been reached. The limit refreshes daily at 0:00 (server time).]],
[560113]=[[Daily reward limit reached.]],
[560114]='Scout',
[560115]='Collecting',
[560116]=[[Unavailable. You've already joined a team.]],
[560117]=[[Rally complete.]],
[560118]=[[Team canceled.]],
[560119]=[[The team you joined has been canceled.]],
[560120]=[[You've been removed from the team.]],
[560121]=[[First Kill]],
[560122]=[[Random Relocation]],
[560123]=[[Randomly relocates anywhere on the world map.]],
[560124]=[[Qty. Gathered]],
[560125]=[[Gather Time]],
[560126]='world',
[560127]='Town',
[560128]='Squad',
[560129]=[[Basic Castle]],
[560130]=[[Increases march speed by {%s1}.]],
[560131]=[[Reduces march speed by {%s1}.]],
[560132]=[[Relocation Order]],
[560133]=[[Allows you to relocate anywhere on the world map.]],
[560134]=[[Union Relocation]],
[560135]=[[Automatically relocates to a rally point near your Union.]],
[560136]=[[Don't worry. Reinforcements have arrived and started to assist you in defense.]],
[560137]=[[50 Stamina]],
[560138]=[[Restores 50 of your Stamina.]],
[560139]=[[Enemy CP is unknown. Outcome unpredictable.]],
[560140]=[[Forces are evenly matched. Please proceed with caution.]],
[560141]=[[We hold a slight advantage, which gives us a chance.]],
[560142]=[[We vastly overpower them. Victory is assured.]],
[560143]=[[We are slightly outmatched. The battle may be tough.]],
[560144]=[[We are severely outmatched. Please think twice!]],
[560145]=[[Lv. 1 Iron Mine]],
[560146]=[[Lv. 2 Iron Mine]],
[560147]=[[Lv. 3 Iron Mine]],
[560148]=[[Lv. 4 Iron Mine]],
[560149]=[[Lv. 5 Iron Mine]],
[560150]=[[Lv. 6 Iron Mine]],
[560151]=[[Lv. 7 Iron Mine]],
[560152]=[[Lv. 8 Iron Mine]],
[560153]=[[Lv. 9 Iron Mine]],
[560154]=[[Lv. 10 Iron Mine]],
[560155]=[[Lv. 1 Field]],
[560156]=[[Lv. 2 Field]],
[560157]=[[Lv. 3 Field]],
[560158]=[[Lv. 4 Field]],
[560159]=[[Lv. 5 Field]],
[560160]=[[Lv. 6 Field]],
[560161]=[[Lv. 7 Field]],
[560162]=[[Lv. 8 Field]],
[560163]=[[Lv. 9 Field]],
[560164]=[[Lv. 10 Field]],
[560165]=[[Lv. 1 Gold Mine]],
[560166]=[[Lv. 2 Gold Mine]],
[560167]=[[Lv. 3 Gold Mine]],
[560168]=[[Lv. 4 Gold Mine]],
[560169]=[[Lv. 5 Gold Mine]],
[560170]=[[Lv. 6 Gold Mine]],
[560171]=[[Lv. 7 Gold Mine]],
[560172]=[[Lv. 8 Gold Mine]],
[560173]=[[Lv. 9 Gold Mine]],
[560174]=[[Lv. 10 Gold Mine]],
[560175]=[[Iron Mine Zombie]],
[560176]=[[Food Zombies]],
[560177]=[[Gold Zombie]],
[560178]=[[Elite Zombie]],
[560179]=[[Zombie Overlord]],
[560180]=[[Zombies that carry abundant Iron resources.]],
[560181]=[[Zombies that carry abundant Food resources.]],
[560182]=[[Zombies that carry abundant Gold resources.]],
[560183]=[[Powerful zombies that often carry rare resources.]],
[560184]=[[Powerful zombies that wander around.]],
[560185]='Special',
[560186]='Friend',
[560187]='Enemy',
[560188]='Attack',
[560189]='Hill',
[560190]='Sun',
[560191]='Cross',
[560192]='Clover',
[560193]='Shield',
[560194]='Triangle',
[560195]='Rhombus',
[560196]='Moon',
[560197]='Pentagram',
[560198]=[[Add Mark]],
[560199]='Personal',
[560200]='Union',
[560201]='Confirm',
[560202]='Delete',
[560203]='Go',
[560204]='Share',
[560205]='World',
[560206]='{%s1}',
[560207]=[[Set up Rally Point]],
[560208]=[[Union Rally Point]],
[560209]=[[Union Mark]],
[560210]=[[Invite Allies to Rally]],
[560211]='{%s1}{%s2}',
[560212]=[[{%s1} {%s2} {%s3}]],
[560213]='Confirm?',
[560214]=[[No marked locations.]],
[560215]=[[Marked! Marked locations can be found in Search.]],
[560216]='Unmarked!',
[560217]=[[Delete the marked location?]],
[560218]=[[{%s1} set up a new Union mark.]],
[560219]=[[{%s1} deleted a Union mark.]],
[560220]=[[New Union Mark]],
[560221]=[[New Union mark created.]],
[560222]=[[{%s1} set up a new Union mark at [X: {%s2}, Y: {%s3}].]],
[560223]=[[New Union Rally Point]],
[560224]=[[New Union rally point created.]],
[560225]=[[{%s1} set up a new rally point at [X: {%s2}, Y: {%s3}].]],
[560226]=[[Set <color=#FF0000>[X: {%s1}, Y: {%s2}]</color> as the new Union rally point?]],
[560227]=[[1. A Union Rally Point is where members spawn or relocate when responding to the leader's call.]],
[560228]=[[2. Only the leader can change its location. Please use it strategically.]],
[560229]=[[You've recently scouted this player, please try again later.]],
[560230]=[[Scout team departed.]],
[560231]=[[No idle scout teams, please try again later.]],
[560232]='Search',
[560233]='Zombie',
[560234]='Gather',
[560235]=[[Elite Zombie]],
[560236]=[[No targets nearby for now.]],
[560237]=[[Frenzy of War]],
[560238]=[[DMG +1%. Cannot activate shields for the duration.]],
[560239]=[[No available teams.]],
[560240]='Shield',
[560241]='Used!',
[560242]=[[Upon use, the shield will be reset to {%s1}h. Confirm?]],
[560243]=[[Invalid action. Cannot activate shield during Frenzy of War.]],
[560244]=[[Cannot attack a shielded target.]],
[560245]=[[Cannot rally against a shielded target.]],
[560246]=[[Cannot scout a shielded target.]],
[560247]=[[Scouting or attacking will disable your shield. Confirm?]],
[560248]=[[Deploy Garrison]],
[560249]='Marching...',
[560250]='Arrived',
[560251]=[[Cannot reinforce as you have already dispatched reinforcements.]],
[560252]=[[Current marked target doesn't exist. Please delete the invalid mark.]],
[560253]=[[Buff List]],
[560254]=[[Iron Ore]],
[560255]=[[Grain Field]],
[560256]=[[Gold Mine]],
[560257]='Reinforcing...',
[560258]=[[Cannot reinforce the target city as its reinforcements are maxed out.]],
[560259]=[[Are you sure you want to use {%s1}?]],
[560260]=[[{%s1} km]],
[560261]=[[Your castle cannot be attacked or scouted for 8 hours.\n<color=#E63838>Please note that troops outside the castle remain vulnerable.</color>]],
[560262]=[[Your castle cannot be attacked or scouted for 12 hours.\n<color=#E63838>Please note that troops outside the castle remain vulnerable.</color>]],
[560263]=[[Your castle cannot be attacked or scouted for 24 hours.\n<color=#E63838>Please note that troops outside the castle remain vulnerable.</color>]],
[560264]=[[Your castle cannot be attacked or scouted for 3 days.\n<color=#E63838>Please note that troops outside the castle remain vulnerable.</color>]],
[560265]='Create',
[560266]=[[Not deployed]],
[560267]=[[Rally Invitation]],
[560268]=[[Waiting in Rally: {%s1}]],
[560269]=[[Waiting for Allies: {%s1}]],
[560270]=[[{%s1} km away]],
[560271]=[[Rally Initiator: {%s1}]],
[560272]='Expired',
[560273]='Used',
[560274]=[[Another player has sent a team to gather here. A battle may occur. Confirm?]],
[560275]='Rallying...',
[560276]=[[Target is shielded. Troops returning.]],
[560277]=[[Target no longer exists. Troops returning.]],
[560278]=[[Cannot attack allies. Troops returning.]],
[560279]=[[Your castle was destroyed. Troops returning.]],
[560280]=[[The team you joined has been canceled.]],
[560281]=[[Your reinforcements have been dismissed.]],
[560282]=[[Team Return]],
[560283]=[[Tapping too fast. Please wait.]],
[560284]='Claim',
[560285]='Chat',
[560286]=[[Troop Info]],
[560287]=[[Not enough troops. Troop stats reduced to {%s1}.]],
[560288]=[[Soldier Level]],
[560289]='Amount',
[560290]='Tip',
[560291]=[[Your castle has been destroyed. Relocating now...]],
[560292]='Waiting...',
[560293]=[[Rally team canceled.]],
[560294]=[[Union Rally Point set successfully.]],
[560295]=[[Resources Plundered Today: {%s1}]],
[560296]=[[Plundered Resources Reduced to: {%s1}]],
[560297]=[[Resets in: {%s1}]],
[560298]=[[Dismiss ally reinforcements?]],
[560299]=[[Troops are defeated and now returning. Further actions unavailable.]],
[560300]=[[Operation unavailable as the highest challengeable level has been selected.]],
[560301]=[[Operation unavailable as the lowest level has been selected.]],
[560302]=[[No marked locations.]],
[560303]=[[You've gained {%s1} Stamina.]],
[560304]=[[The monster will disappear before the troops arrive. Dispatch anyway?]],
[560305]=[[Insufficient Diamonds to purchase Stamina.]],
[560306]=[[Since daily rally rewards are all claimed, you cannot earn more rewards. Proceed anyway?]],
[560307]=[[Requires a Lv. {%s1} or higher castle to challenge.]],
[560308]=[[8H Shield]],
[560309]=[[12H Shield]],
[560310]=[[24H Shield]],
[560311]=[[3D Shield]],
[560312]='Share',
[560313]=[[Stamina Return]],
[560314]=[[The attack has been canceled. {%s1} Stamina has now been returned.]],
[560315]='Assist',
[560316]='Protect',
[560317]='Gather',
[560318]='Garrison',
[560319]='Cancel',
[560320]='Back',
[560321]='City',
[560322]='ATK',
[560323]='Accelerate',
[560324]='Town',
[560325]='Rally',
[560326]='Skills',
[560327]='Rank',
[560328]='Confirm',
[560329]='Cancel',
[560330]='Search',
[560331]='Appearance',
[560332]='Details',
[560333]=[[Declare War]],
[560334]='Reinforcement',
[560335]='Scout',
[560336]='Rewards',
[560337]='Build',
[560338]='Move',
[560339]=[[Cancel War Declaration]],
[560340]='Start',
[560341]=[[Intelligence Challenge]],
[560342]=[[Resource Collection]],
[560343]=[[Full-Scale Attack]],
[560344]='Steal',
[560345]='Info',
[560346]=[[1. [Hero Combat Power] is the combat power calculation of the hero attribute interface; it includes all the combat power that can be provided to the hero attribute interface development system, such as equipment, special weapons, summoned beasts, honor walls, decorations, etc.\n2. [Soldier Combat Power] is the combat power calculation of the actual soldiers going to battle\n3. The attributes provided by [Technology] take effect according to the situation in the battle and are not directly displayed in the combat power of this interface]],
[560347]=[[Unlocks at Castle Lv. {%s1}]],
[560348]=[[The Union member {%s1} canceled the team.]],
[560349]=[[Warning: You are relocating to the Contaminated Area. After moving, Intelligence will expire and shields cannot be activated. Proceed?]],
[560350]='Alert',
[560351]=[[Scout Alert]],
[560352]=[[Screen Effects]],
[560353]='Marching...',
[560354]='Events',
[560355]=[[Coming soon!]],
[560356]=[[The current team has already departed. Please wait for their return.]],
[560357]=[[Insufficient soldiers]],
[560358]=[[Train Soldier]],
[560359]=[[Lite Mode]],
[560360]=[[Lord, you've claimed {%s1} Stamina]],
[560361]=[[{%s1}'s rally team has already departed.]],
[560362]=[[The team has joined {%s1}'s rally.]],
[560363]=[[{%s1} has disappeared]],
[560364]=[[{%s1}: Rally Invitation]],
[560365]=[[You've already joined a rally and cannot join again.]],
[560366]=[[You've left the rally.]],
[560367]=[[Rally has ended. Cannot join now.]],
[560368]=[[Target not found. Cannot redirect.]],
[560369]=[[Owned: {%s1}]],
[600001]=[[Union Gift​]],
[600002]=[[Loot Chest]],
[600003]=[[Common Ally Gift]],
[600004]=[[Advanced Ally Gift]],
[600005]=[[Rare Ally Gift]],
[600006]=[[Unique Ally Gift]],
[600007]=[[Epic Ally Gift]],
[600008]=[[Legendary Ally Gift]],
[600009]=[[ Auto Rally]],
[600010]=[[Quick Build]],
[600011]=[[Quick Build]],
[600012]=[[Help Boost]],
[600013]=[[Quick Gather]],
[600014]=[[Iron Production]],
[600015]=[[Food Production]],
[600016]=[[Gold Production]],
[600017]=[[Quick Research]],
[600018]=[[Research Cost]],
[600019]=[[Quick Craft]],
[600020]=[[Building Cost]],
[600021]=[[Troop Burden]],
[600022]=[[Quick Train]],
[600023]=[[Training Cost]],
[600024]=[[Wild Interception]],
[600025]=[[Speedy Siege]],
[600026]=[[Quick Garrison]],
[600027]=[[Troop March]],
[600028]=[[Rally HP]],
[600029]=[[Rally ATK]],
[600030]=[[Rally DEF]],
[600031]=[[Treatment Cost]],
[600032]=[[Garrison HP]],
[600033]=[[Garrison ATK]],
[600034]=[[Garrison DEF]],
[600035]=[[Drill Ground Expansion]],
[600036]=[[Offline members auto-join <color=#f2875e>rallies</color> against enemies including Doom Elites.]],
[600037]=[[Increases Union members' construction efficiency.]],
[600038]=[[Increases Union members' construction efficiency.]],
[600039]=[[Increases Union assistance. Each assist now provides extra time reduction.]],
[600040]=[[Increases Union members' gathering speed.]],
[600041]=[[Increases Union members' Iron output in the castle.]],
[600042]=[[Increases Union members' Food output in the castle.]],
[600043]=[[Increases Union members' Gold output in the castle.]],
[600044]=[[Increases Union members' technology research speed.]],
[600045]=[[Reduces Union members' technology research resource cost.]],
[600046]=[[Increases Union members' gear crafting speed.]],
[600047]=[[Reduces Union members' gear crafting resource cost.]],
[600048]=[[Increases Union members' troop load.]],
[600049]=[[Increases Union members' soldier training speed.]],
[600050]=[[Reduces Union members' soldier training resource cost.]],
[600051]=[[Increases Union members' troop march speed when attacking another player's castle.]],
[600052]=[[Increases Union members' troop march speed when attacking a neutral city.]],
[600053]=[[Increases Union members' troop march speed when reinforcing an allied castle.]],
[600054]=[[Increases Union members' troop march speed in all scenarios.]],
[600055]=[[Increases HP of Union members' heroes in rally teams.]],
[600056]=[[Increases ATK of Union members' heroes in rally teams.]],
[600057]=[[Increases DEF of Union members' heroes in rally teams.]],
[600058]=[[Reduces Union members' soldier treatment resource cost.]],
[600059]=[[Increases HP of Union members' heroes in garrison teams.]],
[600060]=[[Increases ATK of Union members' heroes in garrison teams.]],
[600061]=[[Increases DEF of Union members' heroes in garrison teams.]],
[600062]=[[Increases Union members' Drill Ground capacity.]],
[600063]=[[Normal Donation]],
[600064]=[[Diamond Donation]],
[600065]='CP',
[600066]='Kill',
[600067]=[[Daily Contribution]],
[600068]=[[Weekly Contribution]],
[600069]=[[Change Union Banner]],
[600070]=[[Change Union Name]],
[600071]=[[Change Union Abbreviation]],
[600072]=[[Change Union Language]],
[600073]=[[Disband Union]],
[600074]=[[Transfer Presidency]],
[600075]=[[Edit Announcements]],
[600076]=[[Invite to Rally]],
[600077]=[[Change Join Conditions]],
[600078]=[[Set up Union Rally Point]],
[600079]=[[Send Union Mail]],
[600080]=[[Change Join Method]],
[600081]=[[Remove Members]],
[600082]=[[Upgrade Union Technology]],
[600083]=[[Recommend Union Technology]],
[600084]=[[Union Mark]],
[600085]=[[Declare War]],
[600086]=[[Remove Reinforcements]],
[600087]=[[Abandon Occupied Cities]],
[600088]=[[Adjust Member Rank]],
[600089]=[[View Members]],
[600090]=[[Leave Union]],
[600091]=[[{%s1} initiated a rally.]],
[600092]=[[{%s1} killed Zombie Overlord.]],
[600093]=[[{%s1} purchased {%s2}.]],
[600094]=[[Daily Donation Rewards]],
[600095]=[[Weekly Donation Rewards]],
[600096]=[[Leadership Induction Notice]],
[600097]=[[Title Appointment Notice]],
[600098]=[[City Occupation Reward reissue]],
[600099]=[[Dear Lord, you ranked No. {%s1} in yesterday's Daily Donation Leaderboard. Here are your rewards.]],
[600100]=[[Dear Lord, you ranked No. {%s1} in yesterday's Daily Donation Leaderboard. Here are your rewards.]],
[600101]=[[Dear Lord, you have become the new Leader. May your Union thrive under your leadership!]],
[600102]=[[Dear Lord, {%s1} has been appointed as the Union's {%s2}. May they serve the Union well.]],
[600103]=[[Dear Lord, unclaimed city occupation rewards have now been sent to you.]],
[600104]='Common',
[600105]='Rare',
[600106]='Fine',
[600107]='Unique',
[600108]='Epic',
[600109]='Legendary',
[600110]='Mythic',
[600111]='Leader',
[600112]=[[Union War]],
[600113]=[[Union City]],
[600114]='Members',
[600115]=[[Union Gift​]],
[600116]=[[Strength Ranking]],
[600117]=[[Union Technology]],
[600118]=[[Union Store]],
[600119]=[[Union Mutual-Help]],
[600120]='Rally',
[600121]=[[Union Badge]],
[600122]='Log',
[600123]='Settings',
[600124]=[[Calls the entire Union to the rally point <color=#E44A2C>{%s1}</color>.]],
[600125]=[[The Leader invites you to the rally point.]],
[600126]='Relocate',
[600127]=[[The Leader invites you to the rally point <color=#E44A2C>{%s1}</color>. It requires <color=#1F88EA>​Union Relocation​ x1</color> (free for the first time). Relocate now?]],
[600128]=[[The Leader invites you to the rally point <color=#E44A2C>{%s1}</color>. It requires <color=#1F88EA>Union Relocation x1</color>. Relocate now?]],
[600129]=[[{%s1} {%s2} attacked {%s3} {%s4}'s castle {%s5} and achieved a glorious victory!]],
[600130]=[[{%s1} {%s2} attacked {%s3} {%s4}'s castle {%s5} but suffered defeat!]],
[600131]=[[{%s1} {%s2} attacked {%s3} {%s4}'s castle {%s5}, but our defenses held strong!]],
[600132]=[[{%s1} {%s2} attacked {%s3} {%s4}'s castle {%s5}, and we regrettably lost it.]],
[600133]=[[What an uneventful day. Enjoy it!]],
[600134]=[[Union Permissions]],
[600135]=[[Request List]],
[600136]=[[Union Settings]],
[600137]=[[Union list]],
[600138]=[[Invitation Link]],
[600139]=[[Union Mail]],
[600140]=[[Leave Union]],
[600141]=[[World Channel]],
[600142]='简体中文',
[600143]=[[Union Channel]],
[600144]=[[Share your Union to increase your size!]],
[600145]=[[You automatically become the Leader since the former leader has gone offline for a long time!]],
[600146]=[[Tap to enter.]],
[600147]=[[Contains prohibited characters.]],
[600148]=[[Union Announcement]],
[600149]=[[Union Permissions]],
[600150]=[[Invalid name length.]],
[600151]=[[Name already exists.]],
[600152]=[[Invalid name]],
[600153]=[[Please enter a name (max 16 characters).]],
[600154]=[[The abbreviation must consist of upper/lower case letters or numbers (max 3 characters).]],
[600155]=[[No approval needed.]],
[600156]=[[Requires approval.]],
[600157]='Development',
[600158]='War',
[600159]=[[Union Technology]],
[600160]=[[Donation Rewards]],
[600161]=[[Technology EXP required.]],
[600162]=[[Set up Recommendations]],
[600163]=[[Cancel Recommendations]],
[600164]='Donation',
[600165]=[[Current Effects]],
[600166]=[[Next-Level Effects]],
[600167]='Go',
[600168]=[[Union Mutual-Help]],
[600169]=[[Help me build a Lv. {%s1} {%s2}.]],
[600170]=[[Help me heal some wounded troops.]],
[600171]=[[Help me research {%s1}.]],
[600172]=[[Help All]],
[600173]=[[Help allies with building construction, upgrades, and technology research. Each help boosts current progress by 1% (minimum 1 minute reduction).]],
[600174]=[[Upgrade Time Remaining]],
[600175]='Research',
[600176]=[[Max level reached.]],
[600177]=[[Strength Ranking]],
[600178]='CP',
[600179]='Kill',
[600180]='Donation',
[600181]='Rank',
[600182]='Name',
[600183]='Kill',
[600184]=[[Daily Donation]],
[600185]=[[Weekly Donation]],
[600186]=[[Daily Donation]],
[600187]=[[Weekly Donation]],
[600188]=[[Donation Ranking Rewards]],
[600189]=[[Rewards require at least {%s1} contribution(s).]],
[600190]=[[Rewards require at least {%s1} contribution(s).]],
[600191]=[[Reward Settled in:]],
[600192]=[[Union Gift​]],
[600193]=[[Claim a Union Gift ​and upgrade it! A higher level grants rarer rewards.]],
[600194]=[[Defeat elite enemies to obtain loot chests.]],
[600195]=[[{%s1}'s Loot Chest]],
[600196]=[[1. Lords can donate Gold and Diamonds to advance Union technology and fill the EXP bar. Once the bar is full, the Union can start to research the technology.]],
[600197]=[[2. Each donation (Gold or Diamonds) will grant 50 Union technology EXP and 50 Union Contribution points. There is also a chance for a CRIT, which grants more technology EXP. CRIT chances are:\nNo CRIT - 45%\nDouble CRIT (extra 50 technology EXP) - 30%\nTriple CRIT (extra 100 technology EXP) - 15%\nQuintuple CRIT (extra 200 technology EXP) - 10%]],
[600198]=[[3. Lords can store up to 20 opportunities to donate Gold for Union technology. Each donation, regardless of a CRIT, consumes one opportunity. When the available donations drop below 20, one opportunity is granted every 20 minutes.]],
[600199]=[[4. There is no limit to daily Diamond donations for Union technology, but the required Diamonds increase with each donation. The first Diamond donation of the day costs 2 Diamonds, with each subsequent donation costing an additional 2 Diamonds until the cost reaches 50.]],
[600200]='Rules:',
[600201]=[[1. When 3 members in the Union donate 1,000 points, the Ranking Rewards will be automatically activated.]],
[600202]=[[2. After Ranking Rewards are activated, Union members with 1,000 donation points are eligible for the rewards.]],
[600203]=[[3. Donation ranking rewards will be sent via mail after settlement.]],
[600204]=[[{PlayerName} initiated a rally.]],
[600205]=[[{%s1} purchased <color=#319F38>{%s2}</color>.]],
[600206]=[[Purchasing a pack containing a Union Gift ​grants the entire Union a reward.]],
[600207]=[[Gift Anonymously]],
[600208]=[[New arrivals in <color=87f425>{%s1}</color>!]],
[600209]=[[War God]],
[600210]='Diplomat',
[600211]='Goddess',
[600212]='Strategist',
[600213]=[[War God is the strongest member and determines the CP ceiling of your Union.]],
[600214]=[[Brings fresh blood to your Union.]],
[600215]=[[The Union's most charismatic individual.]],
[600216]=[[From territory management and event coordination to diplomacy, all will be handled with expertise.]],
[600217]=[[Remove Title]],
[600218]=[[Appoint Title]],
[600219]='Chat',
[600220]='Appoint',
[600221]='Kick',
[600222]='Promote/Demote',
[600223]=[[Max members for this rank.]],
[600224]=[[Transfer leadership to this Lord?]],
[600225]=[[Remove this Lord from your Union?]],
[600226]='Rallying...',
[600227]=[[Waiting for allies...]],
[600228]=[[{%s1} km away]],
[600229]='Qty/Limit',
[600230]=[[Team Intelligence]],
[600231]=[[Solo Intelligence]],
[600232]=[[Team formed]],
[600233]=[[Remove the teammate?]],
[600234]=[[Disband the team?]],
[600235]=[[Exit the team?]],
[600236]=[[Union Ranking]],
[600237]=[[Cannot have the same name as another Union.]],
[600238]=[[Max 3 characters (letters/numbers only).]],
[600239]=[[No other Unions.]],
[600240]=[[Tip: Ranking updates have delays.]],
[600241]=[[No Union]],
[600242]=[[Expires in 1 day.]],
[600243]=[[No invitations from any Union.]],
[600244]=[[Select a banner you like.]],
[600245]=[[Union Banner]],
[600246]=[[Received declaration of war.]],
[600247]='Occupied',
[600248]='Occupiable',
[600249]=[[City Buffs]],
[600250]=[[Cities Owned:]],
[600251]=[[Upon occupying a city, Union members gain a buff.]],
[600252]=[[Current Buffs:]],
[600253]=[[Occupy City]],
[600254]=[[Union-Wide Victory Rewards]],
[600255]=[[Siege Participation Rewards]],
[600256]=[[R4 are executives who assist with Union management.]],
[600257]=[[R3 members are the backbone of a Union.]],
[600258]=[[R2 represents the majority, a crucial force in building up a Union.]],
[600259]=[[R1 members are newcomers with a lot of potential.]],
[600260]=[[CP Requirements:]],
[600261]=[[Castle Requirements:]],
[600262]=[[No available Unions, please try again later.]],
[600263]='Leader:',
[600264]='Members:',
[600265]='Language:',
[600266]='CP:',
[600267]='Establish',
[600268]='Required',
[600269]=[[Application Approval]],
[600270]=[[Remove Members]],
[600271]=[[minute(s) ago]],
[600272]=[[hour(s) ago]],
[600273]=[[day(s) ago]],
[600274]=[[Joined the Union]],
[600275]=[[Remove/Appoint Title]],
[600276]=[[Today's Contribution]],
[600277]=[[Union Ranking]],
[600278]=[[Total CP]],
[600279]=[[Attempts: Unlimited]],
[600280]='Charges:',
[600281]=[[Required Time for Research]],
[600282]=[[Confirm Rally]],
[600283]=[[Respond to Rally]],
[600284]=[[1-Minute Building Speed Up]],
[600285]=[[5-Minute Building Speed Up]],
[600286]=[[1-Hour Building Speed Up]],
[600287]=[[8-Hour Building Speed Up]],
[600288]=[[1-Minute Research Speed Up]],
[600289]=[[5-Minute Research Speed Up]],
[600290]=[[1-Hour Research Speed Up]],
[600291]=[[8-Hour Research Speed Up]],
[600292]=[[1-Minute Soldier Speed Up]],
[600293]=[[5-Minute Soldier Speed Up]],
[600294]=[[1-Hour Soldier Speed Up]],
[600295]=[[8-Hour Soldier Speed Up]],
[600296]=[[1-Minute Universal Speed Up]],
[600297]=[[5-Minute Universal Speed Up]],
[600298]=[[1-Hour Universal Speed Up]],
[600299]=[[8-Hour Universal Speed Up]],
[600300]=[[Union Contribution]],
[600301]=[[Help your allies upgrade buildings, research technologies, and heal wounded soldiers. The effect of each help is determined by the higher of the following two values:]],
[600302]=[[1. 0.5% of the total progress]],
[600303]=[[2. Direct help time, with a minimum base of 1 minute]],
[600304]=[[Each tab can only display a limited number of Union gifts]],
[600305]=[[1. Lords can donate Gold and Diamonds to advance Union technology and fill the EXP bar. Once the bar is full, the Union can start to research the technology.]],
[600306]=[[2. Each donation (Gold or Diamonds) will advance the Union technology EXP by 50 points and grant 50 Union Contribution points. There is also a chance for a CRIT, which grants more technology EXP. CRIT chances are:]],
[600307]=[[No CRIT - 45%]],
[600308]=[[Double CRIT (extra 50 technology EXP) - 30%]],
[600309]=[[Triple CRIT (extra 100 technology EXP) - 15%]],
[600310]=[[Quintuple CRIT (extra 200 technology EXP) - 10%]],
[600311]=[[3. Lords can store up to 20 opportunities to donate Gold for Union technology. Each donation, regardless of a CRIT, consumes one opportunity. When the available donations drop below 20, one opportunity is granted every 20 minutes.]],
[600312]=[[4. There is no limit to daily Diamond donations for Union technology, but the required Diamonds increase with each donation. The first Diamond donation of the day costs 2 Diamonds, with each subsequent donation costing an additional 2 Diamonds until the cost reaches 50.]],
[600313]='Rules:',
[600314]=[[1. When 3 members in the Union donate 6,000 points, the Ranking Rewards will be automatically activated.]],
[600315]=[[2. After Ranking Rewards are activated, Union members with 6,000 donation points are eligible for the rewards.]],
[600316]=[[3. Donation ranking rewards will be sent via mail after settlement.]],
[600317]=[[Union Mutual-Help]],
[600318]=[[Union Gift​]],
[600319]=[[Union Technology]],
[600320]=[[Union Donation Ranking Rewards]],
[600321]='Development',
[600322]='Economy',
[600323]='Heroes',
[600324]='Soldier',
[600325]=[[Team 1]],
[600326]=[[Team 2]],
[600327]=[[Team 3]],
[600328]=[[Team 4]],
[600329]=[[Union Duel]],
[600330]=[[Trade Wagon]],
[600331]=[[Super Soldier]],
[600332]=[[City Occupation Reward Reissue]],
[600333]=[[Dear Lord, your unclaimed rewards will be sent to your bag.]],
[600334]=[[Transfer Presidency]],
[600335]=[[Dear Lord, your leader has been inactive for a long time, so the system has automatically transferred the leadership to you. Here's hoping you can lead the Union to new glory.]],
[600336]=[[Daily Donation Rewards]],
[600337]=[[Dear Lord, you ranked {%s1} in yesterday's Union Donation Leaderboard. Please claim your reward.]],
[600338]=[[Weekly Donation Rewards]],
[600339]=[[Dear Lord, you ranked {%s1} in this week's Union Donation Leaderboard. Please claim your reward.]],
[600340]=[[1-Minute Healing Speed Up​]],
[600341]=[[5-Minute Healing Speed Up​]],
[600342]=[[1-Hour Healing Speed Up]],
[600343]=[[8-Hour Healing Speed Up]],
[600344]=[[To all Union members, <color=#ed810f>{%s1}</color> has been appointed as <color=#ed810f>{%s3}</color> by the leader {%s2}. Here's hoping he/she can lead all members in exploring this post-apocalyptic world.]],
[600345]=[[Leader Transfer Notice]],
[600346]='Members',
[600347]='Level',
[600348]='Manage',
[600349]=[[Quick Join]],
[600350]='Send',
[600351]='Union',
[600352]='Language',
[600353]=[[Manage Union]],
[600354]=[[Rare Gift]],
[600355]='Online',
[600356]=[[Recommended Unions]],
[600357]='Join',
[600358]='Create',
[600359]=[[Union Abbreviation]],
[600360]='Replace',
[600361]='Daily',
[600362]='Weekly',
[600363]=[[Minimum level required]],
[600364]=[[Minimum CP]],
[600365]=[[Union Size]],
[600366]=[[Union Language]],
[600367]=[[View Members]],
[600368]='Apply',
[600369]=[[Contact Leader]],
[600370]=[[Probability disclosure]],
[600371]=[[Gift Level — Lv. {%s1}]],
[600372]=[[Dear Lord,]],
[600373]=[[1. Leaving the Union will prevent you from receiving Union gifts.]],
[600374]=[[2. Leaving the Union will prevent you from benefiting from Union technology bonuses.]],
[600375]=[[3. You will be unable to participate in most Union events within 24 hours of rejoining a Union.]],
[600376]='Applied',
[600377]=[[Length does not meet the requirements]],
[600378]=[[Duplicate name with another Union]],
[600379]=[[Name contains prohibited characters]],
[600380]=[[Insufficient diamonds]],
[600381]=[[Today's Help Points Rewards]],
[600382]=[[Reduced Time]],
[600383]=[[You've been removed from the Union. Don't worry, many other great Unions await you.]],
[600384]=[[Adjust Rank]],
[600385]=[[Transfer Leader Role]],
[600386]='Claimed',
[600387]=[[Does not meet the Union's requirements]],
[600388]=[[Please try again later]],
[600389]=[[Union created]],
[600390]=[[Union Joined]],
[600391]=[[Allies, thank you very much for your help.]],
[600392]=[[{%s1} has provided Speed Up help for your {%s2}]],
[600393]='Sent',
[600394]='Modified',
[600395]=[[Union language changed]],
[600396]=[[Union abbreviation changed]],
[600397]=[[Union name changed]],
[600398]=[[You have reached the daily mail sending limit]],
[600399]=[[Mail sending is in cooldown]],
[600400]=[[Please enter the mail content first]],
[600401]=[[Please enter the Union name or abbreviation]],
[600402]=[[Please enter the announcement content first]],
[600403]=[[Please enter the Union name or abbreviation]],
[600404]=[[Invalid Name]],
[600405]=[[Invalid Abbreviation]],
[600406]=[[Invalid Language]],
[600407]=[[Invalid Banner]],
[600408]=[[Pop-up user information]],
[600409]=[[No permission]],
[600410]=[[After careful consideration, {%s1} is now transferring the leader position to {%s2}. Hope {%s2} will be thoughtful and proficient in management, leading the entire Union to achieve great success!]],
[600411]=[[Sold Out]],
[600412]='Build',
[600413]='Research',
[600414]='Healing',
[600415]=[[Leader cannot leave the Union]],
[600416]=[[Insufficient Gold donations]],
[600417]=[[No help content available]],
[600418]=[[Requires Union Gift Lv. 5 to unlock]],
[600419]=[[No applications received yet]],
[600420]=[[Your ally has purchased {%s1}]],
[600421]=[[Your ally]],
[600422]='Claimed',
[600423]=[[Send invitation link]],
[600424]=[[Iron Ore Protection]],
[600425]=[[Food Protection]],
[600426]=[[Gold Protection]],
[600427]=[[Senior Craftsman]],
[600428]=[[Offline Members auto-dispatch Teams to Rally]],
[600429]=[[Building Speed Up]],
[600430]=[[Extra Time Reduction per Help]],
[600431]=[[Gathering Speed]],
[600432]=[[Iron Mine Output Speed Increase]],
[600433]=[[Food Output Speed Increase]],
[600434]=[[Gold Mine Output Speed Increase]],
[600435]=[[Technology Research Speed Up]],
[600436]=[[Technology Resource Cost Reduction]],
[600437]=[[Iron Ore Protection Capacity Increase]],
[600438]=[[Food Protection Capacity Increase]],
[600439]=[[Gold Protection Capacity Increase]],
[600440]=[[Gear Factory Crafting Speed Up]],
[600441]=[[Gear Crafting Gold Cost Reduction]],
[600442]=[[Building Resource Cost Reduction]],
[600443]=[[Load Increase]],
[600444]=[[Soldier Training Speed Increase]],
[600445]=[[Soldier Training Resource Cost Reduction]],
[600446]=[[March Speed Increase When Attacking Other Lords]],
[600447]=[[March Speed Increase When Attacking Cities]],
[600448]=[[March Speed Increase When Garrisoning]],
[600449]=[[March Speed Increase]],
[600450]=[[HP Increase When Rallying]],
[600451]=[[ATK Increase When Rallying]],
[600452]=[[DEF Increase When Rallying]],
[600453]=[[Healing Resource Cost Reduction]],
[600454]=[[HP Increase When Garrisoning]],
[600455]=[[ATK Increase When Garrisoning]],
[600456]=[[DEF Increase When Garrisoning]],
[600457]=[[Drill Ground Capacity Increase]],
[600458]=[[4. Confirm to leave the Union?]],
[600459]=[[You have {%s1}m {%s2}s remaining before you can join another Union. Please try again later.]],
[600460]=[[Greetings, everyone!]],
[600461]=[[Is anyone up for the event?]],
[600462]=[[Just joined! Excited for your support!]],
[600463]='English',
[600464]='Français',
[600465]='Deutsch',
[600466]='Русский',
[600467]='한국어',
[600468]='ไทย',
[600469]='日本語',
[600470]='Português',
[600471]='Español',
[600472]='Türkçe',
[600473]='繁體中文',
[600474]='Italiano',
[600475]='العربية',
[600476]=[[Tiếng Việt]],
[600477]='简体中文',
[600478]=[[Bahasa Indonesia]],
[600479]=[[Union members eliminate a total of {%s1} elite zombies]],
[600480]=[[Occupied {%s1} cities]],
[600481]=[[Union members have completed a total of {%s1} Intelligence Quests]],
[600482]=[[Completed research of Union technology: Auto Rally]],
[600483]=[[Completed research of {%s1} Union technologies]],
[600484]=[[Enable auto-research]],
[600485]=[[Guess who put out the fire in your castle? It was me!]],
[600486]=[[Union Goals Rewards Reissue]],
[600487]=[[Dear Lord, you have unclaimed Union Goals Rewards. Here's what you have left to claim:]],
[600488]=[[Union members have reached {%s1}]],
[600489]=[[Mutual assists among Union members have reached {%s1}]],
[600490]=[[Total Union CP has reached {%s1}]],
[600491]=[[Total Union technology donations have reached {%s1}]],
[600492]=[[Union members have completed a total of {%s1} hero recruitments]],
[600493]=[[Union gift level has reached {%s1}]],
[600494]=[[Union Goals]],
[600495]='Countdown',
[600496]='Go',
[600497]=[[Completion Time]],
[600498]='Claimed',
[600499]='Claim',
[600500]='Uncompleted',
[600501]=[[Unlock Countdown]],
[600502]=[[Goals ended]],
[600503]=[[Castle Fallen]],
[600504]=[[We're sorry, Lord. We fought bravely, but the castle fell at {%s1}. During this time:]],
[600505]=[[{%s1} enemies attacked the castle {%s2} times]],
[600506]=[[Enemy {%s1} has attacked the castle {%s2} times]],
[600507]=[[{%s1} and other allies assisted your castle]],
[600508]=[[Ally {%s1} assisted your castle]],
[600509]=[[Your leader {%s1} has sent the following supplies to help you rebuild your home]],
[600510]=[[Got it]],
[600511]=[[Full-scale Attack Switch]],
[600512]=[[Union Technology Auto Research]],
[600513]=[[Invitation List]],
[600514]='View',
[600515]=[[Invite to Join]],
[600516]=[[Invite the player to join the Union? They can join directly without approval once the invite is sent.]],
[600517]=[[Almost there! Keep going to claim the reward!]],
[600518]='Manage',
[600519]=[[Your ally has sent the following supplies to help you rebuild your home:]],
[600520]=[[Full of Talent]],
[600521]=[[Mutual Assistance]],
[600522]=[[Formidable Army]],
[600523]=[[United in Effort]],
[600524]=[[Auto Rally]],
[600525]=[[Eliminate Threats]],
[600526]=[[Defeat Boss]],
[600527]=[[Long Live Technology]],
[600528]=[[Recruit Hero]],
[600529]=[[Build the Union]],
[600530]=[[Expand the Territory]],
[600531]=[[Defeat Boss 2]],
[600532]='Tip',
[600533]=[[Dear Lord, welcome to the Union!]],
[600534]=[[Relocate to the Union rally point and grow stronger with your fellow members!]],
[600535]=[[This relocation is free!]],
[600536]=[[Dear Lord, skipping this free relocation will limit your participation in Union events and rewards. Please think carefully!]],
[600537]=[[Union Goals - {%s1}]],
[600538]=[[Unlocks after {%s1}]],
[600539]=[[Goals Locked]],
[600540]='{%s1}',
[600541]=[[Unlocks after building the Union Center]],
[600542]=[[Join Union]],
[600543]=[[Win great rewards]],
[600544]=[[Joining Rewards]],
[600545]=[[Joining a Union <color=#319f38>significantly reduces building and research times</color>]],
[600546]=[[Joining a Union allows you to <color=#319f38>claim many chests</color> daily]],
[600547]=[[Joining a Union for the first time rewards you with]],
[600548]='List',
[600549]=[[The current leader is inactive! We recommend you join another Union.]],
[600550]='Join',
[600551]='Cancel',
[600552]=[[You are now the leader]],
[600553]=[[Quick Join]],
[600554]='Create',
[600555]=[[Joining Rewards]],
[600556]=[[Congratulations on joining the Union! Here are your rewards:]],
[600557]='Invited',
[600558]=[[Is anyone there?]],
[600559]=[[Hi, everyone!]],
[600560]=[[Thanks for taking me in, is anyone around? Please give me all the help you can, thank you!]],
[600561]=[[Nice to meet you all!]],
[600562]=[[Hi everyone, anyone up for helping defeat some monsters?]],
[600563]=[[Thanks for letting me join!]],
[600564]=[[Event has ended.]],
[600565]=[[Leader Transfer Successful]],
[600566]=[[Let's greet our new ally!]],
[600567]=[[Auto Transfer]],
[600568]=[[Tap to claim]],
[600569]='Unoccupied',
[600570]=[[Join Conditions]],
[600571]=[[Home Level]],
[600572]=[[Join Method]],
[600573]=[[Auto-Clear Inactive Members]],
[600574]=[[This leader left nothing behind]],
[600575]=[[Help, my castle is on fire!]],
[600576]='Recipient',
[600577]='Body',
[600578]='Title',
[600579]=[[No recipient selected]],
[600580]=[[Invalid title]],
[600581]=[[Can only request help after joining the Union]],
[600582]=[[Your ally has brought you a Union gift]],
[600583]=[[Rally Invitation Successful]],
[600584]=[[Your castle is too far away. The Union Leader invites you to the rally point <color=#319f38>{%s1}</color>. Do you wish to relocate?]],
[600585]=[[Have requested help from all allies]],
[600586]=[[Union Switch Notice]],
[600587]=[[Dear Lord, to enhance your experience, we've transferred you to a more active Union based on recent Union Activity Points.\nEnjoy the following benefits in your new Union:\n\n<color=#319f38>Speed Up Buildings and Research:</color> Join a Union to <color=#317BC7>request help</color> from your allies. The more active your Union is, the <color=#317BC7>faster your building and research timers</color> will be!\n\n<color=#319f38>Union Gifts:</color> Whenever your allies <color=#317BC7>Defeat a Boss</color> or <color=#317BC7>Purchase Packs</color>, you'll receive Union Gifts filled with valuable resources.\n\n<color=#319f38>Tech Bonuses:</color> Active Unions unlock higher-level Union Technology, giving you powerful bonuses like <color=#317BC7>faster research</color> and <color=#317BC7>march speed</color>.\n\n<color=#319f38>Union Store:</color> The more active your Union, the more Union Contribution Points you'll receive. Use them in the Union Store to purchase <color=#317BC7>high-value items!</color>]],
[600588]=[[Operation unavailable while in a cross-server state. Please try again after returning to your server.]],
[600589]=[[Invite to Gather]],
[600590]=[[<color=#58f5ff>{%s1}</color> member(s) are too far away.]],
[600591]=[[Quick Relocate]],
[600592]=[[You are <color=#23932a>close enough</color> to the rally point <color=#c032df>{%s1}</color> for union event participation. No more relocation is needed.]],
[600593]=[[You are <color=#23932a>far from</color> the rally point <color=#c032df>{%s1}</color> for union event participation. Please use Union Relocation to find a suitable location!]],
[600594]=[[Use 1 Union Relocation to move to the Union's rally point? (Currently owned: <color=#58f5ff>{%s1}</color>)]],
[600595]=[[Use 1 Union Relocation to move to the Union's rally point? (<color=#c032df>Free</color>)]],
[600596]=[[{%s1} km]],
[600597]=[[No gifts available to claim]],
[600598]='Hindi',
[600600]=[[Please leave the current union before proceeding]],
[600601]='Invitation',
[600602]=[[Dear {%s1}]],
[600603]=[[It is detected that your current union is not active enough, which may affect resource benefits. Here are some new active unions recommended for you. Come join!]],
[600604]=[[Get this reward after joining ({%s1}).]],
[600605]=[[Union Members]],
[600606]=[[Join Now]],
[600607]=[[Apply to join]],
[600608]=[[The union is full. You cannot join.]],
[600609]=[[The current union no longer exists]],
[600610]=[[This union is full]],
[600611]=[[No permission]],
[600612]=[[Union invitation]],
[600613]=[[Member invitation]],
[600614]='Invite',
[600615]=[[Click the button to share union information to the chat channel]],
[600616]=[[Sharing on cooldown]],
[600617]=[[Join us now!]],
[600618]=[[Union Sharing]],
[600619]=[[Union Invitation Sharing]],
[600620]=[[Request submitted. Please wait.]],
[600901]='中文',
[600902]='English',
[600903]='Deutsch',
[600904]='Français',
[600905]=[[Bahasa Indonesia]],
[600906]=[[Bahasa Melayu]],
[600907]='ไทยไไทไทย',
[600908]='Русский',
[600909]='Español',
[600910]='हिन्दी',
[600911]='한국어',
[600912]='繁體中文',
[600913]='日本語',
[600914]=[[Tiếng Việt]],
[600915]='Português',
[600916]='العربية',
[600917]='Italiano',
[600918]='Türkçe',
[600919]='Polski',
[600920]='Filipino',
[600921]='Nederlands',
[601000]='Castle',
[601001]='Farm',
[601002]=[[Iron Mine]],
[601003]=[[Gold Mine]],
[601004]=[[Training Ground]],
[601005]='Barracks',
[601006]=[[Drill Ground]],
[601007]='Hospital',
[601008]=[[1st Team]],
[601009]=[[Builder's Hut]],
[601010]=[[Mini Games]],
[601011]=[[Summoning Hall]],
[601012]=[[Giant's Shop]],
[601013]=[[Survivor Hall]],
[601014]=[[Union Center]],
[601015]=[[Castle Gate]],
[601200]=[[Symbol of the Territory]],
[601201]=[[Plant Wheat]],
[601202]=[[Mine Iron]],
[601203]=[[Mine Gold]],
[601204]=[[Generate Hero EXP]],
[601205]=[[Train Soldiers]],
[601206]=[[Soldier Rally Point]],
[601207]=[[Heal Wounded Soldiers]],
[601208]=[[Deploy Troops]],
[601209]=[[Extend Free Construction Speedup Time]],
[601210]=[[Mini Game Portal]],
[601211]=[[Upgrade to Reduce Free Recruitment Cooldown]],
[601212]=[[Use Currency to Redeem Various Items]],
[601213]=[[Boosts Survivor Abilities]],
[601214]=[[Ally Assistance]],
[601215]=[[Set Up Defense Troops]],
[601216]=[[Spend Diamonds to Instantly Finish]],
[601217]=[[Complete Now]],
[601218]='Technology',
[601219]=[[Research Queue]],
[601220]='Idle',
[601221]=[[2nd Research Center: Doubles Your Research Speed]],
[601222]=[[Quick Speedup]],
[601223]=[[Economy Rec.]],
[601224]=[[Combat Rec.]],
[601225]=[[Unlocks after building the 2nd Research Center.]],
[601226]=[[2nd Team]],
[601227]=[[3rd Team]],
[601228]=[[4th Team]],
[601229]=[[EXP Bonus]],
[601230]=[[Hero Level Cap]],
[601231]=[[Base Time:]],
[601232]=[[Food Yield per Hour]],
[601233]=[[Max Production Time]],
[601234]=[[Iron Yield per Hour]],
[601235]=[[Gold Yield per Hour]],
[601236]=[[EXP Yield per Hour]],
[601237]=[[Building Details]],
[601238]='Survivors',
[601239]=[[Quick Dispatch]],
[601240]=[[Total Speedup:]],
[601241]='Economy',
[601242]='Military',
[601243]='Decoration',
[601244]=[[Allows {%s3} Buildings When {%s1} Reaches Lv. {%s2}.]],
[601245]=[[Construction Queue]],
[601246]=[[1st Construction Queue]],
[601247]=[[2nd Construction Queue]],
[601248]=[[3rd Construction Queue]],
[601249]=[[4th Construction Queue]],
[601250]=[[<color=#FF4D2A>Permanently Unlocks</color> the 2nd Construction Queue - Develop Faster!]],
[601251]=[[<color=#FF4D2A>Permanently Unlocks</color> the 3rd Construction Queue – Develop Faster!]],
[601252]=[[<color=#FF4D2A>Permanently Unlocks</color> the 4th Construction Queue – Develop Faster!]],
[601253]=[[Rent for 2 Hours]],
[601254]=[[Unlock permanently]],
[601255]=[[Lord, your Construction Queues can be managed here. Unlock additional queues to boost development or speed up construction instantly.]],
[601256]=[[Lv. {%s1} {%s2} is Upgrading]],
[601257]=[[Time Remaining:]],
[601258]=[[Activate Queue]],
[601259]=[[Unlock New Building(s)]],
[601260]=[[Lord, spend {%s1} Diamonds to finish now?]],
[601261]=[[Team Load Capacity]],
[601262]='Training',
[601263]=[[Soldier Promotion]],
[601264]='Promote',
[601265]=[[Train Soldiers]],
[601266]=[[Max Soldiers in Training]],
[601267]=[[Training Soldier Level]],
[601268]=[[Soldier Count]],
[601269]=[[Inside Home]],
[601270]=[[Outside Home]],
[601271]=[[No Soldiers Deployed Outside]],
[601272]=[[Hospital Capacity]],
[601273]=[[Gravely Wounded Soldiers]],
[601274]=[[No Injured Soldiers]],
[601275]=[[Healing in Progress:]],
[601276]=[[General March Speed Boost]],
[601277]=[[Free Speedup Time]],
[601278]=[[Free Hero Recruitment Cooldown]],
[601279]=[[Helps Received]],
[601280]='Construction',
[601281]=[[Purchase the pack to unlock the queue permanently.]],
[601282]=[[Research Center]],
[601283]=[[2nd Research Center]],
[601284]=[[Research technologies to boost your Home's capabilities.]],
[601285]=[[Research Speed]],
[601286]=[[Requires Lv. {%s2} {%s1}.]],
[601287]=[[Requires Lv. {%s2} {%s1}.]],
[601288]=[[Clear dangerous tile No. {%s1}.]],
[601289]=[[Building limit reached.]],
[601290]=[[For every 1% Morale advantage against the enemy, your troops deal 1% bonus damage.]],
[601291]=[[Each point of Load increases the maximum resources you can carry during gathering and plundering.]],
[601292]=[[Increases HP for all heroes in the troop.]],
[601293]=[[Increases ATK for all heroes in the troop.]],
[601294]=[[Increases DEF for all heroes in the troop.]],
[601295]=[[Unlocks at Barracks Lv. {%s1}.]],
[601296]=[[Complete Technology Research: {%s1}]],
[601297]='Train',
[601298]=[[{%s1} Upgraded]],
[601299]='Repair',
[601300]=[[Food Depot]],
[601301]=[[Iron Depot]],
[601302]=[[Gold Vault]],
[601303]=[[Enhancement Well]],
[601304]=[[Material Workshop]],
[601305]=[[Gear Workshop]],
[601306]=[[Mystic Beast Spring]],
[601307]=[[Mystic Beast Grounds]],
[601308]='Scout',
[601309]=[[Intelligence Center]],
[601310]=[[Forest Altar]],
[601311]=[[Human Altar]],
[601312]=[[Nightfall Altar]],
[601313]=[[Must be placed within the construction zone.]],
[601314]=[[Conflicts with the placement of another building.]],
[601315]=[[Go to Research]],
[601316]=[[Wall Status:]],
[601317]=[[If the defense fails, each attacking troop will deal 1,000 damage to your City Defense.]],
[601318]=[[During a defense battle, idle soldiers within the city will automatically reinforce the defending team. If the Castle falls, soldiers stationed in the Drill Grounds will take siege damage and may die instantly.\n1. The higher your Castle level, the more siege damage it suffers.\n2. The higher the level of the soldiers in the Drill Grounds, the fewer casualties they take from the same amount of siege damage.]],
[601319]=[[Survivor List]],
[601320]='All',
[601321]=[[Obtain Survivors]],
[601322]=[[Filter Survivors]],
[601323]='Golden',
[601324]='Purple',
[601325]='Dispatched',
[601326]=[[Not Dispatched]],
[601327]=[[Now Owned]],
[601328]=[[Survivor Dispatched]],
[601329]='Notes:',
[601330]=[[1. You can receive help from allies up to 200 times per day.]],
[601331]=[[2. If you manually adjust the number of wounded soldiers to treat only a portion, the system will save the required treatment time as the default for the next treatment.]],
[601332]=[[3. If you choose to treat all wounded soldiers, the system will reset the preset time and default to treating all soldiers the next time.]],
[601333]=[[4. If you don't have enough resources to treat all wounded soldiers, the system will automatically calculate the number of soldiers that can be treated based on the current resources.]],
[601335]=[[Upgrade {%s1} to Lv. {%s2} to build.]],
[601336]=[[Insufficient {%s1}.]],
[601337]=[[Insufficient resources]],
[601338]=[[City Defense]],
[601339]=[[City Defense decreases at a slower rate while burning.]],
[601340]=[[City Defense recovery rate increased.]],
[601341]='Burning',
[601342]=[[The Castle has fallen and the walls are in flames, gradually losing City Defense.]],
[601343]=[[Training in progress. Promotion will be available once training is complete!]],
[601344]=[[Protects a portion of Food from being plundered.]],
[601345]=[[Protects a portion of iron from being plundered.]],
[601346]=[[Protects a portion of gold from being plundered.]],
[601347]=[[Produces Enhancement Stones.]],
[601348]=[[Produces Gear crafting materials.]],
[601349]=[[Crafts Gear.]],
[601350]=[[Crafts Lv. 1 Mystic Beast Accessory Chest.]],
[601351]=[[Upgrades Mystic Beasts.]],
[601352]=[[Scout World Intel.]],
[601353]=[[Explore the World.]],
[601354]=[[Boosts stats for Forest Heroes.]],
[601355]=[[Boosts stats for Human Heroes.]],
[601356]=[[Boosts stats for Nightfall Heroes.]],
[601357]=[[Food Protection Capacity]],
[601358]=[[Iron Protection Capacity]],
[601359]=[[Gold Protection Capacity]],
[601360]=[[Enhancement Stones per Hour]],
[601361]=[[Gear Crystals per Hour]],
[601362]=[[Mark Generation Time]],
[601363]=[[Forest Hero Troop Capacity]],
[601364]=[[Forest Hero HP]],
[601365]=[[Forest Hero ATK]],
[601366]=[[Forest Hero DEF]],
[601367]=[[Human Hero Troop Capacity]],
[601368]=[[Human Hero HP]],
[601369]=[[Human Hero ATK]],
[601370]=[[Human Hero DEF]],
[601371]=[[Nightfall Hero Troop Capacity]],
[601372]=[[Nightfall Hero HP]],
[601373]=[[Nightfall Hero ATK]],
[601374]=[[Nightfall Hero DEF]],
[601375]=[[Build the 2nd Research Center to double your research speed.]],
[601376]=[[No soldiers stationed inside home.]],
[601377]=[[No soldiers stationed outside home.]],
[601378]=[[Wall Status: Intact]],
[601379]=[[Wall Status: Burning - Losing City Defense at {%s1}/sec]],
[601380]=[[Wall Status: Repairing — Regaining City Defense at {%s1}/sec.]],
[601381]=[[Defending teams will engage invading forces in the order from 1 to 4!]],
[601382]=[[Soldiers inside home: {%s1}]],
[601383]=[[Target is under protection. Operation unavailable.]],
[601384]=[[You're currently under protection. Continuing will remove this status.]],
[601385]=[[Producing resources. Please wait patiently.]],
[601386]=[[Cannot build on a road.]],
[601387]=[[Soldier training complete!]],
[601388]=[[Soldier promotion complete!]],
[601389]=[[Join the Defense]],
[601390]=[[Dangerous tile No. {%s1}]],
[601391]=[[A reward shrouded in mist, waiting for the brave to claim it.]],
[601392]=[[While burning, your Castle will continue losing City Defense.]],
[601393]=[[Soldier CP]],
[601394]=[[Mystic Beast Skill CP]],
[601395]=[[1. Hero CP reflects the combat power displayed in the Hero Stats interface, including all enhancements from Gear, Exclusive Gear, Mystic Beasts, Honor Wall, Decorations, and other hero progression systems.\n2. Soldier CP is calculated based on the hero leading a full team of max-level soldiers.\n3. Technology bonuses apply conditionally in combat and are not directly included in this interface's CP calculation.]],
[601396]=[[The current team is on a mission. Heroes cannot be changed at this time.]],
[601397]=[[My Lord, the fire is steadily reducing the Castle's durability. What are your orders?]],
[601398]=[[Request Assistance]],
[601399]=[[Put Out the Fire Ourselves]],
[601400]=[[The Castle is Burning]],
[601401]=[[Forest units take <color=#FF4D2A>20%</color> less damage from Nightfall units.]],
[601402]=[[Nightfall units take <color=#FF4D2A>20%</color> less damage from Human units.]],
[601403]=[[Human units take <color=#FF4D2A>20%</color> less damage from Forest units.]],
[601404]=[[Loot Accumulation]],
[601405]=[[Loot Resource Output]],
[601406]=[[Adventure Journey]],
[601407]=[[Adventure Journey Rules:/n1. Congratulations! You've completed all home explorations. You can now send heroes to take on higher-level challenges for even greater rewards./n2. Clear Adventure stages to earn first-clearance rewards and unlock AFK rewards based on your highest stage cleared—the further you go, the better the rewards./n3. AFK rewards can accumulate for up to 8 hours. Be sure to collect them in time. Assigning Survivors can extend the maximum accumulation time.]],
[601408]=[[AFK Rewards]],
[601409]=[[AFK Time]],
[601410]='{%s1}/h',
[601411]=[[Claim Rewards]],
[601412]=[[AFK rewards can accumulate for up to {%s1} hours.]],
[601413]='Claim',
[601414]=[[Honor Level]],
[601415]='Title',
[601446]=[[Hero Bonus]],
[601447]=[[Troop Type Bonus]],
[601448]=[[Building level too low.]],
[601449]=[[Taj Mahal]],
[601450]=[[Sydney Opera House]],
[601451]=[[The Taj Mahal, a radiant jewel in Agra, India, stands as one of the Seven Wonders of the World.]],
[601452]=[[The Sydney Opera House is world-renowned for its iconic sail-shaped design and is one of the most prestigious centers for performing arts.]],
[601453]=[[Hunter's wagon, can help us collect the spoils]],
[601454]=[[Decoration Gallery]],
[601455]=[[View your decoration collection progress here.]],
[601456]=[[Multiple Survivors have been quick-dispatched.]],
[601457]=[[No available Survivors to dispatch at the moment.]],
[601458]=[[Mist Areas Cleared: 102]],
[601459]='Assist',
[601460]=[[Loot Accumulation Time]],
[601461]=[[New Survivor arrived!]],
[601462]=[[A wounded girl awaits rescue!]],
[601463]=[[Expedition Truck]],
[601464]=[[Acorn Tavern]],
[601465]='Deploy',
[601466]='Upgrade',
[601467]='Decorate',
[601468]='Buff',
[601469]='Train',
[601470]='Soldier',
[601471]='Healing',
[601472]='Construction',
[601473]='Warfare',
[601474]=[[Form Team]],
[601475]='Recruit',
[601476]=[[City Defense]],
[601477]='Research',
[601478]='Crafting',
[601479]=[[Mystic Beast]],
[601480]='Medal',
[601481]='Rescue',
[601482]='Expedition',
[601483]='Overview',
[601484]='Codex',
[601485]=[[Current Collection Progress]],
[601486]=[[Total Decoration CP]],
[601487]=[[Decoration Bonuses]],
[601488]=[[ATK Stats]],
[601489]=[[DEF Stats]],
[601490]=[[Development Stats]],
[601491]=[[Level: {%s1}]],
[601492]=[[Building: {%s1/%s2}]],
[601493]='Select',
[601494]=[[Filter Decorations]],
[601495]='All',
[601496]='Owned',
[601497]=[[Now Owned]],
[601498]=[[Decoration Stats]],
[601499]=[[Redeem Decorations]],
[601500]=[[Redeem {%s1} using Universal Components.]],
[601501]=[[Every {%s1} components can redeem 1 decoration.]],
[601502]=[[Total Hero CP boosted by Decorations]],
[601503]=[[This decoration is not in possession.]],
[601504]=[[Get Now]],
[601505]=[[All queues are currently under construction.]],
[601506]=[[{%s1} {%s2} has entered your territory.]],
[601507]=[[{%s1} day(s)]],
[601508]=[[ATK Stats]],
[601509]=[[DEF Stats]],
[601510]=[[Development Stats]],
[601511]=[[Decoration providing this bonus.]],
[601512]=[[Not enough soldiers available for promotion.]],
[601513]=[[Unlock more team slots by purchasing the Deluxe Monthly Card!]],
[601514]=[[Please reclaim the more forward areas first.]],
[601515]=[[Treatment complete. {%s1} soldiers are healed.]],
[601516]=[[Team 1]],
[601517]=[[Team 2]],
[601518]=[[Team 3]],
[601519]=[[Team 4]],
[601520]=[[Faction Bonus]],
[601521]=[[Bonus unlocks after the first Imperial City Battle.]],
[601522]=[[My Lineup]],
[601523]=[[Enemy Lineup]],
[601524]=[[Deploy 3 heroes from the same faction.]],
[601525]=[[Deploy 3 heroes from one faction and 2 from another.]],
[601526]=[[Deploy 4 heroes from the same faction.]],
[601527]=[[Deploy 5 heroes from the same faction.]],
[601528]=[[In Preview]],
[601529]=[[Resource Replenishment Notice]],
[601530]=[[You are about to use a significant number of Diamonds to purchase resources, which offers poor cost efficiency. We recommend more economical methods for replenishment. Explore alternative options? (Do not show again during this session)]],
[601531]=[[Continue Purchase]],
[601532]=[[Replenish Resources]],
[601533]=[[Spend <color=#2EC564>250 Diamonds</color> to restore 2,500 City Defense]],
[601534]=[[Help from Allies]],
[601535]=[[Received help from <color=#FDE01B><size=40>{%s1}</size></color> generous allies]],
[601536]=[[Any Research Center]],
[601537]=[[Any Altar]],
[602001]='Confirm',
[602002]='Cancel',
[602003]='Back',
[602004]='Go',
[602005]='Complete',
[602006]='Claim',
[602007]=[[Tap a blank space to close]],
[602008]='Rewards',
[602009]='CP',
[602010]='Share',
[602011]='Accept',
[602012]='Reject',
[602013]=[[Claim All]],
[602014]='Lv.{%s1}',
[602015]='+',
[602016]='Required',
[602017]='{%s1}/{%s2}',
[602018]=[[Finish Now]],
[602019]='Upgrade',
[602020]='Get',
[602021]='CP',
[602022]=[[Speed Up]],
[602023]=[[Instant Speed-up]],
[602024]='Use',
[602025]=[[Purchase and use]],
[602026]=[[Owned: {%s1}]],
[602027]=[[{%s1}% Value]],
[602028]='New',
[602029]='Time',
[602030]='Building',
[602031]='{%s1}S',
[602032]='Tips',
[602033]=[[Don't remind me again today]],
[602034]='Morale',
[602035]=[[Congratulations, you've got]],
[602036]='Claimed',
[602037]='({%s1}/{%s2})',
[602038]='Help',
[602039]='Stage',
[602040]='NO.{%s1}',
[602041]='Purchase',
[602042]='Construct',
[602043]='Research',
[602044]='Train',
[602045]='Promote',
[602046]='Heal',
[602047]='Refresh',
[602048]='Skip',
[602049]='Search',
[602050]='Select',
[602051]='Activate',
[602052]='Unlock',
[602053]='Change',
[602054]='Reset',
[602055]='Accept',
[602056]='Delete',
[602057]='Send',
[602058]='Details',
[602059]='Join',
[602060]=[[Quick Join]],
[602061]='Kick',
[602062]='Claim',
[602063]='Switch',
[602064]='Free',
[602065]='Matching',
[602066]='Like',
[602067]='Preview',
[602068]='Calendar',
[602069]='Goals',
[602070]='Log',
[602071]='Settings',
[602072]='Rank',
[602073]='Battle',
[602074]='Recruit',
[602075]='Donate',
[602076]='View',
[602077]=[[Help All]],
[602078]=[[Quick Deploy]],
[602079]=[[Battle Log]],
[602080]=[[Enter Battlefield]],
[602081]=[[Take All]],
[602082]='Lord',
[602083]='Wheat',
[602084]='Iron',
[602085]='Gold',
[602086]='Diamond',
[602087]='Resource',
[602088]='Stamina',
[602089]='World',
[602090]='Town',
[602091]='Quests',
[602092]=[[Main Quest]],
[602093]=[[Side Quest]],
[602094]=[[Daily Quests]],
[602095]='Store',
[602096]='Mail',
[602097]='Bag',
[602098]='Hero',
[602099]=[[Rec. CP]],
[602100]='Kills',
[602101]='Victory',
[602102]='Defeat',
[602103]='Online',
[602104]='Offline',
[602105]='Level',
[602106]='EXP',
[602107]='Instructions',
[602108]='Intro',
[602109]=[[Personal Rewards]],
[602110]=[[Union Rewards]],
[602111]=[[Ranking Rewards]],
[602112]=[[My Rank]],
[602113]=[[Union Ranking]],
[602114]=[[Strength Ranking]],
[602115]=[[Highest Damage]],
[602116]='Points',
[602117]='Log',
[602118]='History',
[602119]='Rules',
[602120]='Season',
[602121]='Drop',
[602122]='Battle',
[602123]=[[Local Time]],
[602124]=[[Server Time]],
[602125]='Backstory',
[602126]=[[Event Overview]],
[602127]=[[Special Rules]],
[602128]=[[Participation Conditions]],
[602129]=[[Upgrade Requirements]],
[602130]=[[Unlock Advanced Rewards]],
[602131]=[[Guaranteed Rewards]],
[602132]=[[Possible Rewards]],
[602133]='N/A',
[602134]='Selected',
[602135]=[[In Use]],
[602136]='Loading',
[602137]='Inactive',
[602138]='Incomplete',
[602139]=[[Owned: {%s1}]],
[602140]=[[Cooldown: {%s1}]],
[602141]=[[Purchase Limit: {%s1}]],
[602142]=[[Remaining Today: {%s1}]],
[602143]=[[Time Left: {%s1}]],
[602144]=[[Attempts left: {%s1}]],
[602145]=[[Validity: {%s1}]],
[602146]=[[Tap any blank space to continue]],
[602147]=[[The event has ended]],
[602148]=[[Insufficient Diamonds]],
[602149]='Deploy',
[602150]=[[Cooldown countdown: {%s1}]],
[602151]='{%s1}h',
[602152]='{%s1}m',
[602153]='{%s1}s',
[602154]='Battle',
[602155]='Completed',
[602156]=[[No. {%s1}]],
[602157]=[[No. {%s1}-No. {%s2}]],
[602158]='{%s1}d',
[602159]='Kingdom',
[602160]='Current',
[602161]='Owned:',
[602162]='Owned:',
[602163]='Castle',
[602164]='Rewards',
[602165]=[[Reward Details]],
[602166]=[[Available Rewards]],
[602167]='Confirm',
[602168]='New',
[602501]=[[Blue Gold level Chest]],
[602502]=[[Purple Gold Level Chest]],
[602503]=[[Golden Gold Level Chest]],
[602504]=[[Blue Steel Level Chest]],
[602505]=[[Purple Steel Level Chest]],
[602506]=[[Gold Steel Level Chest]],
[602507]=[[Blue Food Level Chest]],
[602508]=[[Purple Food Level Chest]],
[602509]=[[Gold Food Level Chest]],
[602510]=[[Blue Resource Selection Chest]],
[602511]=[[Purple Resource Selection Chest]],
[602512]=[[Gold Resource Selection Chest]],
[602513]=[[1k Food]],
[602514]=[[10k Food]],
[602515]=[[50k Food]],
[602516]=[[1k Iron Ore]],
[602517]=[[10k Iron Ore]],
[602518]=[[50k Iron Ore]],
[602519]=[[1k Gold]],
[602520]=[[10k Gold]],
[602521]=[[50k Gold]],
[602522]=[[Limited purchases with Union Contributions]],
[602523]='Go',
[602524]=[[Quantity collected:]],
[602525]='Claim',
[602526]='Owned:',
[602527]='Use',
[602528]='Requirements',
[602529]=[[to proceed.]],
[602530]=[[Insufficient Resources]],
[602531]=[[Replenish All]],
[602532]=[[Use Items]],
[602533]=[[No Items available]],
[602534]=[[Union Store]],
[602535]=[[Insufficient Items]],
[602536]='Purchase',
[602537]='Limited',
[602538]='Gather',
[602539]=[[Collecting can obtain a large amount of specific resources!]],
[602540]=[[Home Output]],
[602541]=[[No access channel]],
[602542]=[[Limited purchases for VlP]],
[602543]=[[Unlimited purchases with Diamonds]],
[602544]=[[Home Harvest Resources]],
[602545]=[[Participate in the Arms Race to get massive rewards]],
[602546]=[[Participate in union duels to get massive rewards]],
[602547]=[[Limited redemption available in Expedition Shop]],
[602548]=[[Limited redemption available in Union Shop]],
[602549]=[[Upgrade buildings to gain massive hero EXP]],
[602550]=[[Complete Intelligence Quests to win rewards]],
[602551]=[[Intelligence Quests]],
[602552]=[[Available from Hero Recruitment]],
[602553]=[[Zombies carrying large amounts of resources]],
[602554]=[[Limited redemption available in Honor Shop]],
[602555]=[[Participate in the Novice Arena to get]],
[602556]=[[Participate in 3V3 Arena to get]],
[602557]=[[Participate in Peak Arena to get]],
[602558]=[[Participate in Faction Trial to get]],
[602559]=[[Complete quests to earn a lot of rewards]],
[602560]=[[Craft materials in the Gear Workshop]],
[602561]=[[Craft gear in the Gear Workshop]],
[602562]=[[Insufficient items to convert materials]],
[602563]=[[Training Ground Gains]],
[602564]=[[Enhancement Well Gains]],
[602565]=[[Mystic Beast Spring Gains]],
[602566]=[[Material Workshop Gains]],
[602567]=[[Gold Mine Gains]],
[602568]=[[Iron Mine Gains]],
[602569]=[[Farm Gains]],
[603001]=[[Increase Construction Speed]],
[603002]=[[Unlocked at Research Center Lv. {%s1}]],
[603003]=[[Progress {%s1} Reaches {%s2}]],
[603004]=[[Lv.{%s1} {%s2} completed]],
[603005]=[[Hero HP]],
[603006]=[[Hero ATK]],
[603007]=[[Hero DEF]],
[603008]=[[Use items to speed up {%s1}]],
[603009]=[[Spend diamonds to speed up {%s1}]],
[603010]=[[No speed up items available]],
[603011]=[[The research queue is full]],
[603012]='Construct',
[603013]='Get',
[603014]=[[Total Speed Up]],
[603015]=[[Research efficiency]],
[603016]=[[Current time:]],
[603017]=[[Original time:]],
[603018]=[[Double your research speed]],
[603019]=[[Medal of Glory]],
[603020]=[[High-speed Construction 1]],
[603021]=[[Hospital Expansion 1]],
[603022]=[[Barrack Expansion 1]],
[603023]=[[Research Speed Up 1]],
[603024]=[[Rapid Treatment 1]],
[603025]=[[Training Speed Up 1]],
[603026]=[[Professional Craftsman]],
[603027]=[[High-speed Construction 2]],
[603028]=[[Hospital Expansion 2]],
[603029]=[[Barrack Expansion 2]],
[603030]=[[Research Speed Up 2]],
[603031]=[[Rapid Treatment 2]],
[603032]=[[Training Speed Up 2]],
[603033]=[[Additional Barrack]],
[603034]=[[High-speed Construction 3]],
[603035]=[[Hospital Expansion 3]],
[603036]=[[Barrack Expansion 3]],
[603037]=[[Research Speed Up 3]],
[603038]=[[Rapid Treatment 3]],
[603039]=[[Training Speed Up 3]],
[603040]=[[Efficient Treatment]],
[603041]=[[High-speed Construction 4]],
[603042]=[[Hospital Expansion 4]],
[603043]=[[Barrack Expansion 4]],
[603044]=[[Research Speed Up 4]],
[603045]=[[Rapid Treatment 4]],
[603046]=[[Training Speed Up 4]],
[603047]=[[Drill Ground Expansion]],
[603048]=[[Survival Skill]],
[603049]=[[Food Production 1]],
[603050]=[[Iron Production 1]],
[603051]=[[Gold Production 1]],
[603052]=[[Food Collection 1]],
[603053]=[[Iron Ore Collection 1]],
[603054]=[[Gold Collection 1]],
[603055]=[[Additional Farmland]],
[603056]=[[Food Production 2]],
[603057]=[[Iron Production 2]],
[603058]=[[Gold Production 2]],
[603059]=[[Food Collection 2]],
[603060]=[[Iron Ore Collection 2]],
[603061]=[[Gold Collection 2]],
[603062]=[[Additional Iron Mine]],
[603063]=[[Food Production 3]],
[603064]=[[Iron Production 3]],
[603065]=[[Gold Production 3]],
[603066]=[[Food Collection 3]],
[603067]=[[Iron Ore Collection 3]],
[603068]=[[Gold Collection 3]],
[603069]=[[Additional gold mine]],
[603070]=[[Food Production 4]],
[603071]=[[Iron Production 4]],
[603072]=[[Gold Production 4]],
[603073]=[[Food Collection 4]],
[603074]=[[Iron Ore Collection 4]],
[603075]=[[Gold Collection 4]],
[603076]=[[Food Protection]],
[603077]=[[Iron Ore Protection]],
[603078]=[[Gold Protection]],
[603079]=[[Forest Specialization 1]],
[603080]=[[Forest ATK Enhancement 1]],
[603081]=[[Forest DEF Enhancement 1]],
[603082]=[[Forest HP Enhancement 1]],
[603083]=[[Human Specialization 1]],
[603084]=[[Human ATK Enhancement 1]],
[603085]=[[Human DEF Enhancement 1]],
[603086]=[[Human HP Enhancement 1]],
[603087]=[[Nightfall Specialization 1]],
[603088]=[[Nightfall ATK Enhancement 1]],
[603089]=[[Nightfall DEF Enhancement 1]],
[603090]=[[Nightfall HP Enhancement 1]],
[603091]=[[Forest Specialization 1]],
[603092]=[[Forest ATK Enhancement 1]],
[603093]=[[Forest DEF Enhancement 1]],
[603094]=[[Forest HP Enhancement 1]],
[603095]=[[Human Specialization 2]],
[603096]=[[Human ATK Enhancement 2]],
[603097]=[[Human DEF Enhancement 2]],
[603098]=[[Human HP Enhancement 2]],
[603099]=[[Nightfall Specialization 2]],
[603100]=[[Nightfall ATK Enhancement 2]],
[603101]=[[Nightfall DEF Enhancement 2]],
[603102]=[[Nightfall HP Enhancement 2]],
[603103]=[[Practical Training]],
[603104]=[[Weapon Enhancement]],
[603105]=[[Armor Enhancement]],
[603106]=[[Physique Enhancement]],
[603107]=[[Offensive Training 3]],
[603108]=[[Defensive Training 3]],
[603109]=[[Armor Upgrade 3]],
[603110]=[[Heavy Load Training 3]],
[603111]=[[Offensive Training 4]],
[603112]=[[Defensive Training 4]],
[603113]=[[Armor Upgrade 4]],
[603114]=[[Heavy Load Training 4]],
[603115]=[[Offensive Training 5]],
[603116]=[[Defensive training 5]],
[603117]=[[Armor Upgrade 5]],
[603118]=[[Heavy Load Training 5]],
[603119]=[[Offensive Training 6]],
[603120]=[[Defensive Training 6]],
[603121]=[[Armor Upgrade 6]],
[603122]=[[Heavy Load Training 6]],
[603123]=[[Offensive Training 7]],
[603124]=[[Defensive Training 7]],
[603125]=[[Armor Upgrade 7]],
[603126]=[[Heavy Load Training 7]],
[603127]=[[Ace Team 1]],
[603128]=[[Virus Resistance Training 1]],
[603129]=[[Team Offensive Training 1]],
[603130]=[[Team Defensive Training 1]],
[603131]=[[Team Physique Training 1]],
[603132]=[[Resource Harvesting 1]],
[603133]=[[Convoy Load Capacity 1]],
[603134]=[[Zombie Hunter 1]],
[603135]=[[Ambush Tactics 1]],
[603136]=[[Resource Hunter 1]],
[603137]=[[Rapid March 1]],
[603138]=[[Rapid Assault 1]],
[603139]=[[Counterattack 1]],
[603140]=[[Quick Response 1]],
[603141]=[[Impenetrable Defense 1]],
[603142]=[[Fighting Spirit 1]],
[603143]=[[Hold the Line 1]],
[603144]=[[Combat Skills 1]],
[603145]=[[Energy Surge 1]],
[603146]=[[Efficient Killing 1]],
[603147]=[[Ace Team 2]],
[603148]=[[Virus Resistance Training 2]],
[603149]=[[Team Offensive Training 2]],
[603150]=[[Team Defensive Training 2]],
[603151]=[[Team Physique Training 2]],
[603152]=[[Resource Harvesting 2]],
[603153]=[[Convoy Load Capacity 2]],
[603154]=[[Zombie Hunter 2]],
[603155]=[[Ambush Tactics 2]],
[603156]=[[Resource Hunter 2]],
[603157]=[[Rapid March 2]],
[603158]=[[Rapid Assault 2]],
[603159]=[[Counterattack 2]],
[603160]=[[Quick Response 2]],
[603161]=[[Impenetrable Defense 2]],
[603162]=[[Fighting Spirit 2]],
[603163]=[[Hold the Line 2]],
[603164]=[[Combat Skills 2]],
[603165]=[[Energy Surge 2]],
[603166]=[[Efficient Killing 2]],
[603167]=[[Ace Team 3]],
[603168]=[[Virus Resistance Training 3]],
[603169]=[[Team Offensive Training 3]],
[603170]=[[Team Defensive Training 3]],
[603171]=[[Team Physique Training 3]],
[603172]=[[Resource Harvesting 3]],
[603173]=[[Convoy Load Capacity 3]],
[603174]=[[Zombie Hunter 3]],
[603175]=[[Ambush Tactics 3]],
[603176]=[[Resource Hunter 3]],
[603177]=[[Rapid March 3]],
[603178]=[[Rapid Assault 3]],
[603179]=[[Counterattack 3]],
[603180]=[[Quick Response 3]],
[603181]=[[Impenetrable Defense 3]],
[603182]=[[Fighting Spirit 3]],
[603183]=[[Hold the Line 3]],
[603184]=[[Combat Skills 3]],
[603185]=[[Energy Surge 3]],
[603186]=[[Efficient Killing 3]],
[603187]=[[Ace Team 4]],
[603188]=[[Virus Resistance Training 4]],
[603189]=[[Team Offensive Training 4]],
[603190]=[[Team Defensive Training 4]],
[603191]=[[Team Physique Training 4]],
[603192]=[[Resource Harvesting 4]],
[603193]=[[Convoy Load Capacity 4]],
[603194]=[[Zombie Hunter 4]],
[603195]=[[Ambush Tactics 4]],
[603196]=[[Resource Hunter 4]],
[603197]=[[Rapid March 4]],
[603198]=[[Rapid Assault 4]],
[603199]=[[Counterattack 4]],
[603200]=[[Quick Response 4]],
[603201]=[[Impenetrable Defense 4]],
[603202]=[[Fighting Spirit 4]],
[603203]=[[Hold the Line 4]],
[603204]=[[Combat Skills 4]],
[603205]=[[Energy Surge 4]],
[603206]=[[Efficient Killing 4]],
[603207]=[[Intelligence Motivation]],
[603208]=[[Acceleration Motivation]],
[603209]=[[Summoning Motivation]],
[603210]=[[Advanced Rewards]],
[603211]=[[Clash Master]],
[603212]=[[Construction Motivation]],
[603213]=[[Research Motivation]],
[603214]=[[Training Motivation]],
[603215]=[[Enemy Kill Motivation]],
[603216]=[[Super Reward]],
[603217]=[[Basic Goods]],
[603218]=[[Counterattack Training]],
[603219]=[[Resilient Guardian]],
[603220]=[[Lightning Speed]],
[603221]=[[Extra Plunder]],
[603222]=[[Lucky Guardian]],
[603223]=[[Extra Wagon]],
[603224]=[[Ultimate Protection]],
[603225]=[[Treasure Wagon]],
[603226]=[[HP Enhancement 1]],
[603227]=[[ATK Enhancement 1]],
[603228]=[[DEF Enhancement 1]],
[603229]=[[Advanced Protection]],
[603230]=[[Defensive Training 8]],
[603231]=[[Offensive Training 8]],
[603232]=[[Advanced Defense 8]],
[603233]=[[Training Speed Up 5]],
[603234]=[[Barrack Expansion 5]],
[603235]=[[HP Enhancement 2]],
[603236]=[[ATK Enhancement 2]],
[603237]=[[DEF Enhancement 2]],
[603238]=[[Morale Enhancement]],
[603239]=[[Defensive Training 9]],
[603240]=[[Offensive Training 9]],
[603241]=[[Advanced Defense 9]],
[603242]=[[Rapid Treatment 5]],
[603243]=[[Hospital Expansion 5]],
[603244]=[[Advanced Protection]],
[603245]=[[HP Enhancement 3]],
[603246]=[[ATK Enhancement 3]],
[603247]=[[DEF Enhancement 3]],
[603248]=[[Soldier 10]],
[603249]=[[Building Speed Up]],
[603250]=[[Hospital Capacity Expansion]],
[603251]=[[Barrack Training Capacity Increase]],
[603252]=[[Technology Research Speed UP]],
[603253]=[[Wounded Healing Speed Increase]],
[603254]=[[Soldier Training Speed Increase]],
[603255]=[[Construction Resource Cost Reduction]],
[603256]=[[Extra Barrack/Farmland/Iron Mine/Gold Mine]],
[603257]=[[Healing Resource Cost Reduction]],
[603258]=[[Drill Ground Capacity Increase]],
[603259]=[[Soldier Casualty Rate Reduction]],
[603260]=[[Food Output Speed Increase]],
[603261]=[[Iron Mine Output Speed Increase]],
[603262]=[[Gold Mine Output Speed Increase]],
[603263]=[[Food Collection Speed Increase]],
[603264]=[[Iron Ore Collection Speed Increase]],
[603265]=[[Gold Collection Speed Increase]],
[603266]=[[Food Protection Capacity Increase]],
[603267]=[[Iron Ore Protection Capacity Increase]],
[603268]=[[Gold Protection Capacity Increase]],
[603269]=[[DMG Dealt by Forest Heroes Increase]],
[603270]=[[Forest ATK]],
[603271]=[[Forest DEF]],
[603272]=[[Forest HP]],
[603273]=[[Human Hero Damage Increase]],
[603274]=[[Human Hero ATK]],
[603275]=[[Human Hero DEF]],
[603276]=[[Human Hero HP]],
[603277]=[[Nightfall Hero Damage Increase]],
[603278]=[[Nightfall Hero ATK]],
[603279]=[[Nightfall Hero DEF]],
[603280]=[[Nightfall Hero HP]],
[603281]=[[Universal Damage Increase]],
[603282]=[[Hero ATK]],
[603283]=[[Hero DEF]],
[603284]=[[Hero HP]],
[603285]=[[Soldier ATK Increase]],
[603286]=[[Soldier HP Increase]],
[603287]=[[Soldier DEF Increase]],
[603288]=[[Soldier Load Increase]],
[603289]=[[Team Hero DMG Against Monsters Increase]],
[603290]=[[Team Hero DMG Taken from Monsters Reduction]],
[603291]=[[Team Hero ATK]],
[603292]=[[Team Hero DEF]],
[603293]=[[Team Hero HP]],
[603294]=[[Team Collection Speed]],
[603295]=[[Team Load Capacity Increase]],
[603296]=[[Team March Speed Against Zombies Increase]],
[603297]=[[Team March Speed Against Other Lords Increase]],
[603298]=[[Team March Speed to Resource Points Increase]],
[603299]=[[Team March Speed Increase]],
[603300]=[[Team ATK Increase When Attacking Enemy Castles]],
[603301]=[[Team ATK Increase When Defending in the Castle]],
[603302]=[[Team DEF Increase When Attacking Enemy Castles]],
[603303]=[[Team DEF Increase When Defending in the Castle]],
[603304]=[[Team HP Increase When Attacking Enemy Castles]],
[603305]=[[Team HP Increase When Defending in the Castles]],
[603306]=[[Team Physical DMG Bonus]],
[603307]=[[Team Energy DMG Bonus]],
[603308]=[[Team Hero Kill Ratio Increase When Attacking Other Lords]],
[603309]=[[Boosts points gained from Intelligence Quests.]],
[603310]=[[Speed Up Points Gain Increase]],
[603311]=[[Hero Recruitment Points Gain Increase]],
[603312]=[[Unlock Tier 4-6 Rewards]],
[603313]=[[All Points Gain Increase]],
[603314]=[[Building CP Points Gain Increase]],
[603315]=[[Technology CP Points Gain Increase]],
[603316]=[[Training Soldiers Points Gain Increase]],
[603317]=[[Kill Enemy Points Gain Increase]],
[603318]=[[Unlock Tier 7-9 Rewards]],
[603319]=[[Increase Wagon Carrying Capacity for Iron, Food, and Gold]],
[603320]=[[Boosts your heroes' HP, ATK, and DEF during attacks.]],
[603321]=[[Boosts your heroes' HP, ATK, and DEF during defense.]],
[603322]=[[Increase Wagon Movement Speed]],
[603323]=[[When the attack is successful, there is a probability of grabbing an additional cargo.]],
[603324]=[[Chance to Reclaim a Cargo When Losing a Defense]],
[603325]=[[Increase Daily Wagon Limit]],
[603326]=[[Increase allowed failures when the same lord attacks your wagon]],
[603327]=[[Chance to refresh Treasure Wagon when using Trade Contracts]],
[603328]=[[General DMG Taken Reduction]],
[603329]=[[Increase Soldier Morale]],
[603330]=[[Unlock Tier 10 Soldier Training]],
[603331]='Development',
[603332]='Economy',
[603333]='Hero',
[603334]='Soldier',
[603335]=[[Team 1]],
[603336]=[[Team 2]],
[603337]=[[Team 3]],
[603338]=[[Team 4]],
[603339]=[[Union Duel]],
[603340]=[[Trade Wagon]],
[603341]=[[Super Soldier]],
[603342]=[[Unlock by Purchasing Monthly Card]],
[603343]=[[Unlock at Research Center Lv. {%s1}]],
[603344]=[[Progress {%s1} Reaches {%s2}]],
[603345]=[[Gain Extra Farmland]],
[603346]=[[Gain Extra Barrack]],
[603347]=[[Gain Extra Iron Mine]],
[603348]=[[Gain Extra Gold Mine]],
[603349]=[[2nd Research Center]],
[603350]=[[Requires {%s1} Building to Reach {%s2} to Unlock]],
[603351]=[[Research Queue Doubled!]],
[603352]=[[Insufficient Energy Crystals!]],
[603353]=[[Acorn Tavern Quest Team Qty]],
[603354]=[[Lucky Supply Crate Qty upon Completing Acorn Tavern Quests]],
[603501]='Adventurer',
[603502]='Lord',
[603503]='Farmer',
[603504]=[[Granary Manager]],
[603505]='Miner',
[603506]=[[Iron Ore Manager]],
[603507]='Prospector',
[603508]=[[Treasury Manager]],
[603509]='Instructor',
[603510]='Officer',
[603511]='Scholar',
[603512]='Envoy',
[603513]='Repairman',
[603514]='Priest',
[603515]='Builder',
[603516]='Hunter',
[603517]='Nurse',
[603551]='Laura',
[603552]='Jessica',
[603561]='William',
[603562]='Kevin',
[603563]='Ronald',
[603564]='Nicholas',
[603565]='Brandon',
[603566]='Franklin',
[603567]='Eric',
[603568]='Megan',
[603569]='Jonathan',
[603570]='Jordan',
[603571]='Mason',
[603572]='Jack',
[603573]='Ernest',
[603574]='Glenn',
[603575]='Marvin',
[603576]='Tiffany',
[603577]='Simon',
[603578]='Warren',
[603579]='Rashid',
[603580]='Franklin',
[603581]='Mason',
[603582]='Jack',
[603583]='Ernest',
[603584]='Autumn',
[603585]='Simon',
[603586]='Warren',
[603587]='Rashid',
[603588]='Franklin',
[603589]='Mason',
[603590]='Jack',
[603591]='Ernest',
[603592]='Irene',
[603593]='Marvin',
[603594]='Simon',
[603595]='Warren',
[603596]='Rashid',
[603597]='Julia',
[603598]='Matthew',
[603599]='Carl',
[603600]='Dean',
[603601]='Dawn',
[603602]='Vanessa',
[603603]='Serena',
[603604]='Tyner',
[603605]='Matthew',
[603606]='Carl',
[603607]='Kayla',
[603608]='Helen',
[603609]='Julia',
[603610]='Carl',
[603611]='Dean',
[603612]='Tyner',
[603613]='Dawn',
[603614]='Vanessa',
[603615]='Matthew',
[603616]='Carl',
[603617]='Michael',
[603618]='Charles',
[603619]='Paul',
[603620]='Andrew',
[603621]='Edward',
[603622]='Ryan',
[603623]='Adam',
[603624]='Linda',
[603625]='Susan',
[603626]='Jessica',
[603627]='Michael',
[603628]='Charles',
[603629]='Paul',
[603630]='Andrew',
[603631]='Edward',
[603632]='Ryan',
[603633]='Adam',
[603634]='Sarah',
[603635]='Karen',
[603636]='Betty',
[603637]='Michael',
[603638]='Charles',
[603639]='Paul',
[603640]='Andrew',
[603641]='Edward',
[603642]='Ryan',
[603643]='Adam',
[603644]='Dorothy',
[603645]='Sandra',
[603646]='Deborah',
[603647]='Willy',
[603648]='Albert',
[603649]='Maxwell',
[603650]='Stephanie',
[603651]='Rebecca',
[603652]='Laura',
[603653]='Larry',
[603654]='Carl',
[603655]='Bruce',
[603656]='Lydia',
[603657]='Julia',
[603658]='Dawn',
[603659]='Seth',
[603660]='Silas',
[603661]='Roosevelt',
[603662]='Vanessa',
[603663]='Serena',
[603664]='Kayla',
[603665]='Zachary',
[603666]='Harold',
[603667]='Carl',
[603668]='Helen',
[603669]='Lydia',
[603670]='Julia',
[603671]='Silas',
[603672]='Roosevelt',
[603673]='James',
[603674]='Christopher',
[603675]='Drake',
[603676]='Paul',
[603677]='Joshua',
[603678]='Willy',
[603679]='Zachary',
[603680]='Harold',
[603681]='Gilbert',
[603682]='Julian',
[603683]='Omar',
[603684]='James',
[603685]='Christopher',
[603686]='Drake',
[603687]='Paul',
[603688]='Joshua',
[603689]='Willy',
[603690]='Zachary',
[603691]='Harold',
[603692]='Gilbert',
[603693]='Julian',
[603694]='Omar',
[603695]='James',
[603696]='Christopher',
[603697]='Drake',
[603698]='Paul',
[603699]='Joshua',
[603700]='Willy',
[603701]='Zachary',
[603702]='Harold',
[603703]='Gilbert',
[603704]='Julian',
[603705]='Omar',
[603706]='Drake',
[603707]='Paul',
[603708]='Joshua',
[603709]='Lydia',
[603710]='Julia',
[603711]='Dawn',
[603712]='John',
[603713]='Richard',
[603714]='Donald',
[603715]='Paul',
[603716]='Brian',
[603717]='Eric',
[603718]='Jimmy',
[603719]='Vanessa',
[603720]='Serena',
[603721]='Kayla',
[603722]='Zachary',
[603723]='Harold',
[603724]='Kyle',
[603725]='Roger',
[603726]='Pedro',
[603727]='Curtis',
[603728]='Gilbert',
[603729]='Joseph',
[603730]='Jeffrey',
[603731]='Linda',
[603732]='Sarah',
[603733]='Betty',
[603734]='Deborah',
[603735]='Stephanie',
[603736]='Steven',
[603737]='Shirley',
[603738]='Gregory',
[603739]='Austin',
[603740]='Nicole',
[603741]='Dorothy',
[603742]='Janet',
[603743]='Sarah',
[603744]='Natalie',
[603745]='Nathan',
[603746]='Mary',
[603747]='Walter',
[603748]='Craig',
[603749]='Ivy',
[603750]='Theresa',
[603751]='Diana',
[603752]='Ruby',
[603753]='Naomi',
[603754]='Antonio',
[603755]='Isla',
[603756]='Martin',
[603757]='Joseph',
[603758]='Millie',
[603759]='Josephine',
[603760]='Lydia',
[603761]='John',
[603762]='Richard',
[603763]='Joseph',
[603764]='Donald',
[603765]='Brian',
[603766]='Jeffrey',
[603767]='Eric',
[603768]='Steven',
[603769]='Gregory',
[603770]='Austin',
[603771]='Jimmy',
[603772]='Nathan',
[603773]='Kyle',
[603774]='Walter',
[603775]='Craig',
[603776]='Antonio',
[603777]='Roger',
[603778]='Pedro',
[603779]='Curtis',
[603780]='Martin',
[603781]='John',
[603782]='Richard',
[603783]='Joseph',
[603784]='Donald',
[603785]='Brian',
[603786]='Jeffrey',
[603787]='Eric',
[603788]='Steven',
[603789]='Gregory',
[603790]='Austin',
[603791]='Jimmy',
[603792]='Nathan',
[603793]='Patricia',
[603794]='Linda',
[603795]='Barbara',
[603796]='Sarah',
[603797]='Betty',
[603798]='Ashley',
[603799]='Donna',
[603800]='Michelle',
[603801]='Deborah',
[603802]='Robert',
[603803]='Thomas',
[603804]='Steven',
[603805]='George',
[603806]='Gary',
[603807]='Stephanie',
[603808]='Cynthia',
[603809]='Shirley',
[603810]='Nicole',
[603811]='Dorothy',
[603812]='Scott',
[603813]='Raymond',
[603814]='Clarence',
[603815]='Philip',
[603816]='Peter',
[603817]='Patricia',
[603818]='Jennifer',
[603819]='Kimberley',
[603820]='Donna',
[603821]='David',
[603822]='Anthony',
[603823]='Kenneth',
[603824]='Timothy',
[603825]='Justin',
[603826]='Abraham',
[603827]='Gerald',
[603828]='Lawrence',
[603829]='Dylan',
[603830]='Rebecca',
[603831]='Michael',
[603832]='Charles',
[603833]='Paul',
[603834]='Edward',
[603835]='Ryan',
[603836]='Adam',
[603837]='Willy',
[603838]='Albert',
[603839]='Maxwell',
[603840]='Jessica',
[603841]='Michael',
[603842]='Charles',
[603843]='Paul',
[603844]='Edward',
[603845]='Ryan',
[603846]='Adam',
[603847]='Willy',
[603848]='Albert',
[603849]='Maxwell',
[603850]='Susan',
[603851]='Karen',
[603852]='Dorothy',
[603853]='Sharon',
[603854]='Katherine',
[603855]='Anna',
[603856]='Pam',
[603857]='Kristen',
[603858]='Emily',
[603859]='Sophia',
[603860]='Chloe',
[603861]='Tara',
[603862]='Mackenzie',
[603863]='Susan',
[603864]='Karen',
[603865]='Dorothy',
[603866]='Sharon',
[603867]='Katherine',
[603868]='Anna',
[603869]='Pam',
[603870]='Michael',
[603871]='William',
[603872]='Charles',
[603873]='mark',
[603874]='Paul',
[603875]='Kevin',
[603876]='Edward',
[603877]='Ronald',
[603878]='Ryan',
[603879]='Nicholas',
[603880]='Brandon',
[603881]='Franklin',
[603882]='Adam',
[603883]='Eric',
[603884]='Jonathan',
[603885]='Jordan',
[603886]='Willy',
[603887]='Albert',
[603888]='Mason',
[603889]='Jack',
[603890]='James',
[603891]='John',
[603892]='William',
[603893]='Richard',
[603894]='Joseph',
[603895]='Christopher',
[603896]='Drake',
[603897]='Mark',
[603898]='Donald',
[603899]='Paul',
[603900]='Joshua',
[603901]='Kevin',
[603902]='Brian',
[603903]='Ronald',
[603904]='Jeffrey',
[603905]='Nicholas',
[603906]='Eric',
[603907]='Steven',
[603908]='Brandon',
[603909]='Franklin',
[603910]='Mary',
[603911]='Susan',
[603912]='Karen',
[603913]='Margaret',
[603914]='Dorothy',
[603915]='Emily',
[603916]='Carol',
[603917]='Sharon',
[603918]='Katherine',
[603919]='Helen',
[603920]='Anna',
[603921]='Brenda',
[603922]='Pam',
[603923]='Kristen',
[603924]='Emily',
[603925]='Sophia',
[603926]='Chloe',
[603927]='Kelly',
[603928]='Caitlin',
[603929]='Faith',
[603930]='Lillian',
[603931]='Tara',
[603932]='Senna',
[603933]='Delilah',
[603934]='Rhea',
[603935]='Mackenzie',
[603936]='Camilla',
[603937]='Destiny',
[603938]='Mary',
[603939]='Susan',
[603940]='Karen',
[603941]='Margaret',
[604001]=[[Daily chest acquisition limit +5]],
[604002]=[[Daily chest acquisition limit +6]],
[604003]=[[Daily chest acquisition limit +7]],
[604004]=[[Daily chest acquisition limit +8]],
[604005]=[[Daily chest acquisition limit +9]],
[604006]=[[Daily chest acquisition limit +10]],
[604007]=[[Daily chest acquisition limit +11]],
[604008]=[[Daily chest acquisition limit +12]],
[604009]=[[Daily chest acquisition limit +13]],
[604010]=[[Daily chest acquisition limit +14]],
[604011]=[[Daily chest acquisition limit +15]],
[604012]=[[Daily chest acquisition limit +16]],
[604013]=[[Daily chest acquisition limit +17]],
[604014]=[[Daily chest acquisition limit +18]],
[604015]=[[Daily chest acquisition limit +19]],
[604016]=[[Daily chest acquisition limit +20]],
[604017]=[[Daily chest acquisition limit +21]],
[604018]=[[Daily chest acquisition limit +22]],
[604019]=[[Daily chest acquisition limit +23]],
[604020]=[[Daily chest acquisition limit +24]],
[604021]=[[Daily chest acquisition limit +25]],
[604022]=[[Daily chest acquisition limit +26]],
[604023]=[[Daily chest acquisition limit +27]],
[604024]=[[Daily chest acquisition limit +28]],
[604025]=[[Daily chest acquisition limit +29]],
[604026]=[[Daily chest acquisition limit +30]],
[604027]=[[+1 Chest from Each Intelligence Center Task]],
[604028]=[[+2 Chest from Each Intelligence Center Task]],
[604029]=[[+3 Chest from Each Intelligence Center Task]],
[604030]=[[+4 Chest from Each Intelligence Center Task]],
[604031]=[[+5 Chest from Each Intelligence Center Task]],
[604032]=[[Chest Upgraded to Rare Adventure Chest]],
[604033]=[[Chest Upgraded to Deluxe Adventure Chest]],
[604034]=[[Adventurer Laura is a good treasure hunter. She brings extra treasures when an Intelligence Quest is completed!]],
[604035]=[[Commander Jessica has done a fine job of leading her troops. She boosts all heroes' Troop Capacity and significantly increases their ATK!]],
[604501]='Location',
[604502]=[[Plundered Resources]],
[604503]=[[Soldier Load]],
[604504]='Overview',
[604505]=[[Round {%s1}]],
[604506]='Details',
[604507]='Overview',
[604508]='Hero',
[604509]='Army',
[604510]=[[Battle Stats]],
[604511]=[[Lord, our scouts successfully infiltrated the enemy {%s1} and gathered the following critical intelligence.]],
[604512]=[[Lord! We have been scouted by {%s1}. Stay vigilant and prepare for battle!]],
[604513]='Scout',
[604514]='Scouted',
[604515]=[[Scout Report]],
[604516]='Team',
[604517]='<color=#319f38>Victory</color>',
[604518]='<color=#e63838>Defeat</color>',
[604519]=[[<color=#319f38>Offensive Victory</color>]],
[604520]=[[<color=#e63838>Offensive Defeat</color>]],
[604521]=[[<color=#319f38>Defensive Victory</color>]],
[604522]=[[<color=#e63838>Defensive Defeat</color>]],
[604523]=[[Scouted Successful]],
[604524]='Scouted',
[604525]=[[Soldier Casualty Rules]],
[604526]=[[General Rules]],
[604527]=[[After the battle, the system will calculate the number of soldiers seriously injured and killed on both sides based on the battle type and the fatality rate bonus of both sides. The basic serious injury rate and death rate in each gameplay are listed below:]],
[604528]=[[Wound Rate]],
[604529]=[[Death Rate]],
[604530]=[[World Monsters]],
[604531]=[[City Defenders]],
[604532]=[[Attack the castle - Attacker]],
[604533]=[[Attack the castle - Defender]],
[604534]=[[Trade Wagon - Attacker]],
[604535]=[[Trade Wagon - Defender]],
[604536]=[[Desert Storm Battlefield]],
[604537]=[[Union Challenge]],
[604538]=[[Zombie Siege]],
[604539]=[[Zombie Overlord]],
[604540]=[[Additional Deaths]],
[604541]=[[1. When the Lord's castle is attacked, if the defense fails, the Lord will have a fixed number of additional soldiers killed in the Drill Ground. The additional soldiers who died in the Drill Ground will be adjusted according to the level of the defender's castle. The lower the defender's castle level, the lower the number of casualties.
2. Severely injured soldiers will be given priority to enter the hospital. When the hospital capacity reaches the upper limit, the seriously injured soldiers will die directly.]],
[604542]=[[Emergency Center]],
[604543]=[[When the Lord's base is attacked, the emergency center will save a certain percentage of soldiers who should have died (combat deaths and Drill Ground deaths), making them endangered soldiers.
The emergency center has an upper limit on the number of soldiers it can accept, and it will no longer accept any more after reaching the upper limit. The percentage of endangered soldiers and the upper limit can be increased by upgrading the emergency center.]],
[604544]='Team',
[604545]=[[Enemy Faction]],
[604546]=[[Our Camp]],
[604547]=[[Our lineup was severely countered by the opponent's lineup]],
[604548]=[[Our lineup was somewhat countered by the opponent's lineup]],
[604549]=[[Our lineup is evenly matched with the opponent's lineup]],
[604550]=[[Our lineup somewhat counters the opponent's lineup]],
[604551]=[[Our lineup strongly counters the opponent's lineup]],
[604552]=[[Mystic Beast]],
[604553]='Technology',
[604554]='Decoration',
[604555]='Soldier',
[604556]=[[Wall of Honor]],
[604557]=[[Team {%s1}]],
[604558]='Total',
[604559]=[[The blue squad's morale is {%s1} times that of the red team, dealing {%s2} damage in battle.]],
[604560]=[[Attribute boosts apply to all heroes.]],
[604561]=[[Hero HP]],
[604562]=[[Hero DEF]],
[604563]=[[Hero ATK]],
[604564]=[[Max Level]],
[604565]=[[Total Obtained:]],
[604566]='Casualties',
[604567]='Hospital',
[604568]=[[Lightly Injured]],
[604569]='Remaining',
[604570]='DMG',
[604571]=[[Damage Taken]],
[604572]='Equip',
[604573]=[[Hero Skills]],
[604574]=[[In this round of attack, the defenders failed to hold their ground, resulting in an additional Drill Ground {%s1} soldiers killed.]],
[604575]=[[In this round of attack, due to the hospital exceeding its capacity, a total of {%s1} seriously injured soldiers succumbed to their wounds.]],
[604576]=[[The hospital is full of wounded soldiers]],
[604577]='Total:',
[604578]=[[The red team's morale is {%s1} times that of the blue team, dealing {%s2} damage in battle.]],
[604579]=[[Castle Level]],
[604580]=[[Hero Level]],
[604581]=[[Hero Star]],
[604582]=[[Soldier CP]],
[604583]=[[Highest Soldier Level]],
[604584]=[[Significant improvement]],
[604585]=[[Average hero level]],
[604586]=[[Average hero star]],
[604587]=[[Fairey helps you analyze the following improvement directions]],
[604588]=[[The higher the level of soldiers the hero carries, the higher the load.]],
[604589]=[[Details of death]],
[604590]=[[Insufficient soldiers, team attributes dropped to {%s1}]],
[604591]=[[Statistics details]],
[604592]='Morale:',
[604593]=[[Soldier Morale Increase]],
[604594]=[[Physical DMG:]],
[604595]=[[Total CRIT DMG:]],
[604596]=[[Actual CRIT Hits:]],
[604597]=[[Energy DMG:]],
[604598]=[[Physical DMG Taken:]],
[604599]=[[Energy DMG Taken:]],
[604600]=[[Plunderable Resources]],
[604601]=[[Defeated {%s1}; Soldiers Killed: {%s2}]],
[604602]=[[Failed to Defeat {%s1}; Soldiers Killed: {%s2}]],
[604603]=[[Attacked {%s1}'s city {%s2} and achieved {%s3} victory]],
[604604]=[[Attacked {%s1}'s city {%s2} and suffered {%s3} defeat]],
[604605]=[[Attack {%s1} city {%s2} victory;Soldiers Killed: {%s3}]],
[604606]=[[Attack on {%s1} city {%s2} failed;Soldiers Killed: {%s3}]],
[604607]=[[Successfully scouted {%s1}'s city {%s2} {%s3}]],
[604608]=[[Successfully scouted {%s1} city {%s2}]],
[604609]=[[Successfully scouted {%s1}'s team]],
[604610]=[[Your team were scouted by {%s1}]],
[604611]=[[Successfully scouted {%s1}'s castle]],
[604612]=[[Your castle was scouted by {%s1}]],
[604613]=[[Recruit Your Heroes!]],
[604614]=[[The Intelligence Center has discovered a large amount of resources]],
[604615]=[[Union Center is now open]],
[604616]=[[Welcome to the Union]],
[604617]=[[Upgrade Your Technology!]],
[604618]=[[<color=#319f38>Recruit Hall</color> has been opened! You can recruit powerful heroes to help you through this building! \nNot only can they help you clean up zombies, they can also bring soldiers to conquer the city for you!]],
[604619]=[[Dear lord, <color=#319f38>Intelligence Center</color> has discovered a lot of resource information around your castle! \n\nThe soldiers are all ready and waiting for you to make arrangements at the intelligence center. Don't miss it!]],
[604620]=[[You have unlocked <color=#319f38>Union Center</color>! The world is dangerous and allies are essential. \n\nIf you join the union, you can enjoy the following benefits in the union:\n\n<color=#319f38>Building Speed Up/Research:</color>After joining the union, you can request allies<color=#317BC7>Ask for help</color>, allies can help you <color=#317BC7> shorten The time required for construction and research</color>, the more active your union is, the better the speed up effect\n\n<color=#319f38>Union Gift</color>: Your allies <color=#317BC7>< Defeat the boss></color> or <color=#317BC7><Purchase pack> </color>, will bring you an union gift and provide you with a lot of resources\n\n<color=#319f38>Technology bonus</color>: The more active the union, the higher the union’s technology level will be. You will enjoy <color=#317BC7>research speed up</color>, <color=#317BC7>march speed up</color> and other powerful technological effect bonuses\n\n<color=#319f38>Union Shop</color>: More union contribution points will be obtained in active unions, and you can use your contributions points to purchase <color=#317BC7>various cost-effective items</color> in the union shop.]],
[604621]=[[Welcome to the Union! You have joined the union, and you can enjoy the following benefits in the union:\n\n<color=#319f38>Building Speed Up/Research:</color>After joining the union, you can request allies<color=#317BC7>Ask for help</color>, allies can help you <color=#317BC7> shorten The time required for construction and research</color>, the more active your union is, the better the speed up effect\n\n<color=#319f38>Union Gift</color>: Your allies <color=#317BC7>< Defeat the boss></color> or <color=#317BC7><Purchase pack> </color>, will bring you an union gift and provide you with a lot of resources\n\n<color=#319f38>Technology bonus</color>: The more active the union, the higher the union’s technology level will be. You will enjoy <color=#317BC7>research speed up</color>, <color=#317BC7>march speed up</color> and other powerful technological effect bonuses\n\n<color=#319f38>Union Shop</color>: More union contribution points will be obtained in active unions, and you can use your contributions points to purchase <color=#317BC7>various cost-effective items</color> in the union shop.]],
[604622]=[[Dear Lord, <color=#319f38>Research Center</color> has been unlocked! \n\nCompleting different research projects can help you <color=#317BC7>speed up city construction</color> and <color=#317BC7>increase hero CP</color>! Please pay attention to your research progress!]],
[604623]='{%s1}{%s2}',
[604624]='{%s1}',
[604625]=[[No mail]],
[604626]='Morale',
[604627]='Amount',
[604628]=[[Scouted on {%s1}]],
[604629]=[[Fought against {%s1} and narrowly lost.]],
[604630]=[[Fought against {%s1} and claimed victory.]],
[604631]='Shared!',
[604632]=[[Share failed, please try again.]],
[604633]=[[Scouted {%s1}.]],
[604634]=[[Enemy Team]],
[605001]=[[Hero Summon]],
[605002]=[[Survivor Recruitment]],
[605003]=[[Free Summon]],
[605004]=[[Summon x10]],
[605005]=[[Summon x1]],
[605006]=[[Next Free:]],
[605007]=[[Recruit {%s1} more times for a guaranteed Gold Hero; after obtaining a Gold Hero, the count will reset. Duplicate heroes will be converted into 10 Hero Shards of the same name.]],
[605008]=[[Unlocks at {%s1} Building Level {%s2}]],
[605009]=[[Recruitment Probability]],
[605010]='Gold',
[605011]='Purple',
[605012]='Blue',
[605013]='Green',
[605014]='White',
[605015]=[[Recruit {%s1} more times for a guaranteed Gold Survivor; after obtaining a Gold Survivor, the count will reset. Duplicate survivors will be converted into 10 Survivor Shards of the same name.]],
[605016]=[[summons to get]],
[605017]='Hero',
[605018]='Survivor',
[606001]='Weapon',
[606002]='Armour',
[606003]='Helmet',
[606004]='Boots',
[606005]='Weapon',
[606006]='Armour',
[606007]='Helmet',
[606008]='Boots',
[606009]='Weapon',
[606010]='Armour',
[606011]='Helmet',
[606012]='Boots',
[606013]='Weapon',
[606014]='Armour',
[606015]='Helmet',
[606016]='Boots',
[606017]='Screw',
[606018]=[[Refined Iron Module]],
[606019]=[[High-Temperature Alloy]],
[606020]=[[Synthetic Resin]],
[606021]=[[Dielectric Ceramic]],
[606022]=[[Superior Gear Chest]],
[606023]=[[Premium Gear Chest]],
[606024]=[[Rare Gear Chest]],
[606025]=[[Enhancement Stone]],
[606026]=[[Legendary Gear Blueprint]],
[606027]=[[Mythic Gear Blueprint]],
[606028]='Type:',
[606029]='Level:',
[606030]=[[Upgrade to Lv. 10 to activate the 1st Bonus Stat.]],
[606031]=[[Upgrade to Lv. 20 to activate the 2nd Bonus Stat.]],
[606032]=[[Upgrade to Lv. 30 to activate the 3rd Bonus Stat.]],
[606033]=[[Upgrade to Lv. 40 to activate the 4th Bonus Stat.]],
[606034]=[[Promote to 1-Star to upgrade the 1st Bonus Stat.]],
[606035]=[[Promote to 2-Star to upgrade the 2nd Bonus Stat.]],
[606036]=[[Promote to 3-Star to upgrade the 3rd Bonus Stat.]],
[606037]=[[Promote to 4-Star to upgrade the 4th Bonus Stat.]],
[606038]=[[Promote to 5-Star to activate the 5th Bonus Stat.]],
[606039]=[[Basic Stats:]],
[606040]=[[Additional Stats:]],
[606041]='Unequip',
[606042]='Upgrade',
[606043]='Replace',
[606044]=[[No Gear equipped]],
[606045]='Equip',
[606046]='Equipped:',
[606047]=[[Obtain Gear]],
[606048]=[[Gear Upgrade]],
[606049]=[[Gear Promotion]],
[606050]=[[Level limit reached]],
[606051]='Promote',
[606052]=[[Star level limit reached]],
[606053]=[[Craft Gear]],
[606054]=[[Gear Disassembly]],
[606055]=[[Merge Material]],
[606056]='Decompose',
[606057]=[[Disassemble All]],
[606058]=[[Disassemble All]],
[606059]=[[Please select materials to disassemble]],
[606060]=[[No materials selected]],
[606061]=[[Insufficient quantity to disassemble]],
[606062]=[[Insufficient quantity to merge]],
[606063]=[[Merge All]],
[606064]='Merge',
[606065]=[[Please select materials for merge]],
[606066]=[[Dismantlement to obtain materials]],
[606067]=[[Please select Gear to disassemble]],
[606068]=[[Auto Select]],
[606069]='Disassemble',
[606070]=[[Auto select Gear of <color=#319F38>Green quality or lower</color>]],
[606071]=[[Auto select Gear of <color=#317BC7>Blue quality or lower</color>]],
[606072]=[[Auto select Gear of <color=#A036D5>Purple quality or lower</color>]],
[606073]=[[Auto Select Settings]],
[606074]='Craft',
[606075]=[[Another Gear is being crafted]],
[606076]='Claim',
[606077]=[[(Max +{%s1})]],
[606078]=[[No equippable Gear now]],
[606079]=[[The material has reached the highest quality and cannot be merged]],
[606080]=[[Low-quality materials cannot be disassembled]],
[606081]=[[Gear Promotion unlocks after Gear Workshop reaches Lv. 20.]],
[606082]='Gear',
[606083]=[[【Equipment】Reissue of items overflowing from backpack]],
[606084]=[[Your equipment backpack is full. Please disassemble your equipment and clean your backpack before collecting it.]],
[607001]=[[Increases target's DMG taken by 15% for 1 turn.]],
[607002]=[[Deals an additional 20% Physical DMG.]],
[607003]=[[Increases target's DMG taken by 20% for 1 turn.]],
[607004]=[[Deals an additional 40% Physical DMG.]],
[607005]=[[Increases target's DMG taken by 25% for 1 turn.]],
[607006]=[[(Requires Mystic Beast Lv. {%s1})]],
[607011]=[[Stat Conversion Ratio]],
[607012]='Breakthrough',
[607013]=[[Mystic Beast Stats]],
[607014]=[[The Mystic Beast's stats are partially converted to all Heroes' stats during battle. Below are the stats on its current Level.]],
[607015]=[[Mystic Beast HP]],
[607016]='Ratio',
[607017]=[[Mystic Beast ATK]],
[607018]=[[Mystic Beast DEF]],
[607019]=[[Stats Enhancement]],
[607020]=[[Skill Enhancement]],
[607021]=[[Mark Stat Summary]],
[607022]=[[Skill DMG Enhancement]],
[607023]=[[Upgrade Cost]],
[607024]=[[Merging Materials]],
[607025]=[[Consumed Materials]],
[607026]=[[Confirm Conversion]],
[607027]=[[Mystic Beast Mark:]],
[607028]='Mark',
[607029]=[[{%s1} {%s2} will be convert into {%s4} {%s5} at a ratio of {%s3}.]],
[607030]=[[HP Conversion Ratio]],
[607031]=[[ATK Conversion Ratio]],
[607032]=[[DEF Conversion Ratio]],
[607033]=[[Convert Materials]],
[607034]=[[Deals <color=#2EC564>355%</color> of ATK as Physical DMG to 2 enemy units.]],
[607035]=[[Deals <color=#2EC564>355%</color> of ATK as Physical DMG to 2 enemy units.]],
[607036]=[[Deals <color=#2EC564>426%</color> of ATK as Physical DMG to 2 enemy units.]],
[607037]=[[Deals <color=#2EC564>426%</color> of ATK as Physical DMG to 2 enemy units.]],
[607038]=[[Deals <color=#2EC564>568%</color> of ATK as Physical DMG to 2 enemy units.]],
[607039]=[[Deals <color=#2EC564>568%</color> of ATK as Physical DMG to 2 enemy units.]],
[607040]=[[Breakthrough Phase:]],
[607041]=[[Disassemble high-level Marks to upgrade the current one.]],
[607042]=[[CRIT x2]],
[607046]=[[Skill Amplification]],
[607047]=[[Combat Boost Stage {%s1}]],
[607048]=[[(Stage Breakthrough: Lv.{%s1})]],
[607049]=[[Advanced stage has been completed]],
[607050]=[[(Advanced level full)]],
[607051]=[[Combat Boost]],
[607052]=[[Skill Core]],
[607053]='Promote',
[607054]='Chest',
[607055]='Attribute',
[607057]=[[Use advanced experience cards and unused crystal cores to upgrade]],
[607058]=[[Quick Add]],
[607060]=[[Get more]],
[607061]=[[Continuously improve your combat advancement level to break through to a higher stage. The breakthrough stage will also increase the crystal skill increase, which will bring additional numerical enhancement to your crystal skills.]],
[607062]=[[Unlock the Crystal Core Skill System]],
[607063]=[[Unlocking Crystal Core Solution 1]],
[607064]=[[Unlocking Crystal Core Solution 2]],
[607065]=[[Unlocking Crystal Core Solution 3]],
[607066]=[[Unlocking Crystal Core Solution 4]],
[607067]=[[Crystal skill increase increased to +1]],
[607068]=[[Crystal skill increase increased to +2]],
[607069]=[[Crystal skill increase increased to +3]],
[607070]=[[Crystal skill increase increased to +4]],
[607071]=[[Crystal skill increase increased to +5]],
[607072]=[[This crystal has been upgraded. Do you still want to consume it for combat advancement?]],
[607073]=[[Crystal Reset]],
[607074]=[[Change queue]],
[607075]=[[Forest Crystal Group]],
[607076]=[[Human Crystal Group]],
[607077]=[[Dark Night Crystal Group]],
[607078]=[[Mixed crystal group]],
[607079]=[[Opening Skills]],
[607080]=[[Attack Skills]],
[607081]=[[Defensive Skills]],
[607082]=[[Disruption Skills]],
[607083]=[[Crystal Star Upgrade]],
[607084]=[[Crystal Details]],
[607085]=[[Star Upgrade Details]],
[607086]=[[Skill Amplification]],
[607087]=[[Current crystal skill increase]],
[607088]=[[Continue to improve the combat advancement level, and the stage breakthrough can strengthen the crystal skill increase]],
[607089]=[[Next stage breakthrough: Lv.{%s1}]],
[607090]=[[Orange quality crystals cannot be used as materials for upgrading the battle of mythical beasts]],
[607091]=[[This crystal is used as a material for the advancement of the mythical beast battle, and can increase {%s1} points of experience]],
[607092]=[[This crystal has been upgraded and can be used as a material for the advanced battle of the mythical beast. It can increase {%s1} experience points.]],
[607093]=[[Select offensive skill]],
[607094]=[[Choose your opening skill]],
[607095]=[[Select defensive skills]],
[607096]=[[Select Disruption Skill]],
[607097]='assembly',
[607098]=[[Crystallization Research Institute]],
[607099]=[[Select the crystal you want to create]],
[607100]=[[Please select experience item first]],
[607101]=[[Daily Quest reward reissue]],
[607102]=[[Dear Lord, this is the unclaimed reward for the daily quest you completed yesterday. Please check!]],
[607103]=[[Daily Quests]],
[607104]=[[Chapter Quest]],
[607105]=[[Main Quest]],
[607106]=[[Side Quest]],
[607107]=[[Next refresh: {%s1}]],
[607108]=[[We have not received any quests yet]],
[607120]=[[No crystal core obtained yet]],
[607121]='type',
[607123]='Rarity',
[607124]=[[Unlocked after {%s1} level of research institute]],
[607125]=[[Manufacturing success]],
[607126]=[[<color=#F47AFF>Battle Advanced Stage {%s1}</color> Unlock]],
[607127]=[[When the battle advances to <color=#FFC64C>Lv.{%s1}</color>, you can break through to <color=#F47AFF>Battle Advancement Stage{%s2}.</color> and keep moving forward!]],
[607128]=[[No equipable cores]],
[607129]=[[{%s1} has been applied to the solution {%s2}, do you want to install it to the current solution?]],
[607130]=[[Star Upgrade Consumption]],
[607131]=[[I haven't obtained the crystal core yet. Do I need to go to the research institute to make one?]],
[607132]=[[This crystal core is used as a material for the advancement of the divine beast's combat, and can provide {%s1} points of experience]],
[607133]=[[Select the skill core you want to reset. \nAfter resetting, the core will become un-upgraded, and all cores with the same name consumed during the upgrade will be returned. \nOnly cores not installed in the plan can be reset.]],
[607134]=[[Please select any crystal core first]],
[607135]=[[No resettable cores]],
[607136]=[[The crystal core solution {%s1} is currently being used in the formation {%s2}. Does it need to be replaced?]],
[607137]=[[Change formation]],
[607138]=[[Crystal Core Combat Power]],
[607139]=[[Modification plan]],
[607140]=[[Select the crystal core skill plan]],
[607141]=[[Skill Core]],
[607142]=[[Combat Advancement Level]],
[607143]=[[Crystal Core Star]],
[607144]=[[1. The mythical beast will continuously attack the enemy during battle, but will not be attacked itself.]],
[607145]=[[2. Divine Beasts gain a certain amount of experience progress when consuming experience items. Once the progress is reached, they level up and their attributes increase. <color=#39A242>Divine Beast attributes are converted to our hero attributes at a certain ratio. The conversion ratio and the actual hero attribute increase can be found in the relevant interface.</color>\nThere is a chance of a critical hit when gaining experience: When the progress bar is less than 80%, there is a 50% chance of a critical hit, doubling the experience progress. When the progress bar is greater than or equal to 80%, critical hits will not trigger. Furthermore, critical hits will not occur during breakthrough stages.]],
[607146]=[[3. When the Divine Beast's level reaches <color=#39A242>30/50/70/90/110</color>, its skill level will increase.]],
[607147]=[[4. When your castle reaches level 15, the <color=#39A242>Divine Beast Mark</color> feature becomes available. Divine Beasts have six marks, each of which significantly boosts a hero's stats. Three marks of the same type and level can be combined to create a higher-level mark of the same type.]],
[607148]=[[5. On the 82nd day of the server launch, the Battle Advancement and Skill Core systems will be unlocked. Battle Advancement allows players to further strengthen their Divine Beasts and improve their attributes. Reaching Battle Advancement Level 10 unlocks the Skill Core system. Once the Core system is unlocked, each Core Plan can install one Opening Skill, Offensive Skill, Disruption Skill, and Defensive Skill. Before a team battles, you can select a Plan to grant your Divine Beast the Core's skill effects. SSR and UR quality Cores can be upgraded by consuming Cores of the same name, further enhancing their skill effects. Furthermore, unused Cores can be used for Battle Advancement. Every time you break through a battle advancement stage, you can increase the crystal core's <color=#FFC64C>skill amplification</color>, further enhancing the crystal core's skill values.]],
[607149]=[[There is currently no formation available, please go to build a formation first]],
[607150]=[[Crystal Core Research Institute]],
[607151]=[[Ability to create and dismantle crystal cores]],
[607152]=[[Keep this crystal core and use it to upgrade the crystal core of the same name. Do you still want to consume it for combat advancement upgrades?]],
[607153]=[[Perhaps the Lord should equip this crystal core. Should it still be consumed for combat advancement and upgrade?]],
[607201]='Mall',
[607202]=[[Value Event]],
[607203]='Events',
[607204]=[[Kingdoms Showdown]],
[607205]=[[Union Showdown]],
[607206]=[[War of Royal City]],
[607207]=[[Lord's Journey]],
[607208]=[[City Clash]],
[607209]=[[1st Recharge]],
[607301]=[[Destroy the zombies that roam the castle!]],
[607302]=[[Good job! Our victory is not far off!]],
[607303]=[[Join the team]],
[607304]=[[Anyone, we need help!]],
[607305]=[[Help Me]],
[607306]=[[The monster is about to <color=#FFE746> lose controll!! </color>]],
[607307]=[[This chain is very strong and we have to find 5 keys that will be able to open the shackles!]],
[607308]=[[I'm so scared...... Please don't leave me behind.]],
[607309]=[[Thank you for filling me with hope again!]],
[607310]=[[Come on, lord!]],
[607311]='Rescue',
[607312]=[[Save me]],
[607313]=[[Remove the chains]],
[607314]='Go',
[607315]=[[Please go to the area and clear it to obtain items.]],
[607316]=[[Thank goodness you came at the right time!]],
[607317]=[[There are zombies everywhere, and we have a lot of buddies trapped in front......]],
[607318]=[[Repair the Castle Gate and rescue your ally!]],
[607319]=[[Thank you for your assistance!]],
[607320]=[[Take me with you and let me help you defeat the zombie bosses!]],
[607321]=[[Thanks for the rescue, if you come one step too late, I won't be able to hold on.]],
[607322]=[[The battle isn't over yet, please give us a boost and fight the zombie boss together!]],
[607323]=[[Delightful! Lord, take the castle from that monster!]],
[607324]=[[Your struggles are in vain! Zombies, let's destroy everything here!]],
[607325]=[[I seem to see the key, but there are zombies along the way.]],
[607326]=[[Let's do a big job and rescue the princess!]],
[607327]=[[Damn it! Is it this endless group of zombies again?]],
[607328]=[[Hey, you're injured. Need help?]],
[607329]=[[Great, the friendly forces have finally arrived, and I'm full of fighting spirit again!]],
[607330]=[[Look, that skeletal dragon is on the lookout, and we can't afford to take it lightly.]],
[607331]=[[Maybe you should level up first to increase your Team's CP.]],
[607332]=[[Get ready and go!]],
[607333]=[[The zombies in the area have been expelled, and we can liberate the area!]],
[607334]=[[The castle is still occupied by zombies, please drive away the zombies first!]],
[607335]=[[The opponent is surprisingly strong, we failed!]],
[607336]=[[In that case...... Leave some surprises for you! My pet, that woman is left to you!]],
[607337]=[[Let's send scouts and find the key.]],
[607338]=[[The monster is about to get out of control! Hurry up and find a way to save her!]],
[607339]=[[Doomsday Boss]],
[607340]=[[See! There's a lost wagon here!]],
[607341]=[[Perhaps we can use it to collect loot from our adventures.]],
[607342]=[[From now on, this will be our trophie wagon, and the work of collecting the treasure will be on me!]],
[607343]=[[Lord, remember to come regularly to unload.]],
[607344]=[[The zombie leader left behind a monster and imprisoned the princess!]],
[607345]=[[Destroy the zombies nearby, I'm willing to work for you!]],
[607346]=[[Lord, please help me!]],
[607347]=[[We defeated the zombies and managed to save the territory!]],
[607348]=[[But the territory is badly damaged, and everything is waiting to be done; We need to continue to <color=#2EC564>repair the ruins, produce resources, and develop the territory!</color>]],
[607349]=[[You are just seeking your own doom!!]],
[607350]=[[Destroy them!!]],
[607351]=[[Found after unlocking [Scout] ]],
[607352]=[[After building [Scout], more main story stages will be unlocked!]],
[607353]=[[Unlock after passing [{%s1}] main level]],
[607354]=[[Go further in the Dangerous Tile to unlock more stages and keep fighting!]],
[607355]=[[Clear [Adventure] NO.{%s1} to unlock]],
[607356]=[[Go further in Adventure to unlock more stages and keep fighting!]],
[607357]=[[Tile {%s1} unlocked]],
[607358]=[[Eat...... their brains!]],
[607359]=[[Brain...... Crunchy ......]],
[607360]=[[Flower, are you hungry too......]],
[607361]=[[Hmm? The scent of humans...]],
[607362]=[[      One of the strongest DPS]],
[607363]=[[Upgrade your castle to increase your strength]],
[607364]=[[My Lord, the Intelligence Center has gathered reports from across the realm. Let's review them at once.]],
[607365]=[[We've located valuable resources scattered throughout the land. Collecting them will accelerate our town's revival!]],
[607366]=[[Splendid! I can't wait to explore. Let's depart immediately!]],
[607367]=[[You've unlocked the <color=#319f38>Union Center</color>! Allies are vital in this perilous world.]],
[607368]=[[Time is ticking. Build the Union Center now and seek some allies!]],
[607369]=[[Hmm? Where did this giant oak barrel come from?]],
[607370]=[[This is the lost barrel from the Acorn Tavern. Return it to the tavern keeper for some lucrative quests!]],
[607371]=[[Upgrade soldiers to boost CP.]],
[607372]=[[I've been trapped here too long. Liberate this area, and I'll swear my loyalty to you!]],
[607373]=[[I can unleash massive AoE damage, wiping out hordes of zombies instantly!]],
[607374]=[[I can shield your troops, enhancing all teams' capabilities.]],
[607375]=[[New Key Obtained]],
[607376]=[[Rescue <color=#F071FF>Verna</color> and she will join you!]],
[607377]=[[Requires <color=#4BFF27>{%s1}</color> more Key(s)]],
[607378]=[[Your Highness, I've found <color=#EE622A>the first key!</color> One lock is open!]],
[607379]=[[Ah, my brave knight! Thanks to you, this chain has finally loosened a bit.]],
[607380]=[[Please find the remaining keys. You're my last hope.]],
[607381]=[[Your Highness, look! I've found another key!]],
[607382]=[[Well done! Each lock you open brings me closer to freedom.]],
[607383]=[[Your bravery is truly appreciated. Only <color=#EE622A>three locks</color> remain, please don't give up!]],
[607384]=[[Your Highness, hold on. <color=#EE622A>The third lock</color> is undone!]],
[607385]=[[A guardian angel descends! I can feel the chain lifting!]],
[607386]=[[Thank you! Just <color=#EE622A>two more to go</color>. Freedom is within reach!]],
[607387]=[[Rest assured that I'll bring you back safe and sound!]],
[607388]=[[Your Highness, <color=#EE622A>the fourth key</color>! You will soon be freed!]],
[607389]=[[Almost there! Just thinking about breaking free makes my heart race!]],
[607390]=[[Only <color=#EE622A>one lock left!</color> Thank you, fearless warrior. I'll never forget what you've done for me. Please find the final key!]],
[607391]=[[Your Highness, this is <color=#EE622A>the last key!</color> We've done it!]],
[607392]=[[Finally... I'm about to be free! Unlock it! Let me be bathed in sunshine once more!]],
[607393]=[[I'm free, finally! Words fail me in expressing my gratitude. You'll forever be my greatest hero!]],
[607394]=[[Clear tiles to obtain Keys]],
[607395]=[[Requires Lv. {%s1} castle]],
[607396]=[[Upgrade your castle to unlock more stages. Keep up the good work!]],
[607397]=[[Soldier Assault]],
[607398]=[[Cannon War]],
[607399]=[[Save the Dog]],
[607400]=[[Quest Complete]],
[607401]=[[Quest Failed]],
[607402]=[[Exceeded {%s1}% players]],
[607403]=[[Unlocks in {%s1}h{%s2}m]],
[607404]=[[Unlocks in {%s1}m{%s3}s]],
[607405]=[[Pass through doors with multipliers to gain more soldiers.]],
[607406]=[[Build the Mini Games building to claim soldier rewards.]],
[607407]=[[New levels unlocked]],
[607408]='200+',
[607409]=[[There can't be any slackness on the defense line.]],
[607410]=[[There is an unusual smell in the wind.]],
[607411]=[[May today be peaceful too.]],
[607412]=[[My blade is always at the ready.]],
[607413]=[[This glass of beer is the best reward for a day's hard work.]],
[607414]=[[Hush, let me listen to the news brought by the wind.]],
[607415]=[[A letter from home is worth a thousand pieces of gold.]],
[607416]=[[Please give instructions.]],
[607417]=[[My sword is at your service.]],
[607418]=[[Any findings?]],
[607419]=[[I'm ready.]],
[607420]=[[Got it, going there immediately.]],
[607421]=[[Target confirmed.]],
[607422]=[[This road is safe, follow me.]],
[607423]=[[Leave it to me.]],
[607424]=[[Set off.]],
[607425]=[[Homeless stray dogs]],
[607426]=[[Poor little guy, who can save it?]],
[607427]=[[Dog House]],
[607428]=[[A cozy cabin that provides shelter for dogs]],
[607429]=[[The exquisite and gorgeous cottage is a happy home for dogs]],
[607430]=[[The puppy is out treasure hunting...]],
[607431]=[[Receive Rewards]],
[607432]=[[Adopt stray dogs]],
[607433]=[[I will search for treasure for my master 24 hours a day!]],
[607434]=[[Permanently unlock the "Pet Treasure Hunt" feature]],
[607435]=[[Can you take me in?]],
[607436]=[[Reward Description]],
[607437]=[[Single mining reward]],
[607438]=[[Ordinary treasure hunt]],
[607439]=[[Rare Treasure Hunt]],
[607440]=[[Legendary Treasure Hunt]],
[607441]=[[When a pet finds a treasure chest, it has a chance to receive the following rewards:]],
[607442]=[[Mining probability: {%s1}%]],
[607443]=[[Upgrade the dog house!]],
[607444]=[[Rare Treasure Hunt has been upgraded to "Legendary Treasure Hunt"!]],
[607445]=[[The daily treasure hunt limit is 10 (<color=#319F38>next level: 15</color>)]],
[607446]=[[Pets can find treasure chests: Rare treasure chests (<color=#319F38>Next level: Legendary treasure chests</color>)]],
[607447]=[[The daily treasure hunt limit is 15]],
[607448]=[[Pets can find treasure chests: Legendary treasure chests]],
[608000]=[[Under blood-red moonlight, the abandoned church rings a rusty bell, awakening the slumbering undead]],
[608001]=[[The underground crypt collapses, holy water spills over, corroding the stone sarcophagus with foul green liquid]],
[608002]=[[The strangled confessor becomes a zombie, and an iron cage falls into a swarm of crows]],
[608003]=[[Crossing the stinking swamp, the corpse-eating water ghost drags the sunken skeletal carriage]],
[608004]=[[Blood diary Tainted convent, holy water zombie nuns glow eyes]],
[608005]=[[At the wall breach of the collapsed city, the rotting siege engine splashes corrosive tar]],
[608006]=[[Deep in the cellar, a giant zombie bound by iron chains breaks free from sacrificial runes]],
[608007]=[[In the moonlit cemetery, tombstones summon skeletal gardeners wielding scythes]],
[608008]=[[Drifting down the underground river, the drowned miners' floating corpses cling to the bottom of a raft]],
[608009]=[[The corrupted paladin summons a bone dragon for a final battle atop the collapsed bell tower]],
[608010]=[[Amid the wreckage of the plague cart, a maddened zombie horse drags steel thorns]],
[608011]=[[Inside the abandoned tribunal, walls nailed with confession scrolls bleed black blood]],
[608012]=[[In the plague laboratory, mutated tentacles tear apart zombie embryos soaked in green liquid]],
[608013]=[[The sewer gate rusts shut, bloated corpses block the gear mechanism]],
[608014]=[[Crest level Broken tanks show church experiments]],
[608015]=[[On the ghost ship's mast, a skeleton pirate with a musket ignites a nitroglycerin barrel]],
[608016]=[[The salt mine tunnel collapses, the zombie miners' eye sockets flicker with phosphorescent blue light]],
[608017]=[[The castle is cut off by the bridge, and chains strangle the climbing zombies]],
[608018]=[[In the blood-red chapel, the choir of zombies sings corrosive hymns, melting the stained-glass windows]],
[608019]=[[In the shadowy cellar, parasitic mushrooms burst from the zombies' mouths, releasing clouds of spores]],
[608020]=[[On the top floor of the decaying barn, windmill blades spin, slicing through attacking ghouls]],
[608021]=[[The stitched-up monster devours the light crystal, dark energy forming a black hole]],
[608022]=[[On the edge of the petrified forest, a zombie priest entangled in tree roots chants a fallen prayer]],
[608023]=[[The sanctum eroded by lava, where solidified magma forms twisted statues of the tortured]],
[608024]=[[Deep in the toxic mine, the necromancer feeds living hearts to the writhing flesh cocoons]],
[608025]=[[Mural clue Faded paladin sacrifice mural]],
[608026]=[[At the bottom of the tectonic rift, a zombified geologist gnaws on glowing ore]],
[608027]=[[In the giant fungal forest, spore zombies trigger purple toxic fog, creating a hallucinatory mist]],
[608028]=[[At the top of the cloud-shrouded tower, a gargoyle dives, clutching a screaming zombie infant]],
[608029]=[[The fallen pope unfurls his rotten wings, an ancient god's tendrils tear the skyline]],
[608030]=[[Broken planks snap underfoot, the zombie horde across the bridge is drawn by the noise]],
[608031]=[[Steam pipes burst intermittently, scalding gas chars the climbing zombies to bone]],
[608032]=[[The decaying chandelier falls suddenly, sparks ignite oil-soaked shrouds on the ground]],
[608033]=[[Moonlight pierces the shattered dome, as beams shift, the undead in the shadows stir to life]],
[608034]=[[In the tilted library, collapsed bookshelves create a domino effect, crushing the escape route]],
[608035]=[[The gallows chains move of their own accord, the noose tightens around the player's neck, dragging them towards the poisoned thorned bushes]],
[608036]=[[In the hall of mirrors, only the zombie in one will materialize and launch an attack]],
[608037]=[[The gear mechanism accelerates with the battle's vibrations, sharp teeth indiscriminately slicing through the living and the dead]],
[608038]=[[The underground altar's gravity reverses, zombies crawl down from the ceiling's decaying soil]],
[608039]=[[Acid rain suddenly pours down, corroding both players and zombies, but the latter feel no pain]],
[608040]=[[Phosphorescent fire flickers in the dense fog, and any moving light source triggers rotting arms to seize the beam]],
[608041]=[[The ringing in the ears distorts direction, the screams actually come from living zombies embedded in the walls]],
[608042]=[[The only light in the dark tunnel is from the glowing maggots writhing in the zombie's decaying eye sockets]],
[608043]=[[Hallucinogenic pollen fills the air, causing normal zombies to momentarily appear as friends or family]],
[608044]=[[The underground ice cavern's cold distorts one's vision, as zombies' split abdomens spew frozen blood spikes]],
[608045]=[[The plague hive is destroyed, the swarm targets living players with higher body temperatures]],
[608046]=[[The rotting suspension bridge's ends snap, requiring balance on the rope under the zombie horde's weight]],
[608047]=[[The corpse cart's rails go haywire, players must jump on top to avoid the zombie hands reaching from the sides]],
[608048]=[[Every note played by the church organ breaks the petrification seal of the corresponding statue zombie]],
[608049]=[[The underground reservoir gate leaks electricity, conductive decaying fluid electrifying every ripple with lethal shocks]],
[630001]='Stats',
[630002]='ATK',
[630003]='HP',
[630004]='DEF',
[630005]=[[Troop Capacity]],
[630006]='{%s1}/{%s2}',
[630007]='Upgrade',
[630008]=[[Upgrade your Castle first!]],
[630009]='Go',
[630010]=[[Hero not obtained]],
[630011]='Get',
[630012]='Skills',
[630013]='Max',
[630014]=[[Skill Info]],
[630015]=[[Lv. {%s1}/{%s2}]],
[630016]=[[CD: {%s1}]],
[630017]='Auto-Attack',
[630018]=[[Auto-attack is the most frequently used skill. The interval between auto-attacks is affected by Attack Speed. By default, a hero's auto-attacks target the nearest enemy.]],
[630019]='Ultimate',
[630020]=[[Ultimate is a hero's most powerful skill that may decide the outcome of a battle. The interval is affected by the Cooldown Time.]],
[630021]='Passive',
[630022]=[[Passive skills take effect after entering battle.]],
[630023]='Specialty',
[630024]=[[Specialty skills take effect outside of battle and will be directly shown on the hero's panel.]],
[630025]=[[<color=#FF3E3E>Physical DMG</color>]],
[630026]=[[The skill deals Physical DMG.]],
[630027]=[[<color=#BD3FFF>Magic DMG</color>]],
[630028]=[[The skill deals Magic DMG.]],
[630029]=[[<color=#FF3E3E>Physical Protection</color>]],
[630030]=[[The skill offers Physical protection, reducing incoming Physical DMG.]],
[630031]=[[<color=#BD3FFF>Magic Protection</color>]],
[630032]=[[The skill offers Magic protection, reducing incoming Magic DMG.]],
[630033]=[[<color=#6AD440>Buff Effect</color>]],
[630034]=[[The skill provides buffs for allies.]],
[630035]='<color=#FF3E3E>Debuff</color>',
[630036]=[[The skill inflicts debuffs on enemies.]],
[630037]=[[Ascend the hero to increase the level cap]],
[630038]=[[Test Skill 1]],
[630039]=[[Test Skill 1. It's description here. It deals {%s1} Physical DMG to the target. (CD: {%s2}s)]],
[630040]=[[Test the 1-star additional effect of skill 1]],
[630041]=[[Test the 2-star additional effect of skill 1]],
[630042]=[[Test the 3-star additional effect of skill 1]],
[630043]=[[Test the 4-star additional effect of skill 1]],
[630044]=[[Test the 5-star additional effect of skill 1]],
[630045]=[[Test skill 1's 1-star additional effect [must have rank 1 star] ]],
[630046]=[[Test skill 1's 2-star additional effect [must have 2-star rank] ]],
[630047]=[[Test the 3-star additional effect of skill 1 [must reach 3-star rank] ]],
[630048]=[[Test the 4-star additional effect of skill 1 [must reach 4-star rank] ]],
[630049]=[[Test skill 1's 5-star additional effect [must have 5-star rank] ]],
[630050]=[[Test Skills 2]],
[630051]=[[Test Skill 2 [Unlocked] ]],
[630052]=[[Unlocks at Lv. 5]],
[630053]=[[This is test skill 2. This is its skill description. It can cause {%s1} points of physical damage to the target (CD: {%s2}s)]],
[630054]=[[Test skill 2's 1-star extra effect]],
[630055]=[[Test the 2-star additional effect of skill 2]],
[630056]=[[Test the 3-star additional effect of skill 2]],
[630057]=[[Test the 4-star additional effect of skill 2]],
[630058]=[[Test the 5-star additional effect of skill 2]],
[630059]=[[Test skill 2's 1-star additional effect [must have rank 1 star] ]],
[630060]=[[Test skill 2's 2-star additional effect [must have 2-star rank] ]],
[630061]=[[Test the 3-star additional effect of skill 2 [must reach 3-star rank] ]],
[630062]=[[Test the 4-star additional effect of skill 2 [must reach 4-star rank] ]],
[630063]=[[Test the 5-star additional effect of skill 2 [must reach 5-star rank] ]],
[630064]=[[Test Skill 3]],
[630065]=[[Test Skill 3 [Unlocked] ]],
[630066]=[[Unlocks at Lv. 21]],
[630067]=[[This is test skill 3. This is its skill description. It can cause {%s1} points of physical damage to the target (CD: {%s2}s)]],
[630068]=[[Test the 1-star additional effect of skill 3]],
[630069]=[[Test the 2-star additional effect of skill 3]],
[630070]=[[Test the 3-star additional effect of skill 3]],
[630071]=[[Test the 4-star additional effect of skill 3]],
[630072]=[[Test the 5-star additional effect of skill 3]],
[630073]=[[Test skill 3's 1-star additional effect [must have 1-star rank] ]],
[630074]=[[Test skill 3's 2-star additional effect [must have 2-star rank] ]],
[630075]=[[Test the 3-star additional effect of skill 3 [must reach 3-star rank] ]],
[630076]=[[Test the 4-star additional effect of skill 3 [must reach 4-star rank] ]],
[630077]=[[Test the 5-star additional effect of skill 3 [must reach 5-star rank] ]],
[630078]=[[Test Skills 4]],
[630079]=[[Test Skill 4 [Unlocked] ]],
[630080]=[[Unlocks at Lv. 31 and 4-Star]],
[630081]=[[This is test skill 4. It is a specialty skill that provides you with a bunch of unknown attributes, increasing your health, attack power, and defense power by {%s1}; skill cooling speed increases by {%s2}]],
[630082]='Star',
[630083]='{%s1}-Star',
[630084]=[[{%s1}-Star T{%s2}]],
[630085]=[[Hero HP]],
[630086]=[[Hero ATK]],
[630087]=[[Hero DEF]],
[630088]='Ascend',
[630089]='Locked',
[630090]=[[Awaken to {%s1}-Star]],
[630091]=[[Unlocks at Lv. {%s1} and {%s2}-Star]],
[630092]=[[Max Level]],
[630093]=[[Upgrade Skill]],
[630094]=[[Capable of taking a great deal of DMG.]],
[630095]=[[Capable of dealing massive DMG.]],
[630096]=[[Capable of offering buffs.]],
[630097]=[[Unlocks at Lv. {%s1}]],
[630098]=[[Unlocks at {%s1}-Star]],
[630099]=[[Obtain Hero]],
[630100]='DEF',
[630101]='ATK',
[630102]='Support',
[630103]=[[Story Unlock Condition: 1-Star Hero]],
[630104]=[[Story Unlock Condition: 2-Stars Hero]],
[630105]=[[Story Unlock Condition: 3-Stars Hero]],
[630106]=[[All Faction Heroes]],
[631001]='Rewards',
[631002]='DMG',
[631003]=[[Damage Taken]],
[631004]=[[Upgrade Hero]],
[631005]=[[Obtain S+ Hero]],
[631006]=[[Upgrade Hero]],
[631007]=[[Ascend Hero]],
[631008]=[[Upgrade Skill]],
[631009]=[[Equip Gear]],
[631010]=[[Upgrade Gear]],
[631011]=[[Mystic Beast Upgrade]],
[631012]=[[Obtain S+ Hero]],
[631013]=[[Adjust Lineup]],
[631014]='Share',
[631015]='Back',
[631016]='Go',
[631017]=[[Recruit Hero]],
[631018]=[[Hero Details]],
[631019]=[[Base Stats]],
[631020]='Mergeable',
[631021]=[[The hero's Honor Wall unlocked]],
[650001]='Home',
[650002]='Male',
[650003]='Female',
[650004]=[[Used Attributes:]],
[650005]=[[Owned Attributes:]],
[650006]=[[Change Gender]],
[650007]=[[Personal Information]],
[650008]='N/A',
[650009]=[[You don't have an union yet, join one now]],
[650010]=[[The Lord's total CP]],
[650011]=[[Total Kills of the Lord]],
[650012]='Decorate',
[650013]=[[Skin Buff]],
[650014]=[[You don't have this type of costume yet]],
[650015]=[[Cannot be the same as the original name]],
[650016]=[[Valid time:]],
[650017]=[[Unlock Description Test]],
[650018]=[[Default Castle]],
[650019]=[[Female Default Castle]],
[650020]=[[Get path description test]],
[650021]=[[Carrot Cottage]],
[650022]=[[{%s1} days {%s2} hours]],
[650023]=[[{%s1} hours]],
[650024]=[[{%s1} days]],
[650025]='Settings',
[650026]='Help',
[650027]=[[Action in cooldown]],
[650028]='Copied',
[650029]=[[This name is too short]],
[650030]=[[This name is too long]],
[650031]=[[This name contains illegal characters]],
[650032]='Rename',
[650033]=[[The name is empty]],
[650034]='Avatar',
[650035]='Home',
[650036]=[[Avatar Frame]],
[650037]='Title',
[650038]='N/A',
[650039]=[[Insufficient Diamonds]],
[650040]='Modified',
[650041]='Leaderboard',
[650042]=[[Tap a blank space to close]],
[650043]=[[Not owned]],
[650044]=[[Validity period:]],
[650045]=[[Obtained from:]],
[650046]=[[View Info]],
[650047]='Like',
[650048]='Add',
[650049]='Chat',
[650050]=[[Likes maxed out]],
[650051]=[[First Time Free]],
[650052]=[[The length should be between 4–16 characters...]],
[650053]='Save',
[650054]='Applied',
[650055]='Blacklisted',
[650056]=[[Skin Buff]],
[650057]=[[Personal information function locked]],
[650058]=[[Purchase the Monthly Card to Receive]],
[650059]=[[Obtained through ranking in the Peak Arena]],
[650060]=[[Obtained through ranking in the Novice Arena]],
[650061]=[[In the Kingdom Showdown, the exclusive avatar frame for the president in the Champion Zone!]],
[650062]=[[Kingdom Battle Event]],
[650063]=[[In the Kingdom Showdown, exclusive avatar frame for the Champion Zone!]],
[650064]=[[Default Avatar Frame]],
[650065]=[[Monthly Card Avatar Frame]],
[650066]=[[Marshal Avatar Frame]],
[650067]=[[General Avatar Frame]],
[650068]=[[Captain Avatar Frame]],
[650069]=[[Commandant Avatar Frame]],
[650070]=[[Centurion Avatar Frame]],
[650071]=[[King's Sigh Avatar Frame]],
[650072]=[[Conqueror Avatar Frame]],
[650073]=[[King's Master Avatar Frame]],
[650074]=[[The other party has not joined the union]],
[650075]=[[Female Exclusive]],
[650076]=[[Limited time event available, so stay tuned]],
[650077]=[[Whether to consume a name change card to change the name]],
[650078]=[[The recharge function is not enabled. After it is enabled, complete the first recharge to permanently unlock the custom avatar privilege!]],
[650079]=[[Unlock permanent privileges: Customize your avatar and upload your favorite avatar!]],
[650080]=[[Unlock now]],
[650081]=[[Warm reminder: After the custom avatar is uploaded and reviewed, it will not be uploaded again within <color=#e18f01>24 hours</color>. Please confirm before submitting.]],
[650082]=[[Upload avatar]],
[650083]=[[Avatar uploaded]],
[650084]=[[Avatar upload failed, please try again]],
[650085]=[[The custom avatar upload is cooling down and can be uploaded once every <color=#e18f01>24 hours</color>. Please wait patiently.]],
[650086]=[[Cooldown countdown: {%s1}]],
[650087]=[[Under review]],
[650088]=[[The avatar is under review and can be used after it is approved.]],
[650089]=[[Are you sure you want to delete this avatar?]],
[650090]=[[Avatar deleted]],
[650091]=[[Custom avatar review failed]],
[650092]=[[Sorry, the custom avatar image you uploaded did not meet the requirements and failed the review. Please re-upload a qualified image or contact the game customer service for processing. I wish you a happy game!]],
[650093]=[[Obtained by ranking high in the 3V3 Arena]],
[650094]=[[Obtained via Recharge Gifts]],
[650095]='Like',
[650096]='Liked!',
[650097]=[[Activate Elite Pass to use]],
[650098]=[[Obtained via Sera's Trial.]],
[650099]=[[Block List]],
[650100]=[[The list is empty]],
[650101]=[[Kingdoms {%s1}]],
[650102]='Cancel',
[650103]=[[{%s1} has been unblocked]],
[650104]=[[The Speed ​​Wings skin can be obtained by participating in the [Duel League] ]],
[650501]=[[Hero HP]],
[650502]=[[Hero ATK]],
[650503]=[[Hero DEF]],
[650504]=[[Rage Limit]],
[650505]='Speed',
[650506]=[[Crit Rate]],
[650507]=[[CRIT RES]],
[650508]=[[Crit DMG]],
[650509]=[[CRIT DMG RES]],
[650510]=[[General DMG Increase]],
[650511]=[[General DMG Taken Reduction]],
[650512]=[[DMG Dealt When Countering Increase]],
[650513]=[[DMG Taken Reduction When Countering]],
[650514]=[[DMG Dealt to Monsters Increase]],
[650515]=[[DMG Taken from Monsters Reduction]],
[650516]=[[Physical DMG Increase]],
[650517]=[[Physical DMG Taken Reduction]],
[650518]=[[Increased Energy DMG]],
[650519]=[[Reduced Energy DMG taken]],
[650520]=[[DMG Dealt by Forest Heroes Increase]],
[650521]=[[Human Hero Damage Increase]],
[650522]=[[Nightfall Hero Damage Increase]],
[650523]=[[General Soldier Capacity]],
[650524]=[[Forest Hero Troop Capacity]],
[650525]=[[Human Hero Troop Capacity]],
[650526]=[[Nightfall Hero Troop Capacity]],
[650527]=[[Skill DMG Increase]],
[650528]=[[Hero Rage Recovery per Turn]],
[650529]=[[Mystic Beast HP]],
[650530]=[[Mystic Beast ATK]],
[650531]=[[Mystic Beast DEF]],
[650541]=[[Initial Rage]],
[650601]=[[Hero HP]],
[650602]=[[Hero ATK]],
[650603]=[[Hero DEF]],
[650604]=[[Forest HP]],
[650605]=[[Forest ATK]],
[650606]=[[Forest DEF]],
[650607]=[[Human Hero HP]],
[650608]=[[Human Hero ATK]],
[650609]=[[Human Hero DEF]],
[650610]=[[Nightfall Hero HP]],
[650611]=[[Nightfall Hero ATK]],
[650612]=[[Nightfall Hero DEF]],
[650701]=[[Hero HP]],
[650702]=[[Hero ATK]],
[650703]=[[Hero DEF]],
[650704]=[[Forest HP]],
[650705]=[[Forest ATK]],
[650706]=[[Forest DEF]],
[650707]=[[Human Hero HP]],
[650708]=[[Human Hero ATK]],
[650709]=[[Human Hero DEF]],
[650710]=[[Nightfall Hero HP]],
[650711]=[[Nightfall Hero ATK]],
[650712]=[[Nightfall Hero DEF]],
[650713]=[[HP Increase When Rallying]],
[650714]=[[ATK Increase When Rallying]],
[650715]=[[DEF Increase When Rallying]],
[650716]=[[HP Increase When Garrisoning]],
[650717]=[[ATK Increase When Garrisoning]],
[650718]=[[DEF Increase When Garrisoning]],
[650719]=[[HP Increase When Attacking Enemy Base]],
[650720]=[[ATK Increase When Attacking Enemy Base]],
[650721]=[[DEF Increase When Attacking Enemy Base]],
[650722]=[[HP Increase When Defending the City]],
[650723]=[[ATK Increase When Defending the City]],
[650724]=[[DEF Increase When Defending the City]],
[650725]=[[HP, ATK, and DEF Increase When Assaulting with a Cart]],
[650726]=[[HP, ATK, and DEF Increase When Defending with a Cart]],
[650801]=[[Food Output Speed Increase]],
[650802]=[[Iron Mine Output Speed Increase]],
[650803]=[[Gold Mine Output Speed Increase]],
[650804]=[[Food Protection Capacity Increase]],
[650805]=[[Iron Ore Protection Capacity Increase]],
[650806]=[[Gold Protection Capacity Increase]],
[650807]=[[AFK Time Accumulation Increase]],
[650808]=[[AFK Resource Output Increase]],
[650809]=[[Gear Factory Production Speed Up]],
[650810]=[[Gear Production Gold Cost Reduction]],
[650811]=[[Free Advanced Recruit Cooldown Reduction]],
[650812]=[[Free Survivor Recruit Cooldown Reduction]],
[650813]=[[Extra Barrack/Farmland/Iron Mine/Gold Mine]],
[650814]=[[Food Protection Capacity]],
[650815]=[[Iron Protection Capacity]],
[650816]=[[Gold Protection Capacity]],
[650901]=[[Barrack Training Capacity Increase]],
[650902]=[[Soldier Training Speed Increase]],
[650903]=[[Soldier Training Resource Cost Reduction]],
[650904]=[[Hospital Capacity]],
[650905]=[[Hospital Capacity Expansion]],
[650906]=[[Wounded Healing Speed Increase]],
[650907]=[[Healing Resource Cost Reduction]],
[650908]=[[Drill Ground Capacity]],
[650909]=[[Drill Ground Capacity Increase]],
[650910]=[[Troop HP Up]],
[650911]=[[Troop ATK Up]],
[650912]=[[Troop DEF Up]],
[650913]=[[Troop HP Up]],
[650914]=[[Troop ATK Up]],
[650915]=[[Troop DEF Up]],
[650916]=[[Increase Soldier Morale]],
[650917]=[[Soldier Load Increase]],
[650918]=[[Unlock Tier 10 Soldier Training]],
[650919]=[[Max Soldier Training Capacity]],
[651001]=[[Auto Dispatch Offline Members for Rally]],
[651002]=[[Help Limit]],
[651003]=[[Extra Time Reduction per Help]],
[651004]=[[Building Speed Up]],
[651005]=[[Construction Resource Cost Reduction]],
[651006]=[[Free Construction Speed-up Time]],
[651007]=[[Technology Research Speed UP]],
[651008]=[[Technology Resource Cost Reduction]],
[651009]=[[Free Technology Speed-up Time]],
[651010]=[[March Speed Up]],
[651011]=[[March Speed Up vs Zombies]],
[651012]=[[March Speed Up vs Resource Points]],
[651013]=[[March Speed Up vs Other Lords]],
[651014]=[[March Speed Up vs Cities]],
[651015]=[[March Speed Up for Garrison]],
[651016]=[[All Teams Collection Speed Up]],
[651017]=[[Food Collection Speed Increase]],
[651018]=[[Iron Ore Collection Speed Increase]],
[651019]=[[Gold Collection Speed Increase]],
[651020]=[[Load Increase]],
[651201]=[[Hero Total ATK]],
[651202]=[[Hero Total HP]],
[651203]=[[Hero Total DEF]],
[651204]=[[Troop Capacity]],
[651205]=[[DMG Taken by Forest Heroes Reduction]],
[651206]=[[DMG Taken by Human Heroes Reduction]],
[651207]=[[DMG Taken by Nightfall Heroes Reduction]],
[651208]=[[PVP Defender Soldier Death Rate Increase]],
[651209]=[[PVP Defender Soldier Death Rate Reduction]],
[651210]=[[Soldier Death Rate Reduction after City Breach]],
[651401]=[[Level Bonus]],
[651402]=[[Star Upgrade Bonus]],
[651403]='Skills',
[651404]=[[Gear Bonus]],
[651405]=[[Mystic Beast Bonus]],
[651406]=[[Wall of Honor]],
[651407]='Survivor',
[651408]='Decoration',
[651409]=[[Building Bonuses]],
[651410]=[[Technology Bonus]],
[651411]=[[Union Technology]],
[651412]='City',
[651413]='Title',
[651414]='Outfit',
[651415]='VIP',
[651416]=[[Deluxe Monthly Card]],
[651417]=[[Intelligence Center Chest per Quest]],
[651418]=[[Daily chest acquisition limit]],
[651419]=[[Chest Upgraded to Rare Adventure Chest]],
[651420]=[[Chest Upgraded: Deluxe Expedition Chest]],
[651501]='Special',
[651502]='Resource',
[651503]=[[Speed Up]],
[651504]='Hero',
[651505]='Gear',
[651506]=[[You have no special items yet]],
[651507]=[[You have no resource items yet]],
[651508]=[[You have no speed up items yet]],
[651509]=[[You have no hero items yet]],
[651510]=[[You have no gear items yet]],
[651511]=[[Common Rewards]],
[651512]=[[Superior Rewards]],
[651513]=[[Rare Rewards]],
[651514]=[[Epic Rewards]],
[651515]=[[Legendary Rewards]],
[651516]=[[Mythic Rewards]],
[651517]=[[Random chest for testing]],
[651518]=[[This is a test item]],
[651519]=[[Probability disclosure]],
[651520]=[[Mythic Rewards]],
[651521]=[[Final Rewards]],
[651522]='1k',
[651523]='10k',
[651524]='50k',
[651525]='1M',
[651526]='5M',
[651527]='15M',
[651528]='1h',
[651529]='8h',
[651530]='5',
[651531]='10',
[651532]='50',
[651533]='100',
[652001]=[[Demon Castle]],
[652002]='Zombie',
[652003]=[[Damn it, I'll be back!]],
[652004]=[[You're no match for me.]],
[652005]=[[Intel Update]],
[652006]='Description',
[652007]=[[Come save us!]],
[652008]=[[On our way!]],
[652009]=[[Thank you so much! I'll do my best to serve your camp!]],
[652010]='Welcome!',
[652011]=[[Demon Castle]],
[652012]=[[Zombie Invasion]],
[652013]=[[Elite Zombie]],
[652014]=[[Gathering Discovery]],
[652015]=[[Resource Collection]],
[652016]=[[Rescue Survivors]],
[652017]=[[Verna's Challenge]],
[652018]=[[Treasure Digging]],
[652019]=[[Help Allies]],
[652020]=[[Zombie Overlord]],
[652021]=[[The Demon Castle has appeared. Defeat it quickly!]],
[652022]=[[Defeat zombies to earn resource rewards!]],
[652023]=[[A rally is required to attack a powerful demon.]],
[652024]=[[A digging spot has been discovered! Dispatch your troops now!]],
[652025]=[[These are valuable resources. Collect them now!]],
[652026]=[[Complete challenges to earn massive rewards.]],
[652027]=[[Treasure spotted! Gather your allies to dig it up!]],
[652028]=[[Help your allies complete Intelligence Quests!]],
[652029]=[[You need the help of your allies for a chance to defeat this monster.]],
[652030]=[[Digging Participation Rewards]],
[652031]=[[Here are your rewards for participating in a dig.]],
[652032]=[[Rare Gold Mine]],
[652033]=[[Rare Iron Mine]],
[652034]=[[Rare Farmland]],
[652035]=[[Refresh every 6h]],
[652036]=[[Stored Event Count]],
[652037]=[[Max Event Count]],
[652038]=[[Gold, Iron, and Food Bonus]],
[652039]=[[Upgrade Conditions]],
[652040]=[[Complete Intelligence Quest {%s1} time(s)]],
[652041]=[[Claim Rewards]],
[652042]=[[Quest Storage]],
[652043]='Gather',
[652044]='Battle',
[652045]=[[Complete various Intelligence Quests to earn big rewards, but each quest has an expiry time, so finish them with time to spare!]],
[652046]=[[If your quest list is full when performing a quest refresh, the quests will be stored for later. However, the number of stored quests is limited, so don't forget to complete them in time.]],
[652047]=[[Send troops to occupy resource points for resource gathering. The higher the resource point level, the more resources you will gather.]],
[652048]=[[You can battle against non-allied Lords in the world, such as by plundering their castles or attacking their resource points. But please note that engaging in battles with other Lords is risky, as it may result in soldier losses, so proceed with caution!]],
[652049]='Common',
[652050]='Rare',
[652051]='Unique',
[652052]='Epic',
[652053]='Legendary',
[652054]=[[Anomaly detected at this location. Send troops to collect samples.]],
[652055]=[[Gathering Speed]],
[652056]=[[Expiry Time]],
[652057]=[[Intelligence Level Info]],
[652058]=[[1. After completing a certain number of Intelligence Quests, you can increase your Intelligence Level, improving various Intelligence stats.]],
[652059]=[[2. Each Intelligence Level upgrade grants extra Intelligence Quests.]],
[652060]=[[3. When Intelligence reaches Lv. 8, you can auto-complete Help Allies and Resource Collection quests, granting a continuous stream of Intelligence Quest rewards.\n]],
[652061]=[[The following Intelligence Quests appear only once per day:]],
[652062]=[[1. Eliminate Elite Zombies (Rare: 60%; Epic: 30%; Legendary: 10%)]],
[652063]=[[2. Defeat Elite Zombies (Rare: 60%; Epic: 30%; Legendary: 10%)]],
[652064]=[[3. Kill Zombie Overlord (Epic: 80%; Legendary: 20%. Requires Intelligence Lv. 7 and Castle Lv. 15.)]],
[652065]=[[4. Rescue Resistance Force (Rare: 60%; Epic: 30%; Legendary: 10%)\n]],
[652066]=[[Intelligence Quest Drop Rates]],
[652067]=[[1. Clear Zombies: 29.93% (Common: 8.98%; Superior: 7.48%; Rare: 5.99%; Epic: 4.49%; Legendary: 2.99%)]],
[652068]=[[2. Help Allies: 29.93% (Common: 8.98%; Superior: 7.48%; Rare: 5.99%; Epic: 4.49%; Legendary: 2.99%)]],
[652069]=[[3. Resource Collection: 19.95% (Common: 9.98%; Superior: 5.99%; Rare: 2.99%; Epic: 0.79%; Legendary: 0.2%)]],
[652070]=[[4. Gather Resources: 19.95% (Rare: 9.98%; Epic: 5.98%; Legendary: 3.99%)]],
[652071]=[[5. Digging Quest: 0.24% (Legendary: 0.24%. Requires Intelligence Lv. 6.)\n]],
[652072]=[[Extra Intelligence Quests upon upgrades:]],
[652073]=[[Lv. 2: Clear Zombies x3]],
[652074]=[[Lv. 3: Diamond Mine x1, Help Allies x2]],
[652075]=[[Lv. 4: Defeat Elite Zombies x1, Diamond Mine x1, Help Allies x2]],
[652076]=[[Lv. 5: Eliminate Elite Zombies x1, Diamond Mine x1, Resource Collection x2]],
[652077]=[[Lv. 6: Diamond Mine x1, Gold Mine x1, Help Allies x2]],
[652078]=[[Lv. 7: Diamond Mine x2, Gold Mine x1, Resource Collection x2]],
[652079]=[[Lv. 8 or above: Diamond Mine x1, Gold Mine x1, Resource Collection x2, Clear Zombies x1\n]],
[652080]=[[Special Note: If you haven't joined a Union yet, you will not receive "Help Allies" as Intelligence Quests.]],
[652081]=[[Next Refresh:]],
[652082]=[[Lucky you! You got double rewards when claiming the treasure!]],
[652083]=[[The {%s1} treasure at {%s2} has been dug up!]],
[652084]='{%s1}s',
[652085]=[[{%s1}m {%s2}s]],
[652086]=[[{%s1}h {%s2}m]],
[652087]=[[Cannot dig up another Union's treasures.]],
[652088]=[[Too late! The rewards have already been claimed.]],
[652089]=[[Congratulations! You've earned a treasure reward.]],
[652090]=[[{%s1}'s Digging Spot]],
[652091]=[[Gathering Speed +100%]],
[652092]=[[Gathering Speed +200%]],
[652093]=[[Gathering Speed +300%]],
[652094]=[[Gathering Speed +10,000%]],
[652095]=[[Gathering Speed +20,000%]],
[652096]=[[Gathering Speed +30,000%]],
[652097]=[[Rare Diamond Mine]],
[652098]=[[The rewards can only be claimed once.]],
[652099]=[[Treasure Digging]],
[652100]=[[Treasure Digging {%s1}]],
[652101]=[[Too late! The treasure has disappeared.]],
[652102]=[[What a pity, no one claimed the treasure!]],
[652103]=[[Skeleton Treasure]],
[652104]=[[Lost Treasure]],
[652105]=[[Intelligence Duration]],
[652106]=[[Level up your Intelligence to earn rewards!]],
[652107]=[[Can consume {%s2} stamina to auto-complete {%s1} quest(s)]],
[652108]=[[Auto Complete]],
[652109]=[[Treasure has disappeared.]],
[652110]=[[Complete all trials to obtain!]],
[652111]=[[Current Progress]],
[652112]=[[Complete the previous difficulty to unlock this one.]],
[652113]=[[Intel Update]],
[652114]=[[Immediately gain 20 Intelligence Quests and 100 marching stamina.]],
[652115]=[[Verna's Challenge Lv. {%s1}]],
[652116]=[[Intelligence Quests cannot be completed in a contaminated area.]],
[652117]=[[In progress]],
[652118]='Digging:',
[652119]=[[Digging Speed: 100%]],
[652120]=[[Time Consumed]],
[652121]=[[Detection Consumption:]],
[652122]=[[Treasure Rewards]],
[652123]=[[Expires in {%s1}]],
[652201]=[[The intel shows that the Demon Legion is gathering all over the world. They've built impenetrable castles and are wreaking havoc everywhere!]],
[652202]=[[Time is ticking. Let's gather our elite soldiers and wipe these demons out, bringing peace to the land!]],
[652211]=[[Zombies are really weak and killing them is a piece of cake! They carry loads of useful resources, too!]],
[652212]=[[I never expected victory to come this easily! It was served on a silver plate!]],
[652221]=[[Careful, guys! That's an elite zombie. If we rush straight in by ourselves, it'll be a suicide mission!]],
[652222]=[[Let's head back to the Union and get some reinforcements before we take on the big guy.]],
[652231]=[[Look, we've got some rare minerals here! Everything is falling into place!]],
[652232]=[[And the best part is they're super easy to mine, meaning they take a lot less time. Let's dispatch the troops here right away.]],
[652241]=[[This is the spot. Let's investigate quickly!]],
[652242]=[[No problem! Finding things is my specialty. I'll handle it no problem!]],
[652261]=[[This is the illusion array I set up. If we complete these challenges, I believe our combat ability will see a significant boost.]],
[652262]=[[Wait... Are you sure these are illusions? They look like the real deal to me!]],
[652271]=[[Oh my, I can't believe my eyes! We've stumbled upon an ancient treasure!]],
[652272]=[[The scale is beyond imagination. We'll need to gather our allies to help with the excavation. Only then can we shorten the digging time.]],
[652281]=[[Our allies have brought valuable intel. How about we help them out?]],
[652282]=[[Sure thing! That's what allies are for. In any case, it's nothing for us. We can handle it easily.]],
[652291]=[[Uh oh! That's the Zombie Overlord! There's no way we can take it down on our own!]],
[652292]=[[Calm down! Let's head back to the Union for help. With a coordinated effort, we can take that monster down for good!]],
[652293]=[[<color=#6fe978>{%s1}</color> has helped you complete the event]],
[652294]=[[Allies have assisted you in completing some of the Intelligence Quests!]],
[652295]='Thanks',
[652296]=[[Thanks sent!]],
[653001]=[[Special Mall]],
[653002]=[[Value Event]],
[653003]=[[Special Event]],
[653004]='Bag',
[653005]='Mail',
[653006]='Trial',
[653007]='Hero',
[653008]='Union',
[653009]='Town',
[653010]='World',
[653011]=[[Town Revival]],
[653012]=[[Novice Arena]],
[653013]=[[Faction Trial]],
[653014]=[[Adventure Journey]],
[653015]=[[No chat messages in this channel]],
[653016]=[[Hero Level]],
[653017]=[[Decoration & Building Attributes]],
[653018]=[[Hero Skills]],
[653019]=[[Hero Star]],
[653020]='Gear',
[653021]=[[Wall of Honor]],
[653022]=[[Mystic Beast Mark]],
[653023]=[[Mystic Beast Level]],
[653024]='Building',
[653025]='Survivor',
[653026]='Soldier',
[653027]='Technology',
[653028]=[[Drone Chip]],
[653029]=[[Hero CP]],
[653030]=[[Mystic Beast CP]],
[653031]=[[Building CP]],
[653032]=[[Soldier CP]],
[653033]=[[Technology CP]],
[653034]='Town',
[653035]=[[Peak Arena]],
[653036]=[[3V3 Arena]],
[653037]=[[Complete tile 38 to unlock the novice arena]],
[653038]=[[The castle level reaches level 15 to unlock the camp trial gameplay.]],
[653039]=[[The gameplay is about to start! Stay tuned]],
[653040]=[[The gameplay will be unlocked in {%s1}! Stay tuned!]],
[653041]=[[CP Details]],
[653501]=[[Union Altar]],
[653502]=[[Union Boss]],
[653503]=[[Union Boss Union Rewards]],
[653504]=[[Congratulations on defeating the Union Boss! Lord {%s1}'s valor has brought {%s2}x Union rewards!]],
[653505]=[[MVP Bonus]],
[653506]=[[Union Boss Personal Rewards]],
[653507]=[[You've dealt {%s1} damage to the Union Boss and gained the following rewards.]],
[653508]=[[Increases DMG to Union Boss by {%s1}.]],
[653509]=[[In battle:]],
[653510]='Preparing:',
[653511]=[[{%s1} Union:]],
[653512]=[[Reward Phase {%s1}]],
[653513]=[[Reward x{%s1}]],
[653514]=[[MVP Bonus]],
[653515]=[[Your Time Zone:]],
[653516]=[[Union Boss]],
[653517]=[[Defeat demons for rewards!]],
[653518]='Build',
[653519]='Rewards',
[653520]=[[The top damage dealer has a chance to make the rewards {%s1}.]],
[653521]=[[Current Difficulty {%s1} Best Rewards Preview]],
[653522]=[[Make an active contribution for better rewards!]],
[653523]='Ongoing:',
[653524]='Preparing:',
[653525]=[[Select a difficulty]],
[653526]=[[Your Time:]],
[653527]='Details',
[653528]=[[1. The battle phase lasts 30 minutes. Create a new Union Total DMG record to earn Union-wide rewards!\n2. The Lord with the most damage has a chance to increase everyone's rewards by <color=#E63838>10x!</color>]],
[653530]='Confirm',
[653531]='Difficulty',
[653532]='Date',
[653533]='h(s)',
[653534]='minute(s)',
[653535]=[[Start Time (Server Time)]],
[653536]=[[Start Time (Local Time)]],
[653537]='Confirm',
[653538]=[[Rewards Preview]],
[653539]=[[Union Boss Rewards Preview]],
[653540]=[[Total DMG]],
[653541]='Rewards',
[653542]=[[The above rewards have included the <color=#E63838>10x bonus</color> from MVP.]],
[653543]=[[Union Rewards]],
[653544]=[[Personal Rewards]],
[653545]=[[Union Boss Rewards]],
[653546]=[[Union Total DMG]],
[653547]=[[MVP Bonus:]],
[653548]=[[Reward x{%s1}]],
[653549]=[[Total DMG:]],
[653550]=[[DMG Ranking]],
[653551]=[[Union reward multiplier scales with the top player's total damage output!]],
[653552]=[[DMG Range]],
[653553]='Multiplier',
[653554]=[[Union Boss Rewards]],
[653555]=[[Total DMG]],
[653556]='Rewards',
[653557]=[[Current DMG]],
[653558]=[[Build Altar]],
[653559]=[[Donate altar materials for stat boosts.]],
[653560]=[[Donation Rewards:]],
[653561]=[[Bonus Details]],
[653562]='Donation',
[653563]=[[Donate All]],
[653564]=[[Level maxed]],
[653565]=[[Donate altar materials for stat boosts.]],
[653566]='Current',
[653567]=[[Join a Union!]],
[653568]=[[Please select a difficulty and start time.]],
[653569]=[[Operation only available to R4 members or above.]],
[653570]=[[Union members are fewer than {%s1}.]],
[653571]=[[Reach the fifth tier of DMG in the previous difficulty to unlock.]],
[653572]='Selected!',
[653573]=[[Status changed, please refresh!]],
[653574]=[[Please wait until preparations are over.]],
[653575]=[[Relocation unavailable as the location is occupied.]],
[653576]=[[Relocation on cooldown, please try again later.]],
[653577]=[[Insufficient items]],
[653578]=[[Please wait for an R4 member or the Leader to start.]],
[653579]=[[Event has ended.]],
[653580]=[[Rally ended.]],
[653581]=[[Only one rally can be initiated against the Union Boss.]],
[653582]=[[Selection Phase]],
[653583]=[[1. After the event starts, R4/R5 members must select a difficulty and start time. Minimum 20 members required.]],
[653584]=[[2. Once confirmed, the event enters the preparation phase.\n]],
[653585]=[[Preparation Phase]],
[653586]=[[1. Members donate resources for contribution rewards and damage bonuses.]],
[653587]=[[2. Donations continue even after goals are met.\n]],
[653588]=[[Battle Phase]],
[653589]=[[1. R4/R5 members must manually start the 30-minute battle.]],
[653590]=[[2. Once the event starts, the altar vanishes, summoning a demon that can only be attacked via rally.]],
[653591]=[[3. Rallies initiated by R4 members or above gain extra damage bonuses.]],
[653592]=[[4. Rewards will be sent based on Union and personal damage dealt at the end of the battle.\n]],
[653593]=[[Reward Settlement]],
[653594]=[[1. Higher Union Total DMG brings better rewards for all members and unlocks higher difficulties!]],
[653595]=[[2. More personal damage dealt brings better personal rewards.]],
[653596]=[[3. Top damage dealer can increase Union-wide rewards by 1–10x based on performance!]],
[653597]='Help',
[653598]=[[No bonuses]],
[653599]=[[Increases DMG to Union Boss by 5%.]],
[653600]=[[Increases DMG to Union Boss by 10%.]],
[653601]=[[Increases DMG to Union Boss by 15%.]],
[653602]=[[Increases DMG to Union Boss by 20%.]],
[653603]=[[Increases DMG to Union Boss by 25%.]],
[653604]='View',
[653605]='Start',
[653606]=[[Ready! Complete the challenge within {%s1}.]],
[653607]=[[Selected time expired. Please choose again.]],
[653608]=[[Challenge cannot be paused once started. Are you ready?]],
[653609]=[[Claim daily activity chests to obtain the item.]],
[653610]='Ready',
[653611]=[[Not enough time remaining to launch a rally. Initiate anyway?]],
[653612]=[[Union Boss Challenge Start]],
[653613]=[[No damage dealt.]],
[653614]=[[No MVP]],
[653615]=[[Join Offline]],
[654001]=[[Wanted Boss Kills Milestone Rewards]],
[654002]=[[Congrats! You've defeated the Wanted Boss {%s1} times today. Here are your rewards!]],
[654003]=[[World Boss Ranking Rewards]],
[654004]=[[Congrats! You ranked {%s1} in the World Boss event. Here are your ranking rewards!]],
[654005]=[[Inflict 1000000 damage in Code 87 with a single attack]],
[654006]=[[Inflict 5000000 damage in Code 87 with a single attack]],
[654007]=[[Inflict 10000000 damage in Code 87 with a single attack]],
[654008]=[[Inflict 20000000 damage in Code 87 with a single attack]],
[654009]=[[Inflict 50000000 damage in Code 87 with a single attack]],
[654010]=[[Inflict 100000000 damage in Code 87 with a single attack]],
[654011]=[[Inflict 200000000 damage in Code 87 with a single attack]],
[654012]=[[Inflict 500000000 damage in Code 87 with a single attack]],
[654013]=[[Inflict 1000000000 damage in Code 87 with a single attack]],
[654014]=[[Inflict 2000000000 damage in Code 87 with a single attack]],
[654015]=[[Two-headed ogre]],
[654016]=[[Violent Zombie]],
[654017]=[[Undead Dragon]],
[654018]=[[World Boss]],
[654019]=[[Server {%s1} point, {%s2} point, {%s3} point, {%s4} point appeared]],
[654020]='Achievement',
[654021]='Rewards',
[654022]='Rankings',
[654023]=[[Mission {%s5}: Attack {%s1} {%s2} times ({%s3}/{%s4})]],
[654024]=[[The remaining times today: {%s1}]],
[654025]='Go',
[654026]=[[{%s1} is refreshed]],
[654027]=[[You've completed all your Boss fights for today. Thanks for your valiant efforts!]],
[654028]=[[Today's Damage Record Broken]],
[654029]=[[Current Ranking]],
[654030]=[[After a fierce battle, you dealt massive damage to the Wanted Boss and set a new personal best for today!]],
[654031]='Confirm',
[654032]='Claim',
[654033]=[[before unlocking]],
[654034]='Achievements',
[654035]='Stats',
[654036]=[[Next Goals Tier]],
[654037]=[[All Goals Achieved]],
[654038]=[[Damage Ranking]],
[654039]=[[Rewards Preview]],
[654040]='Me',
[654041]=[[Not ranked]],
[654042]=[[No challenge data available.]],
[654043]='Damage:',
[654044]=[[Challenge Lineup]],
[654045]=[[Challenge Lineup]],
[654046]=[[No. {%s1} Rewards]],
[654047]=[[Ranking Rewards]],
[654048]=[[Personal Rewards]],
[654049]=[[Quest {%s5}: Attack {%s1} {%s2} times ({%s3}/{%s4})]],
[654050]=[[Today's bonus: Human hero damage increased]],
[654051]=[[Today's bonus: Forest hero damage increased]],
[654052]=[[Today's bonus: Night Elf hero damage increased]],
[654053]=[[{%s1} has been refreshed.]],
[654054]=[[You've used up all your challenge attempts for today.]],
[654055]=[[The Boss is about to vanish and you cannot arrive in time with your current march time. Deployment unavailable.]],
[654056]=[[No ranking data available at the moment.]],
[654057]=[[My Lord, your Castle must reach Lv. {%s1} before you can challenge it.]],
[654058]=[[My Lord, you need to clear the main story stage {%s1} before you can challenge it.]],
[654059]=[[Deal at least {%s1} damage to {%s2} in a single attack.]],
[654060]='Notes:',
[654061]='Rules:',
[654062]=[[1. Every {%s1} and {%s2} at {%s3}, {%s4}, {%s5}, and 1{%s6} server time, {%s7} will appear in the World for {%s8} hours. Lords can locate it quickly through the event interface.]],
[654063]=[[2. While the {%s1} is active, all Lords with a Castle level of 8 or higher can dispatch their troops to challenge it.]],
[654064]=[[3. Up to 5 challenges per day, no rallies.]],
[654065]=[[4. {%s1} deal bonus damage to the {%s2}.]],
[654066]=[[Reward Details]],
[654067]=[[1. Lords can claim rewards after completing a certain number of challenges.]],
[654068]=[[2. On the day of the challenge, the higher your best single damage rank, the better the rewards. Only your highest single-damage attempt of the day will be recorded for ranking purposes.]],
[654069]=[[3. Goals Rewards will be unlocked progressively based on server time. You can only claim each tier's reward once.]],
[654070]=[[Human heroes deal more damage to {%s1}]],
[654071]=[[Forest heroes deal more damage to {%s1}]],
[654072]=[[Night heroes deal more damage to {%s1}]],
[654073]=[[Wanted Boss]],
[654074]=[[Today's Boss event has ended.]],
[654075]='Rules:',
[654076]=[[1. Every Monday,Thursday and Sunday at 00:00, 06:00, 12:00, and 18:00 server time, the Twin-Headed Ogre will appear in the World for 3 hours. Lords can locate it quickly through the event interface.]],
[654077]=[[2. While the Twin-Headed Ogre is active, all Lords with a Castle level of 8 or higher can dispatch their troops to challenge it.]],
[654078]=[[3. Up to 5 challenges per day, no rallies.]],
[654079]=[[4. Forest heroes deal bonus damage to the Twin-Headed Ogre.]],
[654080]=[[Reward Details]],
[654081]=[[1. Lords can claim rewards after completing a certain number of challenges.]],
[654082]=[[2. On the day of the challenge, the higher your best single damage rank, the better the rewards. Only your highest single-damage attempt of the day will be recorded for ranking purposes.]],
[654083]=[[3. Goals Rewards will be unlocked progressively based on server time. You can only claim each tier's reward once.]],
[654084]='Rules:',
[654085]=[[1. Every Tuesday and Friday at 00:00, 06:00, 12:00, and 18:00 server time, the Rampaging Zombie will appear in the World for 3 hours. Lords can locate it quickly through the event interface.]],
[654086]=[[2. While the Rampaging Zombie is active, all Lords with a Castle level of 8 or higher can dispatch their troops to challenge it.]],
[654087]=[[3. Up to 5 challenges per day, no rallies.]],
[654088]=[[4. Nightfall Heroes deal bonus damage to the Rampaging Zombie.]],
[654089]=[[Reward Details]],
[654090]=[[1. Lords can claim rewards after completing a certain number of challenges.]],
[654091]=[[2. On the day of the challenge, the higher your best single damage rank, the better the rewards. Only your highest single-damage attempt of the day will be recorded for ranking purposes.]],
[654092]=[[3. Goals Rewards will be unlocked progressively based on server time. You can only claim each tier's reward once.]],
[654093]='Rules:',
[654094]=[[1. Every Wednesday and Saturday at 00:00, 06:00, 12:00, and 18:00 server time, the Undead Dragon will appear in the World for 3 hours. Lords can locate it quickly through the event interface.]],
[654095]=[[2. While the Undead Dragon is active, all Lords with a Castle level of 8 or higher can dispatch their troops to challenge it.]],
[654096]=[[3. Up to 5 challenges per day, no rallies.]],
[654097]=[[4. Human heroes deal bonus damage to the Undead Dragon.]],
[654098]=[[Reward Details]],
[654099]=[[1. Lords can claim rewards after completing a certain number of challenges.]],
[654100]=[[2. On the day of the challenge, the higher your best single damage rank, the better the rewards. Only your highest single-damage attempt of the day will be recorded for ranking purposes.]],
[654101]=[[3. Goals Rewards will be unlocked progressively based on server time. You can only claim each tier's reward once.]],
[654102]='Notes:',
[654103]=[[Unions in this warzone are ranked based on total damage]],
[654104]=[[Human hero make DMG+50% to BOSS]],
[654105]=[[Forest hero make DMG+50% to BOSS]],
[654106]=[[Night hero make DMG+50% to BOSS]],
[654107]=[[1. All Lords with a Castle level of 8 or higher can dispatch their troops to challenge it.]],
[654108]=[[2. Up to 5 challenges per day, no rallies.]],
[654109]=[[3. After attacking the World Boss, the highest damage of each member is recorded, and the total damage of the Union is calculated.]],
[654110]=[[4. Unions compete in daily rankings based on total damage!]],
[654111]=[[Battle Timed Out]],
[654112]=[[Damage Dealt in This Attack]],
[654113]=[[New Record]],
[654114]=[[The following rewards are available]],
[654115]=[[The highest damage of lords in two kingdoms! 
Calculate the total damage caused by the top 200 lords in each battle zone, and the one with the higher total damage wins!]],
[655001]=[[Seven Day Challenge]],
[655002]='Go',
[655003]=[[Participate in the following activities to obtain {%s1} shards]],
[655004]=[[Pioneer Event]],
[655005]=[[Your unclaimed event rewards have been sent via mail!]],
[655006]=[[Complete quests to obtain]],
[655007]='Verna',
[655008]=[[Increase your VIP level for more rewards]],
[655009]='Day',
[655010]=[[Seven Day Challenge]],
[655011]=[[Common Reward]],
[655012]=[[VIP Rewards]],
[655013]=[[<color=#f0f5f8>Help Verna</color> <color=#ffeb5e>awaken</color>\n <color=#f0f5f8>Get a lot of</color> <color=#ffeb5e>extra resources</color>]],
[655014]=[[Unlock new daily challenges to win rewards!]],
[655015]=[[1. The event lasts 10 days, with corresponding daily quests unlocking each day for the first seven days.]],
[655016]=[[2. All quests can be completed and rewards claimed before the event ends.]],
[655017]=[[3. Lords will earn Event Points for each quest completed.]],
[655018]=[[4. Obtain the required number of Event Points to obtain corresponding rewards.]],
[655019]=[[Seven Day Challenge]],
[655020]=[[Pioneer Lord]],
[655021]=[[Rescue Verna]],
[655022]=[[Clean Up Blocks]],
[655023]=[[Intelligence Quest: Verna’s Challenge]],
[655024]=[[Complete 7D Challenge quests]],
[655025]=[[Participate in Rally Assault actively]],
[655026]=[[Novice Arena]],
[655027]=[[Unlock Next Reward: {%s1}/{%s2}]],
[655028]=[[Seven Day Challenge]],
[655029]=[[Princess's Plea]],
[655030]=[[Lord's Journey]],
[655031]=[[Rare Hero & Premium Rewards]],
[655032]=[[Limited-time Event]],
[655050]=[[Arms Race]],
[655051]=[[Competition Stage:]],
[655052]=[[Enhance Hero]],
[655053]=[[City Construction]],
[655054]=[[Soldier Training]],
[655055]=[[Technology Research]],
[655056]=[[Mystic Beast Training]],
[655057]=[[Today's goal]],
[655058]=[[Complete the following tasks to earn points]],
[655059]=[[Today's opponent]],
[655060]=[[Arms Race Ranking Rewards]],
[655061]=[[Congratulations on winning {%s1} place in today's arms race]],
[655062]=[[Arms Race Event Unclaimed Rewards Reissue]],
[655063]=[[The following are the rewards for the "Arms Race" event that you have not received. Please check]],
[655064]=[[Points Description]],
[655065]=[[Opening time]],
[655066]=[[Event Theme]],
[655067]='Details',
[655068]=[[1. The arms race starts in a cycle every day. Each round has a different theme. Each round lasts 4 hours.]],
[655069]=[[2. Every day at midnight, 12-20 players with similar main city levels will be randomly matched.]],
[655070]=[[3. Players in the same group will compete for ranking rewards based on the total points of the day.]],
[655071]=[[4. Ranking rewards are settled once a day, and rewards are sent via email.]],
[655072]=[[5. Points can only be obtained by purchasing packs containing diamonds]],
[655073]=[[6. Unclaimed chests and progress rewards will be reissued via email after settlement, and combat medal items will not be reissued.]],
[655074]=[[Event Time]],
[655075]=[[Event Calendar]],
[655076]='ME',
[655077]=[[You are in another warzone and cannot join the Arms Race group. Please return to your original warzone and try again.]],
[655078]=[[Failed to retrieve event data. Please try again later.]],
[656001]=[[Nightfall Trial]],
[656002]='Ends:',
[656003]=[[Only one difficulty level can be selected for the challenge!]],
[656004]=[[Recommended CP]],
[656005]=[[Current Objective]],
[656006]=[[Rewards for defeating {%s1} enemies]],
[656007]=[[Ask allies for help when the enemy is too strong!]],
[656008]=[[Difficulty {%s1}]],
[656009]='Claimed',
[656010]=[[Complete Challenge：]],
[656011]=[[The rewards for all completed challenges will be issued after the event ends.]],
[656012]=[[Completing challenges grants rewards to all Union members.]],
[656013]=[[This event is available only after joining a Union!]],
[656014]=[[Join Now]],
[656015]=[[Unlocks after at least {%s1} allies complete Stage <color=#e63838>{%s3}</color> of <color=#e63838>Difficulty {%s2}</color>]],
[656016]=[[Not started yet.]],
[656017]='Ended',
[656018]='Challenger',
[656019]=[[Nightfall Servant]],
[656020]=[[Claim your rewards now!]],
[656021]=[[Claim Now]],
[656022]=[[Eligibility Requirements:]],
[656023]=[[1. The Castle must be level 8 or higher.]],
[656024]=[[2. Only one difficulty can be selected per event.]],
[656025]=[[3. Complete all challenges of the previous difficulty to unlock higher ones.\n]],
[656026]=[[Personal Challenge]],
[656027]=[[1. Lords can challenge the Instructor on their own or rally allies to fight together.]],
[656028]=[[2. Defeat the Instructor to unlock higher-level ones.]],
[656029]=[[3. When the event ends, Lords will receive rewards for all completed personal challenges.]],
[656030]=[[4. After completing all challenges of the current difficulty, a higher difficulty will be available in the next General's Trial.\n]],
[656031]=[[Union Challenge]],
[656032]=[[1. Once enough members in the Union have completed a difficulty's challenges,]],
[656033]=[[R4 and higher officers can initiate a challenge against the General's Nightfall Elites!]],
[656034]=[[2. Once the event begins, the Nightfall Elites will remain for 24 hours. They are extremely powerful and require rallying allies to defeat!]],
[656035]=[[3. Defeating the Nightfall Elites earns generous rewards for all Union members when the event ends!]],
[656036]=[[Nightfall Servant]],
[656037]=[[Nightfall Elite]],
[656038]=[[Nightfall Trial Personal Rewards Reissued]],
[656039]=[[Dear Lord, you have unclaimed rewards from the Nightfall Trial. Please collect them.]],
[656040]=[[Nightfall Trial Union Challenge Rewards]],
[656041]=[[Dear Lord, your Union defeated the Nightfall Elites in the Nightfall Trial. You've earned extra rewards.]],
[656042]=[[The invading Nightfall Servants have formidable strength.]],
[656043]=[[The terrifying Nightfall Elites can only be defeated through a rally.]],
[656044]=[[This challenge is tough. Once selected, the difficulty cannot be changed. Confirm your choice?]],
[656045]=[[Unlock conditions not met. Cannot summon.]],
[656046]=[[No permissions, operation unavailable]],
[656047]=[[Cannot summon again]],
[656048]=[[Enemies of each difficulty will appear for a limited time and can only be summoned once. Are you sure you want to start the challenge?]],
[656049]=[[Challenge Completed]],
[656050]=[[Only one difficulty can be selected per event.]],
[656051]=[[Recommended CP: <color=#E63838>{%s1}</color>. Your current team's CP is low. If you encounter difficulties, <color=#319F38>consider rallying with allies</color> to take on the challenge.]],
[656052]=[[Clearance Rewards:]],
[656053]=[[Remaining HP: {%s1}%]],
[656054]=[[Union rally point required]],
[656055]=[[Only one difficulty can be selected per event. Confirm your choice?]],
[656056]=[[Current Progress]],
[656057]=[[You are currently in another Kingdom and cannot search for Nightfall Trial enemies. Please return to your original Kingdom and try again.]],
[657001]=[[Forest Trial]],
[657002]=[[Human Trial]],
[657003]=[[Nightfall Trial]],
[657004]=[[Unlocks after clearing Forest Trial Lv. 5]],
[657005]=[[Unlocks after clearing Forest Trial Lv. 10]],
[657006]=[[Warning: <color=#e63838>Your CP is low!</color> You can choose a lower level for an easier battle, or <color=#e63838>face the trial with courage!</color>]],
[657007]=[[Our Forces]],
[657008]=[[Enemy Forces]],
[657009]='Leaderboard',
[657010]=[[Last Selected Difficulty:]],
[657011]=[[Total Rewards for Clearing]],
[657012]='Cleared',
[657013]='Select',
[657014]='Quest',
[657015]='Store',
[657016]=[[Reset Countdown]],
[657017]=[[Challenge progress resets every week on <color=#87F425>Tuesday/Thursday/Sunday</color>]],
[657018]='Challenged',
[657019]=[[Rewards Preview]],
[657020]='Achievements',
[657021]=[[Clear Lv. {%s1} to receive]],
[657022]='Deploy',
[657023]=[[Quick Battle]],
[657024]=[[Skip Battle]],
[657025]='{%s1}F',
[657026]=[[Current Stage: {%s1}]],
[657027]=[[Deployed Heroes]],
[657028]=[[Cleared {%s1} stages. Rewards are as follows:]],
[657029]=[[{%s1} stages remain at this difficulty. Rewards include:]],
[657030]=[[Faction Store]],
[657031]=[[Faction Trial]],
[657032]='Go',
[657033]=[[Power of Nature]],
[657034]=[[Power of Courage]],
[657035]=[[Power of Devil]],
[657036]=[[This hero is already assigned to another trial and cannot be deployed in the team.]],
[657037]=[[Each hero can only participate in one trial. Confirm deployment?]],
[657038]=[[Difficulty Already Cleared]],
[657039]=[[Challenge Aborted]],
[657040]=[[Are you sure you want to start {%s2} at <color=#FF7878>Difficulty {%s1}</color>? Only one difficulty level can be selected per cycle and it cannot be changed.]],
[657041]=[[Each hero can only participate in one trial. Choose carefully.]],
[657042]=[[Confirm Challenge]],
[657043]=[[Complete {%s1} Trial at Difficulty {%s2}]],
[657044]=[[Quick Challenge in Progress]],
[657045]=[[Current Difficulty Cleared]],
[657046]=[[Reach and activate VIP 8 to use.]],
[657047]=[[Congratulations on clearing this difficulty for the first time! You've earned one extra challenge attempt. Each trial type allows only one extra challenge per cycle.]],
[657048]=[[Challenge reset. Please select a difficulty again.]],
[657049]=[[Damage Dealt by Forest Heroes]],
[657050]=[[Damage Dealt by Human Heroes]],
[657051]=[[Damage Dealt by Nightfall Heroes]],
[657052]=[[Stage: {%s1}]],
[657053]=[[1. There are three Faction Trials: Forest, Human, and Nightfall. All three can run at the same time!]],
[657054]=[[2. Each trial has multiple difficulties. Once selected during the event, the difficulty cannot be changed. Please choose carefully.]],
[657055]=[[3. Heroes from the matching faction will gain significant bonuses in their trial. For example, in the Forest Trial, Forest heroes will gain extra bonuses, making the challenge easier.]],
[657056]=[[4. Trial progress resets every Tuesday, Thursday, and Sunday at 00:00 server time. You can reselect the difficulty and earn rewards again! (Join the event during these times each week!)]],
[657057]=[[5. The first time you clear a difficulty in any trial, you'll unlock one extra challenge for a new difficulty during the same event. (This bonus can only be triggered once per trial per event.)]],
[657058]=[[6. Each hero may only take part in one trial per event. Plan your lineups carefully!]],
[657059]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657060]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657061]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657062]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657063]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657064]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657065]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657066]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657067]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657068]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657069]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657070]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657071]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657072]=[[<color=#319f38>{%s1}</color>'s HP increased by <color=#319f38>50%</color>]],
[657073]=[[<color=#319f38>{%s1}</color>'s ATK increased by <color=#319f38>25%</color>]],
[657074]=[[Forest Hero damage increased by <color=#319f38>25%</color>]],
[657075]=[[Human Hero damage increased by <color=#319f38>25%</color>]],
[657076]=[[Nightfall Hero damage increased by <color=#319f38>25%</color>]],
[657077]=[[Completed the Forest Trial at Difficulty {%s2}]],
[657078]=[[Completed the Human Trial at Difficulty {%s2}]],
[657079]=[[Completed the Nightfall Trial at Difficulty {%s2}]],
[658001]=[[Plunders Today:]],
[658002]='History',
[658003]='Refresh',
[658004]=[[Arrival Time: <color=#319f38>{%s1}</color>]],
[658005]='Raid',
[658006]='Share',
[658007]=[[Other Wagon]],
[658008]=[[My Wagon]],
[658009]='At',
[658010]=[[Filter Wagons in This Warzone]],
[658011]='View',
[658012]=[[Related Technology]],
[658013]='Go',
[658014]=[[Wagon Garrison]],
[658015]=[[No soldier will be lost in wagon defense battles]],
[658016]='Refresh',
[658017]=[[Set Off]],
[658018]=[[Team {%s1}]],
[658019]=[[Trade Wagon]],
[658020]='Name',
[658021]='Integrity',
[658022]='Details',
[658023]=[[1. Wagons lose integrity when they are plundered.]],
[658024]=[[2. Wagons can no longer be plundered when their integrity drops to 50%]],
[658025]=[[3. Integrity will never drop below 50% from plundering.]],
[658026]=[[Vehicle Estimation]],
[658027]=[[Our wagons are rushing deliveries, wish them a smooth journey!]],
[658028]=[[Escort Hero]],
[658029]=[[Battle History]],
[658030]=[[The target wagon becomes easier to locate when it's wanted.]],
[658031]='Wanted',
[658032]='Retrieve',
[658033]='Extra',
[658034]=[[Wagon Details]],
[658035]=[[Plundered by {%s1}]],
[658036]=[[Safe and Sound]],
[658037]=[[{%s1} hour(s) ago]],
[658038]=[[{%s1} minute(s) ago]],
[658039]=[[{%s1} day(s) ago]],
[658040]=[[Plundered {%s1}]],
[658041]='Depart',
[658042]='Favorites',
[658043]=[[until arrival at destination]],
[658044]=[[Plundered You]],
[658045]='[Overview]',
[658046]=[[1. Lords can initiate up to 4 Intercity Trades per day. Each trade uses one wagon for a set amount of time. Unlocking more teams grants access to more trade wagons.]],
[658047]=[[2. After departure, the wagon travels along a random route and may be plundered by others. No soldiers will be lost in these battles, and each wagon can be plundered up to 2 times.]],
[658048]=[[3. Once the wagon reaches its destination, the sender can claim the remaining rewards on it.\n]],
[658049]=[[[Starting Trade] ]],
[658050]=[[1. Up to five heroes can be selected to protect the wagon, and each hero can only protect one wagon at a time.]],
[658051]=[[2. The guard heroes can no longer be changed once the wagon departs.]],
[658052]=[[3. When selecting a wagon, each wagon has a free opportunity to change the reward.\n]],
[658053]=[[[Trade Rewards] ]],
[658054]=[[1. After the wagon arrives, the remaining goods on the wagon can be collected.]],
[658055]=[[2. The chances of obtaining higher quality wagons increase when you use Trade Contracts to change the wagon consecutively.]],
[658056]=[[3. The higher the wagon's quality and Lord's Castle level, the higher the base cargo value of the wagon.\n]],
[658057]=[[[Plundering Wagons] ]],
[658058]=[[1. Wagons moving on the map can be plundered by non-allied Lords, with up to 4 plunders per day.]],
[658059]=[[2. After a successful attack, random goods and resources will be plundered, and the wagon's integrity will be reduced.]],
[658060]=[[3. Failed plunders will not count towards the daily limit.]],
[658061]=[[4. Wagons with less than 50% integrity can no longer be plundered.]],
[658062]=[[5. No soldiers are lost during plundering, and each wagon can be plundered up to 2 times.\n]],
[658063]=[[[Probability Info] ]],
[658064]=[[1. Wagons of different rarities have different appearance weights depending on the number of refreshes (including free refreshes).]],
[658065]=[[[1]：C[50] B[30] A[10] S[5] S+[5] ]],
[658066]=[[[2]：C[30] B[30] A[20] S[10] S+[10] ]],
[658067]=[[[3]：C[20] B[20] A[30] S[15] S+[15] ]],
[658068]=[[[4]：C[0] B[20] A[40] S[20] S+[20] ]],
[658069]=[[[5]：C[0] B[0] A[50] S[25] S+[25] ]],
[658070]=[[[>5]：C[0] B[0] A[0] S[0] S+[100] ]],
[658071]=[[2. After unlocking Treasure Wagons, they may appear when you reroll with a Trade Contract. Their weight is fixed at 10 and is unaffected by the refresh count.]],
[658072]=[[3. Probability calculation: If the weight is [100] for S+ Wagons and [10] for TreasS+e Wagons, then the probability of getting a S+ Wagon is 90.9%, while the probability of getting a TreasS+e Wagon is 9.1%.\n]],
[658073]='Help',
[658074]=[[Arrival Time:]],
[658075]=[[Today's Trade Attempts]],
[658076]=[[1. Each time the wagon is plundered, its integrity will decrease.]],
[658077]=[[2. Wagons with less than 50% integrity cannot be plundered.]],
[658078]=[[3. Integrity will never drop below 50% from plundering.]],
[658079]='Wagon',
[658080]=[[{%s1}'s {%s2} Wagon {%s3}]],
[658081]=[[Today's Trade Attempts]],
[658082]='Plundered',
[658083]='By',
[658084]=[[Wagon Location]],
[658085]=[[Free Refresh]],
[658086]=[[The current value of the cargo is high. Are you sure you want to refresh?]],
[658087]=[[Unlock more fleets by acquiring more teams]],
[658088]=[[Insufficient Plunder Attempts]],
[658089]=[[Insufficient Trade Attempts]],
[658090]=[[A defense team must be set before departure]],
[658091]=[[Each wagon can be plundered up to {%s1} times]],
[658092]=[[No other wagons found at the moment, please try again later]],
[658093]=[[The current wagon quality is low, and a free refresh is available. Send it out anyway?]],
[658094]=[[Other players are in battle, please try again later.]],
[658095]=[[The Trade Wagon will be launched on day {%s1} of the server opening, so stay tuned!]],
[658096]=[[Not defeated yet]],
[658097]=[[The function is not unlocked. Complete the tile {%s1} to unlock the Trade Wagon system!]],
[658098]=[[Spend {%s1} Diamonds to refresh wagons?]],
[658099]=[[Tap a wagon to plunder its cargo]],
[658100]=[[5 or more refreshes guarantees an <size=34><color=#FF9600>S+</color></size> wagon]],
[658101]=[[Open after %s]],
[659001]=[[Function locked.]],
[659002]=[[Requires clearing main story stage {%s1}]],
[659003]=[[Requires level {%s2} {%s1}]],
[659004]=[[Requires {%s1} Lv. {%s2}]],
[659005]=[[Pack locked. Cannot purchase it.]],
[660001]=[[Emails older than 30 days will be deleted]],
[660002]='Mail',
[660003]=[[Battle Report]],
[660004]='Union',
[660005]='System',
[660006]='Daily',
[660007]='Events',
[660008]=[[Delete All]],
[660009]=[[Claim All]],
[660010]='Claim',
[660011]='Translating...',
[661001]=[[Union Ranking]],
[661002]='Rank',
[661003]=[[Union Name]],
[661004]=[[Total CP]],
[661005]=[[Tip: Ranking list updates may be delayed]],
[661006]=[[No Union]],
[661007]=[[Not Ranked]],
[661008]=[[Union Kill Ranking]],
[661009]=[[Personal CP Ranking]],
[661010]=[[Lord Base Ranking]],
[661011]=[[Lord Kill Ranking]],
[661012]=[[Cleared Stages Count]],
[661013]=[[Total Hero CP]],
[661014]=[[Strongest Hero CP]],
[661015]=[[Forest Trial Ranking]],
[661016]=[[Human Trial Ranking]],
[661017]=[[Nightfall Trial Ranking]],
[661018]='Kills',
[661019]='Lord',
[661020]='CP',
[661021]='Level',
[661022]='Stage',
[661023]=[[Any lord on this server reaches Personal CP of {%s2}]],
[661024]=[[Any lord on this server reaches Base Level of {%s2}]],
[661025]=[[Any lord on this server reaches Kill Count of {%s2}]],
[661026]=[[Any lord on this server clears {%s2} stages]],
[661027]=[[Any lord on this server reaches Total Hero CP of {%s2}]],
[661028]=[[Any lord on this server has a hero with CP of {%s2}]],
[661029]=[[Any lord on this server clears {%s2} Forest Trial]],
[661030]=[[Any lord on this server clears {%s2} Human Trial]],
[661031]=[[Any lord on this server clears {%s2} Nightfall Trial]],
[661032]=[[1. The ranking does not update in real time and may be delayed.]],
[661033]=[[2. Only your strongest hero’s CP is counted in the [Strongest Hero CP Ranking].]],
[661034]='Hero:',
[661035]=[[First Reach Reward]],
[661036]=[[Unlocks at Castle Level {%s1}]],
[661037]=[[Progress Rewards]],
[661038]=[[Not Ranked Yet]],
[661039]='Kingdom:',
[662001]=[[Summoner Reward for Undead Boss]],
[662002]=[[The Lv. {%s1} Undead Boss you summoned has been defeated by player {%s2}, and you have received massive rewards!]],
[662003]=[[Congratulations on dealing {%s1} damage in the Undead Treasure event. You ranked {%s2} and received the following reward.]],
[662004]=[[Undead Treasure Damage Ranking Rewards]],
[662005]=[[Undead Treasure]],
[662006]=[[Congratulations on successfully repelling the Undead invasion!]],
[662007]=[[{%s1} until the event page closes]],
[662008]=[[The event has ended. You can still redeem rewards in the shop before the page closes.]],
[662009]='Search',
[662010]=[[Defeat the Undead invading the city]],
[662011]=[[Participate in the Undead Boss Rally to earn great rewards!]],
[662012]=[[Union Discovery]],
[662013]=[[My Discovery]],
[662014]=[[Search for Undead Treasures]],
[662015]=[[Redeem Shop]],
[662016]='Exchange',
[662017]=[[Undead Boss]],
[662018]=[[Congratulations, you have found {%s1} Undead Boss!]],
[662019]=[[Defeat it together for rewards!]],
[662020]=[[The event is about to start. Be prepared!]],
[662021]=[[The next wave of Undead Treasures will arrive in {%s1}.]],
[662022]=[[Damage Ranking]],
[662023]=[[Damage: {%s1}]],
[662024]=[[Challenge Lineup]],
[662025]=[[Rank {%s1} Rewards]],
[662026]=[[Rally Initiation Rewards]],
[662027]=[[Rallying against the Undead Boss consumes Stamina. Initiating a rally that defeats the enemy will grant at least the following rewards. There is no limit on rally initiation rewards.]],
[662028]=[[Rally Participation Rewards]],
[662029]=[[Participating in an Undead Boss rally does not consume Stamina. Participating in a rally that defeats the enemy will grant at least the following rewards (which are extremely generous). There is no limit on rally participation rewards.]],
[662030]=[[Bosses you helped defeat in this event: {%s1}]],
[662031]=[[Event Info:]],
[662032]=[[1. After the Undead Treasure event starts, a large number of Undead Treasures will appear in the World, with extremely generous rewards!]],
[662033]=[[2. You can quickly search for nearby Undead Treasures in the event interface, or manually look for them on the map.]],
[662034]=[[3. After defeating Undead Treasures, you will receive event currency, which can be redeemed for extra rewards in the shop.]],
[662035]=[[4. Defeating Undead Treasures may summon an Undead Boss, which offers great rewards but also requires a rally to defeat due to its immense power.]],
[662036]=[[5. During the event, damage dealt to the Undead Boss will contribute to the rankings. After the event ends, additional rewards will be given based on your ranking.]],
[662037]=[[Your Union has not discovered any Undead Boss. Defeating the Undead Treasure may help uncover their whereabouts.]],
[662038]=[[You have not discovered any Undead Boss. Defeating the Undead Treasure may help uncover their whereabouts.]],
[662039]=[[Wait for the event to begin]],
[662040]=[[Undead Treasure]],
[662041]=[[Defeat the invading Undead to earn rewards]],
[662042]=[[Rally against the Undead Boss and defeat it to earn huge rewards]],
[662043]=[[Summoner Rewards]],
[662044]='Summoner',
[662045]=[[Undead Treasure Preview]],
[662046]=[[Undead with lucrative treasures are about to invade!]],
[662047]=[[Time until Invasion]],
[662048]=[[Novice Guide]],
[662049]=[[<color=#a036d5><size=30>Undead Treasure</size></color>]],
[662050]=[[<color=#ed810f><b>·</b>Spawn Time:</color>]],
[662051]=[[Refresh every hour on the hour from 00:00 to 23:00 each day during the event.]],
[662052]='<color=#ed810f><b>·</b>Rewards:</color>',
[662053]=[[Defeat them to obtain Food, Iron, Gold, Hero EXP, and the event item, <color=#319f38>Protection Medals</color>.]],
[662054]='<color=#ed810f><b>·</b>Tips:</color>',
[662055]=[[The monsters pose little challenge but offer bountiful rewards! We recommend purchasing Stamina or using Stamina items to defeat more Undead Treasures for maximum loot.]],
[662056]=[[<color=#a036d5><size=30>Undead Boss</size></color>]],
[662057]=[[<color=#ed810f><b>·</b>Spawn Rules:</color>]],
[662058]=[[Defeating the Undead Treasure has a chance to spawn an Undead Boss.]],
[662059]='<color=#ed810f><b>·</b>Rewards:</color>',
[662060]=[[Summon Rewards: After the summoned Undead Boss is defeated, the summoner receives Food, Iron, Gold, Hero EXP, and the event item, <color=#319f38>Protection Medals</color>.]],
[662061]=[[Rally Rewards: After the Undead Boss is defeated in a rally, the initiator receives massive Food, Iron, Gold, Hero EXP, and the event item, <color=#319f38>Protection Medals</color>.]],
[662062]='<color=#ed810f><b>·</b>Tips:</color>',
[662063]=[[Focus your rallies on weaker Undead Bosses first. Call upon strong allies when challenging higher-level ones!]],
[662064]=[[<color=#a036d5><size=30>Ranking Rewards</size></color>]],
[662065]=[[During the event, the total damage dealt to Undead Bosses is ranked and rewarded at the event's end.]],
[663000]=[[Redeem rewards with Courage Medals.]],
[663001]=[[Initiate or participate in {%s1}/{%s2} event rallies]],
[663002]=[[Time Remaining:]],
[663003]=[[Rally Assault]],
[663004]=[[Monsters have appeared at your Union's stronghold. Initiate a rally for rescue and you will receive Union Contribution upon defeating them!]],
[663005]=[[Rewards Preview]],
[663006]=[[Union Store]],
[663007]=[[Today's Quests]],
[663008]=[[Complete {%s1}/{%s2} event rallies]],
[663009]='Claim',
[663010]=[[Each rally initiated will grant 2 random rewards]],
[663011]=[[Search Monsters]],
[663012]=[[High Priest's Gift]],
[663013]=[[Rally Initiation Rewards]],
[663014]=[[Initiating a rally consumes Stamina. Each rally you initiate will randomly grant 2 rewards from the following list. There is no limit on rally initiation rewards.]],
[663015]=[[Rally Participation Rewards]],
[663016]=[[Participating in a rally does not consume Stamina and offers a chance to receive the following rewards.]],
[663017]=[[Remaining reward chances for today: {%s1}/{%s2}]],
[663018]=[[Rally Assault]],
[663019]=[[Undead Dragon]],
[663020]=[[Event countdown: {%s1} day(s) {%s2} hour(s)]],
[663021]=[[1. During the event, consume energy to initiate a rally to kill "Klass". After victory, you will have a chance to obtain fragments of the hero Verna.]],
[663022]=[[2. Participation rewards for defeating Klass are limited to 20 times per day; there is no limit on rally initiation rewards.]],
[663023]='Klass',
[663024]='Events',
[663025]=[[Maximum search level equals Castle level]],
[663026]=[[Quest conditions not met]],
[663027]=[[initiated a rally and defeated {%s1} invading monster(s)!]],
[663028]=[[{%s1} has completed the Rally Battle quest]],
[663029]=[[Unavailable for the event has ended.]],
[664001]='VIP',
[664002]='VIP{%s1}',
[664003]=[[You've logged in for {%s1} day(s). Collect <color=#FFE63B>{%s2}</color> VIP EXP tomorrow.]],
[664004]=[[VIP{%s1} privileges]],
[664005]='Details',
[664006]=[[Purchase limit: {%s1}]],
[664007]=[[Activate VIP]],
[664008]=[[Unlock Time]],
[664009]=[[Remaining VIP time: {%s1}]],
[664010]=[[Remaining: {%s1}]],
[664011]=[[<color=##89DAFF >Complete </color><color=#FFE63B>Intelligence Quest</color>]],
[664012]=[[<color=#89DAFF >Obtain </color><color=#FFE63B>extra chests</color>]],
[664013]=[[Daily Random Chest]],
[664014]=[[Return to purchase]],
[664015]='Back',
[664016]='Purchase',
[664017]=[[Congratulations, you've got]],
[664018]=[[VIP EXP Today:]],
[664019]=[[Consecutive login days:]],
[664020]=[[VIP EXP Tomorrow:]],
[664021]=[[The more consecutive login days you have, the more privilege points you will get.]],
[664022]=[[VIP EXP]],
[664023]='Perks',
[664024]=[[Daily Chest]],
[664025]='Claim',
[664026]=[[Get extra chests every day]],
[664027]='Locked',
[664028]='Purchased',
[664029]=[[Unlock the ability to view avatars]],
[664030]=[[Tavern Quest Auto dispatch]],
[664031]=[[Shake to collect resources]],
[664032]=[[Unlock Faction Trial Auto Battle]],
[664033]=[[Acorn Tavern Mega Mode]],
[664034]=[[Tap a blank space to close]],
[664035]='Purchase',
[664036]=[[<color=##89DAFF >Complete </color><color=#FFE63B>Intelligence Quest</color>]],
[664037]=[[<color=#89DAFF >Obtain </color><color=#FFE63B>extra chests</color>]],
[664038]=[[VIP Exclusive Survivor]],
[664039]=[[VIP {%s1}]],
[664040]=[[Do you want to spend {%s1} diamonds to purchase {%s2}]],
[664041]=[[VIP{%s1} daily gift]],
[664042]=[[VIP{%s1} exclusive gift]],
[664043]='Locked',
[664044]=[[VIP Shop]],
[664045]=[[Unlock VIP]],
[664046]=[[Collect All Available\n (castle lv.20 unlock)]],
[664047]='Inactive',
[664048]=[[Purchase limit reached today. Further purchases unavailable.]],
[664049]=[[You can purchase {%s1} VIP EXP with diamonds every day]],
[664050]=[[VIP Pack]],
[664051]=[[VIP1 Exclusive Gift Pack]],
[664052]=[[VIP2 Exclusive Gift Pack]],
[664053]=[[VIP3 Exclusive Gift Pack]],
[664054]=[[VIP4 Exclusive Gift Pack]],
[664055]=[[VIP5 Exclusive Gift Pack]],
[664056]=[[VIP6 Exclusive Gift Pack]],
[664057]=[[VIP7 Exclusive Gift Pack]],
[664058]=[[VIP8 Exclusive Gift Pack]],
[664059]=[[VIP9 Exclusive Gift Pack]],
[664060]=[[VIP10 Exclusive Gift Pack]],
[664061]=[[VIP11 Exclusive Gift Pack]],
[664062]=[[VIP12 Exclusive Gift Pack]],
[664063]=[[VIP13 Exclusive Gift Pack]],
[664064]=[[VIP14 Exclusive Gift Pack]],
[664065]=[[VIP15 Exclusive Gift Pack]],
[664066]=[[VIP16 Exclusive Gift Pack]],
[664067]=[[VIP17 Exclusive Gift Pack]],
[664068]=[[VIP18 Exclusive Gift Pack]],
[664069]='Star',
[664070]='Amount',
[664071]=[[Random Rewards]],
[664072]=[[Value Monthly Card]],
[664073]=[[Purchase the Monthly Card to Receive]],
[664074]='Go',
[664075]=[[VIP{%s1} unlocks View Avatar.]],
[665001]=[[Rewards Preview]],
[665002]='Claim',
[665003]='Claimed',
[665004]=[[Goals Rewards]],
[665005]=[[Ranking Rewards]],
[665006]=[[The match is over. Rewards are automatically sent to players via mail.]],
[665007]='Ends:',
[665008]='Rewards',
[665009]='Challenge',
[665010]='Defense',
[665011]=[[Battle Report]],
[665012]=[[Skip Battle]],
[665013]='Refresh',
[665014]='Free',
[665015]='Defeated',
[665016]=[[Eligibility Requirements:]],
[665017]='Rank',
[665018]='Challenges',
[665019]=[[Next round starts in]],
[665020]=[[Heroes whose total combat power reaches the top 200 in the region and server can participate immediately!]],
[665021]=[[(Your current ranking: {%s1})]],
[665022]=[[You have not participated in this match and may miss out on abundant rewards.]],
[665023]=[[You have not participated in this match]],
[665024]=[[Only the top 200 heroes in terms of total combat power in each region and server can participate in each competition!]],
[665025]=[[Free Charges]],
[665026]=[[No. {%s1} Rewards]],
[665027]=[[All union allies of the No. {%s1} player in 3V3 Crucible will receive corresponding union rewards.]],
[665028]=[[Rank rewards will be granted when the season ends. You will receive your unclaimed rewards automatically when entering the 3V3 Arena in the next match.]],
[665029]=[[Ranking Rewards]],
[665030]=[[Daily Rewards]],
[665031]=[[Union Rewards]],
[665032]=[[{%s1} win(s) daily]],
[665033]=[[Event closed]],
[665034]=[[Arena Reward Reissue]],
[665035]=[[You have not claimed your Novice Arena daily rewards for yesterday. Here are your rewards.]],
[665036]=[[You have not claimed your Peak Arena daily rewards for yesterday. Here are your rewards.]],
[665037]=[[Win 5 times every day to claim all chest rewards!]],
[665038]=[[Challenge 5 times every day to claim all chest rewards!]],
[665039]=[[Opening Time]],
[665040]=[[1. The Peak Arena opens every Tuesday.]],
[665041]=[[2. The Peak Arena will remain open for matches for 3 days after opening and will close after Friday.]],
[665042]=[[3. The offseason is from Saturday to Monday.\n]],
[665043]=[[Eligibility Requirements]],
[665044]=[[1. Once the Peak Arena opens, the server's top 200 players in Total Hero CP will automatically participate in the match.]],
[665045]=[[2. If you are ranked in the top 200 in Total Hero CP during the event, you can still join the match midway.]],
[665046]=[[3. Each round of the Peak Arena will consist of Lords from 1 to 4 servers.\n]],
[665047]=[[Match Rules]],
[665048]=[[1. A player's initial ranking in the Peak Arena is based on their CP when they enter the arena.]],
[665049]=[[2. Each challenge in the Peak Arena matches you against multiple opponents. After defeating an opponent, if their rank is higher than yours, rankings will be swapped.]],
[665050]=[[3. You can challenge a limited number of opponents per day.\n]],
[665051]=[[Reward Settlement]],
[665052]=[[1. The Peak Arena grants daily rewards. After defeating 1, 3, and 5 opponents each day, you can claim Daily Victory Chests on the Challenge interface.]],
[665053]=[[2. Season rewards will be issued at the end of matches on Friday.\n]],
[665054]='Defense',
[665055]=[[1. The default defense team in the Peak Arena will be your strongest sandbox team.]],
[665056]=[[2. After the competition starts, you can adjust your defense team on the Defense interface.]],
[665057]=[[3. The defense team in the Peak Arena is saved separately. It will not affect your other teams.]],
[665058]=[[Opening Time]],
[665059]=[[1. The Novice Arena will open on the first day after the server launch.]],
[665060]=[[2. The Novice Arena will remain open for 10 days.]],
[665061]=[[3. After 10 days, the Novice Arena will be closed.\n]],
[665062]=[[Eligibility Requirements]],
[665063]=[[1. When the Novice Arena is open, all players who have unlocked the Arena Building can participate.\n]],
[665064]=[[Match Rules]],
[665065]=[[1. The initial rankings in the Novice Arena are based on the order in which players enter the arena. The earlier you enter, the higher you will be ranked.]],
[665066]=[[2. Each player entering the Novice Arena will start with 1,000 points.]],
[665067]=[[3. Each challenge matches you against multiple opponents. Winning against an opponent grants you points, and defeating an opponent with more points earns you more points.]],
[665068]=[[4. Points will be deducted upon a lost battle. The lower the opponent's points, the more points will be deducted.]],
[665069]=[[5. The final arena rankings are determined by points.\n]],
[665070]=[[Reward Settlement]],
[665071]=[[1. The Novice Arena grants daily rewards. After fighting 1, 3, and 5 opponents each day, you can claim Daily Chests on the Challenge interface. (You may claim the rewards regardless of battle results.)]],
[665072]=[[2. Season rewards will be issued at the end of the match.\n]],
[665073]='Defense',
[665074]=[[1. The default defense team in the Novice Arena will be your strongest sandbox team.]],
[665075]=[[2. After the competition starts, you can adjust your defense team on the Defense interface.]],
[665076]=[[3. The defense team in the Novice Arena is saved separately. It will not affect your other teams.]],
[665077]='Duration',
[665078]=[[1. The 3V3 Arena will remain open for 14 days during each opening.\n]],
[665079]=[[Eligibility Requirements]],
[665080]=[[1. Once the 3V3 Arena opens, the server's top 200 players in Total Hero CP will automatically participate in the match.]],
[665081]=[[2. If you are ranked in the top 200 in Total Hero CP during the event, you can still join the match midway.]],
[665082]=[[Match Rules]],
[665083]=[[1. A player's initial ranking in the 3V3 Arena is based on their Total Hero CP.]],
[665084]=[[2. Each player entering the 3V3 Arena will start with 1,000 points.]],
[665085]=[[3. Each challenge matches you against multiple opponents. Winning against an opponent grants you points, and defeating an opponent with more points earns you more points.]],
[665086]=[[4. Points will be deducted upon a lost battle. The lower the opponent's points, the more points will be deducted.]],
[665087]=[[5. The final arena rankings are determined by points.\n]],
[665088]=[[Battle Rules]],
[665089]=[[1. The 3V3 Arena uses a best-of-three system. You need to configure 3 teams to battle against the opponent's 3 defense teams.\n]],
[665090]=[[Reward Settlement]],
[665091]=[[1. The 3V3 Arena grants daily rewards. After defeating 1, 3, and 5 opponents each day, you can claim Daily Victory Chests on the Challenge interface.]],
[665092]=[[2. Season rewards will be issued at the end of the match.\n]],
[665093]=[[3. The 3V3 Arena grants union rewards. At the end of the match, union allies of the top 10 Lords will receive generous union rewards.]],
[665094]='Defense',
[665095]=[[1. The default defense teams in the 3V3 Arena will be your first, second, and third teams.]],
[665096]=[[2. After the competition starts, you can adjust your defense team on the Defense interface.]],
[665097]=[[3. The defense teams in the 3V3 Arena are saved separately. They will not affect your other teams.]],
[665098]=[[You have not claimed your 3V3 Arena daily rewards for yesterday. Here are your rewards.]],
[665099]=[[3V3 Arena Reward Reissue]],
[665100]=[[3V3 Arena Settlement Rewards]],
[665101]=[[Congratulations! You ranked No. {%s1} in 3V3 Arena! Here are your rewards.]],
[665102]=[[3V3 Arena Union Rewards Issued]],
[665103]=[[Congratulations! Your union ally {%s1} ranked No. {%s2} in 3V3 Arena, and all union members will receive this reward.]],
[665104]=[[Novice Arena]],
[665105]=[[Peak Arena]],
[665106]=[[3V3 Arena]],
[665107]=[[Dear Lord, congrats on ranking <color=#FFA07A>No. {%s1}</color> in this season of Peak Arena! Your rewards have been issued. Keep up the good work!]],
[665108]=[[Peak Arena Rewards Issued]],
[665109]=[[Insufficient challenge attempts today. You can continue challenging tomorrow.]],
[665110]=[[Peak Arena Ticket]],
[665111]=[[Novice Arena Ticket]],
[665112]=[[Used to challenge opponents in the Peak Arena.]],
[665113]=[[Used to challenge opponents in the Novice Arena.]],
[665114]=[[No defense set. The attacking player won without fighting.]],
[665115]='Arena',
[665116]=[[Practice fighting with other Lords!]],
[665117]=[[Unable to view the personal details of the Starter Instructor]],
[665118]=[[Current Rewards]],
[665119]=[[Old Ranking Rewards]],
[665120]=[[Complete tile 41 to unlock the Arena!]],
[665121]=[[Novice Arena Rank Reward Reissue]],
[665122]=[[Novice Arena Ranking Reward Reissue]],
[665123]=[[You have not claimed your Novice Arena rank rewards. Here are your rewards.]],
[665124]=[[Congratulations! You have ranked No. {%s1} in Novice Arena! Here are your rewards.]],
[665125]=[[Total DMG Taken]],
[665126]=[[Recruit Training]],
[665127]=[[Complete Challenge]],
[665128]=[[Novice Arena Goals Rewards Reissue]],
[665129]=[[Here are your unclaimed Novice Arena Goals Rewards.]],
[665130]=[[Climb the leaderboard and obtain all rewards]],
[665131]=[[Increase ranking to No. {%s1}]],
[665132]=[[Unlock Arena]],
[665133]=[[Spend {%s1} Diamonds to purchase 1 challenge attempt?]],
[665134]=[[Spend %s%d Diamonds to purchase 1 challenge attempt?]],
[665135]=[[Spend %s %d Diamonds to refresh the opponent list?]],
[665136]=[[Unlock Novice Arena]],
[665137]=[[Reach No. {%s1} in the Novice Arena]],
[665138]=[[Out of daily refreshing chances]],
[665139]=[[Rewards Overview]],
[665140]=[[Core Prize]],
[665141]=[[VIP %d only allow to purchase %d.]],
[665142]=[[The enemy has no defense lineup. Battle skipped.]],
[665143]=[[The enemy's soldier level is higher. Your heroes will be suppressed!]],
[665144]=[[Starter Instructor]],
[666001]=[[Currently in Kingdom {%s1}]],
[666002]=[[You're currently in Kingdom {%s1}. Tap the message to return to your home Kingdom.]],
[666003]=[[Unable to retrieve intel while in a cross-server state. Please return to your home Kingdom and try again.]],
[666004]=[[Cannot discover Tavern Quests during an assault in another Kingdom. They will resume once you return to your home Kingdom.]],
[666005]=[[Unable to proceed. You are not in the same area.]],
[666006]=[[The troop is in another Kingdom. Go to Kingdom {%s1}?]],
[667001]=[[Declare War]],
[667002]=[[We launched an attack on city {%s2} {%s3} on {%s1}.\nWar Duration: {%s4} to {%s5}\nLet's stand united and win this war!]],
[667003]=[[Received declaration of war.]],
[667004]=[[{%s1} has initiated a battle on our City {%s2}{%s3}!\nWar Duration: {%s4} to {%s5}\nLet's stand united and win this war!]],
[667005]=[[War Declaration Canceled]],
[667006]=[[{%s1} has canceled our war declaration on City {%s2}{%s3}. Operation aborted.]],
[667007]=[[War Declaration Canceled]],
[667008]=[[{%s1} has canceled their war declaration on our City {%s2}{%s3}.]],
[667009]=[[Congrats to Union {%s1} for capturing {%s2} {%s3} ({%s4})!]],
[667010]=[[Protection Phase: {%s1}]],
[667011]=[[City Ownership: {%s1}]],
[667012]=[[Recommended Troop CP: {%s1}]],
[667013]=[[Event starts in: {%s1}]],
[667014]=[[Protection Status: {%s1}]],
[667015]=[[Abandon Countdown: {%s1}]],
[667016]=[[City Occupation Points]],
[667017]='Vacancy',
[667018]=[[Abandon City]],
[667019]=[[Abandoning will trigger a {%s1}-hour delay. After that, the city will be relinquished. Confirm abandonment?]],
[667020]=[[Declare war on {%s1} {%s2}]],
[667021]=[[Unions that declared war: {%s1} {%s2}]],
[667022]=[[Cities occupied by the Union:]],
[667023]='Date',
[667024]='h(s)',
[667025]='minute(s)',
[667026]=[[Begin the Siege Now]],
[667027]=[[Mobilization Announcement]],
[667028]=[[We must stay aggressive!]],
[667029]=[[Victory will be ours!]],
[667030]=[[Please issue your command!]],
[667031]=[[Daily war declarations: {%s1}/{%s2}]],
[667032]=[[War can be declared before the city is unlocked.]],
[667033]=[[War Declaration Successful]],
[667034]=[[Your Union has not met all the conditions for declaring war. The declaration has failed.]],
[667035]=[[Union has declared war on another city.]],
[667036]=[[War declarations available for today.]],
[667037]=[[Insufficient war declaration chances remaining today.]],
[667038]=[[Union age meets the requirement of {%s1}.]],
[667039]=[[Union age is less than {%s1}.]],
[667040]=[[Union has occupied an adjacent city.]],
[667041]=[[Union has not occupied an adjacent city.]],
[667042]=[[Union must first occupy a Lv. {%s1} City.]],
[667043]=[[The number of Union members meets the requirement {%s1}/{%s2}.]],
[667044]=[[Insufficient Union members {%s1}/{%s2}.]],
[667045]=[[Set up Rally Point]],
[667046]=[[You've declared war on {%s1} {%s2}. Would you like to set a Union Rally Point to help your members group up?]],
[667047]='Cancel',
[667048]='Deploy',
[667049]=[[Relocation Notice]],
[667050]=[[The Union Leader has declared war on {%s1} {%s2}. Relocating nearby could give you a tactical advantage. Do you wish to relocate?]],
[667051]='Cancel',
[667052]=[[Free Relocation]],
[667053]='Teleport',
[667054]=[[First Occupation Rewards]],
[667055]=[[Union Siege On]],
[667056]=[[Siege Countdown]],
[667057]=[[Your Turn to Attack]],
[667058]=[[Attack in progress]],
[667059]=[[Enemy Defense]],
[667060]=[[Your Turn to Defend]],
[667061]='Defending',
[667062]=[[Enemy Attack]],
[667063]='Locked',
[667064]='Unclaimed',
[667065]=[[Full-Scale Attack Command: Dispatch 1 troop from each offline Lord to attack the target city. Confirm use?
\n1. This command has a 5-minute cooldown.
\n2. Lords with too few remaining soldiers or full hospitals will not respond.]],
[667066]=[[City Occupied]],
[667067]=[[The Union city {%s1}{%s2} has been occupied by {%s3}. Don't be discouraged, keep going!]],
[667068]=[[Event Duration:]],
[667069]=[[Abandon city {%s1}? Once confirmed, our occupation will end in {%s2}!]],
[667070]=[[Cancel the abandonment of city {%s1}? You will retain control of the city.]],
[667071]=[[Use it to summon eligible offline members to join the attack!]],
[667072]=[[War Start Time]],
[667073]=[[Server Time]],
[667074]=[[Local Time]],
[667075]=[[Confirm War Declaration]],
[667076]=[[War Declaration Failed]],
[667077]=[[Cancel the war declaration on {%s1} <a href=1>{%s2}</a> and declare war on {%s3}<a href=2>{%s4}</a> instead?]],
[667078]=[[Please enter your declaration of war first!]],
[667079]=[[Your Union hasn't occupied a Lv. 6 city yet and can't participate in the attack on the Royal City.]],
[667080]=[[The current {%s1} reinforcement team is full. Unable to dispatch more.]],
[667081]=[[Battle Incoming]],
[667082]=[[Break on Through]],
[667083]=[[Defeat a total of {%s1} garrison teams during the siege.]],
[667084]=[[Cancel War Declaration]],
[667085]=[[{%s1} has canceled the Union's war declaration on City {%s2}{%s3}!]],
[667086]=[[Must be declared war upon]],
[667087]=[[The Union {%s1} has canceled the declaration of war on our city {%s2}{%s3}!]],
[667088]=[[Siege Rewards Reissue]],
[667089]=[[Your unclaimed city capture reward has been reissued via mail. Check it out!]],
[667090]='Tip',
[667091]=[[Congrats to <color=#FF4D2A>{%s1}</color> on the first occupation of <color=#FF4D2A>{%s2} {%s3}</color>!]],
[667092]=[[Congrats to <color=#FF4D2A>{%s1}</color> on capturing <color=#FF4D2A>{%s2} {%s3}</color>!]],
[667093]=[[The Union {%s1} {%s2} has been occupied by {%s3}.]],
[667094]=[[You're not in a Union! Please join one first.]],
[667095]=[[Insufficient permissions. Only R4/R5 members of the Union can operate on cities.]],
[667096]=[[Capture City]],
[667097]=[[Participate in the occupation of {%s1} cities.]],
[667098]=[[At the end of the event, your Union must occupy {%s1} Lv. {%s2} cities.]],
[667099]=[[Get {%s1} points in the Royal City Clash.]],
[667100]=[[Kill {%s1} Lv. {%s2} soldiers in the Royal City and Mega Cannon.]],
[667101]=[[Kill {%s1} Lv. {%s2} soldiers in the contaminated area.]],
[667102]=[[The Mega Cannon kills {%s1} Lv. {%s2} soldiers when it's garrisoned with troops.]],
[667103]=[[When troops garrisoned at the Mega Cannon or Royal City return, points are awarded based on garrison time.]],
[667104]=[[No cities available for occupation.]],
[667105]=[[Changes have occurred in your Union. You can no longer view assistance info.]],
[667106]=[[City Clash]],
[667107]=[[City Clash I]],
[667108]=[[City Clash II]],
[667109]=[[City Clash III]],
[667110]=[[Race Period: {%s1}–{%s2}]],
[667111]=[[Union Points: {%s1}]],
[667112]=[[Union Ranking: {%s1}]],
[667113]=[[{%s1} unlocks for battle in]],
[667114]=[[First Occupation Rewards]],
[667115]=[[Cities the Union has declared war on]],
[667116]=[[Make an active contribution for better rewards!]],
[667117]='Tutorial',
[667118]=[[Race Rewards]],
[667119]=[[City List]],
[667120]=[[Race Quests]],
[667121]=[[All rewards will be issued via mail after the race ends.]],
[667122]=[[No Union]],
[667123]='Objective',
[667124]='Rank',
[667125]='Rewards',
[667126]=[[Rank {%s1} Rewards]],
[667127]=[[Congrats to <color=#FF4D2A>Union {%s1}</color> for the first occupation of <color=#FF4D2A>{%s1} {%s2} {%s3}</color> ({%s4})!]],
[667128]='Notes:',
[667129]='Rules:',
[667130]=[[1. After the R5/R4 members of the Union declare war on a city, the competition for that city unlocks. Each Union can only declare war on one city at a time.]],
[667131]=[[2. Unions that have not occupied any cities can declare war on any Level 1 city.]],
[667132]=[[3. Unions that have already occupied a city can only declare war on cities that are adjacent to one of their current cities.]],
[667133]=[[4. City Clash battles are intense and will result in direct soldier deaths. The exact casualty rates can be found in the city building description.]],
[667134]=[[5. When a city is occupied for the first time, the members of the winning Union who participated in the siege will receive First Occupation Rewards, and all members of the Union will receive a city reward.]],
[667135]=[[6. After occupying a city, all Union members will receive corresponding buffs.]],
[667136]=[[7. Complete the race objectives within the time limit to earn rewards, which will be issued via mail after the race ends.]],
[667137]=[[War declarations for this city are currently unavailable!]],
[667138]=[[City Race - Union Rewards]],
[667139]=[[Congrats! Your Union ranked {%s1} in the City Race event. Here are your rewards!]],
[667140]=[[City Race - Personal Rewards]],
[667141]=[[Congrats! Here are your personal rewards from the City Race event!]],
[667142]=[[Royal City Clash - Union Rewards]],
[667143]=[[Congrats! Your Union ranked {%s1} in the Royal City Clash event. Here are your rewards!]],
[667144]=[[Royal City Clash - Personal Rewards]],
[667145]=[[Congrats! Here are your personal rewards from the Royal City Clash event!]],
[667146]=[[Union war declaration successful.]],
[667147]=[[You've applied for the position of {%s1}.]],
[667148]=[[Your castle level is below {%s1}, please upgrade it before proceeding.]],
[667149]=[[City Clash]],
[667150]=[[R4 or R5 members may declare war on adjacent cities. During this time, Union members can engage in siege battles.]],
[667151]=[[To take the city, you'll need to defeat the garrison first. They're no pushovers.]],
[667152]=[[After defeating the garrison, you must demolish the city's durability. The more members participate, the faster it goes.]],
[667153]=[[Once the durability is reduced to 0, the total points for killing garrisons and reducing durability will be calculated. The Union with the highest score wins control of the city.]],
[667154]=[[Declare War]],
[667155]=[[Defeat the Enemy]],
[667156]=[[Demolish Durability]],
[667157]=[[Occupy City]],
[667158]=[[You cannot claim this reward as you did not participate in the city capture.]],
[667159]=[[War Phase: {%s1}]],
[667160]=[[Withdraw the war declaration on city {%s1}?]],
[667161]=[[You haven't joined a Union yet, so you can't occupy cities or enjoy the great benefits.]],
[667162]=[[Join Now]],
[667163]=[[2. Only adjacent cities can be declared war on.]],
[667164]=[[No active city buffs.]],
[667165]=[[Ranking Settlement Time]],
[667166]=[[{%s1}'s City Capture Chest]],
[667167]=[[City Clash I]],
[667168]=[[City Clash II]],
[667169]=[[City Clash III]],
[667170]=[[War of Royal City]],
[667171]='Go',
[667172]=[[View Royal City]],
[667173]='Rules',
[667174]=[[King's Authority]],
[667175]=[[Warzone Title]],
[667176]=[[Royal City Rewards]],
[667177]=[[Honor Points]],
[667178]=[[City Capture Chest]],
[667179]=[[Expires in {%s1}]],
[667180]=[[Abandon successful. City will be relinquished in 1 hour.]],
[667181]=[[{%s1} has abandoned City {%s2}.]],
[667182]=[[Garrison Zombies]],
[667183]=[[City Defense Zombies]],
[667184]=[[Coming soon!]],
[667185]='Tutorial',
[667186]=[[<color=#a036d5><size=30>Siege Tactics</size></color>]],
[667187]=[[<color=#ed810f><b>·</b>Declare War In Advance</color>: You can declare war on a city 2 days before it's unlocked!]],
[667188]=[[<color=#ed810f><b>·</b>Set Rally Points</color>: After setting a Rally Point for your Union, members can use the "Union Relocation" feature for easy relocation.]],
[667189]=[[<color=#ed810f><b>·</b>Invite to Rally</color>: In the "Invite to Rally" section of the Union interface, you can summon your Union members to assemble.]],
[667190]=[[<color=#ed810f><b>·</b>Notify Allies</color>: A siege requires strong cooperation from all Union members. It's a good idea to notify your allies in advance so everyone can join the siege on time!\n]],
[667191]=[[<color=#a036d5><size=30>Important Tips</size></color>]],
[667192]=[[<color=#ed810f><b>·</b>Siege Time</color>: Once the siege begins, it must be completed within 1 hour. Ensure all Union members can participate within this time frame to successfully conquer the city!]],
[667193]=[[<color=#ed810f><b>·</b>Siege Planning</color>: You can only declare war on cities adjacent to those already occupied by your Union. Make sure your siege routes are strategically planned to have more cities accessible for attack!]],
[667194]=[[<color=#ed810f><b>·</b>First Occupation Rewards</color>: The siege participants of the first Union to occupy each city will receive generous first occupation rewards. Don't miss out!]],
[667195]=[[Royal City Contested:]],
[667196]=[[You've already liked this.]],
[667197]='Notes:',
[667198]=[[1. Royal City Clash opens weekly. Only Unions that occupy a Lv. 6 City can compete for control of the Royal City.]],
[667199]=[[2. Royal City Clash lasts for 8 hours. The first Union to occupy the Royal City for a total of 4 hours, or the Union with the longest total occupation time by the end of the battle, will be declared the winner.]],
[667200]=[[3. The leader of the winning Union will be crowned King.]],
[667201]=[[4. The Royal City lies within the Contaminated Area. Lords who enter this area cannot activate shields, accept Intelligence Quests, or complete Acorn Tavern Quests.]],
[667202]='Tutorial',
[667203]=[[<color=#a036d5><size=30>Siege Tactics</size></color>]],
[667204]=[[<color=#ed810f<b>·</b>Set Rally Points</color>: After setting a Rally Point for your Union, members can use the "Union Relocation" feature for easy relocation.]],
[667205]=[[<color=#ed810f><b>·</b>Invite to Rally</color>: In the "Invite to Rally" section of the Union interface, you can summon your Union members to assemble.]],
[667206]=[[<color=#ed810f><b>·</b>Notify Allies</color>: A siege requires strong cooperation from all Union members. It's a good idea to notify your allies in advance so everyone can join the siege on time!]],
[667207]=[[<color=#ed810f><b>·</b>Multiple Assistance</color>: The more teams you send for assistance, the faster you occupy the Royal City!\n]],
[667208]=[[<color=#a036d5><size=30>Important Tips</size></color>]],
[667209]=[[<color=#ed810f><b>·</b>Attack Rivals</color>: It's a good idea to attack your rivals' castles during battle to secure your position.]],
[667210]=[[<color=#ed810f><b>·</b>Cooperative Defense</color>: If allied teams are away and can't return in time, request nearby allies to help defend!]],
[667211]=[[<color=#ed810f><b>·</b>Timely Healing</color>: After being attacked, heal your soldiers promptly. Wounded soldiers beyond your hospital's capacity will die immediately.]],
[667212]=[[Competition Points Info\n1. Royal City Clash opens weekly. Only battles within the Contaminated Area award Honor Points.\n2. Earn points by severely wounding or defeating enemy soldiers during battles in the Contaminated Area. The points gained increase with the soldier's level.\n3. You also gain points when your own soldiers fall in battle. The points gained increase with the soldier's level.\n4. Reach certain point thresholds to unlock corresponding Honor Rewards.]],
[667213]=[[Cannot claim another Union's chest]],
[667214]=[[Lv. {%s1} city is about to be open for a contest.]],
[667215]=[[War Preparation]],
[667216]=[[The City Race event is about to begin! Get prepared and strategize your city sieges with allies to gain abundant resources!]],
[667217]=[[Siege Countdown]],
[667218]=[[Local Time]],
[667219]=[[Relocate near a Union rally point.]],
[667220]=[[Upgrade your castle.]],
[667221]=[[Things You Can Do for the Union]],
[668001]=[[Assist Allies]],
[668002]=[[Plunder Others]],
[668003]='Log',
[668004]='Difficulty',
[668005]=[[My Quests]],
[668006]=[[Ally Quests]],
[668007]=[[Rescue Reinforcements]],
[668008]=[[Explore Region]],
[668009]='Go',
[668010]='Claim',
[668011]='Refresh',
[668012]='Mega',
[668013]='Normal',
[668014]=[[Mega refresh]],
[668015]=[[Mega dispatch]],
[668016]=[[Acorn Tavern]],
[668017]=[[Mysterious treasure]],
[668018]=[[Current Star Level]],
[668019]=[[Daily Quest Count]],
[668020]=[[Quest Level]],
[668021]=[[Upgrade Requirements]],
[668022]=[[Castle reaches Lv. {%s1}]],
[668023]=[[Max Level Reached]],
[668024]=[[Bounty Quest]],
[668025]=[[Quest Rewards]],
[668026]=[[Dispatch Requirements]],
[668027]=[[Deploy Team]],
[668028]=[[Available Heroes]],
[668029]=[[Auto Dispatch]],
[668030]=[[No eligible heroes for dispatch.]],
[668031]=[[Tavern Quest Log]],
[668032]=[[ assisted you in completing a quest.]],
[668033]=[[ stole your quest reward.]],
[668034]=[[Dispatch requirements not met.]],
[668035]='Proceed',
[668036]=[[Mega Refresh Tips]],
[668037]=[[Mega Refresh will upgrade {%s1} non-orange Tavern Quest(s) to orange. Please confirm.]],
[668038]=[[Refresh Cost]],
[668039]='Assist',
[668040]=[[No available ally quests for assistance.]],
[668041]=[[Tavern Quest Report]],
[668042]=[[Rescue Merchant]],
[668043]=[[Training Soldiers]],
[668044]=[[Explore Ancient Ruins]],
[668045]='Rules:',
[668046]=[[1. You receive a set number of Tavern Quests daily and can dispatch heroes to complete them for rewards. (Dispatching heroes for Tavern Quests does not affect their availability in other gameplay.)]],
[668047]=[[2. The higher your Base Level, the higher your Tavern Quests star level and the better the rewards.]],
[668048]=[[3. Tavern Quests come in Blue, Purple, and Orange quality. Orange quests offer the best rewards. If unsatisfied with a quest's quality, you can refresh it using items or diamonds. (Quests marked with a like icon will not be refreshed.)]],
[668049]=[[Refresh probabilities:]],
[668050]=[[Blue Quest: 68%]],
[668051]=[[Purple Quest: 24%]],
[668052]=[[Orange Quest: 8%]],
[668053]=[[4. Normal Tavern Quests refresh daily at 00:00 server time. Quests with a like icon refresh once every three days.]],
[668054]=[[5. You can plunder quest rewards from non-allies. Allies can assist in completing quests to prevent theft.]],
[668055]=[[Spending {%s1} diamonds to refresh the quest?]],
[668056]=[[Select at least one quest to proceed.]],
[668057]=[[<color=#FF0000>Reach and activate VIP 4</color> to unlock Quick Deploy.]],
[668058]=[[<color=#FF0000>Reach and activate VIP 12</color> or <color=#FF0000>upgrade your castle to Lv. 27</color> to use.]],
[668059]=[[No non-orange quests available for refresh.]],
[668060]=[[6. Each Team allows you to run 2 Acorn Tavern Quests simultaneously.]],
[668061]=[[Insufficient available dispatch queues. Maximum simultaneous quests: {%s1} Teams.]],
[668062]=[[Mega Dispatch Tips]],
[668063]='Dispatched',
[668064]=[[Assistance Rewards]],
[668065]=[[Hidd Quest Report]],
[668066]=[[Refresh successful]],
[668067]=[[Plunder Rewards]],
[668068]='Plunder',
[668069]=[[Select a hero for the quest]],
[668070]=[[{%s1}'s Tavern Quests]],
[668071]=[[You currently have undispatched Orange quests. Refresh anyway?]],
[668072]=[[Quest not completed yet, please try again later.]],
[668073]=[[Already stolen the quest. Leave some rewards for the player!]],
[668074]=[[Cannot steal the quest anymore, please find another one to steal]],
[668075]=[[Mysterious treasure]],
[668076]=[[Treasure Digging can reward you with a lot! Don't miss out.]],
[668077]=[[<color=#319f38>{%s1}</color> more dig(s) guarantee a Legendary Chest! This guarantee count will be reset upon a Legendary Chest drop.]],
[668078]=[[Treasure Digging]],
[668079]=[[Skip ]],
[668080]=[[Exchange Pieces]],
[668081]=[[Each dig requires 1 Treasure Map Piece of each type.]],
[668082]=[[Common Treasure]],
[668083]=[[rare treasure]],
[668084]=[[legendary treasure]],
[668085]=[[Opening the chest grants {%s1} random rewards, with the following drop rates:]],
[668086]=[[Drop Rate: <color=#319F38>{%s1}</color>]],
[668087]=[[You will get]],
[668088]=[[You will lose]],
[668089]=[[No. 1 Piece]],
[668090]=[[No. 2 Piece]],
[668091]=[[No. 3 Piece]],
[668092]=[[No. 4 Piece]],
[668093]=[[No. 5 Piece]],
[668094]=[[No. 6 Piece]],
[668095]=[[No. 7 Piece]],
[668096]=[[Initiate Exchange]],
[668097]=[[My Exchange]],
[668098]=[[Ally's Exchange]],
[668099]=[[Please select the item you wish to receive. After the exchange, you will <color=#319438>obtain</color> the item.]],
[668100]=[[Please select the item you wish to offer. After the exchange, you will <color=#ff5656>lose</color> the item.]],
[668101]=[[Owned: {%s1}]],
[668102]=[[Exchange <color=#ed810f>{%s1}</color> for your ally's <color=#ed810f>{%s2}</color>?]],
[668103]=[[Start Exchange]],
[668104]=[[You've exchanged <color=#ed810f>{%s1}</color> with {%s2} for <color=#ed810f>{%s3}</color>]],
[668105]=[[Treasure Map Shard No.1]],
[668106]=[[Treasure Map Shard No.2]],
[668107]=[[Treasure Map Shard No.3]],
[668108]=[[Treasure Map Shard No.4]],
[668109]=[[Treasure Map Shard No.5]],
[668110]=[[Treasure Map Shard No.6]],
[668111]=[[Treasure Map Shard No.7]],
[668112]=[[Exchange Record]],
[668113]=[[The exchange has been initiated. Awaiting a response from your ally.]],
[668114]=[[Cancel Exchange]],
[668115]=[[Cannot exchange identical pieces]],
[668116]=[[Cannot exchange a piece you don't have]],
[668117]=[[You haven't joined a Union yet. Join one to participate in the event!]],
[668118]=[[You have to join a Union to share your exchange.]],
[668119]=[[The exchange has expired. Please exchange with another ally.]],
[668120]=[[Is anyone interested in exchanging?]],
[668121]=[[exchanged with you.]],
[668122]=[[Initiate New Exchange]],
[668123]=[[Exchanged! You've obtained {%s1}]],
[668124]=[[No. {%s1} Piece]],
[668125]=[[Single Dig Reward]],
[668126]=[[No exchange initiated by allies]],
[668127]=[[Unavailable as you haven't made your selection yet.]],
[668128]=[[1. Complete Tavern Quests of purple or higher quality to obtain random Treasure Map Pieces. If the quests are completed without being plundered, you will have a chance to receive extra Treasure Map Pieces.]],
[668129]=[[2. There are a total of 7 Treasure Map Pieces. Treasure Digging requires 1 of each type.]],
[668130]=[[3. Exchange pieces with your allies. After initiating an exchange, the piece you offered will be temporarily deducted.]],
[668131]=[[4. Tavern Quests give an equal chance to drop any Treasure Map Pieces.]],
[668132]=[[5. Completing a purple quest without being plundered has a 5% chance to grant 1 additional random Treasure Map Piece. For orange quests, the chance increases to 15%.]],
[668133]=[[6. Legendary Treasures have a "guarantee" mechanic: If you fail to obtain a Legendary Treasure after 15 consecutive digs, the 16th dig is guaranteed to drop a Legendary Treasure.]],
[668134]=[[Thanks sent.]],
[668135]=[[{%s1} appreciates your exchange.]],
[668136]=[[I want to exchange {%s1} for {%s2}]],
[668137]=[[You are currently in another Kingdom and cannot complete Tavern Quests. Please return to your original Kingdom and try again.]],
[668138]=[[The quest has not started yet. Go check out others.]],
[668139]=[[Wait, I can smell the sweet scent of treasures!]],
[668140]=[[What? I didn't see any.]],
[668141]=[[It's right here in this Acorn Tavern reward. With my years of treasure hunter experience, I'm sure of it!]],
[668142]=[[Dear Lord, let's claim the quest rewards!]],
[668143]=[[Here it is, a treasure map!]],
[668144]=[[Let's hurry and find the treasure!]],
[668145]=[[These pieces perfectly form a complete treasure map! We're lucky!]],
[668146]=[[Let's dig and see what we can get!]],
[668151]=[[Allies assisted you in claiming the rewards!]],
[668152]='Thanks',
[668153]='Liked!',
[668154]=[[Thank you for your help!]],
[668155]=[[This is so kind of you.]],
[668156]=[[Thank you for helping me claim the rewards!]],
[668157]=[[A true ally indeed!]],
[668158]=[[You're a great help!]],
[668159]=[[Giving you a like!]],
[668160]=[[Thank you!]],
[668161]=[[A wise decision!]],
[668162]=[[Excellent, my Lord!]],
[668163]=[[Your wisdom is unmatched!]],
[668164]=[[No quests available to refresh.]],
[669001]=[[Arms Race rewards not obtained!]],
[669002]=[[Gameplay Rewards]],
[669003]=[[You didn't complete yesterday's Arms Race. Finish it next time to earn the following rewards!]],
[669004]=[[How to Participate]],
[669005]=[[<color=#319f38>Arms Race Themes:</color> There are <color=#317BC7>6 themes</color> in the Arms Race each day. The ways to earn points vary depending on the theme.\n\n<color=#319f38>Theme Schedule:</color> Each theme starts at a different time on different days. Please check the Arms Race calendar for details.]],
[669006]='Tutorial',
[669007]=[[Gameplay Tips]],
[669008]=[[<color=#319f38>Get Points:</color> Complete specific actions in the current Arms Race theme to earn points, so be sure to check the active theme once you're online!\n\n<color=#319f38>Get Rewards:</color> You can claim up to <color=#317BC7>6 Medals</color> per theme, and <color=#317BC7>18 Medals</color> will get you the grand prize!]],
[669009]=[[Union Duel rewards not obtained!]],
[669010]=[[Gameplay Rewards]],
[669011]=[[You missed several objectives in yesterday's Union Duel. Complete them next time to earn the following rewards!]],
[669012]=[[How to Participate]],
[669013]=[[<color=#319f38>Duel Themes:</color> Union Duel features different themes every day, each lasting 24 hours. The way you earn points in each theme is different!]],
[669014]=[[Gameplay Tips]],
[669015]=[[<color=#319f38>Upgrade Union Duel Tech:</color> Union Duel Technology is essential to your success. Prioritize these upgrades whenever possible!\n\n<color=#319f38>Stock up on Survivor Recruitment Vouchers:</color> Save your Survivor Recruitment Vouchers and use them on Tuesdays for maximum benefit.\n\n<color=#319f38>Speed Up Buildings:</color> Upgrade and speed up your buildings on Tuesdays to earn a ton of Union Duel points!\n\n<color=#319f38>Dispatch Orange Wagons/Tavern Quests:</color> Send out orange wagons or complete orange Tavern Quests for a ton of points.]],
[669016]=[[Member attrition risk!]],
[669017]=[[Risk Warning]],
[669018]=[[Your Union is severely understaffed, which may prevent you from unlocking Union events or cause slow progress and major reward loss. We recommend expanding to 60–80 members.]],
[669019]=[[How to Recruit Members]],
[669020]=[[<color=#319f38>Send Invitation Links:</color> In the Union Settings menu, tap "Invitation Link" to send the recruitment link to the World Channel or Language Channel.\n\n<color=#319f38>Manual Invitation:</color> When you find like-minded Lords, tap on their Avatar, then go to their <color=#317BC7>Profile Page</color> and tap <color=#317BC7>"Invite"</color> to send them a Union invitation.\n\n<color=#319f38>Assign R4 Members:</color> R4 members have partial management permissions. You can assign trusted active members as R4 in the Members list, allowing them to help recruit new members and manage the Union.]],
[669021]=[[We recommend switching to another Union.]],
[669022]='Tips',
[669023]=[[We've noticed that your Union isn't very active right now. To help you enjoy the game to the fullest, we recommend finding a more active one.]],
[669024]=[[Benefits of Active Unions]],
[669025]=[[<color=#319f38>Speed Up Buildings and Research:</color> Join a Union to request help from your allies. The more active your Union is, the faster your building and research timers will be!\n\n<color=#319f38>Union Gifts:</color> Whenever your allies [<color=#317BC7>Defeat a Boss</color>] or [<color=#317BC7>Purchase Packs</color>], you'll receive Union Gifts filled with valuable resources.\n\n<color=#319f38>Tech Bonuses:</color> Active Unions unlock higher-level Union Technology, giving you powerful bonuses like faster research and march speed.\n\n<color=#319f38>Union Store:</color> The more active your Union, the more Union Contribution Points you'll receive. Use them in the Union Store to purchase high-value items!\n\n<color=#319f38>Event Rewards:</color> Active Unions perform better in events—meaning better Union Rewards for all the members!]],
[669026]=[[Go to the Module]],
[669027]=[[<color=#319f38>Yesterday's Duel Theme:</color> Intelligence Training. Unclear on how to gain points? You'll find everything you need on the Union Duel page. The button below can take you straight to the module.]],
[669028]=[[<color=#319f38>Today's Duel Theme:</color> Castle Construction. Earn points mainly by speeding up or completing building construction/upgrades.]],
[669029]=[[<color=#319f38>Yesterday's Duel Theme:</color> Castle Construction. Unclear on how to gain points? You'll find everything you need on the Union Duel page. The button below can take you straight to the module.]],
[669030]=[[<color=#319f38>Today's Duel Theme:</color> Technology Research. Earn points mainly by using tech speedups, researching techs, and opening Mystic Beast Mark Chests.]],
[669031]=[[<color=#319f38>Upgrade Union Duel Tech:</color> Union Duel Technology is essential to your success. Prioritize these upgrades whenever possible!\n]],
[669032]=[[<color=#319f38>Speed Up Technology:</color> Earn a ton of points every Wednesday by speeding up and completing tech research. Focus your Technology speedups that day to maximize your rewards!\n]],
[669033]=[[<color=#319f38>Accumulate Radar Quests:</color> Complete Radar Quests on Mondays, Wednesdays, and Fridays to earn Union Duel points. Remember to stock up on Radar Quests the day before to maximize your points!\n]],
[669034]=[[<color=#319f38>Stock up on Mystic Beast Mark Chests:</color> Earn a ton of points by opening Mystic Beast Mark Chests every Wednesday. Be sure to save them up until Wednesday to maximize your rewards!\n]],
[669035]=[[<color=#319f38>Yesterday's Duel Theme:</color> Technology Research. Unclear on how to gain points? You'll find everything you need on the Union Duel page. The button below can take you straight to the module.]],
[669036]=[[<color=#319f38>Today's Duel Theme:</color> Hero Training. Earn points mainly by recruiting heroes, leveling them up, and using Hero Shards.]],
[669037]=[[<color=#319f38>Upgrade Union Duel Tech:</color> Union Duel Technology is essential to your success. Prioritize these upgrades whenever possible!\n]],
[669038]=[[<color=#319f38>Stock up on Hero Recruitment Vouchers:</color> Earn Union Duel points by using your Hero Recruitment Vouchers every Thursday. Be sure to save them until Thursday to maximize your rewards!\n]],
[669039]=[[<color=#319f38>Hero Upgrades:</color> Earn a ton of points by upgrading heroes every Thursday. Be sure to upgrade your heroes on Thursday to maximize your rewards!\n]],
[669040]=[[<color=#319f38>Hero Ascension:</color> Ascend heroes on Thursdays to earn a huge amount of Union Duel points!\n]],
[669041]=[[<color=#319f38>Yesterday's Duel Theme:</color> Hero Training. Unclear on how to gain points? You'll find everything you need on the Union Duel page. The button below can take you straight to the module.]],
[669042]=[[<color=#319f38>Today's duel theme:</color> Comprehensive preparation for war, mainly earning points by training soldiers and completing intelligence tasks]],
[669043]=[[<color=#319f38>Upgrade Union Duel Tech:</color> Union Duel Technology is essential to your success. Prioritize these upgrades whenever possible!\n]],
[669044]=[[<color=#319f38>Speed Up Soldier Training:</color> Earn a ton of points every Friday by training and speeding up soldiers. Save your training items for a big push on Friday!\n]],
[669045]=[[<color=#319f38>Accumulate Radar Quests:</color> Complete Radar Quests on Mondays, Wednesdays, and Fridays to earn Union Duel points. Remember to stock up on Radar Quests the day before to maximize your points!\n]],
[669046]=[[<color=#319f38>Yesterday's Duel Theme:</color> Full Preparation. Unclear on how to gain points? You'll find everything you need on the Union Duel page. The button below can take you straight to the module.]],
[669047]=[[<color=#319f38>Today's Duel Theme:</color> Defeat the Enemy. Earn points mainly by defeating soldiers from the opposing Union.]],
[669048]=[[<color=#319f38>Upgrade Union Duel Tech:</color> Union Duel Technology is essential to your success. Prioritize these upgrades whenever possible!\n]],
[669049]=[[<color=#319f38>Defeat Enemies:</color> You can launch assaults from the Union Assault page during Union Duels! Earn a ton of points by attacking opposing Union members' castles!\n]],
[669050]=[[<color=#319f38>Yesterday's Duel Theme:</color> Defeat the Enemy. Unclear on how to gain points? You'll find everything you need on the Union Duel page. The button below can take you straight to the module.]],
[669051]=[[<color=#319f38>Today's Duel Theme:</color> No Union Duel on Sunday. Monday's theme is Intelligence Training: earn points mainly by completing Radar Quests and upgrading Mystic Beasts.]],
[669052]=[[<color=#319f38>Upgrade Union Duel Tech:</color> Union Duel Technology is essential to your success. Prioritize these upgrades whenever possible!\n]],
[669053]=[[<color=#319f38>Accumulate Radar Quests:</color> Complete Radar Quests on Mondays, Wednesdays, and Fridays to earn Union Duel points. Remember to stock up on Radar Quests the day before to maximize your points!\n]],
[669054]=[[<color=#319f38>Hero Upgrades:</color> Earn a ton of points by upgrading heroes on Mondays and Thursdays. Be sure to upgrade your heroes on those days to maximize your rewards!\n]],
[669055]=[[<color=#319f38>Stock up on Mystic Beast Upgrade Items:</color> Earn a ton of Union Duel points by upgrading your Mystic Beast every Monday. Be sure to save your Mystic Beast resources until Monday!\n]],
[670001]='World',
[670002]='Union',
[670003]=[[Chinese Simplified]],
[670004]=[[Event Channel]],
[670005]=[[Private Chat]],
[670006]='Auto-Translate',
[670007]=[[Please Enter Content]],
[670008]=[[Union Announcement]],
[670009]=[[Emergency Announcement]],
[670010]=[[Emergency Publish]],
[670011]=[[No announcements posted]],
[670012]='Publish',
[670013]=[[Tap the button or the publish icon in the upper right corner to add a new announcement]],
[670014]=[[Regular Announcement]],
[670015]=[[Publish Union Announcement]],
[670016]=[[Only 3 announcements can be published per day]],
[670017]=[[Tap to enter]],
[670018]=[[Regular Union Announcement]],
[670019]=[[Emergency Union Announcement]],
[670020]=[[Emergency Publish]],
[670021]='R4&R5',
[670022]='Duel',
[670023]=[[Desert Storm Battlefield]],
[670024]=[[Show VIP]],
[670025]=[[Hide VIP]],
[670026]=[[Emergency announcement is on cooldown]],
[670027]=[[No available uses for emergency function]],
[670028]=[[Announcement text exceeds the limit]],
[670029]=[[After an emergency announcement is published, all members will receive a notification. Emergency announcements can be used once per hour, up to three times per day.]],
[670030]=[[Deleting this announcement will remove all related content. Do you want to delete it?]],
[670031]=[[Chat Settings]],
[670032]=[[System Message:]],
[670033]='English',
[670034]=[[Share to Chat]],
[670035]=[[Local Chat]],
[670036]=[[Send a message]],
[670037]=[[This message can only be viewed after updating to the latest version from the app store.]],
[671001]='King',
[671002]=[[Prime Minister]],
[671003]='Marshal',
[671004]=[[Minister of Health]],
[671005]=[[Minister of Construction]],
[671006]=[[Minister of Science]],
[671007]=[[Minister of Domestic Affairs]],
[671008]='Conqueror',
[671009]=[[Military commander]],
[671010]=[[Political commander]],
[671011]=[[King Awards]],
[671012]=[[General Award]],
[671013]=[[Sergeant Award]],
[671014]=[[Soldier Award]],
[671015]=[[King Awards]],
[671016]=[[Congrats! You've received a {%s2} awarded by {%s1}. Check it out!]],
[671017]=[[Royal City Management]],
[671018]=[[Current King]],
[671019]=[[No King at the moment.]],
[671020]=[[Royal Decree]],
[671021]=[[A powerful Warzone is built by strong Lords like you. Let's grow stronger together.]],
[671022]=[[Title Assignment]],
[671023]=[[King Skills]],
[671024]=[[Royal City Rewards]],
[671025]=[[Past Kings]],
[671026]=[[Royal Mail]],
[671027]=[[Warzone Badge]],
[671028]=[[Warzone Title]],
[671029]='Bonus',
[671030]=[[Appointment Time (Local)]],
[671031]=[[No Lords in queue.]],
[671032]=[[No appointment records.]],
[671033]=[[No applications.]],
[671034]='Appoint',
[671035]='Player',
[671036]=[[Union Identity]],
[671037]=[[Insufficient level.]],
[671038]='Leaderboard',
[671039]=[[Replace the position of {%s1} from {%s2} to {%s3}? This will take effect immediately.]],
[671040]=[[Award Records]],
[671041]=[[The King {%s1} has awarded {%s2} a {%s3}.]],
[671042]='Award',
[671043]=[[Grant {%s1} a {%s2}?]],
[671044]=[[Appointment Log]],
[671045]=[[{%s1}th King]],
[671046]=[[{%s1}th King (Invader)]],
[671047]=[[Assume Position]],
[671048]=[[No King crowned this session.]],
[671049]=[[Royal Mail]],
[671050]=[[Royal Decree]],
[671051]=[[Please enter the mail subject.]],
[671052]=[[Please enter the mail body.]],
[671053]=[[This mail will be sent to all Lords in the Warzone.]],
[671054]='Send',
[671055]=[[Warzone Badge]],
[671056]=[[System Message]],
[671057]=[[Congrats to {%s2}, the leader of {%s1}, on their promotion to {%s3}!]],
[671058]=[[Congrats to Union member {%s2} of {%s1} on their promotion to {%s3}!]],
[671059]='Coronation',
[671060]=[[Congrats to {%s2}, the leader of {%s1}, on their promotion to {%s3}!]],
[671061]=[[Officer Appointment]],
[671062]=[[The King {%s1} has appointed you as {%s2}.]],
[671063]=[[Not yet in office.]],
[671064]=[[Please enter the subject text for the mail first.]],
[671065]=[[Please enter the content text for the mail first.]],
[671066]=[[Unable to send. Your message exceeds {%s1} characters.]],
[671067]=[[Application submitted. Please wait for approval.]],
[671068]='Dismiss',
[671069]=[[5 minutes]],
[671070]=[[10 minutes]],
[671071]='20min',
[671072]=[[30 minutes]],
[671073]=[[Officer Application]],
[671074]=[[Appointment Time (Server)]],
[671075]=[[Auto-Appointment List]],
[671076]='Award',
[671077]=[[King Awards]],
[671078]=[[Award Records]],
[671079]=[[You've assumed the position of <color=#E63838>{%s1}</color>.]],
[671080]=[[King Bonus]],
[671081]=[[Prime Minister Bonus]],
[671082]=[[Marshal Bonus]],
[671083]=[[Minister of Health Bonus]],
[671084]=[[Minister of Construction Bonus]],
[671085]=[[Minister of Science Bonus]],
[671086]=[[Minister of Domestic Affairs Bonus]],
[671087]=[[Conqueror Bonus]],
[671088]=[[Military Commander Bonus]],
[671089]=[[Grand Chancellor Bonus]],
[671090]=[[Your scheduled appointment time: {%s1}\n{%s2}]],
[671091]=[[Only the King/Prime Minister has permission to send mail. Operation unavailable!]],
[671092]=[[Title Removal]],
[671093]=[[Remove {%s1} from the position of {%s2}?]],
[671094]=[[Application submitted. Please wait for approval.]],
[671095]=[[You've already applied for <color=#E63838>{%s1}</color>. You can't apply for another title.]],
[671096]=[[Application available in {%s1}.]],
[671097]=[[Hero HP +5%, Hero ATK +5%, Hero DEF +5%]],
[671098]=[[Building speed +20%, Technology speed +20%, Soldier training speed +10%]],
[671099]=[[Hospital capacity +20%, healing speed for wounded soldiers +20%]],
[671100]=[[Barracks training capacity +20%, soldier training speed +20%]],
[671101]=[[Building speed +50%, Technology speed +25%]],
[671102]=[[Technology speed +50%, Building speed +25%]],
[671103]=[[Food production speed +100%, Iron Mine production speed +100%, Gold production speed +100%.]],
[671104]=[[Hero HP +5%, Hero ATK +5%, Hero DEF +5%]],
[671105]=[[March speed +5%]],
[671106]=[[March speed +5%, Building speed +60%, Technology speed +60%]],
[671107]=[[Time in Office: {%s1}]],
[671108]=[[You're currently holding this title.]],
[671109]=[[You're currently serving as <color=#E63838>{%s1}</color>. You can't apply for another title.]],
[671110]=[[Officer Appointment]],
[671111]=[[Congrats to Union member {%s2} of {%s1} on their promotion to {%s3}!]],
[671112]=[[Once set, officers in the appointment queue will take the position in rotation according to the specified schedule.]],
[671113]=[[No award records available.]],
[671114]=[[Coming soon!]],
[671115]=[[Target castle level too low to grant awards.]],
[671116]=[[You've awarded this member.]],
[671117]='Claim',
[671118]=[[Royal Mail]],
[671119]=[[Royal Mail Title]],
[671120]=[[Mail Content]],
[671121]='Send',
[671122]=[[Queue Rotation Time]],
[671123]=[[Auto-Approval Settings]],
[671124]=[[Auto-Approve President Applications]],
[671125]=[[Auto-Approve Officer Applications]],
[671126]=[[No Conqueror yet]],
[671127]=[[Conqueror crowned]],
[671128]=[[You've assumed the position of <color=#E63838>{%s1}</color>.]],
[671129]=[[Congrats to {%s2}, the leader of {%s1}, on their promotion to {%s3}!]],
[672001]=[[Activity Badge]],
[672002]=[[Castle Badge]],
[672003]=[[Union Duel Badge]],
[672004]=[[Max daily rewards upon activation: {%s1}]],
[672005]=[[Activated! Remember to claim your rewards daily.]],
[672006]=[[Activated when {%s1} union members reach {%s2} daily activity points.]],
[672007]=[[Activation grants resource collection bonus!]],
[672008]=[[Activated! Extra {%s1} resources collected from city buildings.]],
[672009]=[[Activated when {%s1} union members reach Castle Level {%s2}.]],
[672010]=[[Activated when {%s1} union members reach {%s2} Union Duel points.]],
[672011]=[[Your daily activities are insufficient. Your leader reminds you to complete daily tasks actively.]],
[672012]=[[Your Castle Level is too low. Your leader encourages you to participate in union events to earn resources and level up.]],
[672013]=[[Your Union Duel points are insufficient. Your leader reminds you to check today's duel requirements for higher scores.]],
[672014]=[[Union Badge Achievement Reward Issue]],
[672015]=[[Congratulations! Your union has met the {%s2} reward requirement for {%s1}. Here are your rewards:]],
[672016]=[[Today's Activity]],
[672017]=[[Castle Level]],
[672018]=[[Duel Points]],
[672019]='Rank',
[672020]='Name',
[672021]=[[Union Badge]],
[672022]='(Activated)',
[672023]='(Inactive)',
[672024]=[[One-tap to remind all inactive players]],
[672025]=[[The badge objective has not been completed.]],
[672026]=[[The badge objective has been completed.]],
[672027]=[[{%s1} union members reaching {%s2} daily activity points.]],
[672028]=[[{%s1} union members reaching Castle Level {%s2}.]],
[672029]=[[{%s1} union members reaching {%s2} Union Duel points.]],
[672030]=[[Obtained from]],
[672031]=[[<color=#37e325ff>Tap the chest to claim rewards</color>]],
[672032]=[[<color=#ffd938>Tap the chest to check rewards</color>]],
[672033]=[[Activity Badge Chest]],
[672034]=[[Union Duel Badge Chest]],
[672035]=[[This badge grants chest rewards. Tap the chest to view details.]],
[672036]=[[This badge reward is sent via mail daily.]],
[672037]=[[Dear Lord, congratulations! Your union met the {%s2} requirement for {%s1} yesterday. Here are your unclaimed rewards.]],
[672038]=[[Activated! Receive a chest reward every day!]],
[672039]=[[Activity Ranking]],
[672040]=[[Castle Ranking]],
[672041]=[[Duel Points Ranking]],
[672042]=[[Castle Badge Chest]],
[672043]=[[Hold to donate continuously]],
[673001]=[[Zombies Defeated: {%s1}]],
[673002]=[[Defeat the Zombie Boss.]],
[673003]=[[Defeat all enemies.]],
[673004]='Quests',
[673005]=[[Swipe left or right to control your character.]],
[673006]=[[Zombie Siege]],
[673007]=[[Total Kills]],
[673008]=[[Stage Time]],
[673009]=[[Stage cleared.\nWell done!]],
[673010]='Defeat',
[673011]=[[Return to Castle]],
[673012]=[[Skip Stage]],
[673013]=[[Try Again]],
[673014]=[[Stage {%s1}]],
[673015]=[[BOSS Incoming]],
[673016]=[[Break through the enemy encirclement.]],
[673017]=[[Reward Time!]],
[673018]=[[Extra Rewards]],
[673019]=[[Obtain all additional rewards]],
[674001]=[[Mystic Beast]],
[674101]='Borias',
[674102]='Bahamut',
[674202]=[[Available in the Strongest Lord event.]],
[674203]=[[Special Effect]],
[675001]=[[Dear Lord, would you like to send the Captain a Trade Contract as a token of your gratitude and support?]],
[675002]=[[Thanks for your generous gift!]],
[675003]=[[You don't have enough items to gift.]],
[675004]=[[You're already in the carriage queue. Switch to a different carriage?]],
[675005]=[[Passenger's Gift of Gratitude]],
[675006]=[[Your unclaimed Passenger's Gift of Gratitude has been reissued via mail. Check it out!]],
[675007]=[[{%s1}'s {%s2} is prepping.]],
[675008]=[[Dear Lord, {%s1} has acquired {%s2} and will depart in {%s3}. Please line up in time and get on board!]],
[675009]=[[Airship Destination Arrival Reward]],
[675010]=[[Congrats, Lord! The Airship initiated by {%s1} has arrived at its destination. Here are your rewards!]],
[675011]=[[{%s1}'s Airship has arrived at its destination.]],
[675012]=[[Dear Lord, Captain {%s1}'s Airship has successfully arrived at its destination. Here are the travel details, please have a look!]],
[675013]=[[{%s1}'s Airship has arrived.]],
[675014]=[[Dear Lord,
{%s1} has booked a Golden Deluxe Airship for us. Departure in {%s2}. Please line up and get on board!]],
[675015]=[[Deployment Tip]],
[675016]=[[This hero is already deployed in Team X. Continue anyway?]],
[675017]=[[Deployment Tip]],
[675018]=[[You can deploy more heroes to Team X. Proceed with the battle anyway?]],
[675019]=[[No loot acquired]],
[675020]='Victory',
[675021]='Defeat',
[675022]=[[Pack sold out for today. Purchase unavailable.]],
[675023]=[[You've been in the Union for less than 24 hours. Please try again later.]],
[675024]=[[Your Union is prepping a train. Purchase unavailable.]],
[675025]=[[This Captain has already been appointed by another member. Appointment failed.]],
[675026]=[[This Captain is no longer in the Union. Appointment failed.]],
[675027]=[[Only R4/R5 members can appoint Captains. Operation failed!]],
[675028]=[[Airship Dock]],
[675029]=[[Airship Escort Team]],
[675030]=[[Super Express Contract]],
[675031]=[[Daily purchase limit: {%s1}/{%s2}]],
[675032]=[[Available through direct purchase.]],
[675033]=[[Airship Random Reward]],
[675034]=[[Grants the Golden Union Airship and all of its position rewards!]],
[675035]=[[Airship unlocks in {%s1} day(s).]],
[675036]=[[Daily pack sold out]],
[675037]=[[Join a Union to unlock Union Trade features.]],
[675038]=[[Refresh <color=#87F425>{%s1}</color> more time(s) to obtain the Golden Airship.]],
[675039]=[[Refresh Goods]],
[675040]=[[Team 1]],
[675041]=[[Team 2]],
[675042]=[[Team 3]],
[675043]=[[Plunders Today: {%s1}/{%s2}]],
[675044]='Raid',
[675045]='Share',
[675046]=[[Passenger List]],
[675047]=[[Passenger Gratitude]],
[675048]=[[Dear Lord, would you like to send the Captain a Trade Contract as a token of your gratitude and support?]],
[675049]=[[Like & Thank]],
[675050]=[[Give Contract]],
[675051]='Tip',
[675052]=[[You're already in the ship cabin queue. Switch to another cabin?]],
[675053]=[[Passenger Gratitude List]],
[675054]=[[Union Trade Airship]],
[675055]='Name',
[675056]=[[Times Plundered:]],
[675057]=[[Next Stop Countdown:]],
[675058]=[[Defending Troops]],
[675059]=[[Invitation List]],
[675060]=[[Once assigned, the captain cannot be changed.]],
[675061]=[[The boarding of the Union Trade Airship is closing in {%s1}. Be sure to get on board in time.]],
[675062]=[[Union Airship]],
[675063]=[[Integrity: {%s1}]],
[675064]=[[Airship in Transit]],
[675065]=[[{%s1}'s Super Deluxe Airship has arrived!]],
[675066]=[[Arrival Time:]],
[675067]='View',
[675068]='Share',
[675069]=[[Battle Plan]],
[675070]=[[Start Battle]],
[675071]='Round1',
[675072]='Round2',
[675073]='Round3',
[675074]='Victory',
[675075]='Defeat',
[675076]=[[No loot acquired!]],
[675077]=[[Battle Details]],
[675078]=[[Successfully fended off an attack from <color=#FF4D2A>{%s1}</color>!]],
[675079]=[[Failed to defend against <color=#FF4D2A>{%s1}</color>'s attack!]],
[675080]=[[Battle Details]],
[675081]=[[Airship Destination Arrival Reward!]],
[675082]=[[{%s1}'s Airship has launched!]],
[675083]=[[Dear Lord,
Captain {%s1}'s Airship has departed and is expected to arrive at its destination in {%s2}. Wishing you a smooth journey without encountering any bandits!]],
[675084]=[[It is an honor to board your Airship.]],
[675085]=[[With you as the captain, I feel very safe. Thank you!]],
[675086]=[[Thank you so much for offering this ride. This airship journey means the world to me!]],
[675087]=[[It's an honor to be aboard the same Airship as you!]],
[675088]=[[The current Airship has arrived at its destination.]],
[675089]=[[Welcome aboard this Union Airship Run!]],
[675090]=[[Defense Team]],
[675091]=[[Tap the "Swap" button to change the deployment order.]],
[675092]=[[Save Team]],
[675093]=[[Rearrange Order]],
[675094]=[[Battle History]],
[675095]='Details',
[675096]='Rewards',
[675097]=[[Airship Details]],
[675098]=[[No heroes have been deployed. Please deploy heroes first.]],
[675099]=[[Union Airship Sharing]],
[675100]=[[Union Airship]],
[675101]=[[Airship Preparing]],
[675102]=[[Airship In Progress]],
[675103]=[[Airship Arrived]],
[675104]=[[{%s1}'s {%s2} Airship (Kingdom: {%s3} {%s4})]],
[675105]=[[{%s1}'s Airship is getting ready.]],
[675106]=[[{%s1}'s Airship is in transit.]],
[675107]=[[{%s1}'s Airship has arrived.]],
[675108]=[[{%s1}'s Airship successfully fended off an attack from {%s2}!]],
[675109]=[[{%s1}'s Airship failed to fend off an attack from {%s2}!]],
[675110]=[[Congratulations, you've become Captain!]],
[675111]=[[You've become Captain of the Union Airship. Head over to check it out!]],
[675112]=[[Your Airship has arrived.]],
[675113]=[[Well done, Captain! Your Airship has successfully arrived. Here are your rewards.]],
[675114]=[[No battle log available.]],
[675115]=[[Standard Airship]],
[675116]=[[Golden Airship]],
[675117]=[[You are already the Captain, no need to switch positions.]],
[675118]=[[Airship Operation Rules]],
[675119]=[[Eligibility Requirements
1. Unions with at least 20 members.
2. Members who have unlocked Airship Dock and have been in the Union for more than 24 hours.
3. One Standard Airship and two Golden Airships are allowed per Union per day. Note: Only the top 20 Unions on the server will receive one Standard Airship daily.
Airship Info
1. Standard Airship: Eligible Unions can obtain 1 Standard Airship daily at 00:00 (server time). After the Captain is designated by an R4 or R5 member, the Airship will begin preparation.
2. Golden Airship: When there are no Airships preparing, a Lord can summon 1 Golden Airship and automatically become its Captain.
Airship Preparation Phase
1. The preparation phase lasts for 4 hours. Afterward, the Airship will automatically depart for trade between cities.
2. The Captain can assign an Airship Escort Team and refresh the cargo. Tip: Refresh 5 times and you may have surprises!
3. Passengers can line up in front of the cabin they wish to board and switch to another one at any time. Once the preparation phase ends, queuing will be suspended. 5 random passengers will be selected from each cabin queue to board.
Airship Operation
1. Airships may be attacked by other Lords during flight. The cargo in 3 random slots will be plundered upon a successful attack.
2. An Airship can be plundered up to 3 times.
Airship Rewards
1. The Captain receives all remaining cargo from the Airship and cabins as rewards.
2. Passengers receive the remaining cargo from the cabins as rewards.]],
[675120]=[[No member selected]],
[675121]=[[Airship has launched]],
[675122]=[[The Airship is about to launch]],
[675123]=[[Your heartfelt thanks to %s1 have been delivered!]],
[675124]=[[Each Airship can be plundered up to {%s1} time(s)]],
[675125]=[[Golden Airship – Cabin {%s1} Gold Reward Slot]],
[675126]=[[Golden Airship – Cabin {%s1} Remaining Reward Slot]],
[676001]=[[Kingdoms Showdown]],
[676002]=[[Matchup Info]],
[676003]=[[This Week's Battle Results]],
[676004]=[[Royal City Contest]],
[676005]=[[Week {%s1}: {%s2}]],
[676006]='Quarterfinals',
[676007]='Semifinals',
[676008]='Finals',
[676009]='Groups',
[676010]='Log',
[676011]='Rewards',
[676012]=[[Preview Info]],
[676013]=[[1. There are {%s1} kingdom participating in this event, and the battle will last for {%s2} weeks; 
2. Participants will receive generous rewards.]],
[676014]=[[Battle Log]],
[676015]='Rank',
[676016]='kingdom',
[676017]=[[Week 1]],
[676018]=[[Week 2]],
[676019]=[[Week 3]],
[676020]=[[Tap a blank space to close.]],
[676021]=[[Rewards Preview]],
[676022]=[[Rank {%s1} Rewards]],
[676023]=[[President Rewards]],
[676024]=[[Kingdom Rewards]],
[676025]=[[Strongest Union Rewards]],
[676026]=[[Participation Rewards (participated at least once)]],
[676027]=[[Select Kingdom]],
[676028]='View',
[676029]=[[In no particular order]],
[676030]=[[Kingdom Contribution MVP]],
[676031]=[[Give a like]],
[676032]=[[Attacker Contest Phase]],
[676033]=[[Points Comparison]],
[676034]=[[Union Duel Victory]],
[676035]=[[Become the MVP in Union Duel]],
[676036]=[[Win in the Kingdoms's World Boss]],
[676037]=[[Rank No.1 in personal damage dealt to World Bosses]],
[676038]=[[Win in the Desert Storm]],
[676039]=[[Become the MVP in the Desert Storm battlefield]],
[676040]=[[Plunder wagons in an enemy Kingdom]],
[676041]=[[Rank No.1 in the Arms Race]],
[676042]='MVP',
[676043]=[[Preview Info]],
[676044]=[[1. From Monday to Friday, points will be accumulated based on the items of competition between both Kingdom.
2. On Friday, points will be finalized, and the Kingdom with the most points become the attacker.
3. If the attacking Kingdom wins, its King will become the Conqueror.]],
[676045]=[[Battle Results]],
[676046]=[[Not started yet]],
[676047]=[[How to Determine the Attacker]],
[676048]=[[Contest Phase: This phase runs from Monday to Friday. The Kingdoms with the most points gains the opportunity to invade the opposing Kingdoms.
Royal City Siege: On Saturday, the attacker can invade the opposing Kingdoms to seize the Royal City.
Royal City Defense: The Kingdoms with the fewest points must defend their Royal City on Saturday.]],
[676049]=[[Complete the following incidents to earn Kingdoms points:]],
[676050]='Plus',
[676051]='Score',
[676052]=[[Point Ranking]],
[676053]=[[Union Ranking]],
[676054]=[[Personal Ranking]],
[676055]='Rank',
[676056]='Name',
[676057]=[[Contribution Points]],
[676058]=[[Note: The ranking updates have delays.]],
[676059]=[[Kingdoms {%s1}]],
[676060]=[[Not started yet]],
[676061]=[[Battle Start Countdown]],
[676062]=[[Battle ends in:]],
[676063]=[[Reward Settlement Countdown]],
[676064]=[[Attack Now]],
[676065]=[[Can relocate again in {%s1}]],
[676066]=[[Kingdoms Winning Streak Rewards]],
[676067]='Claim',
[676068]=[[Personal points need to reach {%s1}]],
[676069]=[[Point gains resume in {%s1}]],
[676070]=[[Complete the following tasks to earn points]],
[676071]='Rewards',
[676072]='???',
[676073]=[[Value of {%s1}]],
[676074]=[[Ranking rewards are subject to change based on actual results.]],
[676075]=[[Point Ranking]],
[676076]='Rank',
[676077]='Rewards',
[676078]='Rank',
[676079]=[[Rewards Preview]],
[676080]='Lord',
[676081]='Points',
[676082]=[[My Kingdoms]],
[676083]=[[My Union]],
[676084]='Conqueror',
[676085]=[[Military commander]],
[676086]=[[Political commander]],
[676087]='Leaderboard',
[676088]=[[Conqueror Award]],
[676089]=[[Clash of Kingdomss - Kingdoms Contest]],
[676090]='Go',
[676091]='Rewards',
[676092]=[[Opens in {%s1}]],
[676093]=[[Clash of Kingdomss – Preview]],
[676094]='Attacker',
[676095]='Defender',
[676096]=[[Clash of Kingdomss – Royal City Clash]],
[676097]='Ongoing...',
[676098]=[[Royal City Contested]],
[676099]=[[Eastern Guard]],
[676100]=[[Southern Guard]],
[676101]=[[Western Guard]],
[676102]=[[Northern Guard]],
[676103]='Lord',
[676104]=[[Winning Kingdoms Rewards]],
[676105]=[[Losing Kingdoms Rewards]],
[676106]=[[Clash of Kingdomss – Progress Rewards Reissued]],
[676107]=[[Your unclaimed progress rewards in the Clash of Kingdomss have been reissued via mail. Check them out!]],
[676108]=[[Clash of Kingdomss – King's Rewards]],
[676109]=[[Congratulations! Your Kingdoms ranked {%s1} in the Clash of Kingdomss. Check out your exclusive King's rewards!]],
[676110]=[[Clash of Kingdomss – Participation Rewards]],
[676111]=[[Congratulations! Your Kingdoms ranked {%s1} in the Clash of Kingdomss. Check out your participation rewards!]],
[676112]=[[Clash of Kingdomss – Bye Rewards]],
[676113]=[[Your Kingdoms had a bye in the last Clash of Kingdomss. Check out your unclaimed chest rewards!]],
[676114]=[[Clash of Kingdomss – Winning Streak Rewards]],
[676115]=[[Your unclaimed winning streak rewards in the Clash of Kingdomss have been reissued via mail. Check them out!]],
[676116]=[[Clash of Kingdomss – Personal Ranking Rewards]],
[676117]=[[Congratulations! You ranked {%s1} in the Clash of Kingdomss - Royal City Clash. Check out your rewards!]],
[676118]=[[Preview Phase]],
[676119]=[[Event starts in {%s1}]],
[676120]=[[Battle start countdown]],
[676121]='Ended',
[676122]=[[Battle begins at: {%s1}]],
[676123]=[[Royal City Contested: {%s1}]],
[676124]=[[Reward Settlement Time: {%s1}]],
[676125]='Liked!',
[606126]='Notes:',
[676127]=[[Match Overview]],
[676128]=[[Clash of Kingdomss is a 1v1 competition between Kingdomss, where victory is claimed by seizing the enemy's Royal City.]],
[676129]=[[Multiple Kingdomss are matched into a group for 2–3 weeks of competition to decide the champion!]],
[676130]=[[Winning the championship won't be easy. The President must unite all strength within the Kingdoms!]],
[676131]=[[Match Rules]],
[676132]=[[Off-season: The schedule is determined randomly within the group. Winning Kingdoms advance to the next round, where they are matched against other winners until a champion emerges.]],
[676133]=[[During the season, Kingdoms are matched within their season group based on their season rankings.]],
[676134]=[[Matchmaking rule: the victorious are matched with the victorious, and the defeated with the defeated.]],
[676135]=[[Victory Condition: Capture the enemy's Royal City within their Kingdom.]],
[676136]=[[Match Phase]],
[676137]=[[Monday–Friday: During this Kingdom point contest phase, excel in various events to increase your Kingdom's total points.]],
[676138]=[[Invaders & Defenders: Game time ends on Friday;]],
[676139]=[[The higher-scoring Kingdom becomes the invader; the lower-scoring one becomes the defender;]],
[676140]=[[Royal City Defense Battle: Starts on Saturday. The invader can relocate to the defender's Kingdom and attack the royal city to seize control.]],
[676141]=[[Royal City Defense Battle]],
[676142]=[[Unlike Kingdom-based Royal City battles, Lords from the same Kingdom will not attack each other's troops during battles for neutral buildings, as all troops will be treated as allies (this does not apply when attacking a lord's castle).]],
[676143]=[[Unity among lords of the same Kingdom is key!]],
[676144]=[[Achieve 1st Place in the Arena]],
[676145]=[[Kill 1 Lv.1 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676146]=[[Kill 1 Lv.2 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676147]=[[Kill 1 Lv.3 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676148]=[[Kill 1 Lv.4 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676149]=[[Kill 1 Lv.5 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676150]=[[Kill 1 Lv.6 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676151]=[[Kill 1 Lv.7 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676152]=[[Kill 1 Lv.8 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676153]=[[Kill 1 Lv.9 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676154]=[[Kill 1 Lv.10 soldier from an enemy Kingdoms at the Royal City or Guardian]],
[676155]=[[Kill 1 Lv.1 soldier from an enemy Kingdoms in the Contaminated Area]],
[676156]=[[Kill 1 Lv.2 soldier from an enemy Kingdoms in the Contaminated Area]],
[676157]=[[Kill 1 Lv.3 soldier from an enemy Kingdoms in the Contaminated Area]],
[676158]=[[Kill 1 Lv.4 soldier from an enemy Kingdoms in the Contaminated Area]],
[676159]=[[Kill 1 Lv.5 soldier from an enemy Kingdoms in the Contaminated Area]],
[676160]=[[Kill 1 Lv.6 soldier from an enemy Kingdoms in the Contaminated Area]],
[676161]=[[Kill 1 Lv.7 soldier from an enemy Kingdoms in the Contaminated Area]],
[676162]=[[Kill 1 Lv.8 soldier from an enemy Kingdoms in the Contaminated Area]],
[676163]=[[Kill 1 Lv.9 soldier from an enemy Kingdoms in the Contaminated Area]],
[676164]=[[Kill 1 Lv.10 soldier from an enemy Kingdoms in the Contaminated Area]],
[676165]=[[Kill 1 Lv.1 Soldier from the enemy Kingdoms]],
[676166]=[[Kill 1 Lv.2 Soldier from the enemy Kingdoms]],
[676167]=[[Kill 1 Lv.3 Soldier from the enemy Kingdoms]],
[676168]=[[Kill 1 Lv.4 Soldier from the enemy Kingdoms]],
[676169]=[[Kill 1 Lv.5 Soldier from the enemy Kingdoms]],
[676170]=[[Kill 1 Lv.6 Soldier from the enemy Kingdoms]],
[676171]=[[Kill 1 Lv.7 Soldier from the enemy Kingdoms]],
[676172]=[[Kill 1 Lv.8 Soldier from the enemy Kingdoms]],
[676173]=[[Kill 1 Lv.9 Soldier from the enemy Kingdoms]],
[676174]=[[Kill 1 Lv.10 Soldier from the enemy Kingdoms]],
[676175]=[[1 Lv.1 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676176]=[[1 Lv.2 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676177]=[[1 Lv.3 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676178]=[[1 Lv.4 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676179]=[[1 Lv.5 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676180]=[[1 Lv.6 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676181]=[[1 Lv.7 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676182]=[[1 Lv.8 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676183]=[[1 Lv.9 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676184]=[[1 Lv.10 soldier killed in battle against an enemy Kingdoms lord in the Contaminated Area]],
[676185]=[[Reduce the City Defense of enemy Kingdoms Lords' castles by 1 point.]],
[676186]=[[Force move an enemy Kingdoms lord's castle]],
[676187]=[[When troops garrisoned at the Guardian or Royal City return, points are awarded per second garrisoned.]],
[676188]=[[For each Lv.1 soldier killed by the Guardian when it's garrisoned with troops]],
[676189]=[[For each Lv.2 soldier killed by the Guardian when it's garrisoned with troops]],
[676190]=[[For each Lv.3 soldier killed by the Guardian when it's garrisoned with troops]],
[676191]=[[For each Lv.4 soldier killed by the Guardian when it's garrisoned with troops]],
[676192]=[[For each Lv.5 soldier killed by the Guardian when it's garrisoned with troops]],
[676193]=[[For each Lv.6 soldier killed by the Guardian when it's garrisoned with troops]],
[676194]=[[For each Lv.7 soldier killed by the Guardian when it's garrisoned with troops]],
[676195]=[[For each Lv.8 soldier killed by the Guardian when it's garrisoned with troops]],
[676196]=[[For each Lv.9 soldier killed by the Guardian when it's garrisoned with troops]],
[676197]=[[For each Lv.10 soldier killed by the Guardian when it's garrisoned with troops]],
[676198]=[[Personal Points Progress Reward 1]],
[676199]=[[Personal Points Progress Reward 2]],
[676200]=[[Personal Points Progress Reward 3]],
[676201]=[[Personal Points Progress Reward 4]],
[676202]=[[Personal Points Progress Reward 5]],
[676203]=[[Personal Points Progress Reward 6]],
[676204]=[[Personal Points Progress Reward 7]],
[676205]=[[Personal Points Progress Reward 8]],
[676206]=[[Personal Points Progress Reward 9]],
[676207]=[[No grouping info]],
[676208]='Notes:',
[676209]=[[A colossal statue guard stationed near the Royal City. Capturing it will deal massive damage to the Royal City.]],
[676210]=[[Unlocks during the Royal City Clash]],
[676211]=[[After being captured, the Guardian attacks the Royal City every 10 seconds, damaging all enemy teams garrisoned there. Each hit inflicts a 1% loss of Max troop capacity. Every 5 teams garrisoned at the Guardian reduces the attack interval by 1 second (up to 4-second reduction).]],
[676212]=[[Point acquisition is now available.]],
[676213]=[[Occupied by {%s1}]],
[676214]='Defend',
[676215]=[[Strongest Union Rewards]],
[676216]=[[Your Kingdom was defeated in the Clash of Kingdoms. The king of {%s1} is now your Kingdom's Conqueror.]],
[676217]=[[Congratulations! Your Kingdom ranked {%s1} in the Clash of Kingdoms event. Here are your Strongest Union Rewards!]],
[676218]=[[{%s1} occupied]],
[677001]=[[[{%s1}]'s Siege Camp]],
[677002]=[[Send troops to assist. After the siege begins, the troops will automatically go there.]],
[677003]=[[Large Siege Camp]],
[677004]=[[Number of troops: {%s1}]],
[677005]=[[Once construction is complete, you can send troops to garrison, and after the siege begins, the troops will automatically go to attack the city!]],
[677006]=[[The union is about to start the siege]],
[677007]=[[The union has started the siege]],
[700001]=[[Skill description test skill description test skill description test skill description {%s1} test skill description test skill description test skill {%s2} description test skill description test skill description test.]],
[700002]=[[Skill description test skill description test skill description test skill description {%s1} test skill description test skill description test skill {%s2} description test skill description test skill description test.]],
[700003]=[[Skill description test skill description test skill description test skill description {%s1} test skill description test skill description test skill {%s2} description test skill description test skill description test.]],
[700004]=[[Skill description test skill description test skill description test skill description {%s1} test skill description test skill description test skill {%s2} description test skill description test skill description test.]],
[700005]=[[Skill description test skill description test skill description test skill description {%s1} test skill description test skill description test skill {%s2} description test skill description test skill description test.]],
[700006]=[[Skill description test skill description test skill description test skill description {%s1} test skill description test skill description test skill {%s3} description test skill description test skill description test.]],
[700007]=[[Star description test 1 Star description test 1 Star description test 1 Star description test 1 Star description test 1 Star description test 1 Star description test 1 Star description test 1 Star description test 1 Star description test 1]],
[700008]=[[Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2 Star description test 2]],
[700009]=[[Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3 Star description test 3]],
[700010]=[[Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4 Star description test 4]],
[700011]=[[Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5 Star description test 5]],
[710001]=[[Story Unlock Condition: Rare Hero]],
[710002]=[[Story Unlock Condition: Excellent Hero]],
[710003]=[[Story Unlock Condition: Epic Hero]],
[710004]=[[Story Unlock Condition: Epic+ Hero]],
[710005]=[[Story Unlock Condition: Legendary+ Hero]],
[710101]='Dragonborn',
[710102]='Dragonic',
[710103]=[[A mechanical T. Rex that uses a "Return Ray" to blast enemies.]],
[710104]=[[　　An unpredictable heart is more fearsome than strength!
　　　　　　　　　　　　　　—Dragonic

　　The first thing people notice about Dragonic is his confidence and strength.
　　The truth is, Dragonic wasn't always as strong as he is today. As a child, he was all skin and bone, which made him look out of place among the famously powerful and fierce Azure Dragons. As a result, the young Dragonic was often teased by others.
　　But Dragonic wasn't like his peers at all. He was innocent and had a positive attitude towards everything. Even when faced with people who laughed at him, Dragonic always responded with a sunny smile, which, in turn, gained him many friends.
　　The real turning point in Dragonic's life was probably when he encountered the Dragonslayer on the day the Battle of the Dragonslayer broke out.
　　Dragonslayer—the natural enemy of all Dragonborn, and a name that can never be erased from Dragonic's mind!
　　The winters in Kotchiwi are so frigid that even ordinary beasts rarely stick around for it. As in previous years, Dragonic's father, the clan leader, started to move south with his people, but they were ambushed halfway. The fierce battle triggered a forest fire, and the clan members fled in fear. But the Dragonslayer, who had been lying in wait for a long time, wasn't about to let go of such a great opportunity. To protect the clan, Dragonic's father stayed behind to fight the Dragonslayer alone, only to fall in battle.
　　The survivors continued to move south, but the young Dragonic made a run for the forest when the clan's guard was lowered. There, he was captured by the Nightfall, who had come sniffing around after the battle. From then on, Dragonic's life was drastically changed.]],
[710105]=[[Dragonic doesn't shy away from talking to people about the days when he was captured by the Nightfall. His light-hearted tone makes it sound as if the suffering was no big deal for him.

The young Dragonic was raised by the Nightfall, but his upbringing wasn't all roses. The Nightfall used all kinds of ways to break his will until he became a puppet who was completely subservient to their orders. For a long time, the meaning of Dragonic's existence was to fight under the control of the Nightfall, regardless of morality. Of course, this period of time was full of violence and bloodshed.

That is, until Dragonic met someone of the same dragon bloodline as him.

Once on a mission, Dragonic was badly wounded. In a trance, he saw a strange woman slowly walking towards him. He tried to get up, but the excruciating pain and endless fatigue knocked him out immediately.
When he woke up, the wounds on his body had already been treated. Although the bandaging looked clumsy, the woman with the beast bones in front of him didn't seem hostile.

"You're awake! Great! Do you know how heavy you are?!" The woman exclaimed as she saw Dragonic wake up.

"..."

"You're a Dragonborn? So am I!" With that, she turned around and swung the dragon tail behind her towards Dragonic.

"Pfft!"

Accustomed to his reaction, the woman wasn't angry at all. Instead, she gave him a bright smile since he was looking a lot better.

The feeling was familiar!

Dragonic tried to grasp this sense of déjà vu. It seemed that when he was a young boy, he was just like her.

"Where are your people?" She asked softly, raising her clear eyes.

The Beastkin have always had the habit of living in groups, so the dragon girl was curious when she saw Dragonic alone.

"My people?" Dragonic drifted into contemplation. The memory fragments were locked by something. He tried to grasp them, but couldn't.

As Dragonic's headache worsened, the dragon girl realized that something was wrong with him, so she whacked him with her bone spear, rendering him unconscious.

A few months passed. Dragonic no longer held the dragon girl's attack against her. They regarded each other as siblings, and their relationship continued to deepen. Although Dragonic often wore a stern face, the dragon girl knew that underneath his cold exterior was a warm heart. Dragonic also felt that his cold heart was beginning to have something to care about.]],
[710106]=[[Dragonic initially didn't tell the dragon girl that he was serving Nightfall, perhaps fearing that she would reject him or fearing that revealing his identity would hurt her. In the corner of Dragonic's heart, there was always a place that didn't sit right with the girl, something called "freedom."
The dragon girl said she wanted to see the world, so she secretly left her hometown. After leaving, she realized that the world was a vast place. There were tribes that followed the water and grass, cold and unforgiving kingdoms, ancient talking trees in the forest, and even sugar-coated fruit skewers in the east. Dragonic had never heard of these things. In his days of serving Nightfall, apart from the demanding missions, there were only the cold walls of the night.
What truly moved Dragonic and made him yearn to escape the control of Nightfall was not merely the desire for freedom. It was when the girl said, "My hometown is called Dracasbreth. It is in the distant northwest of the continent, also known as the 'Land of the Setting Sun.' Every year, during the twilight of the autumn equinox, the descendants of the Dragon gather on the cliffs by the sea, spreading their wings and waiting for the response of our ancestors. I'll take you to see it if possible."
Dragonic understood that if he didn't leave Nightfall, if he didn't have freedom, he would never have the opportunity again.
Growing up alone in Nightfall, he didn't know what it meant to worry or understand what it meant to care. But like anyone willing to risk their lives, it felt as if an invisible hand was pushing him forward. By the time he regained his senses, he stood amidst the ruins left by battles, covered in scars. He bent down, emitting a low growl mixed with pain and excitement—he was finally free.
When Dragonic accompanied the girl back to her homeland, beneath the cliff, the surging waves of the sea rolled, splashing the twilight onto his azure wings. The scars from that battle still faintly ached, but watching her excited shouts, it seemed that every sacrifice was worth it.
The dragons roared, and the dragon wings instantly erased the remaining sunlight, as if nightfall had suddenly arrived. Within the bloodline known as the "Dragonborn," there was still a glimmer of radiance, waiting for the day of revival.
People? It seemed that a long, long time ago, there was such a scene—a cold winter, swirling snow, footsteps advancing toward the south... No, there were also forests, raging fires, battles, blood...
Dragonic's roar sounded somewhat painful, appearing out of place. The scene before his eyes seemed to be erased, replaced by painful memories that pounded in his head and heart. Until an angry roar pierced through the darkness, gradually fading back into reality—"Fellow kin! It's the Dragonslayer! The Dragonslayer! He's come with the knights of the kingdom! The light of the dragons shall endure! Fight with me!"
Dragonslayer! Dragonslayer! Dragonslayer! Dragonslayer! Dragonslayer!
The memories that were sealed away by Nightfall began to loosen. Deep blue lightning surrounded Dragonic's body. He knew that now was the moment to fight with all his strength!]],
[710107]=[[Coming soon!]],
[710108]=[[Thunder Breath]],
[710109]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[710110]=[[Deals an additional 30% Physical DMG.]],
[710111]=[[Deals an additional 70% Physical DMG.]],
[710112]=[[Deals an additional 120% Physical DMG.]],
[710113]=[[Deals an additional 185% Physical DMG.]],
[710114]=[[Deals an additional 270% Physical DMG.]],
[710115]='Overload',
[710116]=[[Reduces Physical DMG taken by front-row allies by {%s1} for 1 turn.]],
[710117]=[[Reduces physical damage received by an additional 2%]],
[710118]=[[Duration extended to 2 turns.]],
[710119]=[[Reduces physical damage received by an additional 2%]],
[710120]=[[The skill effect applies to all allies.]],
[710121]=[[Reduces physical damage received by an additional 2%]],
[710122]=[[Thunderous Wrath]],
[710123]=[[During battle, reduces all damage taken by your front-row units by {%s1}.]],
[710124]=[[Reduces all damage taken by an additional 2%.]],
[710125]=[[Reduces all damage taken by an additional 2%.]],
[710126]=[[Reduces all damage taken by an additional 2%.]],
[710127]=[[Reduces all damage taken by an additional 2%.]],
[710128]=[[Reduces all damage taken by an additional 2%.]],
[710129]=[[Thunder Mark]],
[710130]=[[HP & ATK & DEF +20%, Speed +40]],
[710201]=[[Winter's Wrath]],
[710202]='Rexar',
[710203]=[[The King of the Northlands, who fought to save his people, no matter the cost to himself.]],
[710204]=[[The quickest and most effective way to annoy Chakiss is to mention her age.

As a Dragonborn, she has a long lifespan. Knowing how much human girls care about their age, it has become a taboo question for her as well.

Chakiss is curious about places other than Dracasbreth, but some of her "curious" behavior is seen as "extremely destructive" by others. Chakiss does not follow the human code of conduct. In her opinion, these "rules" are annoying, and as a result, she has only a few friends.

The title of "Dragon Warrior" was first spread among human beings. It was rumored that someone had lured and trapped Chakiss, hoping to sell her for a fortune on the black market. But when talking about Chakiss' age, the remark "You'd never guess she's hundreds of years old!" set her off. She immediately beat the greedy black market merchant to a pulp. Everyone watched dumbstruck as Chakiss saw herself out without a care.

The title of "Dragon Warrior" has since spread, along with speculation about Chakiss' true age...]],
[710205]=[[In the third year of his reign as King of the Northlands, Rexar quietly set aside his crown and disappeared into the howling blizzard. With him vanished the Pure Ice, the source of unparalleled glory for the Takin Tribe. The hero's sudden absence left his people in a state of confusion and despair, with only an empty throne to whisper of once-great splendor.
In truth, this was not an unfamiliar story for the Takins. It was over a century ago that the "Last King" Minoran had stripped the tribe of their Pure Ice and led them south to Kotchiwi. Legend has it that the spire atop Mount Parcius, the gateway to the far northern lands, was crafted by Minoran himself from bronze, where he made his people swear an unbreakable vow: the Takin Tribe shall never set foot in the far North again.

As time flowed on and memories faded, the Takin Tribe ultimately broke their vow and returned to the northern lands, driven by the looming threat of the Void. History, it seemed, was doomed to repeat itself. Although Rexar did not lead his people away from the North, suspicion festered like an unshakeable virus. Just as rumors had once plagued Minoran, whispers began to spread: "Rexar seeks to hoard the power of the Pure Ice for himself."

Bereft of both the Pure Ice and their king, the Takin Tribe retreated to a corner of the northern lands. Though Rexar's lingering influence afforded them a tenuous hold on survival, the glory and splendor of their past had dispersed like fragile snowflakes, vanishing into the cold, unfeeling winds.

Where had Rexar vanished to? Was he truly coveting the power of the Pure Ice? Was there a connection between Minoran's actions and Rexar's disappearance? Countless questions continued to swirl within the Takin Tribe. It wasn't until decades later, when the gods revealed the sins of the Void to the world, that the answers came to light, exposing the truth for all to see.]],
[710206]=[["No, I was wrong. It's not glory or power—it's a curse! I only hope it's not too late... it can't be!"

After Omega, the Void Lord, uncovered the realm of Esper Continent, he did not sit idly by. Seven "seeds" were sent through the labyrinthine corridors of the Void to the farthest reaches of the continent. These seeds took on the guise of "power," subtly warping the minds of living beings, driving them to become increasingly irritable and aggressive, eventually morphing them into Void abominations. Among these sinister seeds was the Pure Ice, once revered by the Takin Tribe as a sacred relic.

The wise and virtuous former king, Minoran, sensing the threat, forcibly separated and sealed the Pure Ice from his people. He then led the Takin Tribe away from the northern lands where the ice was sealed. If not for Rexar, it might have remained hidden from the world forever.

When the Ice Realm God projected this history into Rexar's mind, he initially found himself baffled. Why had Minoran chosen to keep the truth about the Pure Ice hidden rather than revealing it openly, and why had he opted to seal it away and relocate the tribe? It wasn't until Rexar proposed the idea of re-sealing the Pure Ice to the esteemed elders and encountered widespread resistance that he began to understand. Not everyone can remain indifferent to the allure of power, especially after savoring its seductive sweetness. How could one simply cast it aside as if it were worthless?

It turned out that the ancestors' true intention was to ensure that the Takin Tribe would completely forget this glorious past.

Go to Kotchiwi, my children. There, you'll find lush pastures and gentle, warming breezes. Forsake the harsh chill of the northern lands and release the hidden power—banish it from your memory, as if it had never existed in the first place.

Yet, by a twist of fate, the Void led the Minotaurs back to the icy plains, and the hero Rexar, long absent for centuries, inadvertently unleashed that very demon. Initially, Rexar, with his pride intact, harbored a glimmer of hope. But as the Takin Tribe grew ever stronger and the dormant Void became increasingly restless, the tribe's obsession with war, destruction, and conquest deepened. Rexar knew it was time for him to act.

It soon became evident that Minoran's escape had been a mere stopgap, failing to address the root of the problem. Rexar, undeterred by the risks, chose to take a daring stand. Resolute and unyielding, he vowed to annihilate the Pure Ice entirely, once and for all breaking the ancient curse that had plagued his people through the ages.
The ancient records reveal that while the Pure Ice epitomizes the utmost chill, it might not be invincible. Perhaps only the most intense of flames could hope to melt it. And the fiercest fire in the Esper Continent burns within the Celestial Realm that crowns Yggdrasil.

That year, Rexar took a monumental risk, sealing the Void's seed, the Pure Ice, within himself. It was also the year he laid aside his crown of power and set out on the uncharted path to the Celestial Realm. At thirty-two, Rexar's name would be remembered differently in the annals of history: before that year, he was hailed as a great hero; after it, he would be celebrated as a true legend.]],
[710207]=[[Coming soon!]],
[710208]=[[Frostlord's Throne]],
[710209]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[710210]=[[Deals an additional 30% Physical DMG.]],
[710211]=[[Deals an additional 70% Physical DMG.]],
[710212]=[[Deals an additional 120% Physical DMG.]],
[710213]=[[Deals an additional 185% Physical DMG.]],
[710214]=[[Deals an additional 270% Physical DMG.]],
[710215]=[[Frostbite Touch]],
[710216]=[[Increases front-row allies' DEF by {%s1} for 1 turn.]],
[710217]=[[Increases DEF by an additional 5%.]],
[710218]=[[Increases DEF by an additional 5%.]],
[710219]=[[The skill effect applies to all allies.]],
[710220]=[[Duration extended to 2 turns.]],
[710221]=[[Increases DEF by an additional 5%.]],
[710222]=[[Eternal Frostlands]],
[710223]=[[During battle, reduces all damage taken by {%s1}.]],
[710224]=[[Reduces all damage taken by an additional 3%.]],
[710225]=[[Reduces all damage taken by an additional 3%.]],
[710226]=[[Reduces all damage taken by an additional 3%.]],
[710227]=[[Reduces all damage taken by an additional 3%.]],
[710228]=[[Reduces all damage taken by an additional 3%.]],
[710229]=[[Chilling Surge]],
[710230]=[[HP & ATK & DEF +20%, Speed +40]],
[710301]='Shadowhunt',
[710302]='Monica',
[710303]=[[A princess of the Felitaur race who ventured into the desert to gain the strength to exact revenge.]],
[710304]=[["Run, Monica. Run as far as your paws can carry you, and never look back!"
Monica could never forget that fateful night when her parents fell victim to a wicked poison. With his last ounce of strength, her father guided her to the edge of the jungle, his hand wavering eastward before finally meeting his fate. Lost and directionless, the young Felitaur girl followed the path indicated by her father's fading gesture, relying on her innate survival instincts to endure. Gone were the days of comfort and abundance as the daughter of the clan leader. Her family was destroyed by her conniving uncle Brad, whose ambitions shattered the idyllic life she had once cherished.
Seeking solace, she ventured into the Varut Desert, hoping for respite from her pain. Exhausted and on the brink of collapse from thirst, she welcomed the sweet release of unconsciousness.
To her surprise, Monica awoke to find herself in the presence of the Madrat Clan, known for their distinct pointed faces and formidable incisors.
One member of the clan, flexing his muscles, quipped, "Big cat, I don't suppose you have a taste for rats, do you?"
This encounter etched itself in Monica's memory. Drawn not only to the Madrats' hospitality but also their unwavering pursuit of strength and combat, Monica sensed a glimmer of hope she had thought lost forever—the hope of reclaiming her homeland and avenging her parents' tragic fate.
From that moment forward, there was another figure traversing the Varut Desert with a bag slung over her back.]],
[710305]=[[As the Void sought to bend the Madrat Clan to its will, igniting the infamous Thunderbreak Battle, Monica had already immersed herself in the ranks of the clan. Years of training had honed her skills, and her natural physical prowess had elevated her to the status of one of the Madrat's most formidable warriors.
The Varut Desert had become her second home, and when the impending onslaught from the void loomed, Monica made a resolute decision—to fight for herself and her newfound family.
The grueling battle waged on for three long years, a relentless struggle that tested Monica's mettle time and again. She faced countless adversaries, each encounter adding to her strength and transforming her into a true force to be reckoned with. It was the Madrat, unyielding in their resilience, who stood beside her, turning the tide in the early stages of the conflict. In a decisive clash, they vanquished the Void's army, laying the groundwork for reclaiming the land of yellow sands.
Monica led the charge in that climactic battle, wielding a colossal hammer crafted from the desert's most precious ore. With each mighty swing, she shattered the enemy ranks and paved the way to victory. The three-year war exacted a heavy toll on the Madrat Clan.
Half of their comrades fell, and their once-proud structures lay in ruins. Intensive post-war reconstruction became their arduous task. Having aided the Madrat Clan in their rebuilding efforts, Monica knew it was time for her to leave, as an unfinished mission beckoned her from a distant jungle, calling her to fulfill the purpose that had driven her thus far.]],
[710306]=[["Hey there, big cat! Is this where you grew up? The place looks alright, but how do you become a fierce warrior in such a charming environment?"
Ali, a member of the Madrat Clan, glanced around as they strolled through the tranquil jungle. The sun's rays filtered through the lush foliage, casting a sprinkling of light on the vibrant grass. Occasionally, they would rustle through a cluster of fallen leaves, startling a few squirrels who would scamper away.
The journey back to Monica's homeland had been filled with boredom and hardship, but having Ali as her companion made it a little less dreary. Monica understood that Ali was concerned for her, which is why he chose to be her travel partner.
"My dear niece, you've grown into quite the warrior. Let your uncle have a good look at you!"
A burly figure emerged from the shadows of the trees, his brows bearing a resemblance to Monica's, a gleaming crown adorning his head. Although his words carried the warmth of family, the enormous axe in his hand gleamed with a chilling light.
It was Monica's uncle, Brad, who had learned of her return. But he came to greet her alone, and she could sense the absence of kindness in his heart.
Monica tightened her grip on her giant hammer and exchanged a knowing glance with Ali, silently telling him to stay back. This was her battle, and she was determined to face it alone.
Moments later.
Monica stood in a daze, staring at Brad lying on the ground. She had emerged victorious, avenging her parents, but the sudden turn of events left her disoriented and somewhat disillusioned. She realized that the Felitaur race she once knew, and even the jungle itself, had changed.
Lost in her thoughts, Monica was brought back to reality by Ali, who approached her with a pat on the shoulder and a thumbs up. "What a battle! You were amazing, the strongest warrior of our Madrat Clan!"
Those words snapped Monica out of her daze. Years of living together had forged a bond between the Madrats and herself. Despite feeling like a stranger in her own homeland, as long as the Madrat Clan was by her side, Monica knew she still had a warm and welcoming home.]],
[710307]=[[Story Unlock Condition: Legendary+ Hero]],
[710308]=[[Desert Ember]],
[710309]=[[Deals {%s1} Magic DMG to a single enemy; if the target is in the front row, the damage splashes to one back-row unit.]],
[710310]=[[Deals an additional 30% Magic DMG.]],
[710311]=[[Deals an additional 70% Magic DMG.]],
[710312]=[[Deals an additional 120% Magic DMG.]],
[710313]=[[Deals an additional 185% Magic DMG.]],
[710314]=[[Deals an additional 270% Magic DMG.]],
[710315]=[[Savage Swoop]],
[710316]=[[Launches 3 attacks, each dealing {%s1} Magic DMG to a random enemy.]],
[710317]=[[Deals an additional 30% Magic DMG.]],
[710318]=[[Attack limit increased to 4 times.]],
[710319]=[[Deals an additional 70% Magic DMG.]],
[710320]=[[Attack limit increased to 5 times.]],
[710321]=[[Deals an additional 120% Magic DMG.]],
[710322]=[[Feline Fury]],
[710323]=[[During battle, increases your CRIT by {%s1}.]],
[710324]=[[Increases CRIT by an additional 3%.]],
[710325]=[[Increases CRIT by an additional 3%.]],
[710326]=[[Increases CRIT by an additional 3%.]],
[710327]=[[Increases CRIT by an additional 3%.]],
[710328]=[[Increases CRIT by an additional 3%.]],
[710329]=[[Bestial Blessing]],
[710330]=[[HP & ATK & DEF +20%, Speed +40]],
[710401]=[[Bow of Tomorrow]],
[710402]='Sera',
[710403]=[[Embark on a journey in search of the Dark Elves to stand against the demons.]],
[710404]=[[Elves live long lives, and for many, endless days in the Evergreen Garden eventually breed a numbing boredom. As a result, they've scattered across the continent. Sera is no exception. But unlike most who have simply settled elsewhere, her heart beats for travel. Wherever she goes, she collects special stickers unique to that land. Now, her elvenwood suitcase is adorned with scenes from every corner of the world.

Still, she never forgets her true purpose. On the day she left, the Elven Queen stroked her forehead and spoke a blessing passed down through the ages: "The forest will watch over its wandering child. Wherever you go, peace will follow."

From that day, Sera set out from the Evergreen Garden on her quest to find the Dark Elves. These long-lost kin from tens of thousands of years ago are no longer as prosperous as the Light Elves. Their traces are scarce, their stories nearly erased in history.

According to the prophecy of the ancient Great Tree, the Void will bring chaos, and the Nightfall looms with threat. The demons once sealed away may return, and they will need the power of the Dark Elves, those who long ago forged pacts with darkness to fight the demons.

"Go, child. As long as you keep moving forward, one day you will meet them. When light covers the world, darkness will return."

So spoke the prophecy.]],
[710405]=[[Sera's Bow of Tomorrow pierces not just flesh, but fate itself. Each arrow she looses is like a letter written in time, foretelling the end of its target.

Witnesses once saw her casually fire an arrow into the sky. The next day, when a Nightfall lord launched a surprise attack, that very arrow streaked down like a shooting star and struck him through the heart.

Sera did not flinch. She simply stroked her cloud-like hair and smiled, as if everything had gone according to plan.

Since then, whenever Sera fired an arrow for no apparent reason, no one dared cross her the next day, or for several days after that. After all, who could say that the Bow of Tomorrow's reach was limited to just one day?

Some whisper she's not a true elf at all, but a butterfly spirit cloaked in elf form, sustained only by an ancient age too long to comprehend.

But those who spread such rumors always wake up the next morning to find a gleaming arrow nailed to their bedpost, pinning a worn letter that echoes the same eerie message—an objection to the phrase "ancient age", phrased with chilling clarity.

Even more astonishing is the fact that Sera's arrows transcend not only time, but distance as well. Once, a traveler in the Northern Wastes casually mentioned how long Sera had lived, then boarded an airship bound for the Alchemy City in the South. Yet still, on the next morning, he awoke to find one of her bright, razor-sharp arrows waiting for him.]],
[710406]=[[In time, even Sera began to wonder: did the so-called Dark Elves truly exist, or were they just a phantom of the past? The ancient prophecy of the Great Tree now seemed empty, like a half-truth: "Light can never fully envelop the world; darkness will always claim half." And when she acquired the last town's sticker on her travels, that doubt crept ever deeper.
The final empty space on her elvenwood suitcase was filled.
Then, the air shimmered with ripples spreading through still water. She felt an invisible hand take hers. She should have resisted, but instead, a strange sense of trust settled over her. She let herself be led onward, stepping through the rippled air. The world around her warped and shifted. Sunlight faded, replaced by shadow, and when Sera blinked again, she stood amidst towering trees where light could no longer reach. Darkness spilled through the canopy, blanketing everything in its hush.
"Where... am I?" she whispered. The place felt familiar, like the Evergreen Garden she hadn't visited in ages, but without the sunlight.
"When light covers the world, darkness will return."
A shadow-cloaked figure emerged slowly and lowered its hood, smiling at Sera.
Pointed ears. Dark hair. A body faintly translucent. Eyes like the void. There was no doubt—Sera had found the Dark Elves at last.]],
[710407]=[[Coming soon!]],
[710408]=[[Arrow of the Butterfly]],
[710409]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[710410]=[[Deals an additional 30% Magic DMG.]],
[710411]=[[Deals an additional 70% Magic DMG.]],
[710412]=[[Deals an additional 120% Magic DMG.]],
[710413]=[[Deals an additional 185% Magic DMG.]],
[710414]=[[Deals an additional 270% Magic DMG.]],
[710415]=[[Arrow of Destiny]],
[710416]=[[Perform 8 attacks, each time causing damage to a random enemy unit.{%s1}physical damage]],
[710417]=[[Deals an additional 30% Magic DMG.]],
[710418]=[[Attack limit increased to 12 times.]],
[710419]=[[Deals an additional 70% Magic DMG.]],
[710420]=[[Attack limit increased to 16 times.]],
[710421]=[[Deals an additional 120% Magic DMG.]],
[710422]=[[Shadow Sprite]],
[710423]=[[During battle, increases the Magic DMG you deal by {%s1}.]],
[710424]=[[Increases Magic DMG dealt by an additional 3%.]],
[710425]=[[Increases Magic DMG dealt by an additional 3%.]],
[710426]=[[Increases Magic DMG dealt by an additional 3%.]],
[710427]=[[Increases Magic DMG dealt by an additional 3%.]],
[710428]=[[Increases Magic DMG dealt by an additional 3%.]],
[710429]=[[Darkness Vanquisher]],
[710430]=[[HP & ATK & DEF +20%, Speed +40]],
[710501]=[[Traveler of the Forest Sea]],
[710502]='Yord',
[710503]=[[The last bard in the world of Hero Clash]],
[710504]=[[To bards, the world is a vast ocean, with life as the cresting currents that flow through it. Yord wanders across the continent, seeking to stir the winds that drive this immense ocean. He tells the youth stories from afar, flipping yellowed pages that record treasures, dragon scales, wonders, and grand sights, gradually outlining the contours of a magnificent world.

"Tell me, wind that stirs the leaves... where do you come from?"
On his quest to trace the origin of the wind, he encountered rootless souls and gathered their stories, which he transformed into Genies using the Vicissitude Vessel. He then spoke these tales through beautiful, exotic ballads played on his Cloudsong Flute.

"How did that story go again? You know, the one about the Takin we heard in the Northern Land?"
The Genie, summoned by his music, swayed to the rhythm before resting its head on its hand, seemingly lost in deep thought. In an instant, the Genie transformed into the Takin, a strong, bulky figure holding a staff, bringing the long-forgotten legend to life through its vivid expressions and body movements.

"Hello, my friend. So long, may we meet again at the other end."
Yord never lingers in one place for long, like a current that never stills at an unknown shore; it always follows the wind. Leaving to explore and tell tales is what exactly a bard does. The old currents will stir up new waters, gradually carrying them toward distant horizons.]],
[710505]=[[There was a golden era of bards when countless hopeful, brave young people set sail into the unknown to explore the vast world. Bards often traveled in groups, singing, playing, and chatting along the way. Yet today, bards have nearly all faded away. Cool magic, advanced alchemy, and lucrative careers appeal more to the youth than the "elusive" concept of hope, courage, and pursuit.

"Don't be silly, Yord! The place is a mess right now. The remaining forces of the Void are still a threat, and Nightfall is causing trouble, too. Why do you have to stay outside instead of just finding a safe spot?"
Yord was busy packing his belongings.

"Yord, listen! I know of a forest. It's pretty much only Tree Spirits living there. It's hidden and safe..."
Yord ignored them.

"Don't be ridiculous, Yord! Nowhere on the continent is safe right now. Hey, where do you think you're going?"
Yord is, as always, on his way to his next destination.]],
[710506]=[[Yord's expression reveals little of what's on his mind. The Tree Spirit's joy and sorrow can only be glimpsed through the melodies of his Cloudsong Flute.

"You sound sad, Yord. Do you miss home?"
Bards rarely feel homesick; for them, the world itself is a home sweet home. Yord is flesh and blood, after all. He does feel sorrow, especially for those who have been displaced by the ravages of war, for separation and departure stand in stark contrast to the bard's way—it's not a cheerful, billowing current but a lament marred by corruption. In response to that, Yord releases the genies of guardians and healers (stories like these can be heard in every corner of the World), channeling gentle energy to heal the suffering lives affected by disasters.

"Yord, you are different from other Tree Spirits. They're either beautiful, pointy-eared elves, gnarled ancient trees, or vibrant, captivating flowers. Which tribe do you belong to?"
Well, that's a different chapter of the story. It could be traced back to the golden era and has been echoing in Yord's mind ever since, making the gloomy present all the more heartbreaking. Back then, he was treasured in his master's vessel and known as the first Genie, the one who recorded the very first tales of the bards.]],
[710507]=[[Coming soon!]],
[710508]=[[Universal Chord]],
[710509]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[710510]=[[Deals an additional 30% Magic DMG.]],
[710511]=[[Deals an additional 70% Magic DMG.]],
[710512]=[[Deals an additional 120% Magic DMG.]],
[710513]=[[Deals an additional 185% Magic DMG.]],
[710514]=[[Deals an additional 270% Magic DMG.]],
[710515]=[[Travel Invitation]],
[710516]=[[Increases all allies' ATK by {%s1} for 1 turn.]],
[710517]=[[Increases ATK by an additional 2%.]],
[710518]=[[Increases CRIT Rate by an additional 10%.]],
[710519]=[[Increases ATK by an additional 2%.]],
[710520]=[[Duration extended to 2 turns.]],
[710521]=[[Increases ATK by an additional 2%.]],
[710522]=[[Lingering Echo]],
[710523]=[[During battle, increases your Speed by {%s1} and reduces all damage taken by the ally with the highest ATK by {%s2}.]],
[710524]=[[+2 additional Speed, and reduces all damage taken by an additional 1%.]],
[710525]=[[+2 additional Speed, and reduces all damage taken by an additional 1%.]],
[710526]=[[+2 additional Speed, and reduces all damage taken by an additional 1%.]],
[710527]=[[+2 additional Speed, and reduces all damage taken by an additional 1%.]],
[710528]=[[+2 additional Speed, and reduces all damage taken by an additional 1%.]],
[710529]=[[Safe Prelude]],
[710530]=[[HP & ATK & DEF +20%, Speed +40]],
[710601]=[[Dragon Warrior]],
[710602]='Chakiss',
[710603]=[[A mechanical Centaur that disperses enemy formations using the Courage Engine's acceleration.]],
[710604]=[[　　It's better to shine brighter than anyone else than to dwell on the past!
                                                          —Chakiss
　　Chakiss, a descendent of the ancient dragons, looks like a little girl on the outside, but she's actually been in this world for centuries.
　　After the Celestial Battle, Chakiss' Holy Dragon Clan migrated to a secluded location and built their own civilization, which they named Dracasbreth.
　　The animals in the forest told Chakiss how fascinating and interesting the outside world was, but when Chakiss asked her parents if she could go on an adventure, they objected adamantly. Chakiss couldn't understand why they saw such a magnificent place as "dangerous".
　　Despite her parents' repeated warnings, Chakiss' keen curiosity overcame her fear of the unknown, leading her to flee Dracasbreth. Chakiss was so excited when she saw the bustling streets, the wide array of goods, and so many novel things.
　　But the horns on her head and the tail behind her still exposed her identity.
　　Legend has it that dragons are powerful and mysterious, but after the Celestial Battle, this race has almost disappeared, and rumors abound. The appearance of Chakiss seemed to announce the return of dragons to this world!
　　After learning the news, the Void lurking on the continent became restless. Chakiss now faces impending peril...]],
[710605]=[[　　The quickest and most effective way to annoy Chakiss is to mention her age.
　　As a Dragonborn, she has a long lifespan. Knowing how much human girls care about their age, it has become a taboo question for her as well.
　　Chakiss is curious about places other than Dracasbreth, but some of her "curious" behavior is seen as "extremely destructive" by others. Chakiss does not follow the human code of conduct. In her opinion, these "rules" are annoying, and as a result, she has only a few friends.
　　The title of "Dragon Warrior" was first spread among human beings. It was rumored that someone had lured and trapped Chakiss, hoping to sell her for a fortune on the black market. But when talking about Chakiss' age, the remark "You'd never guess she's hundreds of years old!" set her off. She immediately beat the greedy black market merchant to a pulp. Everyone watched dumbstruck as Chakiss saw herself out without a care.
　　The title of "Dragon Warrior" has since spread, along with speculation about Chakiss' true age...]],
[710606]=[[Chakiss has few friends, and Dragonic is one among them. They get along fairly well despite their vastly different personalities.
　　The first time Chakiss met Dragonic, she was missing her family. Chakiss had been away from Dracasbreth for some time. The outside world was glorious, but she still felt lonely. She had no friends here, and people looked at her with either greed or fear, which frustrated her deeply.
　　Chakiss was leaning against a large tree in the forest when the sound of a fierce fight caught her attention.
　　"That scent is so familiar," was her first reaction.
　　Chakiss peered out from the bushes and saw a dragon-winged Beastkin covered in lightning and surrounded by the Void. The Void was nowhere near as powerful as the dragon, but the sheer number of them was still proving a bit challenging.
　　"Should I go help him?" Chakiss didn't know what to do!
　　She had known about the Void's strength for a while now, and had no idea why they wanted her so much. They even laid traps multiple times in an attempt to capture her. Would she expose herself if she stepped up now? Just as Chakiss made up her mind to help him, she realized that the Void surrounding him had all been wiped out and, after checking his surroundings one last time, the dragon-winged Beastkin collapsed...
　　Perhaps because they were both dragons, Chakiss was particularly enthusiastic about Dragonic. And Dragonic, whose heart had long grown as cold as ice, showed rare tenderness in the face of the clumsy but attentive Chakiss.
　　Dragonic answered many nonsensical questions from Chakiss, and Chakiss considered Dragonic as her family outside of Dracasbreth. The seeds of destiny began to germinate that day...]],
[710607]=[[Coming soon!]],
[710608]=[[Valiant Charge]],
[710609]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[710610]=[[Deals an additional 20% Physical DMG.]],
[710611]=[[Deals an additional 45% Physical DMG.]],
[710612]=[[Deals an additional 70% Physical DMG.]],
[710613]=[[Deals an additional 100% Physical DMG.]],
[710614]=[[Deals an additional 150% Physical DMG.]],
[710615]=[[Valiant Counterattack]],
[710616]=[[Deals {%s1} Physical DMG to all enemies in a row and reduces their ATK by 6% for 1 turn.]],
[710617]=[[Deals an additional 20% Physical DMG.]],
[710618]=[[Deals an additional 45% Physical DMG.]],
[710619]=[[Deals an additional 70% Physical DMG.]],
[710620]=[[Deals an additional 100% Physical DMG.]],
[710621]=[[Deals an additional 150% Physical DMG.]],
[710622]=[[Shield Shock]],
[710623]=[[During battle, reduces all monster damage you take by {%s1}.]],
[710624]=[[Reduces damage taken from monsters by an additional 4%.]],
[710625]=[[Reduces damage taken from monsters by an additional 4%.]],
[710626]=[[Reduces damage taken from monsters by an additional 4%.]],
[710627]=[[Reduces damage taken from monsters by an additional 4%.]],
[710628]=[[Reduces damage taken from monsters by an additional 4%.]],
[710629]=[[Draconic Bloodline]],
[710630]=[[HP & ATK & DEF +10%]],
[710701]=[[Spore Warrior]],
[710702]='Edric',
[710703]=[[An expert martial artist who specializes in Tai Chi.]],
[710704]=[[<color=#FFFFFF00>aaaa</color>The Spore People live and thrive in a swamp near Yggdrasil. The swamp is also home to spore plants of all shapes and sizes, many of which release a poison to attack any living beings passing by that only Spore People can cure. Thus, the peace-loving Spore People formed a platoon of guards to help poisoned travelers. Edric is the most outstanding Spore Warrior in the platoon, and he'll even use the Sacred Ganoderma growing from his own body to make an antidote for lucky travelers. As Edric always says, "I'm just a Spore Person who can make the spore antidote."]],
[710705]=[[<color=#FFFFFF00>aaaa</color>Edric is easygoing by nature. He planned to carry various spore plants on him and live out a peaceful life in the swamp. However, dreams rarely match up to reality. Following the appearance of Abyss City, danger began to encroach upon the swamp. Edric had no choice but to train even harder. Once he became stronger, the Sacred Ganoderma growing on him developed more curious effects as well.]],
[710706]=[[<color=#FFFFFF00>aaaa</color>Once when fighting an alien, Edric accidentally bumped into a branch that unexpectedly took root in his head. Edric then snapped off a portion of it to use as a weapon. The bird living on his head is named Popo. Edric saved it from the swamp. Since then, they've been inseparable.]],
[710707]=[[Coming soon!]],
[710708]=[[Savoir Faire]],
[710709]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[710710]=[[Deals an additional 20% Physical DMG.]],
[710711]=[[Deals an additional 45% Physical DMG.]],
[710712]=[[Deals an additional 70% Physical DMG.]],
[710713]=[[Deals an additional 100% Physical DMG.]],
[710714]=[[Deals an additional 150% Physical DMG.]],
[710715]='Agility',
[710716]=[[Reduces all monster damage you take by {%s1} for 1 turn.]],
[710717]=[[Reduces damage taken from monsters by an additional 4%.]],
[710718]=[[Reduces damage taken from monsters by an additional 4%.]],
[710719]=[[Reduces damage taken from monsters by an additional 4%.]],
[710720]=[[Duration extended to 2 turns.]],
[710721]=[[Reduces damage taken from monsters by an additional 4%.]],
[710722]='Longevity',
[710723]=[[During battle, increases your DEF by {%s1}.]],
[710724]=[[Increases DEF by an additional 10%.]],
[710725]=[[Increases DEF by an additional 10%.]],
[710726]=[[Increases DEF by an additional 10%.]],
[710727]=[[Increases DEF by an additional 10%.]],
[710728]=[[Increases DEF by an additional 10%.]],
[710729]='Strengthen',
[710730]=[[HP & ATK & DEF +10%]],
[710801]=[[Soul Ferrier]],
[710802]='Crystal',
[710803]=[[A maiden who guides lost souls and leads them to their resting place.]],
[710804]=[[Crystal has lived with her clan in the vast mountains since childhood. Despite their Beastkin bloodline, they have little special powers besides a strong physique. As a result, they wandered the remote and barren mountains uninhabited by other clans, making a peaceful life gathering plants and herding animals.
But Crystal had always kept a secret to herself, fearing that her kinsmen would shun her were it ever come to light. Every night, as she sat by the bonfire under the starry sky, she could see many green shadows floating in the air. She didn't know what they were, but as she grew older, the shadows became clearer, and some even began to interact with her. Sometimes, they would swing back and forth over her head, while other times, they would rest on her shoulders.
It was not until she saw the same shadow floating out of a dying marmot that she realized they were all wandering animal souls. Crystal had no idea why they came to her, but she had a feeling that they meant no harm.
From that day on, Crystal began to explore the areas around her home after her daily tasks. Though there seemed to be nothing but endless ridges and valleys around, she had a feeling that one day, she would uncover the secrets of these shadows.]],
[710805]=[[As the dust-covered moss on the ruins was pushed aside, Crystal finally learned the truth. It turned out that her clan did have a special power of their own, but it had been lost at some point in history.
Known as the Orvis Amons, her ancestors were once considered the pinnacle of a nomadic and pastoral clan at the height of their glory. Legend has it that they guided every stray animal back to their herd and led them in search of the most fertile land in the vast mountains and grasslands. Heralding homecoming and peace, this Beastkin clan acted as guides for all animals.
A deeper story revealed that the Orvis Amons served as guides not just for the living but also for the souls of the departed. Influenced by their own emotions, the energy in the air, and Yggdrasil, many departed souls could find their way back, doomed to become wanderers in their deaths. The clan's soul ferriers would have these souls find their way back home.
To become a soul ferrier, one must first be able to see the souls, which was an extremely rare ability even amongst the Orvis Amon.
The second thing one needed was a Soul Lamp, which enhances the power of soul ferriers and can be used to store wandering souls.
Feeling the warmth emanating from the Soul Lamp, Crystal understood her mission but was also beset by confusion and contemplation: this "way back"... just what was it?]],
[710806]=[[In fact, this enigmatic "way back" was actually "letting go of regret". Though animals may not be as intelligent as humans or elves, they can also be trapped by a singular regret.
When Crystal placed a grass cake next to the marmot's soul, she could feel that even though it couldn't eat the cake, its soul was filled with joy. The originally deep, dark green soul gradually grew lighter and lighter, then eventually disappeared in joy.
Crystal knew that her first experience as a soul ferrier had been successful, and the marmot's soul was on its way back. But not all regrets were as simple as "not eating a grass cake". Another creature was a small deer from the distant Yggdrasil. When the Void attacked, it fled to these lands to seek refuge but eventually died of hunger and thirst. Its dying wish was to return to its homeland.
And so, Crystal embarked on an arduous journey to help it return home. As she traveled across the world, the number of souls stored in her lamp only grew. Crystal had no choice... she would just have to fulfill their wishes one by one.
The Soul Lamp had another feature... the more souls it stored, the more powerful its magic became. Though the journey was full of dangers, Crystal managed to take care of them all. She gradually earned herself a reputation and the moniker of "Lamp Mage".
The Lamp Mage had a mysterious protective power that could weaken any magic that came near her.
None knew that this aura was a barrier of grateful wandering souls who kept her safe as she guided them on the way back.]],
[710807]=[[Coming soon!]],
[710808]=[[Ferry Soul]],
[710809]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[710810]=[[Deals an additional 20% Physical DMG.]],
[710811]=[[Deals an additional 45% Physical DMG.]],
[710812]=[[Deals an additional 70% Physical DMG.]],
[710813]=[[Deals an additional 100% Physical DMG.]],
[710814]=[[Deals an additional 150% Physical DMG.]],
[710815]=[[Shadowy Soul]],
[710816]=[[Deals {%s1} of ATK as Physical DMG to a single enemy; if the target is killed, the skill is cast again but deals 50% DMG.]],
[710817]=[[Deals an additional 20% Physical DMG.]],
[710818]=[[Deals an additional 45% Physical DMG.]],
[710819]=[[Deals an additional 70% Physical DMG.]],
[710820]=[[Deals an additional 100% Physical DMG.]],
[710821]=[[Deals an additional 150% Physical DMG.]],
[710822]=[[Soul Protection]],
[710823]=[[During battle, increases all damage your back-row Forest heroes deal to monsters by {%s1}.]],
[710824]=[[Increases damage dealt to monsters by an additional 2%.]],
[710825]=[[Increases damage dealt to monsters by an additional 2%.]],
[710826]=[[Increases damage dealt to monsters by an additional 2%.]],
[710827]=[[Increases damage dealt to monsters by an additional 2%.]],
[710828]=[[Increases damage dealt to monsters by an additional 2%.]],
[710829]=[[Guiding Lamp]],
[710830]=[[HP & ATK & DEF +10%]],
[710901]=[[Electric Boxer]],
[710902]='Ali',
[710903]=[[A hero with numerous clones who mocks justice in a chaotic world.]],
[710904]=[[　　In the eastern Varut Desert of Kotchiwi, the yellow sand drifted in all directions, and the scorching sun seared everything it gazed down upon.
　　"I'll end up dry as jerky at this rate! Can't you slow down?" Two tall figures trudged through the desert in single file.
　　"Turn around and take a look. Not only can Varut's heat dry you up like leather," the Camel Merchant in front said, gradually increasing his pace, "its lightning can fry you to a crisp, and the sandstorms can blow you clear across the desert..." His voice faded rapidly as his footsteps accelerated.
　　The figure behind stopped in confusion and turned to look back, only to see a massive sandstorm sweeping toward them, with lightning flashing at its center. He didn't even want to imagine what would happen if he were caught in it.
　　"Lotta! Wait for me! Help me unload some cargo! I can't run anymore!" The Camel Merchant, burdened with a huge cargo box, panicked as the goods scattered over the sandy ground. The sandstorm was rapidly approaching. "I guess this is my fate as a Camel Merchant, to perish along with my goods and camel!" He lay flat on the scorching sand, resigned to his fate.
　　"Let me help!" A young and vibrant voice suddenly resounded. Before the Camel Merchant could even react, a powerful force had already lifted him from the ground.
　　Camel Merchant Lobah had never imagined that he could run so fast, or rather, that he could be pulled so fast. It was as if the camel were running in front while his soul was chasing from behind! When he finally came to his senses, he found himself standing at the edge of the desert, far away from the approaching sandstorm.
　　"Wait! My goods!" Lobah exclaimed as he looked back at the raging sandstorm in the desert, certain that it had scattered and smashed his precious goods by now.
　　"Your things are all right here! I picked up everything I could find!" The bright voice rang out again, and Lobah suddenly remembered that he was only standing there safe and sound thanks to this Madrat youth who had come to his rescue.
　　"You're truly my hero! How can I ever thank you?" Lobah exclaimed with tears of gratitude in his eyes, knowing that he had narrowly escaped disaster.
　　"No need to thank me, it's only a sandstorm! I'm Ali! My training for today isn't over yet, so I'd better get going!" As the strong and cheerful voice echoed in Lobah's ears, the Madrat youth named Ali vanished into the sandstorm.
　　"The legend of the Madrat Clan possessing the Power of Lightning is true! I'll be sure to thank you properly when we meet again!" Lobah watched as the young man vanished into the sandstorm, his heart brimming with so much excitement and gratitude that he was unable to calm down for a while.]],
[710905]=[[　　The Madrat Clan resides in the heart of the Varut Desert, where sandstorms rage and the temperature changes dramatically between day and night. Other tribes often avoid this region due to frequent lightning strikes. However, the Madrat Clan reveres this harsh environment. Obsessed with practicing martial arts, they consider the desert a blessed place.
　　"The more challenging the environment, the tougher the martial artist will become!" The Madrat Clan firmly believes in this principle.
　　Ali was born into the most prestigious martial arts family within the clan, inheriting the exceptional fighting talents of his parents. Even in his early childhood, he displayed astonishing explosive power. While other Madrat Clan fledglings still cried while hitting sandbags as practice, Ali could already run with one slung over his shoulders.
　　For generations, a legend has been passed down in the clan stating that the most formidable fighter will acquire the Power of Lightning, unparalleled speed, indomitable will, and a body impervious to lightning. Every year, the clan's martial artists gather at the temple to undergo the Lightning Trial, which grants those who pass recognition from the temple and bestows upon them the Power of Lightning.
　　"I can hardly think of anyone more deserving of the title of the strongest martial artist than myself!" Ali lifted his chin and eyed his fellow martial artists standing with him in front of the temple. "Those younger than me couldn't keep up, and I've already surpassed those older than me. The Camel Merchant has already spread word of my heroic deeds far and wide. There's no doubt—I'm the strongest!"
　　Ali pushed past the martial artists ahead of him and rushed into the temple to undergo the trial. It didn't take long before Ali, crackling with electric energy, returned before everyone's eyes.
　　After obtaining the Power of Lightning, Ali became even more arrogant and conceited. He would often disrupt the training and cultivation of other martial artists just to show off his power.
　　The Void set its sights on the Madrat Clan, believing it could lure these martial artists into darkness and manipulate them for its own purposes. The day they would release their "Master" from the seal would finally be on the horizon.
　　Although the Madrat Clan wouldn't involve itself in any conflicts, they would never compromise with darkness. The Thunderbreak Battle between the Void and the Madrat Clan would ravage the entire desert and leave it devastated by anguish, despair, and raging sandstorms.]],
[710906]=[[　　The fierce "Thunderbreak Battle" in the Varut Desert lasted three years. While the nascent army of the Void was eventually driven out of the yellow sands, the Madrat Clan lost nearly half of its members to the fighting.
　　"If you were truly powerful, the war would have ended long ago! Our clan wouldn't have had to suffer this much!"
　　"You're a coward, Ali! You're not worthy of the Power of Lightning!"
　　"You're a shame to our clan, Ali!"
　　When standing on the battlefield, Ali had not once unleashed the power he took such pride in. Faced with the emergent yet daunting Void, he hesitated to strike as he felt their evil storm enveloping him, fear weighing down on every muscle in his body.
　　"I am the most unworthy coward to ever become the strongest fighter." Ali looked at the temple, which now lay in ruins, and was filled with regret as the angry voices of his clan members echoed incessantly in his mind.
　　"Bam!" Ali punched at the stone door that had once opened to him, leaving only a shallow mark and a muffled sound. He realized he couldn't even break the damaged stone door now, so what hope did he have of defeating the enemy that could return at any moment?
　　Under the indifferent moonlight shining upon the gravel, Ali decided to leave his clan in the cold desert night and embark on a path of self-redemption.
　　"Ali, no one is born a true powerhouse. Only when you can confront your own strength head-on can you truly become powerful. I hope that by the time you return, you will have come to understand this." Ali's mother watched her son's desolate figure vanish over the desert horizon.
　　Over the following years, Ali traveled to many places. The brutal realities of war progressively wore away his ego and conceit, and he lent a warm, friendly hand to every person in need he encountered along the way.
　　Ali spent several months as a combat instructor in the Geryon territory. It was during this time that he met Torun, the Minotaur. Ali had heard of Torun's enormous axe, but he now found Torun empty-handed.
　　"Torun, your tribe obeys and fears you, so why did you leave the volcano?" Ali asked Torun as they stood on a hill, facing the setting sun.
　　"My opponents say I'm brainless. Besides, I don't want to just keep being some brute with an axe."
　　"Have you found a way to transcend yourself?"
　　"No."
　　"Me neither." Their aimless conversation ended as dawn broke the next day. The two men, both in search of answers within themselves, eventually formed a bond.
　　"Ali, I'm leaving. I've met a special kid who may be able help me find the answers I seek."
　　"She must be an interesting one if she's earned your recognition. It's time for me to leave as well. Staying in the same place won't allow me to see myself clearly."
　　Ali and Torun set off on their journey together, and in an unnoticed corner of Kotchiwi, a new chapter of the legend quietly took flight on the wind.]],
[710907]=[[Coming soon!]],
[710908]=[[Electric Punch]],
[710909]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[710910]=[[Deals an additional 20% Physical DMG.]],
[710911]=[[Deals an additional 45% Physical DMG.]],
[710912]=[[Deals an additional 70% Physical DMG.]],
[710913]=[[Deals an additional 100% Physical DMG.]],
[710914]=[[Deals an additional 150% Physical DMG.]],
[710915]=[[Piercing Fists]],
[710916]=[[Performs 4 attacks, each dealing {%s1} physical damage to a random enemy unit.]],
[710917]=[[Deals an additional 20% Physical DMG.]],
[710918]=[[Attack limit increased to 6 times.]],
[710919]=[[Deals an additional 45% Physical DMG.]],
[710920]=[[Attack limit increased to 8 times.]],
[710921]=[[Deals an additional 70% Physical DMG.]],
[710922]=[[Unstoppable War Drive]],
[710923]=[[During battle, increases your ATK by {%s1}.]],
[710924]=[[Increases ATK by an additional 3%.]],
[710925]=[[Increases ATK by an additional 3%.]],
[710926]=[[Increases ATK by an additional 3%.]],
[710927]=[[Increases ATK by an additional 3%.]],
[710928]=[[Increases ATK by an additional 3%.]],
[710929]=[[Unending Trials]],
[710930]=[[HP & ATK & DEF +10%]],
[711001]=[[Merciful Tree Spirit]],
[711002]='Faerie',
[711003]=[[A journalist with the ability to manipulate the wind and rain.]],
[711004]=[[The cold, silvery moonlight dappled the ground through the gaps between the branches and leaves. Fluorescent light hovered lazily over the shrubs.

"Zzzz..."—The snoring sound of the giant Tree Spirits would often break the peace and startle clusters of fluorescent light into scurrying about as Faerie enjoyed the night in the Hazy Forest alone.

"If you were still here, the moonlight would seem warmer." Faerie closed her eyes, "I wonder if I'll see you in my dreams tonight, Sylry?"

"Lady Faerie is the daughter of the Forest Demigod, so why haven't I ever seen her use guardian magic?" The patrolling young Tree Spirit assigned to keep vigil asked in a whisper.

"Lady Faerie's guardian magic has only been used once, but it failed and she lost her sister, Lady Sylry. Lady Faerie now lives just like her sister did..." The older patrolling Tree Spirit lamented, and the young Tree Spirit looked at Canopy Hall and wondered what the gentle Lady Faerie had been through.

The night deepened, and fog shrouded the moon.

"Lady Sylry! The Chaos Legion of the Void has entered the forest. They will soon reach the main hall." The Tree Spirit, who looked almost identical to Faerie, stood in front of Canopy Hall. As the frenetic Aura of Chaos continued to rush in, it was clear that the Guardian Shield couldn't hold up for much longer.

"Sister, retreat before they breach the Guardian Shield. I'll buy us time!" Faerie didn't wait for a response. She leaped down the main hall and rushed toward the cracking Guardian Shield.

"Faerie! The Void is not something you can face alone!" Her sister's voice was getting farther and farther away, and when Faerie tried turning back, she was dragged into the darkness by an invisible hand behind her.

Faerie woke up from her dream. The war was a lingering nightmare to her for a very long time.]],
[711005]=[[Sylry and Faerie were twin daughters of the Forest Demigod, Tree Spirits born of an Ancient Tree Spirit and a demigod. They were supposed to possess regenerative powers and the powerful guardian magic that could match the Phoenix Clan.

However, the demigod had been weakened by Asgard's punishment before giving birth to them. The newborn Sylry and Faerie were as weak as mayflies, but the Tree Spirit power in their bodies gradually awakened as the years went by, and Sylry and Faerie acquired their unique abilities. They inherited the will of the demigod and guarded the Hazy Forest.

In the Hazy Forest, Tree Spirits live in symbiosis with humans.

It was an autumn afternoon when Faerie encountered a vicious Wolf Spirit in the forest. At that time, Faerie was powerless to resist and was about to be devoured by the Wolf Spirit when a figure stepped in front of her. It was an ordinary human without even a weapon.

"Run!" His voice sounded calm, and Faerie had no time to think as she staggered away to escape. Later, Faerie went searching in the human villages, but she never found him again.

"Sister, my guardian magic is weak in the face of fear, I'm too weak to protect anyone."

After autumn, Faerie no longer practiced guardian magic with Sylry. She replaced the shield in her hand with a sharp blade, which she swung when the thistles and thorns came.

Faerie grew up quickly, and no one in the Hazy Forest was a match for her. She finally had the power to be strong without relying on anyone, including Sylry.

The Hazy Forest was pulled into a war ever since the appearance of the Void, an evil force from an unknown world. Faerie would often fight alone. She only felt truly powerful when she had no armor or weaknesses.]],
[711006]=[[Even if the future is an endless cold winter, as long as there is light in your heart, you will eventually find yourself in spring.
—Faerie

For as long as Faerie could remember, she saw her sister's figure in every moment of her life.

Everyone in the Hazy Forest trusted Sylry, the gentle Tree Spirit, while her powerful sister kept people at bay.

The invasion by the Void was getting more chaotic, and Faerie still insisted on fighting alone.

"Why can't we trust our companions? We can rely on each other and keep each other safe." Sylry looked at Faerie's wounds, which multiplied with each passing day.
"I am strong enough. Fighting alone is my way." Faerie was unwilling to listen to her sister anymore.

The Void sent more and more forces to take over the Hazy Forest, and the bitterly cold winter day seemed even colder. The Guardian Shield that Sylry wrapped around the entire forest had cracked, and Faerie rushed alone again, despite her sister's advice, to the entrance where the Void legions were pouring in.

"Hold on, just hold on a little longer. I know I can destroy them." Faerie's eyes had long since glazed over and her body was covered with wounds. The moment she collapsed, a warm light shined down on her—it was her sister and her companions from the Hazy Forest.

There were too many enemies, and when the war ended, the Hazy Forest fell into an unprecedented deathly silence.

Sylry and more than half of her companions lost their lives in the Celestial Battle. Faerie spent the centuries-long harsh winter in grief, and she wondered if spring would ever come again.

"Ors! Get behind me!" Faerie, patrolling the forest alone, heard a firm yet childish voice ahead, "Don't be afraid! I'm here!" Faerie quickly locked onto the direction of the voice and saw two human children being attacked by Wolf Spirits.

Faerie turned away after fighting off the wolves. "That Tree Spirit is so strong. I'll get stronger so I can protect you too, sis." The little voice was getting farther and farther away, and the light that had been dormant in Faerie's heart for a long time reilluminated her world.

From then on, Faerie also became the gentle and powerful Tree Spirit of legend.]],
[711007]=[[Coming soon!]],
[711008]=[[Nature's Awakening]],
[711009]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[711010]=[[Deals an additional 20% Physical DMG.]],
[711011]=[[Deals an additional 45% Physical DMG.]],
[711012]=[[Deals an additional 70% Physical DMG.]],
[711013]=[[Deals an additional 100% Physical DMG.]],
[711014]=[[Deals an additional 150% Physical DMG.]],
[711015]=[[Nature's Punishment]],
[711016]=[[Deals {%s1} of ATK as Physical DMG to 2 random enemy units.]],
[711017]=[[Deals an additional 20% Physical DMG.]],
[711018]=[[Deals an additional 45% Physical DMG.]],
[711019]=[[Deals an additional 70% Physical DMG.]],
[711020]=[[Deals an additional 100% Physical DMG.]],
[711021]=[[Deals an additional 150% Physical DMG.]],
[711022]=[[Unending Life]],
[711023]=[[During battle, increases your ATK by {%s1}.]],
[711024]=[[Increases ATK by an additional 3%.]],
[711025]=[[Increases ATK by an additional 3%.]],
[711026]=[[Increases ATK by an additional 3%.]],
[711027]=[[Increases ATK by an additional 3%.]],
[711028]=[[Increases ATK by an additional 3%.]],
[711029]=[[Primal Realm]],
[711030]=[[HP & ATK & DEF +10%]],
[711101]=[[Starlit Princess]],
[711102]='Verna',
[711103]=[[A princess of the Star Realm who studies at the Sage's Academy. She's skilled in the starlight magic, which allows her to communicate with the stars. During one of her spells, she heard the "cry for help" from the stars.]],
[711104]=[[It had been five long years since Verna was sent to the Sage's Academy to study the magic of the stars. Five years without a single trip back to the Star Realm. Sometimes, it almost felt like the world had forgotten her—the daughter of the king, the Violet Princess herself. Well... perhaps that wasn't entirely fair. Life at the Academy was nothing if not overwhelming. The professors were brilliant and brutally demanding, their assignments designed to push every student to the brink. Verna barely had time to breathe, let alone feel homesick.
"Remarkable talent! Then again... what else would you expect from a Syngid?"
Her mentor, the Grand Mage Cindrilla, never missed a chance to offer praise with just a touch of pride. The Syngids were the royal family of the Star Realm, and Verna had always taken quiet pride in carrying that name.
It was said that long ago, an ancestor of the Syngid family once saved a fallen star and became a friend to the night sky. In gratitude, the star bestowed a rare gift upon the Syngid bloodline—the power to speak with the stars, to share in their ancient wisdom, and to glimpse the tides of fate.
Verna, for her part, had never been sure how much of that was true. She certainly hadn't held any heart-to-heart chats with the cosmos. The closest she'd come was being exceptionally gifted in stellar magic, and even that felt more like talent than destiny.
To her, the tale was just another myth, stretched and embroidered by time.
That is, until that night. She stood, as always, on the highest floor of the Academy's observatory, gazing into the endless sprawl of stars above. Then—
"Please... help me... help us..."
Who's there?
Verna froze.]],
[711105]=[[Verna had heard the stars calling. It wasn't a voice, not exactly—but a surge of feeling, vast and urgent, wrapped in a pulse of raw energy. The feeling rushed in all at once—wild and overwhelming—stirring the magic in her blood until it nearly slipped loose from her grip. Perhaps the ancient pact her ancestors made with the stars had long begun to fade. Even as she pushed her magic to its very limits, Verna could only catch fragments—scattered whispers slipping through the ether.
"Star Realm... night... dragon... king..." And then—"Void."
Only pieces came through—and none of them quite made sense. But that one—"Void"—sent a chill crawling through her veins, a cold dread that gripped her spine. She wasted no time. Verna sought out her mentor, requested leave, and began her journey home—for the first time in five years. All along the way, she tried to reach out to the stars again, to find some lingering thread of connection. But silence was all that answered.
Just to be safe, Verna told no one of her return to the realm. Wrapped in a deep violet cloak that hid her beauty, she slipped into the royal city like a shadow. She didn't seek out the king. Instead, she found a quiet inn and watched. Waited. Searched for change.
There was none.
Days passed in stillness. And on a windless night, as starlight rained down across the rooftops, it came. A roar—high and mournful—ripped through the silence. The cry of a dragon.]],
[711106]=[[If fate hadn't put Verna in the royal city that night, the entire city—perhaps the entire kingdom—might have been lost.
The Void, a force born from the cosmic stars themselves, had always shared a mysterious kinship with the stars. Yet after arriving in this world, it was stunned to discover that this bond had nearly vanished. The reason? Star Realm. The kingdom's very existence disrupted the Void's natural influence.
What began as a mission to wipe out the Star Realm soon twisted into something darker: a scheme to use the kingdom to siphon power from the stars themselves.
Slowly, the Void crept into the kingdom that revered the stars, poisoning it from within. Corruption and whispers took root, subtle but relentless. Things began to change—quietly, insidiously.
The first to notice was the King himself. But by then, it was already too late. When he tried to awaken the Starborn Dragon, the ancient guardian slumbering beneath the palace, he found it had already been tainted. The cry it released was not one of fury—but grief.
The Void made its move. Possessing the dragon, it rose into the night sky and began its ritual: a dance that drew power from the constellations above. Only one thing could stop it—the royal starmagic array, a relic that responded solely to the blood of the royal line. But the Void had accounted for it. The King was already in the dragon's grasp.
And his only daughter, Verna the Violet Princess, was far away at the Sage's Academy...
No. I'm here.
In the end, it was Verna who activated the starmagic array. Her power halted the corrupted dragon, forcing the Void to retreat—especially as human reinforcements raced to the royal city.
The city roared with cheers. But Verna did not smile. Her father had been taken by the Void. Worse still, although she had studied the Void at the Academy, memorized the profiles of Void Lord, Blade of the Void, and others, this new foe matched none of those records. A new threat.
And so, Verna realized: her homecoming was far from over.]],
[711107]=[[Coming soon!]],
[711108]=[[Dance of the Stars]],
[711109]=[[Deals {%s1} of ATK as Physical DMG to 2 random enemy units.]],
[711110]=[[Deals an additional 20% Physical DMG.]],
[711111]=[[Deals an additional 45% Physical DMG.]],
[711112]=[[Deals an additional 70% Physical DMG.]],
[711113]=[[Deals an additional 100% Physical DMG.]],
[711114]=[[Deals an additional 150% Physical DMG.]],
[711115]=[[Whispers of the Stars]],
[711116]=[[Deals {%s1} Physical DMG to a random enemy target; if the target is a monster, increases all damage it takes by 12% for 2 turns.]],
[711117]=[[Deals an additional 20% Physical DMG.]],
[711118]=[[Deals an additional 45% Physical DMG.]],
[711119]=[[Deals an additional 70% Physical DMG.]],
[711120]=[[Deals an additional 100% Physical DMG.]],
[711121]=[[Deals an additional 150% Physical DMG.]],
[711122]=[[Astral Guardian]],
[711123]=[[During battle, increases all damage you deal to monsters by {%s1}. Your team gains {%s2} more Food, Iron Mine, and Gold after defeating normal or elite monsters.]],
[711124]=[[Increases damage dealt to monsters by an additional 2%; increases resource acquisition by an additional 5%.]],
[711125]=[[Increases damage dealt to monsters by an additional 2%; increases resource acquisition by an additional 5%.]],
[711126]=[[Increases damage dealt to monsters by an additional 2%; increases resource acquisition by an additional 5%.]],
[711127]=[[Increases damage dealt to monsters by an additional 2%; increases resource acquisition by an additional 5%.]],
[711128]=[[Increases damage dealt to monsters by an additional 2%; increases resource acquisition by an additional 5%.]],
[711129]=[[Star Sigil Protection]],
[711130]=[[HP & ATK & DEF +10%]],
[711201]=[[Berserk Axe]],
[711202]='Torun',
[711203]=[[The Deep Sea Ironclad grows stronger by devouring metal and dimensional energy.]],
[711204]=[[　　"This volcano is getting scarier and scarier. A few days ago, the crack on the eastern side suddenly burst. Hey, watch it! That's my right foot!"
　　"Get your dirty hoof off me. You just got splashed with some hot soup, nothing to squeal like a calf about."
　　Torun watched expressionlessly as the Minotaur barbarians tackled each other after just two sentences. He shifted slightly to the side and readjusted his position, tightening his grip around his huge iron axe. If he didn't need to occasionally lift his feet for respite from the burning heat, he would have looked like a statue standing guard.
　　"The volcano will swallow up our tribe sooner or later," Torun said, looking at the erupting magma not far away, "but we'll probably get trampled by the other tribes first." When they heard that a fight had broken out in front of the chieftain's precious cave, a crowd of Minotaurs ran over to watch and cheer—another one of their traditions.
　　The Minotaur tribe lives in the most inhospitable part of the Kotchiwi, where the Crickert twin volcanoes are located. Life has largely remained unchanged there for tens of thousands of years. Various groups were drawn to the abundant mineral resources and vied for control of them, until eventually, the Minotaurs and the Centaurs settled and divided the area.
　　However, after an earthquake, the long-dormant volcanoes gradually began stirring again. Food grew scarce, and conflicts between the two groups of bulk feeders escalated.
　　The new Minotaur chieftain was hot-headed and would resort to violence whenever there was a shortage of food. When the food stores were almost empty, he would summon his barbarian army to search the surrounding area. Torun couldn't say for sure if there was a better way, but he knew that it was definitely not a long-term solution.
　　During a mountain patrol, Torun spotted Centaurs in the distance trading precious stones endemic to the volcano for goods from other tribes. When Torun returned to the army camp in the middle of the night, he told the other Minotaurs what he had seen.
　　"How could they trade away the chieftain's precious stones? We're strong enough to protect his treasures, and he'll guide the direction of our fists!"
　　These irrefutable words were burned into Torun's heart with the heat of the volcano.
　　He stood up and, gripping the huge battle axe that no other in the tribe could lift, walked towards the chieftain's cave in the light of the moon and volcanic flames.]],
[711205]=[[　　The only night no one in the Minotaur tribe slept was the night Torun defeated the chieftain.
　　"That night, when the chieftain yelled that he was willing to be exiled, one of the horns on his head was still in Torun's hand."
　　"The chieftain lost to Torun, so Torun should be our new chieftain."
　　"He said he didn't want to be the chieftain." "Then why did he fight the chieftain and even force him to guard the treasure in the cave?" "I can't understand it. You know we Minotaurs are better at using our muscles than our heads."
　　The tribe members weren't the only ones that were confused—not even Torun was sure what to do next. He only knew their "trading fists" approach would render the once dominant force in Kotchiwi useless and vulnerable.
　　Torun and his tribe sat on their hands like this for two days. On the third day, the Centaurs, taking advantage of the upheaval, launched an attack. With no chieftain to command them, the Minotaurs, who only had brute strength at their disposal, were no match for the Centaurs. Seeing defeat was inevitable, Torun finally burst out, "Enough! We surrender!" At this moment, Torun stood with the imposing air of a chieftain.
　　The Centaur chieftain captured all the leaderless Minotaurs near the volcano, with their heads lowered and only Torun standing tall, facing the Centaur chieftain.
　　"You have courage and great strength, but no brains, unfortunately." Torun just stared into the Centaur chieftain's eyes, seemingly not listening. "I heard that few in Kotchiwi can wield your huge axe. Why not melt it down to make several smaller ones?"
　　Torun's battle axe was then thrown into the iron furnace before him.
　　However, the Centaurs choose not to occupy the Minotaurs' territory. The environment in their territory was already harsh enough—the Centaurs didn't stand to benefit from occupying a tribe in a similarly inhospitable place. It was better to use the Minotaur's power to mine more rare ores while the volcano could still be excavated, then trade them with other tribes.
　　This was the agreement reached between Torun and the Centaur chieftain. Some tribes beyond the volcano had already been infiltrated by the Nightfall, so this was the most suitable arrangement for their own tribes.
　　Watching his tribe members gradually adjust to life without a chieftain, Torun decided to leave. "Which is more important, strength or wisdom?" Torun hoped someone could give him an answer. He also needed to go to further afield to find a new weapon.]],
[711206]=[[　　Leaving the Crickert volcanic zone, Torun wandered alone across the vast Kotchiwi continent, his feet finally no longer numb from the heat.
　　He crossed the Gogori Valley, frightening many human children and hearing tunes hummed by human bards. He then entered the territory of the Wing Clan, where he helped them move giant stones for a few days. At last, he passed by the dragon caves and was dazzled by the treasures hidden in there... but he still had not found the answer to his question about whether strength or intelligence is more important.
　　Torun arrived at the Geryon's territory, and even with the rough, hard skin, huge horns, and muscular physique that made up Torun's imposing presence, he seemed diminutive among them. Perhaps he could find a suitable weapon here.
　　Most Geryon were relatively simple and kind, and a group of young Geryon followed Torun around for several days. They wanted to hear stories from the Crickert volcanic zone, but Torun, not being a skilled storyteller, ended up sharing his vexing question with them instead.
　　"I think being smarter is more important. Humans are physically weak, but they've put up a good fight against the Nightfall!"
　　"If you're really strong, you can beat your enemies faster! But using your brain takes time!"
　　Several young Geryon started arguing, and Torun listened quietly, feeling that they were making sense.
　　Suddenly, Torun felt something strike his head—it was a stone with a rolled-up piece of paper wrapped around it. Torun removed the paper and unrolled it. On it were big, crooked words that read, "You're awful at telling stories, you big, horned cow! Only an idiot wouldn't get such a simple question!" There was a signature in the lower right corner—a small drawing of a lamb's head.
　　Torun stood up and walked around for a while until he saw a wooden stick supporting a large boulder not far away. Was the stone that hit him launched from there? During his travels, Torun had seen some small-statured tribes using simple tools to borrow strength.
　　If you lack strength, use your intelligence! Torun seemed to have found some answers to his question.
　　"That clever runt shouldn't be able to walk too fast, right? I really want to see him." Torun had completely forgotten about finding a suitable weapon in the Geryon's territory. He hastily took his leave, hoping to catch up with his answer soon.]],
[711207]=[[Coming soon!]],
[711208]=[[Berserk Axe]],
[711209]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[711210]=[[Deals an additional 15% Physical DMG.]],
[711211]=[[Deals an additional 30% Physical DMG.]],
[711212]=[[Deals an additional 50% Physical DMG.]],
[711213]=[[Deals an additional 75% Physical DMG.]],
[711214]=[[Deals an additional 100% Physical DMG.]],
[711215]=[[Rising Wrath]],
[711216]=[[Deals {%s1} Physical DMG to 2 enemy units.]],
[711217]=[[Deals an additional 15% Physical DMG.]],
[711218]=[[Increases the number of targets to 3.]],
[711219]=[[Deals an additional 30% Physical DMG.]],
[711220]=[[Deals an additional 50% Physical DMG.]],
[711221]=[[Deals an additional 75% Physical DMG.]],
[711222]=[[Triumphant Howl]],
[711223]=[[HP & ATK & DEF +20%]],
[711224]=[[Effect increased by an additional {%s1}%.]],
[711225]=[[Effect increased by an additional {%s1}%.]],
[711226]=[[Effect increased by an additional {%s1}%.]],
[711227]=[[Effect increased by an additional {%s1}%.]],
[711228]=[[Effect increased by an additional {%s1}%.]],
[711301]=[[Garlic Samurai]],
[711302]='Moga',
[711303]=[[A Cat of Revenge who wields a Golden Pistol.]],
[711304]=[[<color=#FFFFFF00>aaaa</color>Garlic Samurai Moga was a sprite who just liked to wander about. He'd often appear among the different sprite clans. Moga loved talking to others, but due to his short stature, he'd be ignored whenever he tried to join a conversation.]],
[711305]=[[<color=#FFFFFF00>aaaa</color>Moga didn't like the feeling of being ignored, so he cultivated a loud voice. With his raised voice, Moga was at last able to join in the daily chatter of the other elves. Easily amused, just about anything could make Moga laugh. Many sprites were astonished at how loud this little bulb of garlic could be.]],
[711306]=[[<color=#FFFFFF00>aaaa</color>Moga used to be carefree. He had nothing other than the garlic cloves that grew on his body. When he learned that the Nightfall had invaded the forest, he raised his long onion javelin out of love for the forest and became a member of the Forest Guard. Never underestimate the tiny Garlic Samurai. A strike from his javelin is not only unpleasant, it leaves its target with a distinct odor as well.]],
[711307]=[[Coming soon!]],
[711308]=[[Log Toss]],
[711309]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[711310]=[[Deals an additional 15% Physical DMG.]],
[711311]=[[Deals an additional 30% Physical DMG.]],
[711312]=[[Deals an additional 50% Physical DMG.]],
[711313]=[[Deals an additional 75% Physical DMG.]],
[711314]=[[Deals an additional 100% Physical DMG.]],
[711315]=[[Log Power]],
[711316]=[[Deals {%s1} Physical DMG to 2 enemy units.]],
[711317]=[[Deals an additional 15% Physical DMG.]],
[711318]=[[Increases the number of targets to 3.]],
[711319]=[[Deals an additional 30% Physical DMG.]],
[711320]=[[Deals an additional 50% Physical DMG.]],
[711321]=[[Deals an additional 75% Physical DMG.]],
[711322]=[[Purifying Dew]],
[711323]=[[HP & ATK & DEF +20%]],
[711324]=[[Effect increased by an additional {%s1}%.]],
[711325]=[[Effect increased by an additional {%s1}%.]],
[711326]=[[Effect increased by an additional {%s1}%.]],
[711327]=[[Effect increased by an additional {%s1}%.]],
[711328]=[[Effect increased by an additional {%s1}%.]],
[711401]=[[Knight of Death]],
[711402]='Andrew',
[711403]=[[A cold and noble knight commander and the last guardian of the moon.]],
[711404]=[["You have forsaken a knight's virtues and forfeited the right to don the armor."

These words were deeply engraved in Andrew's heart as he was expelled from the Knight Academy of the United Kingdom.

Humility, honesty, compassion, bravery, justice, sacrifice, honor, and spirit.

As a top performer in the Academy, he could recite the eight knightly virtues effortlessly. Those words used to embody Andrew's aspirations and pride, but now they served as damning evidence against him.

Andrew stole something.

With the onslaught of Omega, resources were concentrated in the hands of warriors and the upper echelons, making life increasingly arduous for the common folk. Despite coming from a poor home, Andrew was able to enroll at the Knight Academy thanks to his natural talent and ensured a life free from want. However, his former comrades and neighbors were on the brink of survival. Hence, he resolved to take a perilous gamble and pilfer from the Academy's provisions.

Upon discovery, the Academy accused him of "losing honesty and honor" as charges and promptly exiled him. Where is compassion, one of the esteemed virtues? During the trial, they contended that knights only extend mercy to those deemed deserving, asserting that lowly peasants held no worth to receive their benevolence.

Andrew was banished to a region within the domain of a hostile Nomad Tribe, far removed from the Kingdom's borders. Stripped down to a meager backpack and tattered garments, he wandered aimlessly across the unfamiliar grasslands. Suddenly, he recollected the distinctive armor set he possessed exclusively at the Academy. Was it now reduced to ashes, condemned as a tainted relic due to its owner's transgressions?

The armor, resplendent in its silver-white hue, gleamed when bathed in sunlight. Those who wore it called themselves holy knights, and aristocrats imbued with a sense of pride. They were held in high esteem, bestowed with respect and honor, and entrusted with the duty to safeguard people's welfare.

But it seemed that Andrew had caught a glimpse of the true faces lurking beneath the armor—hypocrisy, selfishness, greed, and arrogance.

With each step growing heavier, he remained oblivious to the dark specter trailing him, manifesting his deepening animosity toward the knights. In addition, as the winds howled across the grassland, his resentment grew more profound and vivid...]],
[711405]=[[In the past two years, the United Kingdom has been increasingly struggling against the relentless plundering by the tribes. Despite the tribes' inferior strength compared to the Kingdom, their agility and hit-and-run tactics always catch the Kingdom off guard.

Another significant reason is the emergence of an exceptionally formidable warrior within the tribes. No one knows his origins or why he bears such deep animosity towards the kingdom. Every battle sees him standing alone, fearlessly forging ahead. With his white hair, black armor, and a crimson-stained cloak flowing behind him, he resembles a mythical figure, almost like a demonic deity. Though his armor resembles the Kingdom's knights, the stark difference in color sets him apart.

At first, a newly appointed Kingdom knight scorned his audacious behavior and challenged him to a duel, only to be swiftly defeated by a single strike. Soon after, as wave upon wave of terrified soldiers rushed in, they were effortlessly mowed down by this enigmatic warrior, leaving none alive among the hundreds who dared to confront him.

"Upon sighting the Black Rider of the tribes, retreat without hesitation, absolved of any responsibility!"

After several clashes, during which this lone warrior single-handedly decimated nearly a thousand soldiers from the Kingdom, they had no choice but to issue such an order. The Kingdom also dispatched numerous scouts, hoping to unveil the origins of this terror-inducing warrior. Wouldn't the tribes become unbeatable if there were more like him?

Yet, the answers they received were consistently three bewildering words: "Found on the street."

The Kingdom's marshal imprisoned all the scouts, dismissing their reports as nonsense. However, they did manage to bring back one valuable piece of information—the name of the Black Rider.

Andrew.

Amidst their frenzied state of terror, nobody connected this name and the exiled youth from several years ago.]],
[711406]=[[Andrew's heart burned with an unrelenting desire for revenge, but he didn't realize when it had transformed into a merciless slaughter spree. The shadowy figure, clad in dark armor, had promised him a chance to reclaim his knighthood, yet failed to reveal the price he would have to pay.

He wore the same armor when he regained consciousness on the grasslands, saved by the tribesmen after an unknown duration. They told him it couldn't be removed, as if it had become an inseparable part of his body. At first, he felt no discomfort, only occasional surges of an intense craving for battle. Andrew attributed it to the tribal valor and martial spirit atmosphere and paid little attention.

It wasn't until his first time on the battlefield that everything changed. Initially, he harbored strong reluctance; after all, they were his compatriots. He couldn't bring himself to engage in outright conflict despite being exiled. He planned to go through the motions, exerting minimal effort.

However, as the battle commenced, the cacophony of war cries and thunderous drums ignited something within him. When his spear pierced the first foe, even he was taken aback. But in the next moment, he was consumed by an overwhelming rush.

He began a relentless slaughter. Even when that young knight stood before him, he showed no mercy or hesitation despite Andrew recognizing him as a former comrade from their time together as students.

His heart remained unfazed, driven solely by the intoxicating pleasure of revenge.

He thirsted for more!

Andrew actively joined the tribe's assaults on the Kingdom, but refrained from plundering. He had no interest in their resources. He just cared about the battle's thrill and slaughtering his enemies. This growing awareness gradually seized control of his thoughts like a savage beast.

He began to lose sight of his former self.

Perhaps only on nights devoid of conflict, as Andrew sits on the grasslands, gazing up at the vast expanse of stars, he might briefly recollect the young boy who had stolen food for his neighbors and friends. But was that truly him? He no longer knows.]],
[711407]=[[Coming soon!]],
[711408]=[[Ultimate Duel]],
[711409]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[711410]=[[Deals an additional 30% Physical DMG.]],
[711411]=[[Deals an additional 70% Physical DMG.]],
[711412]=[[Deals an additional 120% Physical DMG.]],
[711413]=[[Deals an additional 185% Physical DMG.]],
[711414]=[[Deals an additional 270% Physical DMG.]],
[711415]=[[Despair's Backlash]],
[711416]=[[Deals {%s1} of ATK as Physical DMG to all enemies and reduces their Magic DMG dealt by {%s2}.]],
[711417]=[[Deals an additional 30% Physical DMG.]],
[711418]=[[Reduces Magic DMG dealt by an additional 3%.]],
[711419]=[[Deals an additional 70% Physical DMG.]],
[711420]=[[Reduces Magic DMG dealt by an additional 3%.]],
[711421]=[[Deals an additional 120% Physical DMG.]],
[711422]=[[Ode to Darkness]],
[711423]=[[During battle, reduces Magic DMG taken by {%s1}.]],
[711424]=[[Reduces Magic DMG taken by an additional 4%.]],
[711425]=[[Reduces Magic DMG taken by an additional 4%.]],
[711426]=[[Reduces Magic DMG taken by an additional 4%.]],
[711427]=[[Reduces Magic DMG taken by an additional 4%.]],
[711428]=[[Reduces Magic DMG taken by an additional 4%.]],
[711429]=[[Avenging Warsong]],
[711430]=[[HP & ATK & DEF +20%, Speed +40]],
[711501]=[[Valiant Warrior]],
[711502]='Valkyr',
[711503]=[[The goddess of the sky and war.]],
[711504]=[[As one of "The Six" of the Celestial Realm, the Valiant Warrior symbolizes the sky and war, overseeing the combat training of the lower deities. Unlike the battle-hungry Wilderness God, the Valiant Warrior is more reserved and contemplative. The Goddess of Light once joked that if she ever fell in battle, Valiant Warrior would only mourn her for five seconds before her mind quickly shifted to strategies and maintaining order.
"Don't underestimate our bond," Valiant Warrior frowned, "Ten seconds."
True to her word, Valiant Warrior proved to be not only rational but also steadfast. On the day the Goddess of Light fell, Valiant Warrior led the charge, watching as the Goddess transformed into countless rays of light, sealing the Void Lord as they sank into the deep sea. In the ensuing silence, it was Valiant Warrior who, after ten seconds, raised her weapon and shouted, "The enemy leader is defeated. Now charge with me!"
This rallying cry reignited the fighting spirit of the demoralized deities, leading to the successful defeat of the Void remnants. Had they wallowed in grief over the Goddess' fall and allowed the enemy to counterattack first, the outcome would have been uncertain…]],
[711505]=[[In the Esper World, there's a fascinating coincidence: across all races, legends tell of a winged goddess wielding a spear and shield. The details vary, but the core remains the same. To Humans, she's a hero who led the rebellion against tyranny. To the Beastkin, she's an undefeated general. In Forest lore, she protects their vast forests...

Strangely, all these tales end the same way: the goddess mysteriously vanishes, as if she never existed. People speculate she's a deity from Yggdrasil, descended to save the mortal realms.

What's even more peculiar is that she seemed to be searching for something. Yet whenever asked, she remained silent. She visited human sanctuaries for heroic spirits, beastkin burial grounds for their honored dead, and the most sacred tree of the forest.

Yet, each time, she left empty-handed, her quest unfulfilled...]],
[711506]=[[When she was young, the Valiant Warrior heard a prophecy from a great prophet in the Celestial Realm. The prophecy spoke of an extraordinary hero who would arise in the Continent, earning the love and respect of all races and ultimately saving the world.
This tale left a deep mark on her heart. From her youth to her rise as one of The Six, she frequently left the Celestial Realm, searching for this hero. Yet, she was disheartened to find that while each race had its heroes, none were universally recognized.
During her quest, the Valiant Warrior got entangled in various conflicts: human dynastic wars, the tree spirits' defense of their ancient trees, and the beastkin's tribal disputes. She always told herself that as a deity, she should minimize her interference in worldly matters. But her compassionate nature led her to intervene every time.
Despite her many journeys to the World, she never found the hero. The Void's invasion forced her to shift her focus entirely to fighting this new threat.
"Maybe, the hero beloved by all races doesn't exist," she sometimes thought during the grueling battles against the Void.
Unbeknownst to her, statues were being built in her honor across the Continent, and her story was being shared among the races. Though her image and name varied, the respect and admiration were the same. This collective reverence strengthened her divine powers, and in the final battle against the Void, this newfound strength played a crucial role.]],
[711507]=[[Coming soon!]],
[711508]=[[Battle Realm]],
[711509]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[711510]=[[Deals an additional 30% Physical DMG.]],
[711511]=[[Deals an additional 70% Physical DMG.]],
[711512]=[[Deals an additional 120% Physical DMG.]],
[711513]=[[Deals an additional 185% Physical DMG.]],
[711514]=[[Deals an additional 270% Physical DMG.]],
[711515]='Judgment',
[711516]=[[Reduces Magic DMG taken by all allies by {%s1} for 1 turn.]],
[711517]=[[Reduces Magic DMG taken by Human heroes by an additional 4%.]],
[711518]=[[Reduces Magic DMG taken by Human heroes by an additional 8%.]],
[711519]=[[Reduces Magic DMG taken by Human heroes by an additional 12%.]],
[711520]=[[Reduces Magic DMG taken by Human heroes by an additional 16%.]],
[711521]=[[Duration extended to 2 turns.]],
[711522]=[[Battle Mark]],
[711523]=[[During battle, reduces all damage taken by your front-row units by {%s1}, and further reduces their Magic DMG taken by {%s2}.]],
[711524]=[[Reduces all damage taken by an additional 1.5%.]],
[711525]=[[Reduces all damage taken by an additional 1.5%.]],
[711526]=[[Reduces all damage taken by an additional 1.5%.]],
[711527]=[[Reduces all damage taken by an additional 1.5%.]],
[711528]=[[Reduces all damage taken by an additional 1.5%.]],
[711529]='Bloodthirst',
[711530]=[[HP & ATK & DEF +20%, Speed +40]],
[711601]=[[Fiery Plume]],
[711602]='Fenixia',
[711603]=[[A pilot who was reborn after a plane crash.
]],
[711604]=[[　　To rise from the ashes is a joy that makes the heart race.
　　　　　　　　　　　　　　—Fenixia

　　Fenixia's appearance completely disrupted the Void's plans.
　　The Phoenix Clan, which Fenixia is from, used to be one of the most mysterious species on the Esper Continent. According to the records, no one has found any trace of the Phoenix Clan since the Celestial Battle. Rumor has it that they all fell in that battle.
　　In fact, the rumors are true. But what people don't know is that phoenixes can rise from the ashes. Having been reborn several times, the Phoenix Clan is deeply familiar with the meaning of reincarnation—The end is just a new beginning, and the law of "all things return" refers to the rebirth of fate itself.
　　Having faced everything stoically, the Phoenix Clan never sought to be in the public eye. Instead, they chose to live in seclusion after discovering the "truth".
　　But fate never stopped moving. After a lengthy period of peace, the Phoenix Clan's inner circle grew corrupt. Somehow, the Void managed to sink its claws deep into the Phoenix Clan. The clan was dealt a massive blow, and all the records about how to seal away the Void were stolen. Many in the clan sacrificed their lives for Fenixia, who was next in line to be queen. During the flight, the young Fenixia narrowly avoided death. Only a few people know the details of this conflict, but what is certain is that the Phoenix Clan suffered heavy casualties in the battle, which came to be known as the Battle of Annihilation.
　　To find out the truth and avenge her clan, Fenixia embarked on a new journey.]],
[711605]=[[Even if the road ahead is filled with unknowns and hardships, I will keep moving against all odds.
　　　　　　　　　　　　　　—Fenixia

　　Choppy waves lapped against the rocks on the shore and dark clouds covered the sky overhead, not letting a ray of sunlight pass through. Leaning against a palm tree at the seaside, Fenixia looked expressionlessly into the distance. Suddenly, she sensed some sort of wail coming from deep beneath the waves. The strong resonance made her shed tears of grief unconsciously.
　　This was Fenixia's seventh year in Asgard. After the Battle of Annihilation, Fenixia, following the old clan chief's instructions, came to Asgard, where the Phoenix Clan originated.
　　According to Fenixia's investigation, seven years ago, the Void didn't just attack the Phoenix Clan to steal its documents on how to seal the Void, but they sought the clan's mysterious Nirvana Flame as well. This flame could even burn the gods themselves to ashes. But no one knew except the clan chief that the Nirvana Flame had been extinguished in the Celestial Battle, which was also one of the major reasons why the Phoenix Clan lived in seclusion.
　　The sea breeze blew gently through Fenixia's hair. Her intuition told her there had to be something tied up beneath this sea. Using her flames, Fenixia carefully searched until she finally found a stone inscribed with runes in a corner of a reef. The soul bound in the sea seemed to react as well. Massive waves billowed violently as Fenixia used all her strength to destroy the runestone. Suddenly, a flash of bright light shot out, and a blazing fire slowly rose from the sea...
　　"The Nirvana Flame!" Fenixia's heart pounded like crazy!
　　The Nirvana Flame hovered in the air for a while, as if looking for the person who set it free. Once confirming that it was Fenixia, the Nirvana Flame abruptly grew brighter and then flew rapidly towards her. At the same time, a vigorous voice rang out softly, "You're finally here!"]],
[711606]=[[　　Asgard is also known by another name—Nirvana. To be more precise, the glittering gravel on the beach was only a cenotaph of the Phoenix Clan, while the Nirvana Flame in the deep sea was the flame of hope left by their ancestors. This flame that never went out was the very foundation of the Phoenix Clan!
　　Fenixia's first death occurred when she left Asgard. At that time, she had just received the Nirvana Flame's recognition.
　　After the Celestial Battle, the records of how to conceal the Nirvana Flame's powerful aura were lost. Consequently, the Void quickly sniffed out Fenixia's trail. Peril was incoming, but Fenixia, still immersed in rapture, didn't know it yet.
　　The Void had failed to get what they wanted since the Battle of Annihilation, so their bounty for people from the Phoenix Clan had increased more than a hundredfold. This was undoubtedly a special case compared to other arrest warrants. Now the Phoenix Clan had emerged again, along with the even more coveted Nirvana Flame. It was a temptation for not just the Void, but other evil forces hiding in the darkness as well!
　　Soon, Fenixia encountered the biggest crisis of her life in the midst of a siege. Despite her efforts to resist and escape, in the face of numerous powerful enemies, she gradually lost consciousness. She tried to fight back, but after a three-day struggle, the truth was far more ruthless than the reality!
　　The Nirvana Flame isn't available to everyone. When people thought Fenixia was dead and abandoned her in the valley, she was reborn from the ashes. Two resurrections had undoubtedly changed the course of Fenixia's life. This time, she was even more resolved to destroy the Void.]],
[711607]=[[Coming soon!]],
[711608]='Incinerate',
[711609]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[711610]=[[Deals an additional 30% Magic DMG.]],
[711611]=[[Deals an additional 70% Magic DMG.]],
[711612]=[[Deals an additional 120% Magic DMG.]],
[711613]=[[Deals an additional 185% Magic DMG.]],
[711614]=[[Deals an additional 270% Magic DMG.]],
[711615]=[[Flame Spiral]],
[711616]=[[Deals {%s1} of ATK as Magic DMG to all enemies.]],
[711617]=[[Deals an additional 30% Magic DMG.]],
[711618]=[[Deals an additional 70% Magic DMG.]],
[711619]=[[Deals an additional 120% Magic DMG.]],
[711620]=[[Deals an additional 185% Magic DMG.]],
[711621]=[[Deals an additional 270% Magic DMG.]],
[711622]='Fireborn',
[711623]=[[During battle, increases your CRIT Rate by {%s1}.]],
[711624]=[[Increases CRIT Rate by an additional 3%.]],
[711625]=[[Increases CRIT Rate by an additional 3%.]],
[711626]=[[Increases CRIT Rate by an additional 3%.]],
[711627]=[[Increases CRIT Rate by an additional 3%.]],
[711628]=[[Increases CRIT Rate by an additional 3%.]],
[711629]=[[Phoenix Wing]],
[711630]=[[HP & ATK & DEF +20%, Speed +40]],
[711701]=[[Spirit of Ripples]],
[711702]='Sophia',
[711703]=[[Born from the ripples, this flawless goddess wields her spear and bow to hunt down all intruders in the forest.]],
[711704]=[[Legend has it that Sophia appears in the forest on moonlit and clear nights. There are times when she offers a helping hand to lost spirits, while on other occasions, she invites handsome spirits to join her in moon-watching. On occasion, her caprices lead her to dance gracefully by the clearest lake in the woods, with moonlight tracing her enchanting silhouette like a graceful butterfly.
During the days of Yggdrasil's gradual withering, Sophia's presence gradually soothed the anxious hearts of the spirits. Though the situation hasn't improved, it seems that as long as Sophia is there, not all hope is lost.
Receiving Sophia's assistance or invitation has become a special honor within the forest.
Once, during a sudden downpour, a rabbit spirit sought shelter in haste but still got drenched. Shivering from the cold, rain suddenly swirled around and took on a human form, and there was Sophia. She handed the rabbit spirit an umbrella and bestowed warmth through magic. After completing these acts of kindness, Sophia waved goodbye and melded back into the rain.
The next day, the rabbit spirit became the center of attention among their friends, with the umbrella being passed around and envy brimming. As news of this incident spread, some even intentionally went out in the rain on rainy nights.
But without exception, none of them ever saw Sophia, for she helped those in need, not those seeking trouble.]],
[711705]=[[When not residing in the forest, Sophia spends the majority of her time traveling far and wide, protecting elves who venture beyond. No one knew why she would make appearances where she did. Perhaps she admired the moon in the forest one night, saved a lost child in the meadows the next day, watched the sunset over the northern snow-capped mountains in the evening, and returned to the forest at night, using her graceful dances to convey her experiences.
Some say Sophia can traverse all lakes, effortlessly appearing in any corner with a water body. Others speculate that her massive curved bow is a manifestation of the moon, allowing her to instantly reach wherever moonlight touches. Some even speculate that Sophia is an embodiment of wisdom, kindness, and power in the world, and thus, the laws of space cannot limit her.
In all the stories about Sophia, besides the forest, the place she most frequently appears is the distant north. Perhaps it's because more spirits travel there, or maybe she simply enjoys its snowy landscapes.
Sophia possesses numerous charms, but what captivates people the most is her formidable strength. It is said that she once single-handedly faced off against an entire horde of cavalry from the grassland tribes to protect a group of besieged spirits, emerging unscathed.
For the spirits who wander, Sophia is like the lakes and moonlight of their homeland forest, bringing deep warmth and a sense of security.]],
[711706]=[[Sophia is on a quest for the truth. She wants to understand why Yggdrasil has suddenly begun to wither, why the River of Life has become polluted, and why the unease in her heart is growing stronger as if a great crisis looms...?
To reach Yggdrasil, one must traverse the surrounding primeval forest, now tainted and teeming with ferocious beasts, with deadly toxins pervading the air. But with astounding determination and strength, Sophia has successfully crossed this perilous terrain to stand before Yggdrasil.
"I demand an audience with the divine!"
Sophia declares firmly, aware that all creatures entering Yggdrasil's domain are under their watchful eye and need not speak too loudly. She grips her weapon, staying vigilant. It has been many years since the spirits last spoke with the gods, and she is uncertain of what had become of their descendants.
To her surprise, she is welcomed by a highly affable and serene goddess, who Sophia later learns is none other than their leader, the Goddess of Light, Aurora.
From Aurora's lips, Sophia finally learns the truth of everything: an impending void threatens, planetary energies are being drained, more and more beings are suffering calamities, and the world is at a critical crossroads.
Invited by Aurora, Sophia joined the Esper Union. She has realized that she is not just a child of the forest but also a resident of this world. She has a duty and responsibility to defend this land.]],
[711707]=[[Coming soon!]],
[711708]='Dragonhunt',
[711709]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[711710]=[[Deals an additional 30% Magic DMG.]],
[711711]=[[Deals an additional 70% Magic DMG.]],
[711712]=[[Deals an additional 120% Magic DMG.]],
[711713]=[[Deals an additional 185% Magic DMG.]],
[711714]=[[Deals an additional 270% Magic DMG.]],
[711715]=[[Dragon's Waltz]],
[711716]=[[Deals {%s1} of ATK as Magic DMG (prioritizing enemy back-row single target), with a 10% chance to Stun the target for 1 turn.]],
[711717]=[[Deals an additional 30% Magic DMG.]],
[711718]=[[Increases the number of targets to 2.]],
[711719]=[[Deals an additional 70% Magic DMG.]],
[711720]=[[Increases the number of targets to 3.]],
[711721]=[[Deals an additional 120% Magic DMG.]],
[711722]=[[Moonlit Ripples]],
[711723]=[[During battle, increases your ATK by {%s1}.]],
[711724]=[[Increases ATK by an additional 4%.]],
[711725]=[[Increases ATK by an additional 4%.]],
[711726]=[[Increases ATK by an additional 4%.]],
[711727]=[[Increases ATK by an additional 4%.]],
[711728]=[[Increases ATK by an additional 4%.]],
[711729]=[[Nocturnal Brilliance]],
[711730]=[[HP & ATK & DEF +20%, Speed +40]],
[711801]=[[Shadow of Exile]],
[711802]='Alvarez',
[711803]=[[A Japanese ronin who uses her blade to hone her dust-stained heart.]],
[711804]=[[　　Alvarez was born in the capital of the Kingdom of Rylina. Her father is a high-ranking military officer in the kingdom, while her mother is from a noble family. Being from a well-off family, Alvarez received the best education possible from a young age.
　　She inherited her parents' best traits. Not only is she remarkably good-looking, she also demonstrated an astounding aptitude for combat. After passing the "Golden Trial", she officially became part of the "Golden Legion".
　　Alvarez is a once-in-a-generation talent. Seeing this, the Golden Legion instructor customized a more advanced syllabus for her. Living up to expectations, her combat skills improved dramatically. Obsessed with war and combat by nature, she even requested permission from the kingdom to consult famous military officers for advice. The Kingdom of Rylina worships strength, and a rising star like Alvarez caught everyone's attention. Many believed that Alvarez would eventually assume command of the Golden Legion.
　　However, a woman of her caliber was bound to make others jealous. A scheme was enacted, the "War of the Oathbreaker", which shocked the entire kingdom. Alvarez was reduced to a mere prisoner in an instant.
　　Thankfully, no wall is truly impenetrable. Alvarez's instructor quickly found evidence to prove her innocence. However, Alvarez refused to be acquitted. Instead, she asked to be "exiled" to Akami, the Trading Nation where people of all sorts gather. Her instructor quickly caught on to Alvarez's intentions. She wanted to help the kingdom find the Void's Akami base!
　　The Kingdom of Rylina has always been the key faction fighting the Void. Over the years, they'd ferreted out the Void's various hiding places. Akami remained the only place where they couldn't make any headway. This was a thorn in the kingdom's side. A new soldier like Alvarez would be the most suitable candidate for a mission like this. However, Akami's situation is very complicated, and the dangers there are unimaginable to outsiders. As if reading her instructor's mind, Alvarez not only reiterated her determination to go to Akami, she also avowed her desire to become stronger.
　　After careful consideration, Alvarez's instructor agreed to her suggestion. Very few people know this secret of how Alvarez became an exile.]],
[711805]=[[　　On the surface, Alvarez seems like a cold-hearted assassin. However, beneath that cool exterior lies a fastidious heart.
　　As a top assassin who operated in the Northeast Desert for many years, Alvarez has inspired countless rumors. There are three especially interesting ones: The first is that she was part of the Kingdom of Rylina's "Golden Legion", the mysterious legion said to be the strongest military force in the world! The second is that she was a key personnel during the "War of the Oathbreaker". The third is that she prizes money above all else. As long as the price is right, there's nothing she won't do on your behalf.
　　Years ago, the Golden Legion was ordered to root out any Void members lurking along the border of the Kingdom of Rylina. However, somehow news of this was leaked, and almost every member of the Golden Legion who went on that mission was killed. Alvarez was the only survivor. The Golden Legion is filled with battle-hardened elites from the Kingdom of Rylina. Alvarez is highly capable, but she lacked the combat experience of her seniors officers. As such, people were baffled that a rookie would be the lone survivor of an army that never knew failure. According to rumors from the Kingdom of Rylina, before the big battle, Alvarez sent a letter by falcon. She explained that she was sending a letter back to base as per her commander's order after she reported an anomaly she discovered. However, the falcon was later found in the Void's territory, and priceless treasures were also found in Alvarez's possession. These discoveries seemed to implicate Alvarez as being in league with the Void.
　　There was no hard evidence to prove that Alvarez was a traitor, but as the only suspect, she was still exiled.
　　Akami, the Trading Nation, is a place where people from all walks of life gather. Alvarez, now exiled from the Golden Legion, stands out like a sore thumb. In order to hide her true goal, Alvarez seldom interacts with anyone, and only takes on missions with high bounties. This is how the rumor that "Alvarez will do anything for the right price" started.]],
[711806]=[[After years of investigation, Alvarez reached the conclusion that the pollution plaguing Akami might not be solely attributable to the traditional understanding of the Void. Rather, it seemed to originate from a mysterious seed that subtly transformed ordinary individuals into creatures of the Void.

Having gathered crucial leads at the Dream Street Chamber of Commerce, Alvarez resolved to infiltrate one of Akami Harbor's most dreaded locations—the Sea Prison, which housed numerous felons.

Within its confines, she forged friendships with Saprta, the God Slayer responsible for annihilating several prominent families of Akami, Atak, a deranged genius attempting to control spirits through alchemy, and Aldis, a fallen legendary mage. These immense negative emotions and wicked thoughts impeded Alvarez's investigation until she finally detected traces of the Void energy. Unfortunately, it was already the eve of the cataclysmic Night of Turbulence.

All the guards of the Sea Prison transformed into Void creatures, leading to the prison's destruction and the escape of all the felons. Amidst the chaos, Alvarez's judgment solidified—the source of the Void's power resided in Saprta's Bloodlust Demon Blade.

With the prison in ruins and the felons dispersed, Alvarez found herself unable to prevent the impending disaster. However, in the interest of preserving world stability, she resolved to capitalize on the turmoil by assassinating Saprta and eradicating the origin of Akami's Void.

Success or failure teetered on a knife's edge. Just as Alvarez's razor-sharp blade inched toward Saprta's heart, flames infused with the essence of a phoenix erupted, knocking her off her feet and sending her hurtling into the depths of the ocean.

When Alvarez eventually regained consciousness, she discovered that fishermen had rescued her. Questions plagued her mind: Why did Saprta have a connection to the Void? From where did his phoenix fire originate? Was Saprta responsible for the outbreak of the Night of Turbulence? Yet, with Saprta and the felons long gone, the truth remained elusive. In her pursuit of answers and peace for this world, Alvarez embarked on a new path—to find Saprta and the Bloodlust Demon Blade and to apprehend the criminals who had escaped during the Night of Turbulence.]],
[711807]=[[Coming soon!]],
[711808]=[[Revolving Blades]],
[711809]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[711810]=[[Deals an additional 30% Physical DMG.]],
[711811]=[[Deals an additional 70% Physical DMG.]],
[711812]=[[Deals an additional 120% Physical DMG.]],
[711813]=[[Deals an additional 185% Physical DMG.]],
[711814]=[[Deals an additional 270% Physical DMG.]],
[711815]=[[Terror Blade]],
[711816]=[[Launches 10 attacks, each dealing {%s1} Physical DMG to a random enemy and reducing the target's DEF by 5% (up to 25% per target) for 2 turns.]],
[711817]=[[Deals an additional 30% Physical DMG.]],
[711818]=[[Attack limit increased to 15 times.]],
[711819]=[[Deals an additional 70% Physical DMG.]],
[711820]=[[Attack limit increased to 20 times.]],
[711821]=[[Deals an additional 120% Physical DMG.]],
[711822]=[[Identify Weakness]],
[711823]=[[During battle, increases the Physical DMG you deal by {%s1}.]],
[711824]=[[Increases Physical DMG dealt by an additional 3%.]],
[711825]=[[Increases Physical DMG dealt by an additional 3%.]],
[711826]=[[Increases Physical DMG dealt by an additional 3%.]],
[711827]=[[Increases Physical DMG dealt by an additional 3%.]],
[711828]=[[Increases Physical DMG dealt by an additional 3%.]],
[711829]=[[Azure Dagger]],
[711830]=[[HP & ATK & DEF +20%, Speed +40]],
[711901]='Dragonslayer',
[711902]='Garuda',
[711903]=[[A muscular man from Novosibirsk who uses a pacemaker in combat and rescue operations.]],
[711904]=[[<color=#FFFFFF00>aaaa</color>The dragon's roar came from above. The people of the empire sensed the impending danger and started to panic. Meanwhile, a warrior donning dragonscale armor was looking into the distance from atop a mountain far from the imperial capital.]],
[711905]=[[<color=#FFFFFF00>aaaa</color>The dragon was circling above the imperial capital, breathing fire into the city as the panicked citizens attempted to flee. The Dragonslayer jumped down from the mountain behind the dragon. Using gravity to his advantage, he landed on the dragon's back! Noticing him, the dragon desperately tried to shake the Dragonslayer off, but he remained fearless and steadfast. Then, he raised his spear and plunged it into the dragon's neck!]],
[711906]=[[<color=#FFFFFF00>aaaa</color>As the dragon fell, the hero's cape was dyed red. The people finally saw the warrior who saved their lives. He was wearing dragonscale armor, a trophy from another dragon he slayed in the past.]],
[711907]=[[Coming soon!]],
[711908]=[[Spear Rain]],
[711909]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[711910]=[[Deals an additional 20% Magic DMG.]],
[711911]=[[Deals an additional 45% Magic DMG.]],
[711912]=[[Deals an additional 70% Magic DMG.]],
[711913]=[[Deals an additional 100% Magic DMG.]],
[711914]=[[Deals an additional 150% Magic DMG.]],
[711915]=[[War Instinct]],
[711916]=[[Increases front-row allies' DEF by {%s1} for 1 turn (unstackable).]],
[711917]=[[Increases DEF by an additional 4%.]],
[711918]=[[Increases DEF by an additional 4%.]],
[711919]=[[Increases DEF by an additional 4%.]],
[711920]=[[Duration extended to 2 turns.]],
[711921]=[[Increases DEF by an additional 4%.]],
[711922]=[[Energizing Lance]],
[711923]=[[During battle, reduces all monster damage you take by {%s1}.]],
[711924]=[[Reduces damage taken from monsters by an additional 4%.]],
[711925]=[[Reduces damage taken from monsters by an additional 4%.]],
[711926]=[[Reduces damage taken from monsters by an additional 4%.]],
[711927]=[[Reduces damage taken from monsters by an additional 4%.]],
[711928]=[[Reduces damage taken from monsters by an additional 4%.]],
[711929]=[[Fervent Battle Soul]],
[711930]=[[HP & ATK & DEF +10%]],
[712001]=[[Spear of Revenge]],
[712002]='Marissa',
[712003]=[[An English super-warrior with an integrated exoskeleton.]],
[712004]=[[<color=#FFFFFF00>aaaa</color>Marissa hails from Alchemy City. She carries two weapons on her: a gun and an intricate crossbow. Her family is famous in Alchemy City for producing genius researchers. Since Marissa was young, she's displayed an astonishing talent for alchemy.]],
[712005]=[[<color=#FFFFFF00>aaaa</color>Marissa comes from a wealthy family. Under her parents' careful tutelage, she learned to conduct herself elegantly, stand her ground, and face any challenges with the utmost focus. Her gun is a family heirloom, and she learned to use it from a very young age. Her marksmanship is on par with the kingdom's soldiers. However, Marissa prefers crossbows to guns. She likes the sound the arrows make as they pierce the air.]],
[712006]=[[<color=#FFFFFF00>aaaa</color>When Marissa was 18, the Nightfall invaded Alchemy City. They took over the Alchemist Guild and abducted many alchemists, but there were plenty of corrupt alchemists that had long since allied themselves with the Nightfall. Marissa's family was in possession of several research reports on advanced alchemy, so naturally the Nightfall wouldn't let them go. Marissa's parents hid her in the attic, where she witnessed them being killed with her own eyes. After that, Marissa joined the ranks of the Demon Hunters. To this day, she is still waiting for her chance to exact vengeance on the Nightfall.]],
[712007]=[[Coming soon!]],
[712008]=[[Absolute Precision]],
[712009]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712010]=[[Deals an additional 20% Physical DMG.]],
[712011]=[[Deals an additional 45% Physical DMG.]],
[712012]=[[Deals an additional 70% Physical DMG.]],
[712013]=[[Deals an additional 100% Physical DMG.]],
[712014]=[[Deals an additional 150% Physical DMG.]],
[712015]='Charge',
[712016]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712017]=[[Deals an additional 20% Physical DMG.]],
[712018]=[[Has a 10% chance to Stun the target for 1 turn.]],
[712019]=[[Deals an additional 45% Physical DMG.]],
[712020]=[[Has a 20% chance to Stun the target for 1 turn.]],
[712021]=[[Deals an additional 70% Physical DMG.]],
[712022]=[[Triumphal Emblem]],
[712023]=[[During battle, increases all damage you deal to monsters by {%s1}.]],
[712024]=[[Increases damage dealt to monsters by an additional 4%.]],
[712025]=[[Increases damage dealt to monsters by an additional 4%.]],
[712026]=[[Increases damage dealt to monsters by an additional 4%.]],
[712027]=[[Increases damage dealt to monsters by an additional 4%.]],
[712028]=[[Increases damage dealt to monsters by an additional 4%.]],
[712029]=[[Ranged Deterrence]],
[712030]=[[HP & ATK & DEF +10%]],
[712101]='Bard',
[712102]='Denise',
[712103]=[[A Jamaican sprinter who uses a prosthetic limb for "dimensional sprints".]],
[712104]=[[<color=#FFFFFF00>aaaa</color>Denise is a famous bard with a soothing voice. She sings of heroic legends, touching love stories, bitter wars, and more. Her voice and melodious mandolin playing never fail to capture the attention of travelers.]],
[712105]=[[<color=#FFFFFF00>aaaa</color>Denise's voice carries a mysterious power. Anyone who listens to her feels revitalized and hopeful about the future. Denise hopes that her singing and music can provide some comfort to people and places torn asunder by war.]],
[712106]=[[<color=#FFFFFF00>aaaa</color>Denise's mandolin was made by her father when she was young. Even after she traveled around the world and received numerous prized instruments, she still insists on using this old mandolin. She hopes that no matter where she goes, her music will be carried off by the wind, right back to her father's side.]],
[712107]=[[Coming soon!]],
[712108]=[[Mirror Play]],
[712109]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712110]=[[Deals an additional 20% Physical DMG.]],
[712111]=[[Deals an additional 45% Physical DMG.]],
[712112]=[[Deals an additional 70% Physical DMG.]],
[712113]=[[Deals an additional 100% Physical DMG.]],
[712114]=[[Deals an additional 150% Physical DMG.]],
[712115]=[[Charming Song]],
[712116]=[[Increases back-row allies' damage against monsters by {%s1} for 1 turn.]],
[712117]=[[Increases damage dealt to monsters by an additional 1%.]],
[712118]=[[Increases damage dealt to monsters by an additional 1%.]],
[712119]=[[Increases damage dealt to monsters by an additional 1%.]],
[712120]=[[Duration extended to 2 turns.]],
[712121]=[[Increases damage dealt to monsters by an additional 1%.]],
[712122]='Inspire',
[712123]=[[During battle, reduces all monster damage you take by {%s1}.]],
[712124]=[[Reduces damage taken from monsters by an additional 4%.]],
[712125]=[[Reduces damage taken from monsters by an additional 4%.]],
[712126]=[[Reduces damage taken from monsters by an additional 4%.]],
[712127]=[[Reduces damage taken from monsters by an additional 4%.]],
[712128]=[[Reduces damage taken from monsters by an additional 4%.]],
[712129]=[[One Last Song]],
[712130]=[[HP & ATK & DEF +10%]],
[712201]=[[Holy Inquisitor]],
[712202]='Romano',
[712203]=[[A sheriff in Detroit City who uses Dimensional Energy Gloves to punish evildoers.]],
[712204]=[[<color=#FFFFFF00>aaaa</color>Romano was born into a family with strict rules and a domineering father who controlled everything. As the eldest son, his father had high hopes for Romano and expected him to take over the family's affairs one day.]],
[712205]=[[<color=#FFFFFF00>aaaa</color>Since childhood, Romano's life was shaped by strict limitations and endless scheduling. For instance, he would patiently wait for the entire family to finish their morning prayers before having breakfast and diligently carry out perseverance training even under the scorching sun, no matter the weather. Growing up in such an environment, Romano became known as the most solemn and unsmiling family head after he assumed the role. In his eyes, all the rules were inviolable.]],
[712206]=[[<color=#FFFFFF00>aaaa</color>After getting married, Romano treated his wife with respect and regarded her as his one true love. One day, she went out shopping and didn't return for a long time. By noon, it was time for Romano's perseverance training. Just then, his wife's attendants rushed in to deliver an urgent message. However, Romano was already deep into his training. Unwilling to tolerate any interruption, he angrily dismissed them. After he'd finished, Romano inquired about the situation, only to learn that his wife had been kidnapped by a gang of bandits from Alchemy City. It was likely impossible to catch up with them now. Romano's wife remains missing to this day, and the incident had become one of his deepest regrets.]],
[712207]=[[Coming soon!]],
[712208]=[[Cleanse Heresy]],
[712209]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712210]=[[Deals an additional 15% Physical DMG.]],
[712211]=[[Deals an additional 30% Physical DMG.]],
[712212]=[[Deals an additional 50% Physical DMG.]],
[712213]=[[Deals an additional 75% Physical DMG.]],
[712214]=[[Deals an additional 100% Physical DMG.]],
[712215]=[[Piercing Fists]],
[712216]=[[Deals {%s1} Physical DMG to 2 enemy units.]],
[712217]=[[Deals an additional 15% Physical DMG.]],
[712218]=[[Increases the number of targets to 3.]],
[712219]=[[Deals an additional 30% Physical DMG.]],
[712220]=[[Deals an additional 50% Physical DMG.]],
[712221]=[[Deals an additional 75% Physical DMG.]],
[712222]=[[Unyielding Fortitude]],
[712223]=[[HP & ATK & DEF +20%]],
[712224]=[[Effect increased by an additional {%s1}%.]],
[712225]=[[Effect increased by an additional {%s1}%.]],
[712226]=[[Effect increased by an additional {%s1}%.]],
[712227]=[[Effect increased by an additional {%s1}%.]],
[712228]=[[Effect increased by an additional {%s1}%.]],
[712301]=[[God Slayer]],
[712302]='Sparta',
[712303]=[[A god of the Nightfall that is armed with a sword. Killing a god is an indelible sin.]],
[712304]=[[　　Sparta had a pitiful past before becoming the iron-blooded leader of the Demon Legion. 
　　Sparta's Infernal Clan originally lived on Hita island, but later migrated to the Hermian Plain after a volcano erupted there. Infernal Clan members were born with a strong physique and were very good at fighting. They soon managed to find a foothold on the Hermian Plain, but they also got into a feud with the indigenous people.
　　Sparta used to hang out around the Amika seaport when he was a child. Because of the unique appearance of the Infernal Clan, lone clansmen often become the target of sneak attacks. It was during those attacks that Sparta honed his combat skills and gradually became a celebrated fighter.
　　In the beginning, Sparta made a living by working as a porter at the docks, but he found the work menial and the salary insufficient. As time went by, Sparta yearned for something more challenging, and his ambition caught the attention of the leader of the Akami Trading Nation. Tempted by the offer, Sparta joined the group and committed unforgivable trespasses, which resulted in several other clans joining forces against the Infernal Clan and slaughtering them.
　　The flames of rage consumed Sparta. He wants revenge... revenge against the world that deceived him!]],
[712305]=[[The Fire Devils were spared from the destruction of their clans due to the obscurity of their ancient texts. Saprta, driven by vengeance, sacrificed his soul to restore his homeland's ruins. In doing so, he stumbled upon a secret: the Fire Devil clan and the Phoenix clan were ancient enemies, yet the Fire Devils relied on their fire to embark on the "Quenching Path." However, as the Fire Devils dwindled, the Phoenix also withdrew from the world, burying this history in the sands of time.

With the aim of dismantling several powerful families, Saprta departed from Akami Harbor and began a quest to find the Phoenix Clan. After crossing mountains and seas, he discovered faint clues leading to the clan's location, coinciding with the outbreak of the Battle of Annihilation, a war launched by the Void to eradicate the Phoenix Clan. Witnessing the phoenixes' deaths, Saprta was haunted by memories of the Fire Devils' tragedy. In this chaos, he managed to save a girl from the Phoenix Clan.

Since the Void had nearly wiped out the Phoenix Clan, and only the fire of a living phoenix could awaken the Quenching Path, Saprta was left with no other option. However, even after leaving the Phoenix Clan's territory, he couldn't shake off his responsibility for the Phoenix girl. Saprta repeatedly reassured himself that there must be alternative paths to gain strength.

Ironically, fate had different plans. Near Akami Harbor, the Phoenix Girl inadvertently revealed herself, attracting the attention of minions from several powerful clans who coveted the Phoenix Clan's power. Saprta valiantly attempted to rescue her but was outnumbered and gravely wounded. In a critical moment, the girl's body erupted in flames, enveloping Saprta in a gentle embrace.

The Phoenix's fire ignited Saprta's heart, burning eternally within his chest. Regret, pain, rage, and anger surged through him. The Quenching Path reached its climax, and as the girl gradually closed her eyes, Saprta's furious roar reverberated. The minions of the clans were instantly consumed by an overwhelming blaze, which then quickly traveled toward the great families of Akami Harbor...]],
[712306]=[[With his own strength, Saprta reduced the powerful families of Akami Harbor to dust. However, he eventually exhausted his strength and was apprehended by law enforcement. Initially, they attempted to execute this monstrous being, but bullets, poison, and even alchemical weapons had no effect. Consequently, they were compelled to confine him to the most dreaded place in Akami—the Sea Prison.

Rumors circulated that after the destruction of the great families, Saprta became unusually quiet. Even when he was bound in heavy chains and confined to the dark dungeon, he exhibited no reaction, as if his soul had been lost. The only constant was the magic blade firmly in his possession, emitting a faint heartbeat-like resonance. His eerie, magical eyes stared into the darkness, seemingly awaiting something.

Several years passed, and memories of the demon imprisoned in the depths of the prison had begun to fade. It was on that night when the answer was unveiled, as Saprta once again revealed his fangs.

The Night of Turbulence descended silently upon the Sea Prison. All the prison guards transformed into Void creatures, and chaos ensued as the prison crumbled. Felons escaped, and a significant number of them willingly followed Saprta, becoming the founding members of the Demon Legion, which also served as a valuable asset that supported Saprta in his bid for the Arch Chancellor position after he joined Nightfall.

No one understood why the guards transformed into Void creatures, nor did they comprehend what Saprta had done to inspire these felons to swear their allegiance. However, it was undeniably connected to the Fire Devil, who burned with the Phoenix's fire.

"Henceforth, I am an enemy of this world."

According to hearsay, these were Saprta's parting words upon leaving Akami. The Sea Prison itself turned to ashes in the flames, signifying that the future Nightfall Lord had fully embraced the path of darkness.]],
[712307]='Farinelli',
[712308]=[[Demonic Sacrifice]],
[712309]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712310]=[[Deals an additional 30% Physical DMG.]],
[712311]=[[Deals an additional 70% Physical DMG.]],
[712312]=[[Deals an additional 120% Physical DMG.]],
[712313]=[[Deals an additional 185% Physical DMG.]],
[712314]=[[Deals an additional 270% Physical DMG.]],
[712315]=[[Scatter the Weak]],
[712316]=[[Taunts 2 enemy units and reduces their ATK by {%s1} for 2 turns.]],
[712317]=[[Reduces ATK by an additional 2%.]],
[712318]=[[Increases the number of targets to 3.]],
[712319]=[[Reduces ATK by an additional 2%.]],
[712320]=[[Increases the number of targets to 4.]],
[712321]=[[Reduces ATK by an additional 2%.]],
[712322]=[[Devil's Rage]],
[712323]=[[During battle, reduces all damage taken by {%s1}.]],
[712324]=[[Reduces all damage taken by an additional 3%.]],
[712325]=[[Reduces all damage taken by an additional 3%.]],
[712326]=[[Reduces all damage taken by an additional 3%.]],
[712327]=[[Reduces all damage taken by an additional 3%.]],
[712328]=[[Reduces all damage taken by an additional 3%.]],
[712329]=[[Demonic Edge]],
[712330]=[[HP & ATK & DEF +20%, Speed +40]],
[712401]=[[Demon Masked Asura]],
[712402]='Kataras',
[712403]=[[The 12th king of the ancient Kingdom of Glendros.]],
[712404]=[[The demon mask of Kataras was not born by chance. He forged a blood pact with an ancient demon in the Abyss of Shadows, trading half his soul for the power to obliterate his enemies. Every time an eclipse occurs, the mask tightens as if alive, reminding him of the price of this power.]],
[712405]=[[He once slew the Lord of the Nightfall, yet refused the throne of bones. The power of corruption shattered beneath his feet, and he made a waist chain from the skulls of his foes. "A crown only breeds the next monster," he whispered, stroking his demon mask before vanishing into the darkness.]],
[712406]=[[On a blood moon night, the demon mask suddenly fell off. A strange woman's phantom appeared through the swamp mist, humming a lullaby he had forgotten. When he reached out to touch her, the mask closed again, and the curse that consumed his sister two centuries ago let out a mocking cackle.]],
[712407]=[[Coming soon!]],
[712408]=[[Frenzied Hellfire]],
[712409]=[[Deals {%s1} of ATK as Physical DMG to a random enemy.]],
[712410]=[[Deals an additional 30% Physical DMG.]],
[712411]=[[Deals an additional 70% Physical DMG.]],
[712412]=[[Deals an additional 120% Physical DMG.]],
[712413]=[[Deals an additional 185% Physical DMG.]],
[712414]=[[Deals an additional 270% Physical DMG.]],
[712415]=[[Asura's Blade]],
[712416]=[[Enters Counterattack Mode for 1 turn. Counterattack Mode: Retaliates when taking damage, dealing {%s1} of basic attack damage. Triggers up to once per turn.]],
[712417]=[[Increases Counterattack DMG by an additional 10%.]],
[712418]=[[Duration extended to 2 turns.]],
[712419]=[[Increases Counterattack DMG by an additional 10%.]],
[712420]=[[Our front row will also enter Counterattack Mode]],
[712421]=[[Increases Counterattack DMG by an additional 10%.]],
[712422]=[[Soul's End]],
[712423]=[[During battle, reduces all damage taken by your front-row units by {%s1}. Nightfall heroes in Counterattack Mode gain double the effect.]],
[712424]=[[Reduces all damage taken by an additional 1.5%.]],
[712425]=[[Reduces all damage taken by an additional 1.5%.]],
[712426]=[[Reduces all damage taken by an additional 1.5%.]],
[712427]=[[Reduces all damage taken by an additional 1.5%.]],
[712428]=[[Reduces all damage taken by an additional 1.5%.]],
[712429]=[[King's Advance]],
[712430]=[[HP & ATK & DEF +20%, Speed +40]],
[712501]=[[Rose's Watch]],
[712502]='Daphne',
[712503]=[[Daphne, the Rose's Watch, is the corrupted and fallen symbol of love.]],
[712504]=[[She was born a witness to beautiful love, but its rivers and mountains have been withered and broken by the ravages of time. The Lord of Hell plucked the "Eternal Rose" from the Dreamland as a present for his beloved and then tossed it into the Mortal Ocean, shattering a once solemn promise into broken red petals drifting soullessly in the water. All vows, sweet whispers, and tender words were consumed by flames, engulfing a torn heart in pitch-black sorrow.
Love now is not the eternal light but rather a dying flame in the wind, with the countdown to its end beginning from the moment of its birth. The forsaken Eternal Rose ultimately transformed into Daphne, the Rose's Watch. She is both the cherished token presented by the Lord of Hell to his love and what was cast away once love was gone. As the symbol of love, she spreads deep-seated resentment and sorrow, weaving a tapestry of heart-wrenching pain.
Encountering, budding, growing, and then withering. The more brutal the struggle of withering and the more painful the end, the greater her joy. This joy follows her as she traverses the world as the writer of ever-darker stories.]],
[712505]=[[Daphne is the mastermind behind the dark sides of stories, leading people towards heart-wrenching conclusions while she laughs wildly, turning her thoughts to her next masterpiece.
She is the gift from a prince to a princess, transforming the latter into a statue of stone; she is the dried-up bookmark in a poet's final chapter, marking a life of solitude; she is the flower that entices a girl to pause, only for her to miss out on love forever; she is the witch who steals a mermaid's beauty, leaving her to melt into the seas... One masterpiece after another became deeply engraved in the world, carried by the breeze across time and space. And Daphne, savoring the resentment and sorrow roused, blossoms ever more brilliantly.
Daphne takes pleasure in crafting tragedies and feeds on their power. Beyond these lies an even greater ambition: Daphne has long held an intense hatred deep within against the Lord of Hell who betrayed his love, discarded his token, and allowed the Eternal Rose to lose its purity and fall into corruption. She vows to make him suffer the sting of betrayal and regret.]],
[712506]=[[In her quest to find the Lord of Hell, Daphne naturally joined the Nightfall Legion. She silenced all questioning voices by obliterating a key stronghold of the United Kingdom with an effortless tragic love story created by a few fallen leaves. She quickly rose to become one of the Nightfall Legion's "Four Shadows," taking on the new name "Heart", representing broken love.
No one, including the Supreme Commander Sparta, knows the full extent of the once Eternal Rose's plans. Yet, rumors abound within the Nightfall Legion: from the royal family of the United Kingdom to the chieftains of the prairie tribes, and even the leaders of the Sage's Academy, Daphne's presence is pervasive. She lurks in the shadows, subtly tugging at the heartstrings of countless key figures, waiting for the day when her grand design is complete and her tragic symphony can be played.]],
[712507]=[[Coming soon!]],
[712508]=[[Tragic Finale]],
[712509]=[[Deals {%s1} of ATK as Magic DMG to a random enemy.]],
[712510]=[[Deals an additional 30% Magic DMG.]],
[712511]=[[Deals an additional 70% Magic DMG.]],
[712512]=[[Deals an additional 120% Magic DMG.]],
[712513]=[[Deals an additional 185% Magic DMG.]],
[712514]=[[Deals an additional 270% Magic DMG.]],
[712515]=[[First Bloom]],
[712516]=[[Deals {%s1} of ATK as Magic DMG (prioritizing back-row enemies).]],
[712517]=[[Deals an additional 30% Magic DMG.]],
[712518]=[[Deals an additional 70% Magic DMG.]],
[712519]=[[Deals an additional 120% Magic DMG.]],
[712520]=[[Deals an additional 185% Magic DMG.]],
[712521]=[[Deals an additional 270% Magic DMG.]],
[712522]=[[Sweet Blade]],
[712523]=[[During battle, increases the Magic DMG you deal by {%s1}.]],
[712524]=[[Increases Magic DMG dealt by an additional 3%.]],
[712525]=[[Increases Magic DMG dealt by an additional 3%.]],
[712526]=[[Increases Magic DMG dealt by an additional 3%.]],
[712527]=[[Increases Magic DMG dealt by an additional 3%.]],
[712528]=[[Increases Magic DMG dealt by an additional 3%.]],
[712529]=[[Love Exchange]],
[712530]=[[HP & ATK & DEF +20%, Speed +40]],
[712601]=[[Cursed Soul of the Dark Tome]],
[712602]='Belial',
[712603]=[[An otherworldly creature summoned through a grimoire.]],
[712604]=[[For half a century, a legend has been passed down in the Summoning Department of the Sage's Academy: In the Summoning section of the Library, there is a book called the "Book of Malice," left behind by a senior many years ago. This book records powerful summoning magic, but its pages, words, and symbols together form an exquisitely intricate summoning circle. When the reader finishes the last page, the circle is completed, and the reader is drawn into the book as a summoned being, with their soul instantly stripped away to become a servant in another world.
The infamous book was allegedly responsible for the disappearances of 18 Summoning students over the past fifty years. As a result, the Academy sealed off the Summoning section and conducted a thorough investigation that lasted three full years. However, the legend of the "Book of Malice" has never died. Even after the reopening, daring students continued to search through the 47,835,700 books in the Summoning section every year, just to get a glimpse of the truth.
Perhaps the Academy's efforts paid off, as no student has gone missing ever since. Each year, someone would claim to have found the "Book of Malice", but it always turned out to be an ordinary book of summoning magic.
Where did the Book of Malice go? Did the Academy find and destroy it? What happened to the missing students? Or is the legend nothing more than a made-up story? These questions have been the talk of the Academy for generations of students, and the book itself is listed among the Ten Legends of the Sage's Academy. Perhaps its truth will remain forever hidden, or perhaps its revealing is just a moment away...]],
[712605]=[[58 years ago, Summoning section of the Library, Sage's Academy.
"Are you kidding me? What use are these scrawny arms? How could you possibly be worthy to serve me, the great Dark Summoner, Deryn? A total disgrace. If you weren't the property of the Academy, I'd have torn you into pieces by now… Never mind, I'll just hide you somewhere else. Let's see who's the "lucky" one to stumble upon you, and that would be so much fun!"
…
Even Deryn, the summoner himself, never noticed the true power of the otherworldly creature he summoned through the dark arts using that grimoire. However, by a strange twist of fate, the Dark Summoner returned to the Academy fifty-eight years after his graduation, under orders from the Nightfall Legion to steal the shards of "Nirvana Flame". There, he felt a surge of dark energy emanating from the very book he had left behind. Driven by intense curiosity, he decided to sneak into the Summoning section of the Library. Despite the sealing magic applied, the chaos caused by the Nightfall Legion's invasion provided Deryn the distraction he needed to lay his hands on the magic book.
After awakening the otherworldly creature in the book with his summoning magic, he was greeted by deep-purple, bulky arms, and demonic eyes flaring with dark flames under a wizard hat.
"It's...you..." The voice sounded like the scraping of iron, hoarse yet resonant at the same time. Otherworldly power instantly filled the room, creating an oppressive tension that enveloped Deryn. The summoning gem on his chest flickered, but he realized to his horror that he had lost all connection to his summoned creatures.
"My…master."
The colossal hands reached out toward Deryn, as if to embrace the master he had been "missing" for decades.
But then, the hands clenched tightly, crushing his body with a grip.]],
[712606]=[[Belial could barely remember what his so-called "Otherworld" even looked like. The last clear memory he had was his parting from his family as a child when a powerful force wrenched him away. The searing pain tore through his body, as if he were being ripped apart. When he came to, his home had gone. Before him stood a strange man, murmuring to himself, "Are you kidding me..."
Then, Belial was sealed within a closed magical book, plunged into an endless abyss of darkness and solitude. Time passed, and thanks to his innate ability to absorb magic and the book's naturally formed magic circle, he managed to devour the power of over a dozen readers. But that wasn't enough to free him from his prison. Alone in the darkness, he could only wait for the "master" who had brought him to this world, one who may or may not ever return...
Utter despair tore at Belial's soul, and what sustained him through the infinite darkness and solitude was his burning desire for revenge.
Perhaps fate had taken pity on him. One day, he finally sensed the presence of his master. Suppressing his overwhelming rage and a rush of exhilaration, he unleashed every ounce of his power, desperate to draw that man's attention.
He's here! He's here! He's here! He spoke the unsealing incantation. He infused the book with his magic!
It's time! It's time! It's time! He extended his arms. He embraced him tightly!
The "master" Deryn now sleeps eternally within the Summoning section of the Library, in the embrace of his *summoned creature*.
Summoned creature? Summoned creature? Summoned creature? No! No! No! It shouldn't exist!
It shouldn't exist!
It shouldn't exist!
…
The name of the Dark Summoner Deryn was erased from the Nightfall Legion's roster. In his place emerged the otherworldly entity who called himself "Cursed Soul." The lone wolf's whereabouts were elusive, with a seemingly fractured mind. Only one thing was certain: he had declared a singular, chilling purpose: to hunt down every summoner in existence and exact his revenge.]],
[712607]=[[Coming soon!]],
[712608]=[[Malice Curse]],
[712609]=[[Deals {%s1} of ATK as Physical DMG to the enemy with the lowest current HP.]],
[712610]=[[Deals an additional 30% Physical DMG.]],
[712611]=[[Deals an additional 70% Physical DMG.]],
[712612]=[[Deals an additional 120% Physical DMG.]],
[712613]=[[Deals an additional 185% Physical DMG.]],
[712614]=[[Deals an additional 270% Physical DMG.]],
[712615]=[[Otherworld Distortion]],
[712616]=[[Deals {%s1} of ATK as Physical DMG to 3 random enemy units.]],
[712617]=[[Deals an additional 30% Physical DMG.]],
[712618]=[[Increases your ATK by 3% until the end of battle.]],
[712619]=[[Deals an additional 70% Physical DMG.]],
[712620]=[[Increases your ATK by 6% until the end of battle.]],
[712621]=[[Deals an additional 120% Physical DMG.]],
[712622]=[[Endless Trepidation]],
[712623]=[[During battle, increases your CRIT Rate by {%s1}.]],
[712624]=[[Increases CRIT Rate by an additional 3%.]],
[712625]=[[Increases CRIT Rate by an additional 3%.]],
[712626]=[[Increases CRIT Rate by an additional 3%.]],
[712627]=[[Increases CRIT Rate by an additional 3%.]],
[712628]=[[Increases CRIT Rate by an additional 3%.]],
[712629]=[[Offering Grimoire]],
[712630]=[[HP & ATK & DEF +20%, Speed +40]],
[712701]=[[Spectral Rakkhasa]],
[712702]='Mirana',
[712703]=[[A mysterious witch that can control souls.]],
[712704]=[[As the prairie winds howled through the desert, the hottest season of the year arrived. It was against this fiery backdrop that destiny brought Mirana to gaze upon Crystal as the girl was pursuing a huge cluster of tumbleweeds. The merciless sun beat down upon her, drenching her in perspiration, and her futile attempts to keep pace with the wind left her in disarray. It was Mirana's helping hand that enabled them to stop the mischievous tumbleweeds.
Intrigued by the orcish maiden's actions, Mirana's mind swirled with curiosity. With an enigmatic wink, Crystal claimed that she had the power to glimpse into souls. She saw the soul of a poor gerbil being wrapped up by a tumbleweed and gave tireless chase just to save them.
On hearing this, Mirana showed great curiosity and admiration, which deepened further when she learned the purpose of Crystal's journey. Mirana immediately expressed her willingness to follow Crystal and work together to save the souls of the world. Initially, Crystal was reluctant to have a follower, but she couldn't resist and finally agreed.
But for Mirana, who couldn't see souls and was also quite limited in what she could do, her main role was to guide Crystal, who was new to the desert. During the journey, there were moments when Crystal would suddenly leap up, sensing the presence of a soul. At these times, Mirana would look around in bewilderment, curious about what Crystal had seen and heard.
Life continued like this until one fateful afternoon. Crystal, who had heard the desperate pleas for help from the souls trapped in the quicksand, remained unaware of the impending danger looming over her on a seemingly sunny day...]],
[712705]=[[When Crystal's legs sank into the quicksand, causing her to lose her balance, she accidentally dropped the soul lamp. The souls stored within it scattered out, wailing in panic. Crystal implored Mirana to assist her in re-collecting the souls. Though Mirana couldn't see them, Crystal expressed that she would help guide her.

Mirana readily agreed, eager to help. But as Crystal prepared to reveal the location of each soul, she noticed Mirana walking directly toward the soul of a young purple-winged dragon before she could utter a word.

Mirana tightly gripped the neck of the purple-winged dragon's soul, her clear eyes now tainted with piercing greed. Crystal felt an eerie chill creep down her spine amidst the scorching heat of the desert.

"She lied to me! She can see souls!" Under Crystal's horrified gaze, a massive magic carpet materialized in the air, with a chilling demonic totem standing behind it. Mirana, seemingly familiar with this sight, flashed a smile at Crystal before leaping onto the carpet, clutching the soul of the dragon.

"Argh!” The soul of the purple-winged dragon seemed to have a strong sense of foreboding.

"Enjoy, Spectral Fiend, this fine feast that I prepared for you!" Mirana outstretched her arms wide, casting a condescending gaze upon the souls attempting to flee. As she did, the once-empty eyes of the Demon Totem behind her ignited with eerie, ghostly blue flames.

Mirana turned her gaze toward Crystal and spoke, "As for you, my dear sister, the Spectral Fiend has an appetite only for souls, not you. Once it finishes its feast, I shall spare you. I must express my gratitude for your efforts in collecting so many... It has saved me a great deal of time."

Mirana, along with her magic carpet totem, soared high into the sky. The scattered souls ignited with ghostly blue flames, and as Mirana's laughter echoed, they dissipated like smoke into the surging quicksand.]],
[712706]=[[Within the esteemed Mihia family, the youngest Demoness, Mirana, was hailed as a prodigious genius, unrivaled in talent for over a century. Their ancestral power derived from a sinister practice — hunting souls and feeding them to their demonic totems. Mirana, like her predecessors, thrived on this macabre sustenance. Yet, even though their dark magic possessed boundless growth and cataclysmic might, the family faced an insurmountable obstacle — they had no means of locating souls.
When Mirana realized Crystal had the ability to sense souls, a surge of excitement ignited within her. Concealing her elation, she swiftly devised a plan to exploit Crystal's gift for her own gains. Deceiving Crystal with calculated precision, Mirana lured her into the quicksand and devoured the souls in one fell swoop. Through consuming these souls, Mirana's demonic power grew immensely, solidifying her position within the family and elevating her status to new heights.
Joining the Nightfall Legion seemed Mirana's inevitable path, facilitated by her family's esteemed recommendation. Swiftly ascending the ranks, she emerged as one of the Legion's commanders, forming a close bond with the enigmatic Soul Witch, Celina. Through their union, Mirana's powers grew increasingly formidable.
"Dear Crystal, are you still saving souls with your efforts?" Mirana sometimes thought of that simple orcish maiden. Though it would have been better to coerce the orc into helping herself, or even her family, in locating souls, why had she set her free at that time?
A faint intuition existed in Mirana's mind that they might meet again someday. However, the nature of that meeting remained shrouded in uncertainty. Would it be as friends or enemies...?]],
[712707]=[[Coming soon!]],
[712708]='Ceremony',
[712709]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712710]=[[Deals an additional 30% Physical DMG.]],
[712711]=[[Deals an additional 70% Physical DMG.]],
[712712]=[[Deals an additional 120% Physical DMG.]],
[712713]=[[Deals an additional 185% Physical DMG.]],
[712714]=[[Deals an additional 270% Physical DMG.]],
[712715]=[[Resentful Chorus]],
[712716]=[[Deals {%s1} of ATK as Physical DMG to all enemies.]],
[712717]=[[Deals an additional 30% Physical DMG.]],
[712718]=[[Deals an additional 70% Physical DMG.]],
[712719]=[[Deals an additional 120% Physical DMG.]],
[712720]=[[Deals an additional 185% Physical DMG.]],
[712721]=[[Deals an additional 270% Physical DMG.]],
[712722]=[[Soul Drain]],
[712723]=[[During battle, increases the Physical DMG you deal by {%s1}.]],
[712724]=[[Increases Physical DMG dealt by an additional 3%.]],
[712725]=[[Increases Physical DMG dealt by an additional 3%.]],
[712726]=[[Increases Physical DMG dealt by an additional 3%.]],
[712727]=[[Increases Physical DMG dealt by an additional 3%.]],
[712728]=[[Increases Physical DMG dealt by an additional 3%.]],
[712729]='Despair',
[712730]=[[HP & ATK & DEF +20%, Speed +40]],
[712801]=[[Howling Wolf King]],
[712802]='Aaron',
[712803]=[[A chainsaw maniac who uses a chainsaw to absorb energy.]],
[712804]=[[<color=#FFFFFF00>aaaa</color>Of the Beastkin, Wolfkin maintain the habits of an ordinary wolf pack. Rigidly hierarchical, they stalk the mountain forests in packs deep in the night. At this moment, however, a Wolfkin with a festering body is gnawing at its prey alone. His fur is messy, and scarlet saliva streams slowly from his mouth. If anyone sees him, his crimson eyes are sure to give them a fright.]],
[712805]=[[<color=#FFFFFF00>aaaa</color>Aaron is a regular Wolfkin who often went hunting with his fellow wolves. One night under the light of the full moon, he felt his body burn hot, like a raging power would burst out from his body. He lost his self-awareness and fought his own kind nonstop. When he awoke the next day, he had no idea what had happened to him. Nonetheless, his fellow clansmen expelled him from the Wolfkin territory, and he wandered alone in the mountains from then on.]],
[712806]=[[<color=#FFFFFF00>aaaa</color>As Aaron experienced more and more full-moon rampages, he slowly lost his mind and came to be controlled by his primitive, wild instincts. Little by little, a hair-raising tale of a werewolf has spread among the travelers passing through the forest.]],
[712807]=[[Coming soon!]],
[712808]=[[Mesmer Talon]],
[712809]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[712810]=[[Deals an additional 20% Magic DMG.]],
[712811]=[[Deals an additional 45% Magic DMG.]],
[712812]=[[Deals an additional 70% Magic DMG.]],
[712813]=[[Deals an additional 100% Magic DMG.]],
[712814]=[[Deals an additional 150% Magic DMG.]],
[712815]=[[Scar of Misfortune]],
[712816]=[[Reduces all damage from monsters taken by front-row allies by {%s1} for 1 turn.]],
[712817]=[[Reduces damage taken from monsters by an additional 2%.]],
[712818]=[[Reduces damage taken from monsters by an additional 2%.]],
[712819]=[[Reduces damage taken from monsters by an additional 2%.]],
[712820]=[[Duration extended to 2 turns.]],
[712821]=[[Reduces damage taken from monsters by an additional 2%.]],
[712822]=[[Lupine Nature]],
[712823]=[[During battle, reduces all monster damage you take by {%s1}.]],
[712824]=[[Reduces damage taken from monsters by an additional 4%.]],
[712825]=[[Reduces damage taken from monsters by an additional 4%.]],
[712826]=[[Reduces damage taken from monsters by an additional 4%.]],
[712827]=[[Reduces damage taken from monsters by an additional 4%.]],
[712828]=[[Reduces damage taken from monsters by an additional 4%.]],
[712829]=[[Ravenous Pack]],
[712830]=[[HP & ATK & DEF +10%]],
[712901]=[[Enchanting Witch]],
[712902]='Reina',
[712903]=[[A Beast King of Hell that emerged from the Abyss.]],
[712904]=[[<color=#FFFFFF00>aaaa</color>Late one night, a woman with an eye-catching figure was jogging briskly down a littered street. She seemed anxious, but there was no avoiding this notoriously shady back street. Soon enough, a pack of delinquents appeared around the corner, clearly looking for trouble.]],
[712905]=[[<color=#FFFFFF00>aaaa</color>Seeing her, they came to an abrupt stop, the corners of their mouths curling into smirks. But then, a pack of imps descended from the sky. Before the delinquents could even react, they fell to the ground unconscious. With that, the woman exposed her true form. Her skin turned red, and devil horns sprouted from her head. Her devilish tail swayed gently, as if to express her satisfaction with the night's "harvest".]],
[712906]=[[<color=#FFFFFF00>aaaa</color>An ancient, fiendish succubus, Reina can summon imps from Hell to steal the soul of any living creature and use its energy to strengthen her malevolent magic.]],
[712907]=[[Coming soon!]],
[712908]=[[Explosive Demon]],
[712909]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[712910]=[[Deals an additional 20% Physical DMG.]],
[712911]=[[Deals an additional 45% Physical DMG.]],
[712912]=[[Deals an additional 70% Physical DMG.]],
[712913]=[[Deals an additional 100% Physical DMG.]],
[712914]=[[Deals an additional 150% Physical DMG.]],
[712915]=[[Demonic Sacrifice]],
[712916]=[[Performs 5 attacks, each dealing {%s1} physical damage to a random enemy unit.]],
[712917]=[[Deals an additional 20% Physical DMG.]],
[712918]=[[Attack limit increased to 7 times.]],
[712919]=[[Deals an additional 45% Physical DMG.]],
[712920]=[[Attack limit increased to 9 times.]],
[712921]=[[Deals an additional 70% Physical DMG.]],
[712922]=[[Devil's Embrace]],
[712923]=[[During battle, increases all damage you deal to monsters by {%s1}.]],
[712924]=[[Increases damage dealt to monsters by an additional 4%.]],
[712925]=[[Increases damage dealt to monsters by an additional 4%.]],
[712926]=[[Increases damage dealt to monsters by an additional 4%.]],
[712927]=[[Increases damage dealt to monsters by an additional 4%.]],
[712928]=[[Increases damage dealt to monsters by an additional 4%.]],
[712929]=[[Ward Evil]],
[712930]=[[HP & ATK & DEF +10%]],
[713001]=[[Deep Terror]],
[713002]='Cthylla',
[713003]=[[The Deep Terror who can summon tens of thousands of venomous snakes with a wave of her staff.]],
[713004]=[[<color=#FFFFFF00>aaaa</color>"Oh, great Lord of R'lyeh, god of the deep, have you ever listened to our voices and given us the hope to continue living?" An old man mutters to himself while staring at the deep sea in front of him. The villagers from the fishing village believe that there is a god in the sea who rules everything in the water, and humans must regularly sacrifice cattle to the god to gain the god's blessing.]],
[713005]=[[<color=#FFFFFF00>aaaa</color>However, it's been a long time since this "god" showed himself. The last time he was glimpsed by a human was already several decades ago. Without the god's blessing, the fishing village has fallen into decline as its annual fishing yields increasingly diminish. The old man, having lost hope, leaves the village.]],
[713006]=[[<color=#FFFFFF00>aaaa</color>A while later, an area on the surface of the water seemingly begins to boil. Then, several huge black tentacles protrude from the water and slowly reach for the shore. They drip with a dark liquid that slowly dyes the sea black. Above the tentacles appears to be a woman wearing a dress and hiding her face behind a mysterious mask. She "walks" from the sea onto land. Cthylla is the child of the deep sea's ruler and a human. No one knows why she's joining the war in the world.]],
[713007]=[[Coming soon!]],
[713008]=[[Soul Trial]],
[713009]=[[Deals {%s1} of ATK as Magic DMG to a single enemy.]],
[713010]=[[Deals an additional 20% Magic DMG.]],
[713011]=[[Deals an additional 45% Magic DMG.]],
[713012]=[[Deals an additional 70% Magic DMG.]],
[713013]=[[Deals an additional 100% Magic DMG.]],
[713014]=[[Deals an additional 150% Magic DMG.]],
[713015]=[[Soul Bash]],
[713016]=[[Deals {%s1} of ATK as Magic DMG to 2 enemy units.]],
[713017]=[[Deals an additional 20% Magic DMG.]],
[713018]=[[Reduces the target's DEF by 10% for 1 turn.]],
[713019]=[[Deals an additional 45% Magic DMG.]],
[713020]=[[Reduces the target's DEF by 15% for 1 turn.]],
[713021]=[[Deals an additional 70% Magic DMG.]],
[713022]=[[Tentacle Strike]],
[713023]=[[During battle, increases your ATK by {%s1}.]],
[713024]=[[Increases ATK by an additional 3%.]],
[713025]=[[Increases ATK by an additional 3%.]],
[713026]=[[Increases ATK by an additional 3%.]],
[713027]=[[Increases ATK by an additional 3%.]],
[713028]=[[Increases ATK by an additional 3%.]],
[713029]=[[Harsh Discipline]],
[713030]=[[HP & ATK & DEF +10%]],
[713101]=[[Tekken Chief]],
[713102]='Harold',
[713103]=[[A Minnesota cyborg with the power of a fierce beast, he stands firmly at the top of the food chain.]],
[713104]=[[<color=#FFFFFF00>aaaa</color>Among the Nightfall, there's an insane beast that no one wants to face. He wears a terrifying biochemical mask and has multiples catheters continuously feeding a bright red liquid into his body. Every time he receives this liquid, he goes berserk, and his strength seems to be greatly enhanced.]],
[713105]=[[<color=#FFFFFF00>aaaa</color>In addition to supplying him with a steady stream of drug-induced stimulation, the Alchemy Organization also transformed his body into a weapon and his right hand into a connected hammer, turning him into a war machine.]],
[713106]=[[<color=#FFFFFF00>aaaa</color>An experimental subject of the Dark Alchemy Organization, Bane was injected with the bodily fluids of the Void descendants. After receiving the injection, his body underwent extreme mutations. He became extremely powerful and invulnerable to weapons, but he also lost his mind and went completely berserk. Not only that, the drug must now be injected directly into his brain through a catheter every 12 hours, or he will die of exhaustion.]],
[713107]=[[Coming soon!]],
[713108]=[[Ruthless Fist]],
[713109]=[[Deals {%s1} of ATK as Physical DMG to a single enemy.]],
[713110]=[[Deals an additional 15% Physical DMG.]],
[713111]=[[Deals an additional 30% Physical DMG.]],
[713112]=[[Deals an additional 50% Physical DMG.]],
[713113]=[[Deals an additional 75% Physical DMG.]],
[713114]=[[Deals an additional 100% Physical DMG.]],
[713115]=[[Emergency Restoration]],
[713116]=[[Deals {%s1} Physical DMG to 2 enemy units.]],
[713117]=[[Deals an additional 15% Physical DMG.]],
[713118]=[[Increases the number of targets to 3.]],
[713119]=[[Deals an additional 30% Physical DMG.]],
[713120]=[[Deals an additional 50% Physical DMG.]],
[713121]=[[Deals an additional 75% Physical DMG.]],
[713122]=[[Fury Stance]],
[713123]=[[HP & ATK & DEF +20%]],
[713124]=[[Effect increased by an additional {%s1}%.]],
[713125]=[[Effect increased by an additional {%s1}%.]],
[713126]=[[Effect increased by an additional {%s1}%.]],
[713127]=[[Effect increased by an additional {%s1}%.]],
[713128]=[[Effect increased by an additional {%s1}%.]],
[719001]='Zombie',
[719002]='Zombie',
[719003]=[[Miner Zombie]],
[719004]=[[Violent Zombie]],
[719005]=[[Two-headed ogre]],
[719006]=[[Butcher Zombie]],
[719007]='Skeleton',
[719008]=[[Undead Dragon]],
[719101]=[[Dragon Knight]],
[720001]='Gold',
[720002]='Diamond',
[720003]=[[Hero EXP]],
[720005]=[[Union Contribution]],
[720006]=[[10 Stamina]],
[720007]=[[50 Stamina]],
[720008]=[[VIP EXP]],
[720009]=[[Energy Crystal]],
[720010]=[[1,000 Energy Crystals]],
[720011]=[[Protection Medal]],
[720012]=[[Trial Coin]],
[720013]=[[War Merits]],
[720014]=[[10K War Merits]],
[720025]=[[Red Diamond]],
[720026]=[[Activity Point]],
[720034]='Key',
[720035]='Wheat',
[720036]='Iron',
[720037]=[[Universal Stamina]],
[720040]=[[Glory Medal]],
[720041]=[[Novice Arena Ticket]],
[720101]=[[Hero Recruitment Voucher]],
[720102]=[[Survivor Recruitment Voucher]],
[720103]=[[Infinity Chest]],
[720104]=[[Rename Card]],
[720105]=[[Recharge EXP]],
[720106]=[[Permanent privilege: Custom avatar]],
[720107]=[[Mystic Beast: Borias]],
[720109]=[[Trade Wagon]],
[720311]=[[Lv. 1 Troop]],
[720312]=[[Lv. 2 Troop]],
[720313]=[[Lv. 3 Troop]],
[720314]=[[Lv. 4 Troop]],
[720315]=[[Lv. 5 Troop]],
[720316]=[[Lv. 6 Troop]],
[720317]=[[Lv. 7 Troop]],
[720318]=[[Lv. 8 Troop]],
[720319]=[[Lv. 9 Troop]],
[720320]=[[Lv. 10 Troop]],
[720321]='Soldier',
[720500]=[[Advanced Relocation]],
[720501]=[[Union Relocation]],
[720502]=[[Random Relocation]],
[720521]=[[8-Hour Shield]],
[720522]=[[12-Hour Shield]],
[720523]=[[24-Hour Shield]],
[720524]=[[3-Day Shield]],
[720541]=[[Advanced March Speed Up]],
[720542]=[[Standard March Speed Up]],
[720601]=[[5 Diamonds]],
[720602]=[[10 Diamonds]],
[720603]=[[20 Diamonds]],
[720604]=[[50 Diamonds]],
[720605]=[[100 Diamonds]],
[720606]=[[5K Diamonds]],
[720607]=[[10K Diamonds]],
[720608]=[[500 Diamonds]],
[720650]=[[10 VIP EXP]],
[720651]=[[50 VIP EXP]],
[720652]=[[100 VIP EXP]],
[720653]=[[500 VIP EXP]],
[720654]=[[1,000 VIP EXP]],
[720655]=[[2,500 VIP EXP]],
[720671]=[[VIP Activation (24H)]],
[720672]=[[VIP Activation (7D)]],
[720673]=[[VIP Activation (30D)]],
[720701]=[[1K Wheat]],
[720702]=[[1K Iron]],
[720703]=[[600 Gold]],
[720704]=[[5K Wheat]],
[720705]=[[5K Iron]],
[720706]=[[3K Gold]],
[720707]=[[10K Wheat]],
[720708]=[[10K Iron]],
[720709]=[[6K Gold]],
[720710]=[[30K Wheat]],
[720711]=[[30K Iron]],
[720712]=[[18K Gold]],
[720713]=[[50K Wheat]],
[720714]=[[50K Iron]],
[720715]=[[30K Gold]],
[720716]=[[500K Wheat]],
[720717]=[[500K Iron]],
[720718]=[[300K Gold]],
[720719]=[[1M Wheat]],
[720720]=[[1M Iron]],
[720721]=[[1M Gold]],
[720751]=[[Excellent Resource Selection Chest]],
[720752]=[[Epic Resource Selection Chest]],
[720753]=[[Legendary Resource Selection Chest]],
[720754]=[[Resource Chest]],
[720761]=[[Wheat Level Chest]],
[720762]=[[Wheat Level Chest]],
[720763]=[[Wheat Level Chest]],
[720764]=[[Wheat Level Chest]],
[720765]=[[Iron Level Chest]],
[720766]=[[Iron Level Chest]],
[720767]=[[Iron Level Chest]],
[720768]=[[Iron Level Chest]],
[720769]=[[Gold Level Chest]],
[720770]=[[Gold Level Chest]],
[720771]=[[Gold Level Chest]],
[720772]=[[Gold Level Chest]],
[720801]=[[1-Minute Speed Up]],
[720802]=[[5-Minute Speed Up]],
[720803]=[[1-Hour Speed Up]],
[720804]=[[3-Hour Speed Up]],
[720805]=[[8-Hour Speed Up]],
[720806]=[[15-Minute Speed Up]],
[720811]=[[1-Minute Building Speed Up]],
[720812]=[[5-Minute Building Speed Up]],
[720813]=[[1-Hour Building Speed Up]],
[720814]=[[3-Hour Building Speed Up]],
[720815]=[[8-Hour Building Speed Up]],
[720816]=[[15-Minute Building Speed Up]],
[720821]=[[1-Minute Technology Speed Up]],
[720822]=[[5-Minute Technology Speed Up]],
[720823]=[[1-Hour Technology Speed Up]],
[720824]=[[3-Hour Technology Speed Up]],
[720825]=[[8-Hour Technology Speed Up]],
[720826]=[[15-Minute Technology Speed Up]],
[720831]=[[1-Minute Training Speed Up]],
[720832]=[[5-Minute Training Speed Up]],
[720833]=[[1-Hour Training Speed Up]],
[720834]=[[3-Hour Training Speed Up]],
[720835]=[[8-Hour Training Speed Up]],
[720841]=[[1-Minute Healing Speed Up]],
[720842]=[[5-Minute Healing Speed Up]],
[720843]=[[1-Hour Healing Speed Up]],
[720844]=[[3-Hour Healing Speed Up]],
[720845]=[[8-Hour Healing Speed Up]],
[720850]=[[5-Minute Speed Up Item Chest]],
[720900]=[[Elite Zombie Loot]],
[720901]=[[Elite Zombie Loot]],
[720902]=[[Elite Zombie Loot]],
[720903]=[[Elite Zombie Loot]],
[720904]=[[Elite Zombie Loot]],
[720905]=[[Elite Zombie Loot]],
[720906]=[[Elite Zombie Loot]],
[720907]=[[Elite Zombie Loot]],
[720908]=[[Elite Zombie Loot]],
[720909]=[[Elite Zombie Loot]],
[720910]=[[Elite Zombie Loot]],
[720911]=[[Elite Zombie Loot]],
[720912]=[[Elite Zombie Loot]],
[720913]=[[Elite Zombie Loot]],
[720914]=[[Elite Zombie Loot]],
[720915]=[[Elite Zombie Loot]],
[720916]=[[Elite Zombie Loot]],
[720917]=[[Elite Zombie Loot]],
[720918]=[[Elite Zombie Loot]],
[720919]=[[Elite Zombie Loot]],
[720920]=[[Elite Zombie Loot]],
[720950]=[[Wandering Overlord Loot]],
[720951]=[[Wandering Overlord Loot]],
[720952]=[[Wandering Overlord Loot]],
[720953]=[[Wandering Overlord Loot]],
[720954]=[[Wandering Overlord Loot]],
[720955]=[[Wandering Overlord Loot]],
[720956]=[[Wandering Overlord Loot]],
[720957]=[[Wandering Overlord Loot]],
[720958]=[[Wandering Overlord Loot]],
[720959]=[[Wandering Overlord Loot]],
[720960]=[[Wandering Overlord Loot]],
[720961]=[[Wandering Overlord Loot]],
[720962]=[[Wandering Overlord Loot]],
[720963]=[[Wandering Overlord Loot]],
[720964]=[[Wandering Overlord Loot]],
[720965]=[[Wandering Overlord Loot]],
[720966]=[[Wandering Overlord Loot]],
[720967]=[[Wandering Overlord Loot]],
[720968]=[[Wandering Overlord Loot]],
[720969]=[[Wandering Overlord Loot]],
[720970]=[[Wandering Overlord Loot]],
[721001]=[[Common Union Gift]],
[721002]=[[Rare Union Gift]],
[721003]=[[Excellent Union Gift]],
[721004]=[[Epic Union Gift]],
[721005]=[[Legendary Union Gift]],
[721006]=[[Mythic Union Gift]],
[721051]=[[50 Union Contribution]],
[721052]=[[500 Union Contribution]],
[721053]=[[2,500 Union Contribution]],
[721100]=[[Rally Assault Loot]],
[721101]=[[Rally Assault Loot]],
[722003]=[[Hero EXP Chest]],
[722004]=[[Hero EXP Chest]],
[722005]=[[Hero EXP Chest]],
[722100]=[[Skill EXP Book]],
[722101]=[[A Hero Universal Shard]],
[722102]=[[S Hero Universal Shard]],
[722103]=[[S+ Hero Universal Shard]],
[722201]=[[Dragonic Shard]],
[722202]=[[Rexar Shard]],
[722203]=[[Monica Shard]],
[722204]=[[Sera Shard]],
[722205]=[[Yord Shard]],
[722206]=[[Chakiss Shard]],
[722207]=[[Edric Shard]],
[722208]=[[Crystal Shard]],
[722209]=[[Ali Shard]],
[722210]=[[Faerie Shard]],
[722211]=[[Verna Shard]],
[722212]=[[Torun Shard]],
[722213]=[[Moga Shard]],
[722214]=[[Andrew Shard]],
[722215]=[[Valkyr Shard]],
[722216]=[[Fenixia Shard]],
[722217]=[[Sophia Shard]],
[722218]=[[Alvarez Shard]],
[722219]=[[Garuda Shard]],
[722220]=[[Marissa Shard]],
[722221]=[[Denise Shard]],
[722222]=[[Romano Shard]],
[722223]=[[Sparta Shard]],
[722224]=[[Kataras Shard]],
[722225]=[[Daphne Shard]],
[722226]=[[Belial Shard]],
[722227]=[[Mirana Shard]],
[722228]=[[Aaron Shard]],
[722229]=[[Reina Shard]],
[722230]=[[Cthylla Shard]],
[722231]=[[Harold Shard]],
[723001]=[[[Legendary] Destiny Sword]],
[723002]=[[[Legendary] Destiny Armour]],
[723003]=[[[Legendary] Destiny Helmet]],
[723004]=[[[Legendary] Destiny Greaves]],
[723005]=[[Judgment Sword]],
[723006]=[[Judgment Armor]],
[723007]=[[Judgment Helmet]],
[723008]=[[Judgment Boots]],
[723009]=[[Azure Blade]],
[723010]=[[Azure Armor]],
[723011]=[[Azure Helmet]],
[723012]=[[Azure Boots]],
[723013]=[[Iron Dagger]],
[723014]=[[Iron Cuirass]],
[723015]=[[Iron Helmet]],
[723016]=[[Iron Boots]],
[723201]=[[Common Gear Crystal]],
[723202]=[[Rare Gear Crystal]],
[723203]=[[Excellent Gear Crystal]],
[723204]=[[Epic Gear Crystal]],
[723205]=[[Legendary Gear Crystal]],
[723250]='Gear',
[723252]=[[Rare Gear Chest]],
[723253]=[[Excellent Gear Chest]],
[723254]=[[Epic Gear Chest]],
[723256]=[[Legendary Gear Chest]],
[723301]=[[Upgrade Crystal]],
[723302]=[[Legendary Gear Scroll]],
[723303]=[[Mythic Gear Scroll]],
[724000]=[[Mystic Beast EXP Potion]],
[724001]=[[10K Mystic Beast EXP Potion]],
[724002]=[[Mystic Beast Breakthrough Potion]],
[724003]=[[1K Mystic Beast EXP Potion]],
[724051]=[[Lv. 1 Mystic Beast Mark Chest]],
[724052]=[[Lv. 2 Mystic Beast Mark Chest]],
[724053]=[[Lv. 3 Mystic Beast Mark Chest]],
[724054]=[[Lv. 4 Mystic Beast Mark Chest]],
[724055]=[[Lv. 5 Mystic Beast Mark Chest]],
[724056]=[[Lv. 6 Mystic Beast Mark Chest]],
[724057]=[[Lv. 7 Mystic Beast Mark Chest]],
[724058]=[[Lv. 8 Mystic Beast Mark Chest]],
[724059]=[[Lv. 9 Mystic Beast Mark Chest]],
[724060]=[[Lv. 10 Mystic Beast Mark Chest]],
[724061]=[[Lv. 1 Mystic Beast Mark Selection Chest]],
[724062]=[[Lv. 2 Mystic Beast Mark Selection Chest]],
[724063]=[[Lv. 3 Mystic Beast Mark Selection Chest]],
[724064]=[[Lv. 4 Mystic Beast Mark Selection Chest]],
[724065]=[[Lv. 5 Mystic Beast Mark Selection Chest]],
[724066]=[[Lv. 6 Mystic Beast Mark Selection Chest]],
[724067]=[[Lv. 7 Mystic Beast Mark Selection Chest]],
[724068]=[[Lv. 8 Mystic Beast Mark Selection Chest]],
[724069]=[[Lv. 9 Mystic Beast Mark Selection Chest]],
[724070]=[[Lv. 10 Mystic Beast Mark Selection Chest]],
[724101]=[[Mark of Life]],
[724102]=[[Mark of Life]],
[724103]=[[Mark of Life]],
[724104]=[[Mark of Life]],
[724105]=[[Mark of Life]],
[724106]=[[Mark of Life]],
[724107]=[[Mark of Life]],
[724108]=[[Mark of Life]],
[724109]=[[Mark of Life]],
[724110]=[[Mark of Life]],
[724121]=[[Mark of Protection]],
[724122]=[[Mark of Protection]],
[724123]=[[Mark of Protection]],
[724124]=[[Mark of Protection]],
[724125]=[[Mark of Protection]],
[724126]=[[Mark of Protection]],
[724127]=[[Mark of Protection]],
[724128]=[[Mark of Protection]],
[724129]=[[Mark of Protection]],
[724130]=[[Mark of Protection]],
[724141]=[[Mark of Tenacity]],
[724142]=[[Mark of Tenacity]],
[724143]=[[Mark of Tenacity]],
[724144]=[[Mark of Tenacity]],
[724145]=[[Mark of Tenacity]],
[724146]=[[Mark of Tenacity]],
[724147]=[[Mark of Tenacity]],
[724148]=[[Mark of Tenacity]],
[724149]=[[Mark of Tenacity]],
[724150]=[[Mark of Tenacity]],
[724161]=[[Mark of Firestrike]],
[724162]=[[Mark of Firestrike]],
[724163]=[[Mark of Firestrike]],
[724164]=[[Mark of Firestrike]],
[724165]=[[Mark of Firestrike]],
[724166]=[[Mark of Firestrike]],
[724167]=[[Mark of Firestrike]],
[724168]=[[Mark of Firestrike]],
[724169]=[[Mark of Firestrike]],
[724170]=[[Mark of Firestrike]],
[724181]=[[Mark of Calamity]],
[724182]=[[Mark of Calamity]],
[724183]=[[Mark of Calamity]],
[724184]=[[Mark of Calamity]],
[724185]=[[Mark of Calamity]],
[724186]=[[Mark of Calamity]],
[724187]=[[Mark of Calamity]],
[724188]=[[Mark of Calamity]],
[724189]=[[Mark of Calamity]],
[724190]=[[Mark of Calamity]],
[724201]=[[Mark of Plunder]],
[724202]=[[Mark of Plunder]],
[724203]=[[Mark of Plunder]],
[724204]=[[Mark of Plunder]],
[724205]=[[Mark of Plunder]],
[724206]=[[Mark of Plunder]],
[724207]=[[Mark of Plunder]],
[724208]=[[Mark of Plunder]],
[724209]=[[Mark of Plunder]],
[724210]=[[Mark of Plunder]],
[724301]=[[The awakening of the spirit]],
[724302]=[[Claw Edge]],
[724303]=[[Beast Roar]],
[724304]=[[Thick skin and tough armor]],
[724305]=[[God's might descends]],
[724306]=[[Sky Rush]],
[724307]=[[Whisper of Chaos]],
[724308]=[[Aura Protection]],
[724309]=[[Senluo Mengdong-Forest]],
[724310]=[[Thorn Whip-Forest]],
[724311]=[[Vines entwined-forest]],
[724312]=[[Ancient Tree Roots-Forest]],
[724313]=[[High morale - human]],
[724314]=[[Breaking the Formation Charge-Human]],
[724315]=[[Order to Disarm - Humanity]],
[724316]=[[Iron Wall United-Humanity]],
[724317]=[[Shadow Curtain-Dark Night]],
[724318]=[[Soul-Eating Fang-Dark Night]],
[724319]=[[Scream of Fear-Dark Night]],
[724320]=[[Shadow Barrier-Dark Night]],
[724321]=[[Worship of All Souls-Forest]],
[724322]=[[Wild Waves-Forest]],
[724323]=[[Life Siphon-Forest]],
[724324]=[[Immortal Spiritual Root-Forest]],
[724325]=[[Blessing of Heroic Spirits - Human]],
[724326]=[[Sword of Judgment - Human]],
[724327]=[[Forbidden Spell Field-Human]],
[724328]=[[Immortal Monument - Humanity]],
[724329]=[[Eternal Night Falls-Dark Night]],
[724330]=[[Void Annihilation-Dark Night]],
[724331]=[[Eternal Silence-Dark Night]],
[724332]=[[Embrace of the Abyss-Dark Night]],
[724401]=[[R Skill Core Chest]],
[724402]=[[SR Skill Core Chest]],
[724403]=[[SSR Skill Core Chest]],
[724404]=[[UR Skill Core Chest]],
[724405]=[[UR Crystal Core Optional Treasure Box]],
[724406]=[[SSR Crystal Core Optional Treasure Box]],
[724451]=[[Ordinary crystal core materials]],
[724452]=[[Advanced crystal core materials]],
[724453]=[[Divine Beast Advanced Ordinary Beads]],
[724454]=[[Divine Beast Advanced Beads]],
[725000]=[[Universal Decoration Stone]],
[725001]=[[Orange Decoration Chest]],
[725002]=[[Purple Decoration Chest]],
[725003]=[[Blue Decoration Chest]],
[725004]=[[Universal Decoration Stone Lucky Bag]],
[725101]=[[Bronze Greatsword]],
[725102]=[[Bronze Shield]],
[725103]=[[Heroic Battle Banner]],
[725104]=[[Bronze Helmet]],
[725105]=[[Silver Greatsword]],
[725106]=[[Silver Shield]],
[725107]=[[Silver Helmet]],
[725108]=[[Silver Hero Statue]],
[725109]=[[Silver Chalice]],
[725110]=[[Golden Barrel]],
[725111]=[[Golden Disc]],
[725112]=[[Golden Marshal Statue]],
[725113]=[[Golden Helmet]],
[725114]=[[Golden Greatsword]],
[725115]=[[Golden Shield]],
[725116]=[[Opera House]],
[725117]=[[Ancient Colosseum]],
[725118]=[[Noble Mausoleum]],
[725119]=[[Castle Keep]],
[725120]=[[Goddess Sculpture]],
[725121]=[[Gold Obelisk]],
[725122]=[[Master's Hammer]],
[725123]=[[Ancient Sacred Sculpture]],
[725124]=[[Sculpture Mountain]],
[725125]=[[Purple Decoration 6]],
[725126]=[[Purple Decoration 7]],
[725127]=[[Purple Decoration 8]],
[725128]=[[Purple Decoration 9]],
[725129]=[[Blue Decoration 1]],
[725130]=[[Blue Decoration 2]],
[725131]=[[Blue Decoration 3]],
[725132]=[[Blue Decoration 4]],
[725206]=[[Tactical Scarecrow]],
[726000]=[[Survivor Currency]],
[726001]=[[Explorer Laura Shard]],
[726002]=[[Lord Jessica Shard]],
[726003]='Survivor',
[726004]='Survivors',
[726005]='Survivors',
[726006]=[[Survivor Chest]],
[726007]=[[Survivor Chest]],
[726008]=[[Survivor Chest]],
[726011]=[[Farmer William Shard]],
[726012]=[[Farmer Kevin Shard]],
[726013]=[[Farmer Ronald Shard]],
[726014]=[[Farmer Nicholas Shard]],
[726015]=[[Farmer Brandon Shard]],
[726016]=[[Farmer Franklin Shard]],
[726017]=[[Farmer Eric Shard]],
[726018]=[[Farmer Megan Shard]],
[726019]=[[Farmer Jonathan Shard]],
[726020]=[[Farmer Jordan Shard]],
[726021]=[[Farmer Mason Shard]],
[726022]=[[Farmer Jack Shard]],
[726023]=[[Farmer Ernest Shard]],
[726024]=[[Farmer Glenn Shard]],
[726025]=[[Farmer Marvin Shard]],
[726026]=[[Farmer Tiffany Shard]],
[726027]=[[Farmer Simon Shard]],
[726028]=[[Farmer Warren Shard]],
[726029]=[[Farmer Rashid Shard]],
[726030]=[[Farmer Franklin Shard]],
[726031]=[[Farmer Mason Shard]],
[726032]=[[Farmer Jack Shard]],
[726033]=[[Farmer Ernest Shard]],
[726034]=[[Farmer Autumn Shard]],
[726035]=[[Farmer Simon Shard]],
[726036]=[[Farmer Warren Shard]],
[726037]=[[Farmer Rashid Shard]],
[726038]=[[Farmer Franklin Shard]],
[726039]=[[Farmer Mason Shard]],
[726040]=[[Farmer Jack Shard]],
[726041]=[[Farmer Ernest Shard]],
[726042]=[[Farmer Irene Shard]],
[726043]=[[Farmer Marvin Shard]],
[726044]=[[Farmer Simon Shard]],
[726045]=[[Farmer Warren Shard]],
[726046]=[[Farmer Rashid Shard]],
[726047]=[[Granary Manager Julia Shard]],
[726048]=[[Granary Manager Matthew Shard]],
[726049]=[[Granary Manager Carl Shard]],
[726050]=[[Granary Manager Dean Shard]],
[726051]=[[Granary Manager Dawn Shard]],
[726052]=[[Granary Manager Vanessa Shard]],
[726053]=[[Granary Manager Serena Shard]],
[726054]=[[Granary Manager Tyner Shard]],
[726055]=[[Granary Manager Matthew Shard]],
[726056]=[[Granary Manager Carl Shard]],
[726057]=[[Granary Manager Kayla Shard]],
[726058]=[[Granary Manager Helen Shard]],
[726059]=[[Granary Manager Julia Shard]],
[726060]=[[Granary Manager Carl Shard]],
[726061]=[[Granary Manager Dean Shard]],
[726062]=[[Granary Manager Tyner Shard]],
[726063]=[[Granary Manager Dawn Shard]],
[726064]=[[Granary Manager Vanessa Shard]],
[726065]=[[Granary Manager Matthew Shard]],
[726066]=[[Granary Manager Carl Shard]],
[726067]=[[Miner Michael Shard]],
[726068]=[[Miner Charles Shard]],
[726069]=[[Miner Paul Shard]],
[726070]=[[Miner Andrew Shard]],
[726071]=[[Miner Edward Shard]],
[726072]=[[Miner Ryan Shard]],
[726073]=[[Miner Adam Shard]],
[726074]=[[Miner Linda Shard]],
[726075]=[[Miner Susan Shard]],
[726076]=[[Miner Jessica Shard]],
[726077]=[[Miner Michael Shard]],
[726078]=[[Miner Charles Shard]],
[726079]=[[Miner Paul Shard]],
[726080]=[[Miner Andrew Shard]],
[726081]=[[Miner Edward Shard]],
[726082]=[[Miner Ryan Shard]],
[726083]=[[Miner Adam Shard]],
[726084]=[[Miner Sarah Shard]],
[726085]=[[Miner Karen Shard]],
[726086]=[[Miner Betty Shard]],
[726087]=[[Miner Michael Shard]],
[726088]=[[Miner Charles Shard]],
[726089]=[[Miner Paul Shard]],
[726090]=[[Miner Andrew Shard]],
[726091]=[[Miner Edward Shard]],
[726092]=[[Miner Ryan Shard]],
[726093]=[[Miner Adam Shard]],
[726094]=[[Miner Dorothy Shard]],
[726095]=[[Miner Sandra Shard]],
[726096]=[[Miner Deborah Shard]],
[726097]=[[Miner Willy Shard]],
[726098]=[[Miner Albert Shard]],
[726099]=[[Miner Maxwell Shard]],
[726100]=[[Miner Stephanie Shard]],
[726101]=[[Miner Rebecca Shard]],
[726102]=[[Miner Laura Shard]],
[726103]=[[Iron Ore Manager Larry Shard]],
[726104]=[[Iron Ore Manager Carl Shard]],
[726105]=[[Iron Ore Manager Bruce Shard]],
[726106]=[[Iron Ore Manager Lydia Shard]],
[726107]=[[Iron Ore Manager Julia Shard]],
[726108]=[[Iron Ore Manager Dawn Shard]],
[726109]=[[Iron Ore Manager Seth Shard]],
[726110]=[[Iron Ore Manager Silas Shard]],
[726111]=[[Iron Ore Manager Roosevelt Shard]],
[726112]=[[Iron Ore Manager Vanessa Shard]],
[726113]=[[Iron Ore Manager Serena Shard]],
[726114]=[[Iron Ore Manager Kayla Shard]],
[726115]=[[Iron Ore Manager Zachary Shard]],
[726116]=[[Iron Ore Manager Harold Shard]],
[726117]=[[Iron Ore Manager Carl Shard]],
[726118]=[[Iron Ore Manager Helen Shard]],
[726119]=[[Iron Ore Manager Lydia Shard]],
[726120]=[[Iron Ore Manager Julia Shard]],
[726121]=[[Iron Ore Manager Silas Shard]],
[726122]=[[Iron Ore Manager Roosevelt Shard]],
[726123]=[[James the Prospector Shard]],
[726124]=[[Christopher the Prospector Shard]],
[726125]=[[Drake the Prospector Shard]],
[726126]=[[Paul the Prospector Shard]],
[726127]=[[Joshua the Prospector Shard]],
[726128]=[[Willy the Prospector Shard]],
[726129]=[[Zachary the Prospector Shard]],
[726130]=[[Harold the Prospector Shard]],
[726131]=[[Gilbert the Prospector Shard]],
[726132]=[[Julian the Prospector Shard]],
[726133]=[[Omar the Prospector Shard]],
[726134]=[[James the Prospector Shard]],
[726135]=[[Christopher the Prospector Shard]],
[726136]=[[Drake the Prospector Shard]],
[726137]=[[Paul the Prospector Shard]],
[726138]=[[Joshua the Prospector Shard]],
[726139]=[[Willy the Prospector Shard]],
[726140]=[[Zachary the Prospector Shard]],
[726141]=[[Harold the Prospector Shard]],
[726142]=[[Gilbert the Prospector Shard]],
[726143]=[[Julian the Prospector Shard]],
[726144]=[[Omar the Prospector Shard]],
[726145]=[[James the Prospector Shard]],
[726146]=[[Christopher the Prospector Shard]],
[726147]=[[Drake the Prospector Shard]],
[726148]=[[Paul the Prospector Shard]],
[726149]=[[Joshua the Prospector Shard]],
[726150]=[[Willy the Prospector Shard]],
[726151]=[[Zachary the Prospector Shard]],
[726152]=[[Harold the Prospector Shard]],
[726153]=[[Gilbert the Prospector Shard]],
[726154]=[[Julian the Prospector Shard]],
[726155]=[[Omar the Prospector Shard]],
[726156]=[[Drake the Prospector Shard]],
[726157]=[[Paul the Prospector Shard]],
[726158]=[[Joshua the Prospector Shard]],
[726159]=[[Treasury Manager Lydia Shard]],
[726160]=[[Treasury Manager Julia Shard]],
[726161]=[[Treasury Manager Dawn Shard]],
[726162]=[[Treasury Manager John Shard]],
[726163]=[[Treasury Manager Richard Shard]],
[726164]=[[Treasury Manager Donald Shard]],
[726165]=[[Treasury Manager Paul Shard]],
[726166]=[[Treasury Manager Brian Shard]],
[726167]=[[Treasury Manager Eric Shard]],
[726168]=[[Treasury Manager Jimmy Shard]],
[726169]=[[Treasury Manager Vanessa Shard]],
[726170]=[[Treasury Manager Serena Shard]],
[726171]=[[Treasury Manager Kayla Shard]],
[726172]=[[Treasury Manager Zachary Shard]],
[726173]=[[Treasury Manager Harold Shard]],
[726174]=[[Treasury Manager Kyle Shard]],
[726175]=[[Treasury Manager Roger Shard]],
[726176]=[[Treasury Manager Pedro Shard]],
[726177]=[[Treasury Manager Curtis Shard]],
[726178]=[[Treasury Manager Gilbert Shard]],
[726179]=[[Instructor Joseph Shard]],
[726180]=[[Instructor Jeffrey Shard]],
[726181]=[[Instructor Linda Shard]],
[726182]=[[Instructor Sarah Shard]],
[726183]=[[Instructor Betty Shard]],
[726184]=[[Instructor Deborah Shard]],
[726185]=[[Instructor Stephanie Shard]],
[726186]=[[Instructor Steven Shard]],
[726187]=[[Instructor Shirley Shard]],
[726188]=[[Instructor Gregory Shard]],
[726189]=[[Instructor Austin Shard]],
[726190]=[[Instructor Nicole Shard]],
[726191]=[[Instructor Dorothy Shard]],
[726192]=[[Instructor Janet Shard]],
[726193]=[[Instructor Sarah Shard]],
[726194]=[[Instructor Natalie Shard]],
[726195]=[[Instructor Nathan Shard]],
[726196]=[[Instructor Mary Shard]],
[726197]=[[Instructor Walter Shard]],
[726198]=[[Instructor Craig Shard]],
[726199]=[[Instructor Ivy Shard]],
[726200]=[[Instructor Theresa Shard]],
[726201]=[[Instructor Diana Shard]],
[726202]=[[Instructor Ruby Shard]],
[726203]=[[Instructor Naomi Shard]],
[726204]=[[Instructor Antonio Shard]],
[726205]=[[Instructor Isla Shard]],
[726206]=[[Instructor Martin Shard]],
[726207]=[[Instructor Joseph Shard]],
[726208]=[[Instructor Millie Shard]],
[726209]=[[Instructor Josephine Shard]],
[726210]=[[Instructor Lydia Shard]],
[726211]=[[Officer John Shard]],
[726212]=[[Officer Richard Shard]],
[726213]=[[Officer Joseph Shard]],
[726214]=[[Officer Donald Shard]],
[726215]=[[Officer Brian Shard]],
[726216]=[[Officer Jeffrey Shard]],
[726217]=[[Officer Eric Shard]],
[726218]=[[Officer Steven Shard]],
[726219]=[[Officer Gregory Shard]],
[726220]=[[Officer Austin Shard]],
[726221]=[[Officer Jimmy Shard]],
[726222]=[[Officer Nathan Shard]],
[726223]=[[Officer Kyle Shard]],
[726224]=[[Officer Walter Shard]],
[726225]=[[Officer Craig Shard]],
[726226]=[[Officer Antonio Shard]],
[726227]=[[Officer Roger Shard]],
[726228]=[[Officer Pedro Shard]],
[726229]=[[Officer Curtis Shard]],
[726230]=[[Officer Martin Shard]],
[726231]=[[Officer John Shard]],
[726232]=[[Officer Richard Shard]],
[726233]=[[Officer Joseph Shard]],
[726234]=[[Officer Donald Shard]],
[726235]=[[Officer Brian Shard]],
[726236]=[[Officer Jeffrey Shard]],
[726237]=[[Officer Eric Shard]],
[726238]=[[Officer Steven Shard]],
[726239]=[[Officer Gregory Shard]],
[726240]=[[Officer Austin Shard]],
[726241]=[[Officer Jimmy Shard]],
[726242]=[[Officer Nathan Shard]],
[726243]=[[Scholar Patricia Shard]],
[726244]=[[Scholar Linda Shard]],
[726245]=[[Scholar Barbara Shard]],
[726246]=[[Scholar Sarah Shard]],
[726247]=[[Scholar Betty Shard]],
[726248]=[[Scholar Ashley Shard]],
[726249]=[[Scholar Donna Shard]],
[726250]=[[Scholar Michelle Shard]],
[726251]=[[Scholar Deborah Shard]],
[726252]=[[Scholar Robert Shard]],
[726253]=[[Scholar Thomas Shard]],
[726254]=[[Scholar Steven Shard]],
[726255]=[[Scholar George Shard]],
[726256]=[[Scholar Gary Shard]],
[726257]=[[Scholar Stephanie Shard]],
[726258]=[[Scholar Cynthia Shard]],
[726259]=[[Scholar Shirley Shard]],
[726260]=[[Scholar Nicole Shard]],
[726261]=[[Scholar Dorothy Shard]],
[726262]=[[Scholar Scott Shard]],
[726263]=[[Scholar Raymond Shard]],
[726264]=[[Scholar Clarence Shard]],
[726265]=[[Scholar Philip Shard]],
[726266]=[[Scholar Peter Shard]],
[726267]=[[Envoy Patricia Shard]],
[726268]=[[Envoy Jennifer Shard]],
[726269]=[[Envoy Kimberley Shard]],
[726270]=[[Envoy Donna Shard]],
[726271]=[[Envoy David Shard]],
[726272]=[[Envoy Anthony Shard]],
[726273]=[[Envoy Kenneth Shard]],
[726274]=[[Envoy Timothy Shard]],
[726275]=[[Envoy Justin Shard]],
[726276]=[[Envoy Abraham Shard]],
[726277]=[[Envoy Gerald Shard]],
[726278]=[[Envoy Lawrence Shard]],
[726279]=[[Envoy Dylan Shard]],
[726280]=[[Repairman Rebecca Shard]],
[726281]=[[Repairman Michael Shard]]}