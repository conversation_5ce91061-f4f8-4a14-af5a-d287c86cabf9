-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local mq_common_pb=require("mq_common_pb")
module('common_new_pb')


V1M=V(4,"InValid",0,0)
V2M=V(4,"GameGoal",1,1)
V3M=V(4,"TheTowerOfIllusion",2,2)
V4M=V(4,"BraveManTried",3,3)
V5M=V(4,"Arena",4,4)
V6M=V(4,"Ashdungeon",5,5)
V7M=V(4,"BrokenSpaceTime",6,6)
V8M=V(4,"LeagueComp",7,7)
V9M=V(4,"LeagueBoss",8,8)
V10M=V(4,"LeagueTreasureBoss",9,9)
V11M=V(4,"Compete",10,10)
V12M=V(4,"MateMonster",11,11)
V13M=V(4,"Replay",12,12)
V14M=V(4,"Cutscene",13,13)
V15M=V(4,"Maze",14,14)
V16M=V(4,"Friend",15,15)
V17M=V(4,"FactionType1",16,16)
V18M=V(4,"FactionType2",17,17)
V19M=V(4,"FactionType3",18,18)
V20M=V(4,"FactionType4",19,19)
V21M=V(4,"PeakOfTime",20,20)
V22M=V(4,"EquipEctype",21,21)
V23M=V(4,"LeagueWar",22,22)
V24M=V(4,"LeaActivityBoss",23,23)
V25M=V(4,"LeagueBossWar",24,24)
V26M=V(4,"VoidArena",25,25)
V27M=V(4,"TopRace",26,26)
V28M=V(4,"SLG",27,27)
V29M=V(4,"SpaceGap",28,28)
V30M=V(4,"Plot",29,29)
V31M=V(4,"SpaceExplorePirates",30,30)
V32M=V(4,"EquipEctypeFuWen1",31,31)
V33M=V(4,"EquipEctypeFuWen2",32,32)
V34M=V(4,"EquipEctypeFuWen3",33,33)
V35M=V(4,"RookiePlot",34,34)
V36M=V(4,"EquipEctypeFuWen4",35,35)
V37M=V(4,"EquipEctypeFuWen5",36,36)
V38M=V(4,"Legend",37,37)
V39M=V(4,"EquipEctypeZhuanShu",38,38)
V40M=V(4,"BossTrial",39,39)
V41M=V(4,"KillingTower",40,40)
V42M=V(4,"MultiKillingTower",41,41)
V43M=V(4,"WeekendArena",42,42)
V44M=V(4,"ChinaRed",43,43)
V45M=V(4,"STypeAllianceTech",44,44)
V46M=V(4,"STypeTopRace",45,45)
V47M=V(4,"STypeNewTopRace",46,46)
V48M=V(4,"StarCraft",47,47)
V49M=V(4,"RebirthSpace",48,48)
V50M=V(4,"MidAutumnBossTrial",49,49)
V51M=V(4,"WMTopRace",50,50)
V52M=V(4,"StarTemple",51,51)
V53M=V(4,"DimensionWarBoss",52,52)
V54M=V(4,"DimensionWar",53,53)
V55M=V(4,"DimensionHoly",54,54)
V56M=V(4,"StarTempleRank",55,56)
V57M=V(4,"BossSlave",56,61)
V58M=V(4,"Sandbox",57,62)
V59M=V(4,"CityTroop",58,63)
V60M=V(4,"RadarMonika",59,64)
V61M=V(4,"Carriage",60,65)
V62M=V(4,"CarriageBot",61,66)
V63M=V(4,"CampTrial",62,67)
V64M=V(4,"AllianceTrain",63,68)
V65M=V(4,"MarchBattleTroop",64,69)
V66M=V(4,"DesertMarchBattleTroop",65,70)
V67M=V(4,"BuffBattle",66,71)
V68M=V(4,"StormArena",67,72)
V69M=V(4,"Stage_Max",68,73)
E1M=E(3,"StageType",".CSMsg.StageType")
V70M=V(4,"CrystalCrown",0,1)
V71M=V(4,"Champion",1,2)
V72M=V(4,"Team",2,3)
V73M=V(4,"AdvanceSingle",3,4)
V74M=V(4,"Advance",4,5)
V75M=V(4,"ArenaType_TopRace",5,6)
V76M=V(4,"ArenaType_NewTopRace",6,7)
E2M=E(3,"ArenaType",".CSMsg.ArenaType")
V77M=V(4,"emDetailType_CrystalCrown",0,1)
V78M=V(4,"emDetailType_Champion",1,2)
V79M=V(4,"emDetailType_Team",2,3)
V80M=V(4,"emDetailType_LeagueComp",3,4)
V81M=V(4,"emDetailType_TopCE",4,5)
V82M=V(4,"emDetailType_AdvanceSingle",5,6)
V83M=V(4,"emDetailType_Friend",6,7)
V84M=V(4,"emDetailType_Advance",7,8)
V85M=V(4,"emDetailType_WeekendArena",8,9)
V86M=V(4,"emDetailType_WMTopRaceTop",9,10)
E3M=E(3,"DetailType",".CSMsg.DetailType")
V87M=V(4,"emActivityBroken_ForAFit",0,1)
V88M=V(4,"emActivityBroken_HelloTo",1,2)
V89M=V(4,"emActivityBroken_AllianceLD",2,3)
E4M=E(3,"BrokenSpaceTimeType",".CSMsg.BrokenSpaceTimeType")
V90M=V(4,"emActivityBrokenBattle_Battle",0,1)
V91M=V(4,"emActivityBrokenBattle_MopUp",1,2)
E5M=E(3,"BrokenSpaceTimeBattleType",".CSMsg.BrokenSpaceTimeBattleType")
V92M=V(4,"SPACEEXPLORE_PART_PrivilegeCard",0,0)
V93M=V(4,"SPACEEXPLORE_PART_CurFightNum",1,1)
V94M=V(4,"SPACEEXPLORE_PART_MAX",2,2)
E6M=E(3,"SpaceExplortionEnum",".CSMsg.SpaceExplortionEnum")
V95M=V(4,"emPrivilege_Equip",0,1)
V96M=V(4,"emPrivilege_Fuwen",1,2)
V97M=V(4,"emPrivilege_Idle",2,3)
V98M=V(4,"emPrivilege_TowerIllusion",3,4)
E7M=E(3,"AnniversaryWay",".CSMsg.AnniversaryWay")
V99M=V(4,"emHeroLimit_WishList",0,1)
V100M=V(4,"emHeroLimit_BlessHero",1,4)
V101M=V(4,"emHeroLimit_HeroSelect",2,10)
V102M=V(4,"emHeroLimit_LeagueHelp",3,11)
E8M=E(3,"HeroLimtWay",".CSMsg.HeroLimtWay")
V103M=V(4,"emUpdateModule_None",0,0)
V104M=V(4,"emUpdateModule_HeroUpgrade",1,1)
V105M=V(4,"emUpdateModule_HeroUpStar",2,2)
V106M=V(4,"emUpdateModule_HeroUpSkill",3,3)
V107M=V(4,"emUpdateModule_Drone",4,4)
V108M=V(4,"emUpdateModule_Decorate",5,5)
V109M=V(4,"emUpdateModule_HonorWall",6,6)
V110M=V(4,"emUpdateModule_Equipment",7,7)
V111M=V(4,"emUpdateModule_Survival",8,8)
V112M=V(4,"emUpdateModule_Buff",9,9)
E9M=E(3,"TroopUpdateModule",".CSMsg.TroopUpdateModule")
V113M=V(4,"eReportedReasonsCode_NONE",0,0)
V114M=V(4,"eReportedReasonsCode_OTHER",1,1)
V115M=V(4,"eReportedReasonsCode_BILK",2,2)
V116M=V(4,"eReportedReasonsCode_HOSTILITY",3,3)
V117M=V(4,"eReportedReasonsCode_BLACKMAIL",4,4)
V118M=V(4,"eReportedReasonsCode_PORN",5,5)
V119M=V(4,"eReportedReasonsCode_ABUSE",6,6)
V120M=V(4,"eReportedReasonsCode_TERRORISM",7,7)
V121M=V(4,"eReportedReasonsCode_PICTURE",8,9)
V122M=V(4,"eReportedReasonsCode_Politics",9,10)
E10M=E(3,"eReportedReasonsCode",".CSMsg.eReportedReasonsCode")
V123M=V(4,"eReportedCmdType_None",0,0)
V124M=V(4,"eReportedCmdType_1",1,1)
V125M=V(4,"eReportedCmdType_2",2,2)
V126M=V(4,"eReportedCmdType_Announcement",3,3)
V127M=V(4,"eReportedCmdType_ChatAnnouncement",4,4)
E11M=E(3,"eReportedCmdType",".CSMsg.eReportedCmdType")
F1D=F(2,"roleID",".CSMsg.PlayerBasicInfo.roleID",1,0,1,false,0,13,3)
F2D=F(2,"faceID",".CSMsg.PlayerBasicInfo.faceID",2,1,1,false,0,13,3)
F3D=F(2,"level",".CSMsg.PlayerBasicInfo.level",3,2,1,false,0,13,3)
F4D=F(2,"name",".CSMsg.PlayerBasicInfo.name",4,3,1,false,"",9,9)
F5D=F(2,"logoutTime",".CSMsg.PlayerBasicInfo.logoutTime",5,4,1,false,0,13,3)
F6D=F(2,"frameID",".CSMsg.PlayerBasicInfo.frameID",6,5,1,false,0,13,3)
F7D=F(2,"robotID",".CSMsg.PlayerBasicInfo.robotID",7,6,1,false,0,13,3)
F8D=F(2,"nationalFlagID",".CSMsg.PlayerBasicInfo.nationalFlagID",8,7,1,false,0,5,1)
F9D=F(2,"worldId",".CSMsg.PlayerBasicInfo.worldId",9,8,1,false,0,5,1)
F10D=F(2,"leagueid",".CSMsg.PlayerBasicInfo.leagueid",10,9,1,false,0,5,1)
F11D=F(2,"leagueName",".CSMsg.PlayerBasicInfo.leagueName",11,10,1,false,"",9,9)
F12D=F(2,"leagueShortName",".CSMsg.PlayerBasicInfo.leagueShortName",12,11,1,false,"",9,9)
F13D=F(2,"leagueFlag",".CSMsg.PlayerBasicInfo.leagueFlag",13,12,1,false,0,5,1)
F14D=F(2,"maxSoldierLv",".CSMsg.PlayerBasicInfo.maxSoldierLv",14,13,1,false,0,5,1)
F15D=F(2,"power",".CSMsg.PlayerBasicInfo.power",15,14,1,false,0,13,3)
F16D=F(2,"positionID",".CSMsg.PlayerBasicInfo.positionID",16,15,1,false,0,13,3)
F17D=F(2,"leaguePosition",".CSMsg.PlayerBasicInfo.leaguePosition",17,16,1,false,0,5,1)
F18D=F(2,"faceStr",".CSMsg.PlayerBasicInfo.faceStr",18,17,1,false,"",9,9)
F19D=F(2,"sex",".CSMsg.PlayerBasicInfo.sex",19,18,1,false,0,5,1)
F20D=F(2,"vipLv",".CSMsg.PlayerBasicInfo.vipLv",20,19,1,false,0,5,1)
F21D=F(2,"passstage",".CSMsg.PlayerBasicInfo.passstage",21,20,1,false,0,5,1)
M1G=D(1,"PlayerBasicInfo",".CSMsg.PlayerBasicInfo",false,{},{},nil,{})
F22D=F(2,"palId",".CSMsg.BattlePalInfo.palId",1,0,2,false,0,13,3)
F23D=F(2,"row",".CSMsg.BattlePalInfo.row",2,1,2,false,0,13,3)
F24D=F(2,"col",".CSMsg.BattlePalInfo.col",3,2,2,false,0,13,3)
F25D=F(2,"weight",".CSMsg.BattlePalInfo.weight",4,3,1,false,0,13,3)
F26D=F(2,"weightlimit",".CSMsg.BattlePalInfo.weightlimit",5,4,1,false,0,13,3)
M2G=D(1,"BattlePalInfo",".CSMsg.BattlePalInfo",false,{},{},nil,{})
F27D=F(2,"palList",".CSMsg.BattleLineUp.palList",1,0,3,false,{},11,10)
F28D=F(2,"weaponId",".CSMsg.BattleLineUp.weaponId",2,1,1,false,0,5,1)
F29D=F(2,"slgWeightCostPect",".CSMsg.BattleLineUp.slgWeightCostPect",3,2,1,false,0,5,1)
F30D=F(2,"ce",".CSMsg.BattleLineUp.ce",4,3,1,false,0,13,3)
F31D=F(2,"order",".CSMsg.BattleLineUp.order",5,4,1,false,0,5,1)
M3G=D(1,"BattleLineUp",".CSMsg.BattleLineUp",false,{},{},nil,{})
F32D=F(2,"skillId",".CSMsg.TSkillPropData.skillId",1,0,2,false,0,5,1)
F33D=F(2,"skillLv",".CSMsg.TSkillPropData.skillLv",2,1,2,false,0,5,1)
M4G=D(1,"TSkillPropData",".CSMsg.TSkillPropData",false,{},{},nil,{})
F34D=F(2,"palID",".CSMsg.ArenaPalInfo.palID",1,0,2,false,0,13,3)
F35D=F(2,"level",".CSMsg.ArenaPalInfo.level",2,1,2,false,0,13,3)
F36D=F(2,"starLevel",".CSMsg.ArenaPalInfo.starLevel",3,2,2,false,0,13,3)
F37D=F(2,"rowNo",".CSMsg.ArenaPalInfo.rowNo",4,3,2,false,0,13,3)
F38D=F(2,"colNo",".CSMsg.ArenaPalInfo.colNo",5,4,2,false,0,13,3)
F39D=F(2,"llCurHp",".CSMsg.ArenaPalInfo.llCurHp",6,5,1,false,0,4,4)
F40D=F(2,"llMaxHp",".CSMsg.ArenaPalInfo.llMaxHp",7,6,1,false,0,4,4)
F41D=F(2,"power",".CSMsg.ArenaPalInfo.power",8,7,1,false,0,13,3)
F42D=F(2,"inSoulSlot",".CSMsg.ArenaPalInfo.inSoulSlot",9,8,1,false,0,13,3)
F43D=F(2,"exclusiveLv",".CSMsg.ArenaPalInfo.exclusiveLv",10,9,1,false,0,13,3)
F44D=F(2,"equip",".CSMsg.ArenaPalInfo.equip",11,10,3,false,{},11,10)
F45D=F(2,"attack",".CSMsg.ArenaPalInfo.attack",12,11,1,false,0,5,1)
F46D=F(2,"defence",".CSMsg.ArenaPalInfo.defence",13,12,1,false,0,5,1)
F47D=F(2,"speed",".CSMsg.ArenaPalInfo.speed",14,13,1,false,0,5,1)
F48D=F(2,"skillProps",".CSMsg.ArenaPalInfo.skillProps",15,14,3,false,{},11,10)
F49D=F(2,"nFactionAddCE",".CSMsg.ArenaPalInfo.nFactionAddCE",16,15,1,false,0,5,1)
F50D=F(2,"nTechAddCE",".CSMsg.ArenaPalInfo.nTechAddCE",17,16,1,false,0,5,1)
F51D=F(2,"weight",".CSMsg.ArenaPalInfo.weight",18,17,1,false,0,13,3)
F52D=F(2,"weightlimit",".CSMsg.ArenaPalInfo.weightlimit",19,18,1,false,0,13,3)
F53D=F(2,"skinID",".CSMsg.ArenaPalInfo.skinID",20,19,1,false,0,13,3)
F54D=F(2,"awakeSkillID",".CSMsg.ArenaPalInfo.awakeSkillID",21,20,1,false,0,13,3)
F55D=F(2,"awakeSkillLV",".CSMsg.ArenaPalInfo.awakeSkillLV",22,21,1,false,0,13,3)
F56D=F(2,"palSid",".CSMsg.ArenaPalInfo.palSid",23,22,1,false,0,5,1)
F57D=F(2,"decorate",".CSMsg.ArenaPalInfo.decorate",24,23,3,false,{},11,10)
F58D=F(2,"stepLv",".CSMsg.ArenaPalInfo.stepLv",25,24,1,false,0,13,3)
F59D=F(2,"nDecorateAddCE",".CSMsg.ArenaPalInfo.nDecorateAddCE",26,25,1,false,0,5,1)
F60D=F(2,"weaponDiamond",".CSMsg.ArenaPalInfo.weaponDiamond",27,26,1,false,nil,11,10)
F61D=F(2,"weaponDiamondCE",".CSMsg.ArenaPalInfo.weaponDiamondCE",28,27,1,false,0,5,1)
F62D=F(2,"bTrialHero",".CSMsg.ArenaPalInfo.bTrialHero",29,28,1,false,false,8,7)
M5G=D(1,"ArenaPalInfo",".CSMsg.ArenaPalInfo",false,{},{},nil,{})
F63D=F(2,"RewardID",".CSMsg.TCommonRewardData.RewardID",1,0,2,false,0,5,1)
F64D=F(2,"RewardCount",".CSMsg.TCommonRewardData.RewardCount",2,1,2,false,0,5,1)
M9G=D(1,"TCommonRewardData",".CSMsg.TCommonRewardData",false,{},{},nil,{})
F65D=F(2,"ResourceType",".CSMsg.TCommonResourceData.ResourceType",1,0,2,false,0,5,1)
F66D=F(2,"ResourceCount",".CSMsg.TCommonResourceData.ResourceCount",2,1,2,false,0,3,2)
M10G=D(1,"TCommonResourceData",".CSMsg.TCommonResourceData",false,{},{},nil,{})
F67D=F(2,"imageId",".CSMsg.TCustomFaceData.imageId",1,0,1,false,"",9,9)
F68D=F(2,"curl",".CSMsg.TCustomFaceData.curl",2,1,1,false,"",9,9)
F69D=F(2,"pos",".CSMsg.TCustomFaceData.pos",3,2,1,false,0,5,1)
F70D=F(2,"status",".CSMsg.TCustomFaceData.status",4,3,1,false,0,5,1)
F71D=F(2,"used",".CSMsg.TCustomFaceData.used",5,4,1,false,0,5,1)
M11G=D(1,"TCustomFaceData",".CSMsg.TCustomFaceData",false,{},{},nil,{})
F72D=F(2,"worldId",".CSMsg.TWorldGroupWorldData.worldId",1,0,1,false,0,13,3)
F73D=F(2,"openTime",".CSMsg.TWorldGroupWorldData.openTime",2,1,1,false,0,13,3)
M12G=D(1,"TWorldGroupWorldData",".CSMsg.TWorldGroupWorldData",false,{},{},nil,{})
F74D=F(2,"datas",".CSMsg.TWorldGroupWorldArray.datas",1,0,3,false,{},11,10)
M13G=D(1,"TWorldGroupWorldArray",".CSMsg.TWorldGroupWorldArray",false,{},{},nil,{})
F75D=F(2,"planId",".CSMsg.TWorldGroupPlanData.planId",1,0,1,false,0,13,3)
F76D=F(2,"groupId",".CSMsg.TWorldGroupPlanData.groupId",2,1,1,false,0,13,3)
F77D=F(2,"worldDatas",".CSMsg.TWorldGroupPlanData.worldDatas",3,2,3,false,{},11,10)
M14G=D(1,"TWorldGroupPlanData",".CSMsg.TWorldGroupPlanData",false,{},{},nil,{})
F78D=F(2,"datas",".CSMsg.TWorldGroupPlanArray.datas",1,0,3,false,{},11,10)
M15G=D(1,"TWorldGroupPlanArray",".CSMsg.TWorldGroupPlanArray",false,{},{},nil,{})
F79D=F(2,"groupId",".CSMsg.TWorldGroupMatchGroupData.groupId",1,0,1,false,0,13,3)
F80D=F(2,"worlds",".CSMsg.TWorldGroupMatchGroupData.worlds",2,1,3,false,{},11,10)
M16G=D(1,"TWorldGroupMatchGroupData",".CSMsg.TWorldGroupMatchGroupData",false,{},{},nil,{})
F81D=F(2,"matchId",".CSMsg.TWorldGroupMatchData.matchId",1,0,1,false,0,13,3)
F82D=F(2,"groupDatas",".CSMsg.TWorldGroupMatchData.groupDatas",2,1,3,false,{},11,10)
M17G=D(1,"TWorldGroupMatchData",".CSMsg.TWorldGroupMatchData",false,{},{},nil,{})
F83D=F(2,"datas",".CSMsg.TWorldGroupMatchArray.datas",1,0,3,false,{},11,10)
M18G=D(1,"TWorldGroupMatchArray",".CSMsg.TWorldGroupMatchArray",false,{},{},nil,{})
F84D=F(2,"nItemID",".CSMsg.TRewardConvertToItem.nItemID",1,0,2,false,0,5,1)
F85D=F(2,"nItemNum",".CSMsg.TRewardConvertToItem.nItemNum",2,1,2,false,0,5,1)
F86D=F(2,"nItemType",".CSMsg.TRewardConvertToItem.nItemType",3,2,2,false,0,5,1)
M19G=D(1,"TRewardConvertToItem",".CSMsg.TRewardConvertToItem",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M}
E2M.values = {V70M,V71M,V72M,V73M,V74M,V75M,V76M}
E3M.values = {V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M}
E4M.values = {V87M,V88M,V89M}
E5M.values = {V90M,V91M}
E6M.values = {V92M,V93M,V94M}
E7M.values = {V95M,V96M,V97M,V98M}
E8M.values = {V99M,V100M,V101M,V102M}
E9M.values = {V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M}
E10M.values = {V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M}
E11M.values = {V123M,V124M,V125M,V126M,V127M}
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D, F21D}
M2G.fields={F22D, F23D, F24D, F25D, F26D}
F27D.message_type=M2G
M3G.fields={F27D, F28D, F29D, F30D, F31D}
M4G.fields={F32D, F33D}
F44D.message_type=mq_common_pb.M4G
F48D.message_type=M4G
F57D.message_type=mq_common_pb.M5G
F60D.message_type=mq_common_pb.M6G
M5G.fields={F34D, F35D, F36D, F37D, F38D, F39D, F40D, F41D, F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D, F50D, F51D, F52D, F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D, F61D, F62D}
M9G.fields={F63D, F64D}
M10G.fields={F65D, F66D}
M11G.fields={F67D, F68D, F69D, F70D, F71D}
M12G.fields={F72D, F73D}
F74D.message_type=M12G
M13G.fields={F74D}
F77D.message_type=M12G
M14G.fields={F75D, F76D, F77D}
F78D.message_type=M14G
M15G.fields={F78D}
F80D.message_type=M13G
M16G.fields={F79D, F80D}
F82D.message_type=M16G
M17G.fields={F81D, F82D}
F83D.message_type=M17G
M18G.fields={F83D}
M19G.fields={F84D, F85D, F86D}

Advance = 5
AdvanceSingle = 4
AllianceTrain = 68
Arena = 4
ArenaPalInfo =M(M5G)
ArenaType_NewTopRace = 7
ArenaType_TopRace = 6
Ashdungeon = 5
BattleLineUp =M(M3G)
BattlePalInfo =M(M2G)
BossSlave = 61
BossTrial = 39
BraveManTried = 3
BrokenSpaceTime = 6
BuffBattle = 71
CampTrial = 67
Carriage = 65
CarriageBot = 66
Champion = 2
ChinaRed = 43
CityTroop = 63
Compete = 10
CrystalCrown = 1
Cutscene = 13
DesertMarchBattleTroop = 70
DimensionHoly = 54
DimensionWar = 53
DimensionWarBoss = 52
EquipEctype = 21
EquipEctypeFuWen1 = 31
EquipEctypeFuWen2 = 32
EquipEctypeFuWen3 = 33
EquipEctypeFuWen4 = 35
EquipEctypeFuWen5 = 36
EquipEctypeZhuanShu = 38
FactionType1 = 16
FactionType2 = 17
FactionType3 = 18
FactionType4 = 19
Friend = 15
GameGoal = 1
InValid = 0
KillingTower = 40
LeaActivityBoss = 23
LeagueBoss = 8
LeagueBossWar = 24
LeagueComp = 7
LeagueTreasureBoss = 9
LeagueWar = 22
Legend = 37
MarchBattleTroop = 69
MateMonster = 11
Maze = 14
MidAutumnBossTrial = 49
MultiKillingTower = 41
PeakOfTime = 20
PlayerBasicInfo =M(M1G)
Plot = 29
RadarMonika = 64
RebirthSpace = 48
Replay = 12
RookiePlot = 34
SLG = 27
SPACEEXPLORE_PART_CurFightNum = 1
SPACEEXPLORE_PART_MAX = 2
SPACEEXPLORE_PART_PrivilegeCard = 0
STypeAllianceTech = 44
STypeNewTopRace = 46
STypeTopRace = 45
Sandbox = 62
SpaceExplorePirates = 30
SpaceGap = 28
Stage_Max = 73
StarCraft = 47
StarTemple = 51
StarTempleRank = 56
StormArena = 72
TCommonResourceData =M(M10G)
TCommonRewardData =M(M9G)
TCustomFaceData =M(M11G)
TRewardConvertToItem =M(M19G)
TSkillPropData =M(M4G)
TWorldGroupMatchArray =M(M18G)
TWorldGroupMatchData =M(M17G)
TWorldGroupMatchGroupData =M(M16G)
TWorldGroupPlanArray =M(M15G)
TWorldGroupPlanData =M(M14G)
TWorldGroupWorldArray =M(M13G)
TWorldGroupWorldData =M(M12G)
Team = 3
TheTowerOfIllusion = 2
TopRace = 26
VoidArena = 25
WMTopRace = 50
WeekendArena = 42
eReportedCmdType_1 = 1
eReportedCmdType_2 = 2
eReportedCmdType_Announcement = 3
eReportedCmdType_ChatAnnouncement = 4
eReportedCmdType_None = 0
eReportedReasonsCode_ABUSE = 6
eReportedReasonsCode_BILK = 2
eReportedReasonsCode_BLACKMAIL = 4
eReportedReasonsCode_HOSTILITY = 3
eReportedReasonsCode_NONE = 0
eReportedReasonsCode_OTHER = 1
eReportedReasonsCode_PICTURE = 9
eReportedReasonsCode_PORN = 5
eReportedReasonsCode_Politics = 10
eReportedReasonsCode_TERRORISM = 7
emActivityBrokenBattle_Battle = 1
emActivityBrokenBattle_MopUp = 2
emActivityBroken_AllianceLD = 3
emActivityBroken_ForAFit = 1
emActivityBroken_HelloTo = 2
emDetailType_Advance = 8
emDetailType_AdvanceSingle = 6
emDetailType_Champion = 2
emDetailType_CrystalCrown = 1
emDetailType_Friend = 7
emDetailType_LeagueComp = 4
emDetailType_Team = 3
emDetailType_TopCE = 5
emDetailType_WMTopRaceTop = 10
emDetailType_WeekendArena = 9
emHeroLimit_BlessHero = 4
emHeroLimit_HeroSelect = 10
emHeroLimit_LeagueHelp = 11
emHeroLimit_WishList = 1
emPrivilege_Equip = 1
emPrivilege_Fuwen = 2
emPrivilege_Idle = 3
emPrivilege_TowerIllusion = 4
emUpdateModule_Buff = 9
emUpdateModule_Decorate = 5
emUpdateModule_Drone = 4
emUpdateModule_Equipment = 7
emUpdateModule_HeroUpSkill = 3
emUpdateModule_HeroUpStar = 2
emUpdateModule_HeroUpgrade = 1
emUpdateModule_HonorWall = 6
emUpdateModule_None = 0
emUpdateModule_Survival = 8

