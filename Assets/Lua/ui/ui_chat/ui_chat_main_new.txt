-- ui_chat_main_new.txt -----------------------------------
-- author:  刘志远
-- date:    2019-10-17
-- ver:     1.0
-- desc:    聊天
----------------------------------------------------------------
local print = print
local require = require
local typeof = typeof
local dump = dump
local ipairs = ipairs
local pairs = pairs
local math = math
local table = table
local string = string
local tostring = tostring
local type= type

local string_util = require "string_util"
local com_class = require "com_class"
local lang = require "lang"
local chat_pb = require "chat_pb"
local player_mgr = require "player_mgr"
local util = require "util"
local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local ui_window_mgr = require "ui_window_mgr"
local chat_mgr_new = require "chat_mgr_new"
local mq_common_pb = require "mq_common_pb"
local flow_text = require "flow_text"
local lang_res_key = require "lang_res_key"
local net_chat_module_new = require "net_chat_module_new"
local block_cache = require "block_cache"
local net_login_module = require "net_login_module"
local game_scheme = require "game_scheme"
local iui_item_detail = require "iui_item_detail"
local goods_item = require "goods_item_new"
local item_data = require "item_data"
local json = require "dkjson"
local const = require "const"
local log = require "log"
local sprite_asset = require "card_sprite_asset"
local module_on_off = require "module_on_off"
local lua_pb = require "lua_pb"
local effect_item = require "effect_item"
local festival_activity_mgr = require "festival_activity_mgr"
local festival_activity_cfg = require "festival_activity_cfg"
-- local dimension_war_helper = require "dimension_war_helper"
local oversea_res = require "oversea_res"
local sociaty_icon = require "sociaty_icon"
local sociaty_title = require "sociaty_title"
local sandbox_gather_mgr = require "sandbox_gather_mgr"
local ui_chat_define_gw = require "ui_chat_define_gw"
local ui_chat_data_gw = require "ui_chat_data_gw"
local alliance_data = require "alliance_data"
local Mask = CS.UnityEngine.UI.Mask
local Transform = CS.UnityEngine.Transform
local Text = CS.UnityEngine.UI.Text
local Slider = CS.UnityEngine.UI.Slider
local RectTransform = CS.UnityEngine.UI.RectTransform
local Canvas = CS.UnityEngine.Canvas
local EmojiSpriteGraphic = CS.EmojiText.Taurus.SpriteGraphic
local InlineText = CS.EmojiText.Taurus.InlineText
local module_scroll_list = require "scroll_list"
local CScrollList = module_scroll_list.CScrollList
local CScrollListItemBase = module_scroll_list.CScrollListItemBase
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local EventTriggerListener = CS.War.UI.EventTriggerListener
local screen_util = require "screen_util"
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Utility = CS.War.Script.Utility
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local LeanTween = CS.LeanTween
local LeanTweenType = CS.LeanTweenType
local CanvasGroup = CS.UnityEngine.CanvasGroup
local ScreenKeyboardStatus = CS.UnityEngine.TouchScreenKeyboard.Status
local TextMeshProUGUI = CS.TMPro.TextMeshProUGUI
local alliance_mgr = require "alliance_mgr"
local UIUtil = CS.Common_Util.UIUtil
local ReviewingUtil = require "ReviewingUtil"
local chat_mgr_pro = require "chat_mgr_pro"
local red_const = require "red_const"
local red_system = require "red_system"
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local InlineManager = CS.EmojiText.Taurus.InlineManager
local LayoutRebuilder   = CS.UnityEngine.UI.LayoutRebuilder
local GRaycaster=CS.War.UI.GRaycaster
local LayoutElement   = CS.UnityEngine.UI.LayoutElement
local Screen = CS.UnityEngine.Screen
local CanvasScaler = CS.UnityEngine.UI.CanvasScaler
local ChatListView
local TMP_InputField
if chat_mgr_pro.GetIsUseChatPro() then
    TMP_InputField= CS.TMPro.TMP_InputField
    ChatListView=CS.SuperScrollView.ChatListView
end

local EmojiConverter = nil
if util.IsCSharpClass(CS.EmojiConverter) then
    EmojiConverter=CS.EmojiConverter
end
local MobileInputMgr = nil
if util.IsCSharpClass(CS.MobileInputMgr) then
    MobileInputMgr=CS.MobileInputMgr
end
local PointClick = nil
if util.IsCSharpClass(CS.PointClick) then
    PointClick=CS.PointClick
end





module("ui_chat_main_new")
local window = nil
local M = {}

local entranceType = nil --老功能，当前暂不区分入口界面类型

local myHelpTicker = nil
local myUnionTicker = nil
local canChange = true
local landID = math.random(939, 968)
local keyboardTicker = nil
local curEmojiPage = 1

--点击了发送按钮，点赞按钮，表情按钮之后不需要触发onEndEdit 事件
local isOnEndEdit = true
local isOnEndEditTicker = nil

local hasSwindle = false --Build数据后是否有诈骗关键字
local hasOtherUI = false -- 诈骗关键字提示框下面是否还有ui
local swindleDefaultPos = { x = 0, y = 0 }
local swindleOnePos = { x = 0, y = 280 }
local swindleTwoPos = { x = 0, y = 392 }
local maxLimitLen = 400

eChatType = ui_chat_define_gw.eChatType

local curViewMode = 1 --当前窗口模式 1.浏览消息 2.文本输入(隐藏频道tog、适应键盘高度) 3.表情输入(隐藏频道tog、显示表情选择面板) 
M.widget_table = {

    settingButton = { path = "settingButton", type = "Button" },

    inputRoot = { path = "Auto_layout/input/root", type = Transform },
    mikeBtn = { path = "Auto_layout/input/root/mikeBtn", type = "Button" },
    extensionBtn = { path = "Auto_layout/input/root/extensionBtn", type = "Button" },
    emojiBtn = { path = "Auto_layout/input/root/emojiBtn", type = "Button" },

    tf_channelTogList_top = { path = "Auto_layout/channelTogList_top", type = Transform },

    goldenEggsBtn = { path = "Auto_layout/ExtensionPanelView/List/Viewport/Content/goldenEggsRoot/goldenEggsBtn", type = "Button", event_name = "goldenEggsEvent"},
    likeBtn = { path = "Auto_layout/ExtensionPanelView/List/Viewport/Content/likeBtnRoot/likeBtn", type = "Button" },
    r_ExtensionPanelView = { path = "Auto_layout/ExtensionPanelView", type = RectTransform },
    togLang = { path = "Auto_layout/channelTogList_top/TogLang", type = "Toggle" },
    togWorld = { path = "Auto_layout/channelTogList_top/TogWorld", type = "Toggle" },
    togUnion = { path = "Auto_layout/channelTogList_top/TogUnion", type = "Toggle" },
    togUnionNotice = { path = "Auto_layout/channelTogList_top/TogUnionNotice", type = "Toggle" },
    togUnionR4R5 = { path = "Auto_layout/channelTogList_top/TogUnionR4R5", type = "Toggle" },
    togManor = { path = "Auto_layout/channelTogList/TogManor", type = "Toggle" },
    togPrivate = { path = "Auto_layout/channelTogList/TogPrivate", type = "Toggle" },
    togRecruit = { path = "Auto_layout/channelTogList/TogRecruit", type = "Toggle" },
    togPeakGameField = { path = "Auto_layout/channelTogList/TogPeakGame", type = "Toggle" },
    togWorldBottom = { path = "Auto_layout/channelTogList/TogWorldBottom", type = "Toggle" },
    togUnionBottom = { path = "Auto_layout/channelTogList/TogUnionBottom", type = "Toggle" },


    togTextLang = { path = "Auto_layout/channelTogList_top/TogLang/txt_name", type = "Text" },
    togTextWorld = { path = "Auto_layout/channelTogList_top/TogWorld/txt_name", type = "Text" },
    togTextUnion = { path = "Auto_layout/channelTogList_top/TogUnion/txt_name", type = "Text" },
    togTextUnionNotice = { path = "Auto_layout/channelTogList_top/TogUnionNotice/txt_name", type = "Text" },
    togTextUnionR4R5 = { path = "Auto_layout/channelTogList_top/TogUnionR4R5/txt_name", type = "Text" },
    togTextManor = { path = "Auto_layout/channelTogList/TogManor", type = "Text" },
    togTextRecruit = { path = "Auto_layout/channelTogList/TogRecruit", type = "Text" },
    togTextPrivate = { path = "Auto_layout/channelTogList/TogPrivate", type = "Text" },
    togTextPeakGameField = { path = "Auto_layout/channelTogList/TogPeakGame", type = "Text" },
    togTextWorldBottom = { path = "Auto_layout/channelTogList/TogWorldBottom", type = "Text" },
    togTextUnionBottom = { path = "Auto_layout/channelTogList/TogUnionBottom", type = "Text" },


    chatList = { path = "Auto_layout/ContentView/chatList", type = Transform },
    chatListRect = { path = "Auto_layout/ContentView/chatList", type = RectTransform },
    r_channelTogList = { path = "Auto_layout/channelTogList", type = RectTransform },
    r_emojiSelectView = { path = "Auto_layout/EmojiSelectView", type = RectTransform },
    r_toggleGroup = { path = "Auto_layout/ToggleGroup", type = RectTransform },
    r_contentView = { path = "Auto_layout/ContentView", type = RectTransform },
    r_emojiBtn = { path = "Auto_layout/input/root/emojiBtn", type = RectTransform },

    btn_emojiBack = { path = "Auto_layout/input/root/btn_emojiBack", type = "Button" },
    btn_contentMask = { path = "Auto_layout/ContentView/btn_contentMask", type = "Button" },

    auto_layout = { path = "Auto_layout", type = RectTransform },
    InputInlineManager = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline", type = InlineManager },
    spriteGraphic = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline/Content/Sprite_0", type = EmojiSpriteGraphic },
    spriteGraphic1 = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline/Content/Sprite_1", type = EmojiSpriteGraphic },
    spriteGraphic2 = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline/Content/Sprite_2", type = EmojiSpriteGraphic },
    spriteGraphic3 = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline/Content/Sprite_3", type = EmojiSpriteGraphic },

    emoji_page1 = { path = "Auto_layout/ToggleGroup/Viewport/Content/toggle_1", type = "Toggle" },
    emoji_page1CheckMark = { path = "Auto_layout/ToggleGroup/Viewport/Content/toggle_1/Checkmark", type = RectTransform },
    emoji_page2 = { path = "Auto_layout/ToggleGroup/Viewport/Content/toggle_2", type = "Toggle" },

    list = { path = "Auto_layout/EmojiSelectView/emojiList", type = ScrollRect },
    goContent = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline/Content", type = Transform },
    goItem = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline/Content/ListItem", type = Transform },

    stadbyGoContent = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInlineStandBy/Content", type = Transform },
    stadbyGoItem = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInlineStandBy/Content/ListItem", type = Transform },

    inlineTrans = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInline", type = RectTransform },
    standbyInlineTrans = { path = "Auto_layout/EmojiSelectView/emojiList/Viewport/TextInlineStandBy", type = RectTransform },

    atBtn = { path = "Auto_layout/ContentView/atBtn", type = "Button" },
    atBtnText = { path = "Auto_layout/ContentView/atBtn/Text", type = "Text" },
    mayDayMarketBtn = { path = "Auto_layout/ContentView/ActivityBtnGroup/mayDayMarketBtn", type = "Button", event_name = "mayDayMarketBtnEvent" },
    mayDayMarketBtnCanvas = { path = "Auto_layout/ContentView/ActivityBtnGroup/mayDayMarketBtn", type = Canvas },
    mayDayNTFBtn = { path = "Auto_layout/ContentView/ActivityBtnGroup/mayDayNTFBtn", type = "Button", event_name = "mayDayNTFBtnEvent" },
    -- mayDayNTFBtnCanvas = {path = "Auto_layout/ContentView/ActivityBtnGroup/mayDayNTFBtn", type = Canvas},

    helpFunc = { path = "Auto_layout/HelpFunc", type = Transform },
    showRankBtn = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Rank", type = "Button" },
    showRankImg = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Rank", type = "Image" },
    myProgressObj = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/HandleSlider", type = Slider },
    myHelpProgressText = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/HandleSlider/progress", type = TextMeshProUGUI },
    myHelpProgressImg = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/HandleSlider", type = Slider },
    myHelpTextWithProgress = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/HandleSlider/Text ", type = TextMeshProUGUI },
    myHelpText = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/Text", type = TextMeshProUGUI },
    myHelpCountDownTime = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/Time", type = TextMeshProUGUI },
    myHelpRed = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/red", type = Transform },
    myHelpIconRoot = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/IconRoot", type = Transform },
    myHelpInitObj = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left/InitObj", type = RectTransform },
    HelpRedBg = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Rank/RedBg", type = RectTransform },
    HelpRedBgNum = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Rank/RedBg/Num", type = Text },
    myHelpGray = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left", type = SpriteSwitcher },
    myHelpBtn = { path = "Auto_layout/HelpFunc/BtnGroup/Btn_Left", type = "Button" },

    BtnRedEnvelope = { path = "Auto_layout/HelpFunc/BtnGroup/BtnRedEnvelope", type = "Button" },
    RedEnvelopeRedBg = { path = "Auto_layout/HelpFunc/BtnGroup/BtnRedEnvelope/HaveRedEnvelope/RedBg", type = RectTransform },
    rtNoRedEnvelope = { path = "Auto_layout/HelpFunc/BtnGroup/BtnRedEnvelope/NoRedEnvelope", type = RectTransform },
    rtEffectRoot = { path = "Auto_layout/HelpFunc/BtnGroup/BtnRedEnvelope/HaveRedEnvelope/EffectRoot", type = RectTransform },
    RedEnvelopeRedBgCanvas = { path = "Auto_layout/HelpFunc/BtnGroup/BtnRedEnvelope/HaveRedEnvelope/RedBg", type = Canvas },

    UnionInviteFunc = { path = "Auto_layout/UnionInviteFunc", type = Transform },
    myUnionBtnGroup = { path = "Auto_layout/UnionInviteFunc/BtnGroup", type = "RectTransform" },
    myUnionIcon = { path = "Auto_layout/UnionInviteFunc/BtnGroup/icon", type = "RectTransform" },
    myUnionTitle = { path = "Auto_layout/UnionInviteFunc/BtnGroup/title", type = "RectTransform" },
    myUnionName = { path = "Auto_layout/UnionInviteFunc/BtnGroup/name", type = "Text" },
    myUnionNum = { path = "Auto_layout/UnionInviteFunc/BtnGroup/num", type = "Text" },
    Btn_Invite = { path = "Auto_layout/UnionInviteFunc/BtnGroup/Btn_Invite", type = "Button" },
    myUnionCountDown = { path = "Auto_layout/UnionInviteFunc/BtnGroup/Btn_Invite/countDown", type = "Text" },

    allianceNoticeObj = { path = "Auto_layout/ContentView/obj_topTipsRoot/AllianceNoticeObj", type = Transform },
    noAllianceNoticeTips = { path = "Auto_layout/ContentView/noAllianceNoticeTips", type = Transform },
    btn_showAllianceNoticeRelease = { path = "Auto_layout/ContentView/noAllianceNoticeTips/btn_showAllianceNoticeRelease", type = "Button" },
    btn_topShowAllianceNotice = { path = "btn_topShowAllianceNotice", type = "Button" },

    unionTips = { path = "Auto_layout/ContentView/unionTips", type = "RectTransform" },
    unionTipsBtn = { path = "Auto_layout/ContentView/unionTips/Button", type = "Button" },
    togAutoTranslation = { path = "togAutoTranslation", type = "Toggle" }, --联盟自动开启翻译按钮
    tagOpenImage = { path = "togAutoTranslation/tagOpen", type = Transform },
    tagOpenBgImageCanvasGroup = { path = "togAutoTranslation/LMliaotiankai", type = CanvasGroup },

    --世界自动翻译开关
    togAutoTranslationWorldRoot = { path = "togAutoTranslationWorldRoot", type = "RectTransform" },
    togAutoTranslationWorld = { path = "togAutoTranslationWorldRoot/togAutoTranslationWorld", type = "Toggle" },
    tagOpenImageWorld = { path = "togAutoTranslationWorldRoot/togAutoTranslationWorld/tagOpen", type = Transform },
    tagOpenBgImageCanvasGroupWorld = { path = "togAutoTranslationWorldRoot/togAutoTranslationWorld/LMliaotiankai", type = CanvasGroup },
    btnAutoTranslationWorldLock = { path = "togAutoTranslationWorldRoot/lock", type = "Button", event_name = "AutoTranslationWorldLockEvent" },

    SwindleTip = { path = "Auto_layout/SwindleTip", type = "RectTransform" },
    SwindleTipTxt = { path = "Auto_layout/SwindleTip/Scroll View/Viewport/SwindleTipText", type = "Text" },
    RecruitTip = { path = "RecruitTip", type = "Text" },

    vipMasker = { path = "Auto_layout/ContentView/chatList/VipMasker", type = Canvas },
    vipMaskerClear = { path = "Auto_layout/ContentView/chatList/VipMaskerClear", type = Canvas },
    --Auto_layout/ContentView/maskRoot/SpriteMask

    topTips = { path = "Auto_layout/ContentView/obj_topTipsRoot/topTips", type = "RectTransform" },
    topTipsText = { path = "Auto_layout/ContentView/obj_topTipsRoot/topTips/txt_tips", type = "Text" },
    btn_topTipsClose = { path = "Auto_layout/ContentView/obj_topTipsRoot/topTips/btn_topTipsClose", type = "Button" },
    btn_topTipsFunc = { path = "Auto_layout/ContentView/obj_topTipsRoot/topTips/btn_topTipsFunc", type = "Button" },


    MobileInputFieldRect= { path = "Auto_layout/input", type = "RectTransform" },
    inputSelectView = { path = "Auto_layout/InputSelectView", type = RectTransform },
    btn_go2Bottom={path = "Auto_layout/btn_go2Bottom", type = "Button"},
    
}
if chat_mgr_pro.GetIsUseChatPro() then
    M.widget_table["closeBtn"]= { path = "Auto_layout/input/root/closeBtn", type = "Button" }
    --输入相关
    M.widget_table["inputFieldTMP"]= { path = "Auto_layout/input/root/inputFieldTMP", type = TMP_InputField }
    M.widget_table["r_inputFieldTMP"]= { path = "Auto_layout/input/root/inputFieldTMP", type = RectTransform }

    M.widget_table["chatListViewport"]= { path = "Auto_layout/ContentView/ChatListView", type = Transform }
    M.widget_table["chatListViewRect"]= { path = "Auto_layout/ContentView/ChatListView", type = RectTransform }
    M.widget_table["chatListView"]= { path = "Auto_layout/ContentView/ChatListView", type = ChatListView }
    M.widget_table["inputSendBtn"]= { path = "Auto_layout/input/root/inputFieldTMP/inputSendBtn", type = Transform }
    M.widget_table["sendBtn"]= { path = "Auto_layout/input/root/sendBtn", type = "Button" }
    M.widget_table["r_sendBtn"]= { path = "Auto_layout/input/root/sendBtn", type =RectTransform}
    M.widget_table["inputSelectLayoutElement"]={ path = "Auto_layout/InputSelectView", type = LayoutElement }

else
    M.widget_table["closeBtn"]= {  path = "Auto_layout/closeBtn", type = "Button"  }
    --输入相关
    M.widget_table["sendBtn"]= { path = "Auto_layout/input/root/sendBtn", type = "Button" }
    M.widget_table["r_sendBtn"]= { path = "Auto_layout/input/root/sendBtn", type =RectTransform}
    M.widget_table["inputFieldContent"]= {  path = "Auto_layout/input/root/inputField/Mask/ContentBg/Content", type = "Text", fitArabic = true}
    M.widget_table["r_inputField"]= {  path = "Auto_layout/input/root/inputField", type = RectTransform  }
    M.widget_table["inputField"]= { path = "Auto_layout/input/root/inputField", type = "InputField" }
    M.widget_table["r_inputFieldText"]= { path = "Auto_layout/input/root/inputField/Mask/ContentBg/Content", type = Transform }
    M.widget_table["r_inputFieldContentBg"]= { path = "Auto_layout/input/root/inputField/Mask/ContentBg", type = RectTransform  }

    M.widget_table["chatListSR"]= { path = "Auto_layout/ContentView/chatList", type = ScrollRect }
    M.widget_table["chatListViewport"]= { path = "Auto_layout/ContentView/chatList/Viewport", type = Transform }
    M.widget_table["ortherSpeakItemContent"]= { path = "Auto_layout/ContentView/chatList/Viewport/ListItems/ortherSpeakItem/ContentBg/Content", type = "Text", fitArabic = true }
    M.widget_table["selfSpeakItemContent"]= { path = "Auto_layout/ContentView/chatList/Viewport/ListItems/selfSpeakItem/ContentBg/Content", type = "Text", fitArabic = true }
end
function M:FixMultiLang()
    if self:IsRTLEnvironment() and chat_mgr_pro.GetIsUseChatPro()==false then
        self.r_inputFieldContentBg.pivot = { x = 1, y = 0.5 }
        self.r_inputFieldContentBg.anchorMin = { x = 1, y = 0.5 }
        self.r_inputFieldContentBg.anchorMax = { x = 1, y = 0.5 }
        self.r_inputFieldContentBg.anchoredPosition = { x = 0, y = 0 }
    end
end

function M:ctor(selfType)
    self.__base:ctor(selfType)
    self.recycle_ui = true
    self.faceSpriteAsset = sprite_asset.CreateLeagueAsset()
    self.redEnvelopeEffect = nil
    self.CustomEffectOrderOffset = 2
end

function M:Init()
     maxLimitLen = ui_chat_data_gw.GetSendMsgMaxLimitLen()


    self.swindleDefaultPos = self.SwindleTip.anchoredPosition
    self.swindleOnePos = { x = 0, y = 80 }
    --self.swindleTwoPos = { x = 0, y = 374 }

    if chat_mgr_pro.GetIsUseChatPro() then
        self.inputFieldTMP.characterLimit = 0
        --self.inputField.shouldHideMobileInput = true
        self.inputFieldEventTrigger = self.inputFieldTMP.gameObject:GetComponent(typeof(EventTriggerListener))

    else
        self.inputFieldText = self.r_inputFieldText:GetComponent(typeof(InlineText))
        self.chatListViewport:GetComponent(typeof(Mask)).enabled = true
        self.inputField.characterLimit = 1000
        --self.inputField.shouldHideMobileInput = true
        self.inputFieldEventTrigger = self.r_inputField:GetComponent(typeof(EventTriggerListener))
    end
    self.inputFieldEventTrigger:onClick('+', function()
        self:ChangeViewMode(2)
    end)
    self:InitView()
    self:CreateLayItem()
    self:InitOnShowUI()
    self.isInit = true
    if ReviewingUtil.IsReviewing() then
        self.togLang.gameObject:SetActive(false)
    end
    self:RefreshHelpRed()
    self.autoLayoutDefBottom = self.auto_layout.offsetMin.y

end

function M:EmojiInit()
    if not self.emojiInit then
        self:EmojiListCreate()
        self:EmojiListStandByCreate()
        self.emojiInit = true
    end
end

function M:UpdateUI()
    -- 刷新红包红点层级
    self.RedEnvelopeRedBgCanvas.sortingOrder = self.EffOrder + 10
    self.mayDayMarketBtnCanvas.sortingOrder = self.curOrder + 10
    self.vipMasker.sortingOrder = self.curOrder + 2
    self.vipMaskerClear.sortingOrder = self.curOrder + 10
    UpdateMayDayBtnOrder(self.curOrder)
    if self.comChatList then
        self.comChatList:InitOrder(self.curOrder)
    end
end

function UpdateMayDayBtnOrder(curOrder)
    if curOrder and window:IsValid() and not util.IsObjNull(window.mayDayMarketBtnCanvas) then
        window.mayDayMarketBtnCanvas.sortingOrder = curOrder + 13
        --window.mayDayNTFBtnCanvas.sortingOrder = curOrder + 10
    end
end

function M:RefreshHelpRed()
    if not self:IsValid() then
        return
    end
    local data = chat_mgr_new.GetMsgByType(chat_mgr_new.ENUM_CHANNEL.GUIDE)
    local count = 0
    for i, v in ipairs(data) do
        if v.sType == mq_common_pb.enSpeak_LeagueMutualHelp then
            local helpInfo = chat_mgr_new.GetHelpInfoByID(v.roleid)
            local selfRoleID = player_mgr.GetPlayerRoleID()
            if helpInfo and v.roleid ~= selfRoleID and helpInfo.helpRoleID[selfRoleID] == nil then
                local progress = helpInfo and helpInfo.prog or 0
                local progressEnd = 0
                local cfg_help = game_scheme:LeagueHelp_0(v.leagueHelp.helpID)
                if cfg_help then
                    progressEnd = cfg_help.helpNum
                    if progress < progressEnd then
                        count = count + 1
                    end
                end
            end
        end
    end
    self.HelpRedBg.gameObject:SetActive(count > 0)
    self.HelpRedBgNum.text = count
end

--刷新红包红点
function M:RefreshRedEnvelopeRedDot()
    if not self:IsValid() or not festival_activity_mgr.IsActivityOpenByCodeType(festival_activity_cfg.ActivityCodeType.ChatRedPackage) then
        return
    end
    local showRedDot = chat_mgr_new.HaveRedEnvelope()
    self.RedEnvelopeRedBg.gameObject:SetActive(showRedDot)
    if showRedDot then
        if not self.redEnvelopeEffect then
            self.redEnvelopeEffect = effect_item.CEffectItem():Init("art/effects/prefabs/ui/effect_ui_chunjiehuodong_hongbao.prefab",
                    self.rtEffectRoot, self.EffOrder)
        end
        if self.redEnvelopeEffect.gameObject then
            self.redEnvelopeEffect.gameObject:SetActive(true)
        end
    elseif self.redEnvelopeEffect and self.redEnvelopeEffect.gameObject then
        self.redEnvelopeEffect.gameObject:SetActive(false)
    end
    self.rtNoRedEnvelope.gameObject:SetActive(not showRedDot)
end

function M:RefreshRecruitTip(channelType)
    if channelType == chat_mgr_new.ENUM_CHANNEL.RECRUIT then
        local msgs = chat_mgr_new.GetMsgByType(chat_mgr_new.ENUM_CHANNEL.RECRUIT)
        if msgs and #msgs > 0 then
            self.RecruitTip.gameObject:SetActive(false)
        else
            self.RecruitTip.gameObject:SetActive(true)
        end
    else
        self.RecruitTip.gameObject:SetActive(false)
    end
end

-- 用于列表切页时的动画
function M:PlayListAni(left)
    if left then
        self.list.content = self.stadbyGoContent
        -- 向左平滑
        -- 主列表往左移动 副列表做动画 完成回调时设置主列表的渲染内容 位置 隐藏副列表 显示主列表
        self.standbyInlineTrans.transform.anchoredPosition = { x = 750, y = 0, z = 0 }

        self.emojilistStandByInstance:ReleaseAllItems()
        local datas = self:GetListData(1)
        self.emojilistStandByInstance:SetListData(datas)

        self.standbyInlineTrans.gameObject:SetActive(true)
        LeanTween.moveX(self.inlineTrans.transform, -800, 0.3)
        -- :setEase(LeanTweenType.linear)
        LeanTween.moveX(self.standbyInlineTrans.transform, 0, 0.4)
    else
        self.list.content = self.goContent
        -- 向右平滑
        self.standbyInlineTrans.transform.anchoredPosition = { x = -750, y = 0, z = 0 }
        
        if self.emojilistInstance then
            self.emojilistInstance:ReleaseAllItems()
            local datas = self:GetListData(2)
            self.emojilistInstance:SetListData(datas)
        end

        self.standbyInlineTrans.gameObject:SetActive(true)
        LeanTween.moveX(self.standbyInlineTrans.transform, 800, 0.3)
        -- :setEase(LeanTweenType.linear)
        LeanTween.moveX(self.inlineTrans.transform, 0, 0.4)
    end
end

--（缓存界面）显示时需要初始化函数
function M:InitOnShowUI()
    self.data = {
        isAutoTranslationAlliance = nil,
        autoTranslationTimer = nil,
    }
    event.Trigger(event.CLOSE_ALL_SCENE, "chat")
    event.Trigger(event.RESET_MENUTOP_ORDER)
    event.Trigger(event.RESET_MENUBOT_ORDER)
    event.Trigger(event.ENABLE_ROW_BUTTON, true)


    self:InitMobileInput()
    self:ChangePage()
    self:UpdateView()
    self:InitTogList()
    self:SubscribeEvents()
    self:UpdateChannel()
    self:UpdataAtBtnState()
    self:ChangeViewMode(1)
end
function M:InitMobileInput()
    self.SwindleTipTxt.text = lang.Get(971)

  -- log.Error(canvasRectTransform.sizeDelta.y)
  --  graycaster.enabled=false
        if chat_mgr_pro.GetIsUseChatPro() and MobileInputMgr  then
            MobileInputMgr.Init()
            local canvas_root = GameObject.Find("/UIRoot/CanvasWithMesh")
            local  canvasRectTransform=canvas_root:GetComponent(typeof(RectTransform))
            -- local canvasScaler = canvas_root:GetComponent(typeof(CanvasScaler))
            self.canvasY = canvasRectTransform.sizeDelta.y
            -- self.canvasRectHeight = canvasRectTransform.rect.height

            if chat_mgr_pro.GetIsAddMobileInputField() then
                self.inputFieldTMP.shouldHideMobileInput=true
                if  not self.OnKeyboardAction then
                    self.OnKeyboardAction = function(isShow,hei)


                        if hei<=0 then
                            isShow=false
                        end

                        log.Warning("OnKeyboardAction isShow:"..tostring(isShow).."hei:"..tostring(hei))

                        if isShow then
                            --self:ChangeViewMode(chat_mgr_new.ViewModeEnum.Mode2)
                            -- local cHeight=Screen.safeArea.height-hei
                            -- local cScale=cHeight/screen_util.height
                            -- local newHei=self.canvasY*(1-cScale)+chat_mgr_pro.GetIsAddMobileInputFieldHight()  --输入框的高度


                            local rate = self.canvasY / Screen.safeArea.height;
                            local newHei = hei * rate + chat_mgr_pro.GetIsAddMobileInputFieldHight();


                          --  2244-780=1464/2363=0.619   1575&(1-0.619)=600
                            log.Warning("手机宽高:"..tostring(screen_util.width).."_"..tostring(screen_util.height).."键盘高度："..tostring(hei).."".."最终高度："..tostring(newHei).."")
                            log.Warning("2手机宽高:"..tostring(Screen.safeArea.width).."_"..tostring(Screen.safeArea.height).."键盘高度："..tostring(hei).."".."最终高度："..tostring(newHei).."缩放："..tostring(rate))
                            --if newHei<600 then
                               -- newHei=600
                           -- end
                            if self.inputSelectLayoutElement.preferredHeight~=newHei then
                                self.inputSelectLayoutElement.preferredHeight=newHei
                                if curViewMode==chat_mgr_new.ViewModeEnum.Mode2 then
                                    curViewMode=chat_mgr_new.ViewModeEnum.Mode1
                                    self:ChangeViewMode(chat_mgr_new.ViewModeEnum.Mode2)
                                end
                            end
                        else

                            -- log.Warning("键盘隐藏bug 打印测试 DeactivateInputField InitMobileInput:566")
                            -- self.inputFieldTMP:DeactivateInputField()


                            if curViewMode==chat_mgr_new.ViewModeEnum.Mode2 then
                                self:ChangeViewMode(chat_mgr_new.ViewModeEnum.Mode1)
                            end
                        end
                    end
                    log.Warning("显示键盘：AddKeyboardAction")
                    --log.Log("初始化键盘高度获取，注册回调函数 ： " .. tostring(self.OnKeyboardAction))
                    --log.LoginError("初始化键盘高度获取，注册回调函数 ： " .. tostring(self.OnKeyboardAction))
                    MobileInputMgr.AddOnKeyboardAction(self.OnKeyboardAction)
                   -- MobileInputMgr.AddMobileInputFieldTMP(self.inputFieldTMP.gameObject)


                end

            else
                self.inputFieldTMP.shouldHideMobileInput=false

            end



        end

end

function M:OnShow()
    if self.recycle_ui and not self.isInit then
        self:InitOnShowUI()
    end

    window.__base:OnShow()
    
    --region 打开界面时判断聊天是否显示集结
    local alliance_mgr_extend = require "alliance_mgr_extend"
    alliance_mgr_extend.ShowChatTopMethod()
    --endregion
    
    chat_mgr_new.ReqChatDataAfterLogin()

    local uiDataMap = ui_chat_define_gw.E_ChatTypeDataMap[eChatType.Default]
    local showChannelData = uiDataMap.showChannelData
    self.closeBtn.gameObject:SetActive(true)
    self.togWorldBottom:SetActive(showChannelData.isShowWorldBottomChannel ~= nil and showChannelData.isShowWorldBottomChannel)
    self.togUnionBottom:SetActive(showChannelData.isShowAllianceBottomChannel ~= nil and showChannelData.isShowAllianceBottomChannel)
    self.togPrivate:SetActive(showChannelData.isShowPrivateChannel ~= nil and showChannelData.isShowPrivateChannel)

    self.noAllianceNoticeTips:SetActive(false)
    self.togManor.gameObject:SetActive(false)
    self.togRecruit.gameObject:SetActive(false)
    self.togPeakGameField.gameObject:SetActive(false)

    util.YieldFrame(function()
        if self and self:IsValid() then
            self:UpdateHelpInfo()
            --self:UpdateUnionInfo()
        end
    end)

    local unionListInfo = ui_window_mgr:GetState("ui_union_list")
    if unionListInfo and unionListInfo.state == 2 then
        ui_window_mgr:UnloadModule("ui_union_list")
    else
        -- if ui_window_mgr:IsModuleShown("ui_sociaty_list") then
        --     ui_window_mgr:UnloadModule("ui_sociaty_list")
        -- end
    end

    self.uiRootRectTrans = self.UIRoot:GetComponent(typeof(RectTransform))
    ---- pivot (0.5,1), anchor min (0,0), anchor max(1,1), 这三个参数若变化，需要修改 chatListBottom 的计算方式
    ---- 此处为计算简化计算，直接采用特定上面指定情况下的方式来计算
    --self.chatListBottom = self.auto_layout.anchoredPosition.y - self.auto_layout.sizeDelta.y

    self:RefreshRedEnvelopeRedDot()
    if ReviewingUtil.IsReviewing() then
        UIUtil.SetActive(self.settingButton,false)
    end
    
    self:BindUIRed(self.extensionBtn.transform,red_const.Enum.SurpriseBagEnterRed,nil,{redPath = red_const.Type.Default})
    self:BindUIRed(self.goldenEggsBtn.transform,red_const.Enum.SurpriseBagAddBtnRed,nil,{redPath = red_const.Type.Default})
end

function M:Close()
    if MobileInputMgr and self.OnKeyboardAction then
        log.Log("关闭键盘高度获取，注销回调函数 ： ".. tostring(self.OnKeyboardAction))
        MobileInputMgr.RemoveOnKeyboardAction(self.OnKeyboardAction)
        self.OnKeyboardAction = nil
    end
    
    if keyboardTicker then
        util.RemoveDelayCall(keyboardTicker)
        keyboardTicker = nil
    end

    if isOnEndEditTicker then
        util.RemoveDelayCall(isOnEndEditTicker)
        isOnEndEditTicker = nil
    end
    --关闭子面板
    ui_window_mgr:UnloadModule("ui_chat_session_list")
    ui_window_mgr:UnloadModule("ui_private_chat_panel")

    --[[ 如果在私聊界面退出 下次进入私聊列表窗口
        local pageState = chat_mgr_new.GetPageState()
        local setState = nil
        if pageState==chat_mgr_new.enum_pState.privateView then
            setState = chat_mgr_new.enum_pState.privateList
        end
        chat_mgr_new.SetPageState(setState,nil,true)
        ]]
    chat_mgr_new.SetPageState(nil, nil, true)
    entranceType = eChatType.Default
    if window.UIRoot and window:IsValid() then
        window:UnsubscribeEvents()
        if self.iconUI then
            self.iconUI:Dispose()
            self.iconUI = nil
        end

        if self.heroUI then
            self.heroUI:Dispose()
            self.heroUI = nil
        end
    end

    if self:IsValid() then
        if self.comChatList then
            self.comChatList:Reset()
        end
    end
    if self:IsValid() and not self.recycle_ui then
        window:UnsubscribeEvents()
        if self.faceSpriteAsset then
            self.faceSpriteAsset:Dispose()
            self.faceSpriteAsset = nil
        end

        if self.comChatList then
            self.comChatList:Dispose()
            self.comChatList = nil
        end

        self:EmojiListDispose()

        if self.OverLayItem then
            self.OverLayItem:Close()
            self.OverLayItem = nil
        end

        if self.inputFieldText then
            self.inputFieldText = nil
        end
        self.inputText = ""
        self.oldInputTex=nil
        if self.redEnvelopeEffect then
            self.redEnvelopeEffect:Dispose()
            self.redEnvelopeEffect = nil
        end

        if self.sociatyIconUI then
            self.sociatyIconUI:Dispose()
            self.sociatyIconUI = nil
        end
        if self.sociatyTitleUI then
            self.sociatyTitleUI:Dispose()
            self.sociatyTitleUI = nil
        end

        isOnEndEdit = true
        entranceType = eChatType.Default

        curViewMode = chat_mgr_new.ViewModeEnum.Mode1

        myHelpTicker = nil
        myUnionTicker = nil

        --清除所有已读未点击的@
        chat_mgr_new.ClearOldAt()
        canChange = false
        self.emojiInit = false
    end

    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.IsGuiding(113) then
        event.Trigger(event.DAY_RABBIT_SELL)
        unforced_guide_mgr.CloseGuide()
    end

    window.__base:Close()
    self.isInit = false

    if self.data then
        util.RemoveDelayCall(self.data.autoTranslationTimer)
    end
    self.data = nil
    --GC.Collect()
end

function M:CheckIsShowChannel(togData)
    if togData.mapChannel == chat_mgr_new.ENUM_CHANNEL.LANG then
        --  审核服情况下，不处理语言频道的显示逻辑
        return not ReviewingUtil.IsReviewing()
    elseif togData.mapChannel == chat_mgr_new.ENUM_CHANNEL.R4R5 then
        local allianceMgr = require "alliance_mgr"
        return allianceMgr.IsSelfR4R5()
    end
    return true
end

function M:GetWeeklyTaskPro()
    local task_data = require "task_data"
    local taskData = task_data.GetWeekTaskData()
    local weekTaskCfg = task_data.GetWeekTaskCfg()
    local fenzi = taskData and taskData[16] and taskData[16].currentValue or 0
    local fenmu = weekTaskCfg and weekTaskCfg[16] and weekTaskCfg[16].nConditionValue or 1
    local pro = fenzi / fenmu
    return pro
end

function M:InitInputField(str)
    local pageStat = chat_mgr_new.GetPageState()
    ------ --print("pageStat",pageStat,"enumState.lang",chat_mgr_new.enum_pState.lang,"str",str)
    if pageStat == chat_mgr_new.enum_pState.lang then
        --聊天周任务还没完成时
        local pro = self:GetWeeklyTaskPro()
        if pro < 1 then
            if canChange and const.OPEN_CHAT_AUTO_INPUT then
                if not landID then
                    landID = math.random(939, 968)
                end
                local str = lang.Get(landID)
                if self.inputFieldTMP then
                    self.inputFieldTMP.text=str
                elseif self.inputField then
                    self.inputField.text = str
                    self.inputFieldText.text = str

                end

                canChange = false
            else
                if self.inputFieldTMP then
                    self.inputFieldTMP.text=self.inputText
                else
                    self.inputField.text = self.inputText
                    self.inputFieldText.text = self.inputText
                end

            end
        end
    else
        if self.inputFieldTMP then
            self.inputFieldTMP.text=self.inputText
        else
            self.inputField.text = self.inputText
            self.inputFieldText.text = self.inputText
        end
    end
end

function M:IsOnToggleAutoTranslation(isOn)
    local bgAlpha = 1
    local pointPos = {}

    if isOn == true then
        bgAlpha = 1
        pointPos = { x = 10, y = 0, z = 0 }
    else
        bgAlpha = 0
        pointPos = { x = -10, y = 0, z = 0 }
    end
    local animationTime = 0.1
    --原点滑动
    LeanTween.moveLocal(self.tagOpenImage.gameObject, pointPos, animationTime):setEase(LeanTweenType.linear)
    --绿色背景透明度变化
    LeanTween.alphaCanvas(self.tagOpenBgImageCanvasGroup, bgAlpha, animationTime)
end

function M:IsOnToggleWorldAutoTranslation(isOn)
    local bgAlpha = 1
    local pointPos = {}

    if isOn == true then
        bgAlpha = 1
        pointPos = { x = 30.87, y = -1.27, z = 0 }
    else
        bgAlpha = 0
        pointPos = { x = -32.1, y = -1.27, z = 0 }
    end
    local animationTime = 0.1
    --原点滑动
    LeanTween.moveLocal(self.tagOpenImageWorld.gameObject, pointPos, animationTime):setEase(LeanTweenType.linear)
    --绿色背景透明度变化
    LeanTween.alphaCanvas(self.tagOpenBgImageCanvasGroupWorld, bgAlpha, animationTime)
end

function M:SubscribeEvents()
    --底部
    self.closeUIEvent = function()

        --SLG临时处理
        if const.USE_MAIN_SLG then
            ui_window_mgr:UnloadModule("ui_chat_main_new")
            local sand_ui_event_define = require "sand_ui_event_define"
            --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
            return
        end


        ----如果当前是盟战频道，关闭时还原到默认频道
        --local pageState = chat_mgr_new.GetPageState()
        --if pageState == chat_mgr_new.enum_pState.territory then
        --    chat_mgr_new.SetPageState(chat_mgr_new.enum_pState.lang, nil, false)
        --end
        --if (pageState == chat_mgr_new.enum_pState.peekGame) then
        --    local ui_match_peak_base = require("ui_match_peak_base")
        --    ui_match_peak_base.ShowWindow()
        --
        --    self:UnsubscribeEvents()
        --    ui_window_mgr:UnloadModule("ui_chat_main_new")
        --else
        --    ui_window_mgr:UnloadModule("ui_chat_main_module")
        --end
    end
    self.closeBtn.onClick:AddListener(self.closeUIEvent)

    self.clickEmoji1Event = function(isOn)
        if isOn then
            self.emoji_page1CheckMark.gameObject:SetActive(true)
            if curEmojiPage ~= 1 then
                self:PlayListAni(false)
                curEmojiPage = 1
            end
        end
    end
    self.emoji_page1.onValueChanged:AddListener(self.clickEmoji1Event)

    self.clickEmoji2Event = function(isOn)
        if isOn then
            self.emoji_page1CheckMark.gameObject:SetActive(false)
            if curEmojiPage ~= 2 then
                self:PlayListAni(true)
                curEmojiPage = 2
            end
        end
    end
    self.emoji_page2.onValueChanged:AddListener(self.clickEmoji2Event)

    self.settingButtonEvent = function()
        --ui_window_mgr:ShowModule("ui_chat_block")
        ui_window_mgr:ShowModule("chat_setting_panel")
    end
    self.settingButton.onClick:AddListener(self.settingButtonEvent)

    self.OnToggleAutoTranslationEvent = function(isOn)
        --开启标识按钮显示
        if self.data.isAutoTranslationAlliance ~= isOn then
            chat_mgr_new.SetIsAutoTranslation(isOn)
            if isOn == true then
                --开启自动翻译，刷新ui上需要翻译的内容
                self.data.autoTranslationTimer = util.DelayCallOnce(0.1, function()
                    self.comChatList:TranslationShowingChatItems()
                end)
            end
            self.data.isAutoTranslationAlliance = isOn
        end
        self:IsOnToggleAutoTranslation(isOn)
    end

    self.AutoTranslationWorldLockEvent = function()
        flow_text.Add(lang.Get(1001618))
    end

    self.OnToggleWorldAutoTranslationEvent = function(isOn)
        --开启标识按钮显示
        chat_mgr_new.SetIsAutoTranslation(isOn, chat_mgr_new.ENUM_TRANSLATION_TYPE.WORLD)
        if isOn == true then
            --开启自动翻译，刷新ui上需要翻译的内容
            self.data.autoTranslationTimer = util.DelayCallOnce(0.1, function()
                self.comChatList:TranslationShowingChatItems()
            end)
        end
        self:IsOnToggleWorldAutoTranslation(isOn)
    end
    --海外版本开启自动翻译
    if util.ShouldUseCustomSDKUI() then
        self.togAutoTranslation.onValueChanged:AddListener(self.OnToggleAutoTranslationEvent)
        self.togAutoTranslationWorld.onValueChanged:AddListener(self.OnToggleWorldAutoTranslationEvent)
    end

    --输入栏
    self.emojiBtnEvent = function()
        self:EmojiInit()
        isOnEndEdit = false
        self:ChangeViewMode(curViewMode == chat_mgr_new.ViewModeEnum.Mode3 and chat_mgr_new.ViewModeEnum.Mode1 or chat_mgr_new.ViewModeEnum.Mode3)
    end
    self.emojiBtn.onClick:AddListener(self.emojiBtnEvent)

    self.extensionBtnEvent = function()
        isOnEndEdit = false
        self:ChangeViewMode(curViewMode == chat_mgr_new.ViewModeEnum.Mode4 and chat_mgr_new.ViewModeEnum.Mode1 or chat_mgr_new.ViewModeEnum.Mode4)
    end
    self.extensionBtn.onClick:RemoveAllListeners(self.extensionBtnEvent)--目前逻辑偶现存在注册多次
    self.extensionBtn.onClick:AddListener(self.extensionBtnEvent)

    --超过这个数值开始移动
    local inputPosX = 515
    self.InputFieldHandler = function(str)
        -- log.Error(str)
        -- self.inputFieldTMPText.text=str
        local convertStr=str
        if EmojiConverter then
            convertStr=EmojiConverter.ConvertEmojiToUnicodeCode(str, "[3EAB&{0}]")--[3EAB&1F602][3EAB&1F603][3EAB&1F604][3EAB&1F47F] --[3&1F601][3&1F602][3&1F603][3&1F604]
        end
        -- log.Warning(convertStr)
        if convertStr ~= str then
            str = convertStr
            self.inputField.text = str
        end

        if string_util.GetUTF8_length(str) > maxLimitLen then
            self.inputField.text = self.inputFieldText.text
            flow_text.Add(lang.Get(100455))
            return
        end

        self.inputFieldText.text = str

        local isChanged = false
        local hyperText = nil
        hyperText = string.match(str, "<")
        if hyperText ~= nil then
            isChanged = true
            str = string.gsub(str, hyperText, '')
        end
        hyperText = nil
        hyperText = string.match(str, ">")
        if hyperText ~= nil then
            isChanged = true
            str = string.gsub(str, hyperText, '')
        end

        if isChanged then
            self.inputField.text = str
        end

        if self:IsRTLEnvironment() then
            local moveX = self.r_inputFieldContentBg.sizeDelta.x - inputPosX + 260
            if moveX > 260 then
                self.r_inputFieldContentBg.localPosition = { x = moveX, y = 0, z = 0 }
            else
                self.r_inputFieldContentBg.localPosition = { x = 260, y = 0, z = 0 }
            end
        else
            local moveX = inputPosX - self.r_inputFieldContentBg.sizeDelta.x - 260
            if moveX < -260 then
                self.r_inputFieldContentBg.localPosition = { x = moveX, y = 0, z = 0 }
            else
                self.r_inputFieldContentBg.localPosition = { x = -260, y = 0, z = 0 }
            end
        end
        --从周任务跳转过来语言频道时输入框随机赋值功能
        --self:InitInputField(str)
        local pageStat = chat_mgr_new.GetPageState()
        local pro = self:GetWeeklyTaskPro()
        if pageStat ~= chat_mgr_new.enum_pState.lang or pro >= 1 then
            self.inputText = str
        end
        self.sendBtn.gameObject:SetActive(self.inputField.text ~= "")
        self.extensionBtn.gameObject:SetActive(self.inputField.text == "")
        --@功能
        local inputStr = self.inputField.text
        if string.sub(inputStr, -1) == "@" then
            local ui_at_player = require "ui_at_player"
            ui_at_player.Open(function(atName, playerID)
                chat_mgr_new.SetAtPlayerData(playerID, atName)
                self.inputField.text = inputStr .. atName .. " "
                self.inputField:ActivateInputField()
                util.DelayCall(0.06, function()
                    if window and window.UIRoot then
                        self.inputField:MoveTextEnd(false)
                    end
                end)
            end)
        end
    end

    self.InputFieldTMPHandler = function(str)

         --log.Warning("oldInputFieldTMPTxt="..self.inputFieldTMP.text)
        --  log.Warning("newInputFieldTMPTxt="..str)
          local isCls=false
        if  self.oldInputText then
            if string_util.GetUTF8_length(str)<string_util.GetUTF8_length(self.oldInputText) then
                isCls=true
            end
        end

        local convertStr=EmojiConverter.GetOutputText(str)--[3EAB&1F602][3EAB&1F603][3EAB&1F604][3EAB&1F47F] --[3&1F601][3&1F602][3&1F603][3&1F604]
        if isCls then
            convertStr=EmojiConverter.ClsBadEmoji(convertStr)
        end
        if convertStr ~= str then
            self.inputFieldTMP.text = convertStr
            --self.inputFieldTMP:MoveTextEnd(false)
            if curViewMode==chat_mgr_new.ViewModeEnum.Mode2 then
                self.inputFieldTMP:ActivateInputField()
            end
        end

        local cLen=string_util.GetUTF8_length(convertStr)-EmojiConverter.DeductEmojiLength(convertStr)
        if cLen > maxLimitLen then
           flow_text.Add(lang.Get(100455))
            if  self.oldInputText then
                self.inputFieldTMP.text = self.oldInputText
            end
           return
        end
        self.oldInputText=convertStr
        if curViewMode==chat_mgr_new.ViewModeEnum.Mode2 then
            self.inputSendBtn.gameObject:SetActive(self.inputFieldTMP.text ~= "")
            self.sendBtn.gameObject:SetActive(false)

        else
            self.sendBtn.gameObject:SetActive(self.inputFieldTMP.text ~= "")
            self.inputSendBtn.gameObject:SetActive(false)
        end
            self.extensionBtn.gameObject:SetActive(self.inputFieldTMP.text == "")

        self.inputText = str
        --@功能
        local inputStr = self.inputFieldTMP.text
        if string.sub(inputStr, -1) == "@" then
            local ui_at_player = require "ui_at_player"
            ui_at_player.Open(function(atName, playerID)
                self.inputFieldTMP.text = inputStr .. atName .. " "
                chat_mgr_new.SetAtPlayerData(playerID, atName)
                --self.inputFieldTMP:ActivateInputField()
                util.DelayCall(0.06, function()
                    if window and window.UIRoot then
                        --self.inputFieldTMP:MoveTextEnd(false)
                    end
                end)
            end)
        end
        
    end



    if chat_mgr_pro.GetIsUseChatPro() then
        self.inputFieldTMP.onValueChanged:AddListener(self.InputFieldTMPHandler)

    else
        self.inputField.onValueChanged:AddListener(self.InputFieldHandler)
    end


    self.updateTaskDataEvent = function()
        if self:IsValid() then
            local pro = self:GetWeeklyTaskPro()
            if pro < 1 then
                self:InitInputField()
            end
        end
    end
    event.Register(event.UPDATE_TASK_DATA, self.updateTaskDataEvent)


    --发送
    self.likeBtnEvent = function()
        isOnEndEdit = false
        self:SendMsg(mq_common_pb.enSpeak_Like)
    end
    self.likeBtn.onClick:AddListener(self.likeBtnEvent)

    self.goldenEggsEvent = function()
        ui_window_mgr:ShowModule("ui_surprise_bag")
    end

    self.sendBtnEvent = function()

        log.Warning("sendBtnEvent")
        if chat_mgr_pro.GetIsUseChatPro() ==false then
            isOnEndEdit = false
            self:ChangeViewMode(chat_mgr_new.ViewModeEnum.Mode1)
        else
            isOnEndEdit = false
            if curViewMode~=chat_mgr_new.ViewModeEnum.Mode2 then
                --- self.inputFieldTMP:ActivateInputField()
                log.Warning("键盘隐藏bug 打印测试 DeactivateInputField sendBtnEvent:1157")
                self.inputFieldTMP:DeactivateInputField()
                self:ChangeViewMode(curViewMode)

            end
        end
        self:SendMsg(mq_common_pb.enSpeak_Text)
    end

    if chat_mgr_pro.GetIsUseChatPro()  then
        local   PointClickCom = self.inputSendBtn:GetComponent(typeof(PointClick))
        PointClickCom.Onclick=self.sendBtnEvent
    end
    self.sendBtn.onClick:AddListener(self.sendBtnEvent)



    self.mikeBtnEvent = function()
        flow_text.Add(lang.Get(902))
    end
    self.mikeBtn.onClick:AddListener(self.mikeBtnEvent)

    self.atBtnEvent = function()
        local pageStat = chat_mgr_new.GetPageState()
        local atState = chat_mgr_new.GetChannelAtState()
        --goto 跳转
        if atState[pageStat] then
            if  chat_mgr_pro.GetIsUseChatPro() then
                self.comChatList:MoveToIndex(atState[pageStat].realPos)
            end
        end
        --清除状态
        chat_mgr_new.ClearChannelAt(pageStat)

        self:UpdataAtBtnState()
    end
    self.atBtn.onClick:AddListener(self.atBtnEvent)
    self.mayDayMarketBtnEvent = function()
        -- local may_day_mgr = require "may_day_mgr"
        -- may_day_mgr.OpenMarket(nil, false)
    end
    self.mayDayNTFBtnEvent = function()
        -- local ui_may_day_chat_notice = require "ui_may_day_chat_notice"
        -- ui_window_mgr:ShowModule("ui_may_day_chat_notice")
        -- --may_day_mgr.OpenNTF()

        -- local unforced_guide_mgr = require "unforced_guide_mgr"
        -- if unforced_guide_mgr.IsGuiding(113) then
        --     event.Trigger(event.DAY_RABBIT_SELL)
        --     unforced_guide_mgr.CloseGuide()
        -- end
    end

    --频道tog
    self.OnToggleEvent = function(data)
        if data.obj.isOn then
            self:RefreshTopTogShow(data)
            local pageState = chat_mgr_new.GetPageState()
            if data.childPoint then
                --父节点，切换频道
                for i, v in ipairs(data.childPoint) do
                    if self.toggleData[v] and self.toggleData[v].mapState and self.toggleData[v].mapState[1] == pageState then
                        self.toggleData[v].obj.isOn = true
                        return
                    end
                end
                self.toggleData[data.defaultChild].obj.isOn = false--确保一定执行toggle事件
                self.toggleData[data.defaultChild].obj.isOn = true
            else
                local isEquel = false
                for i, v in pairs(data.mapState) do
                    if v == pageState then
                        isEquel = true
                        break
                    end
                end
                if not isEquel then
                    if pageState == chat_mgr_new.enum_pState.lang then
                        canChange = true
                        landID = math.random(939, 968)
                    end
                    if data.moduleName and not module_on_off.GetModuleOnOff(data.moduleName) then
                        flow_text.Add(lang.Get(100484))
                        return
                    end
                    --标记已读、保存页面状态
                    chat_mgr_new.NtfMsgReaded(data.mapChannel)
                    chat_mgr_new.SetPageState(data.mapState[1], nil, false, true)
                    ui_chat_data_gw.TrySetMainChatPageStateAndRefresh(data.mapState[1])
                    local pro = self:GetWeeklyTaskPro()
                    if pro < 1 then
                        self:InitInputField()
                    end
                    self:RefreshRecruitTip(data.mapChannel)
                end
            end
        end
    end

    for i, v in ipairs(self.toggleData) do
        v.obj.onValueChanged:AddListener(function()
            self.OnToggleEvent(v)
        end)
    end


    --数据or状态刷新事件
    self.OnChannelDotEvent = function()
        self:UpdateTogDot()
    end
    event.Register(event.UPDATE_CHANNEL_DOT, self.OnChannelDotEvent)
    self.OnPageStateChangeEvent = function()
        self:UpdateTogState()
        self:UpdateView()
        self:UpdataAtBtnState()
    end
    event.Register(event.UPDATE_CHAT_PAGE_CHANGE, self.OnPageStateChangeEvent)

    self.OnHelpOther = function(_, id)
        self.comChatList:SetHelpOther(id)
    end
    event.Register(event.PLEAGUEGELP_HELP_ROLE_ID, self.OnHelpOther)
    --聊天内容变化
    self.OnChatMsgUpdate = function(event, channelType, singleMsg, isHelp)
        self:UpdateChatList(channelType, nil, singleMsg, isHelp)
        self:RefreshHelpRed()
        self:RefreshRedEnvelopeRedDot()
        self:RefreshRecruitTip(channelType)
        self:TryShowAllianceNotice()
    end
    event.Register(event.UPDATE_CHAT_MSG, self.OnChatMsgUpdate)

    --频道发生变化
    self.OnChannelChange = function()
        self:UpdateChannel()
    end
    event.Register(event.UPDATE_CUR_CHANNEL, self.OnChannelChange)

    --有人@你
    self.OnAtStateChange = function()
        self:UpdataAtBtnState()
    end
    event.Register(event.AT_STATE_CHANGE, self.OnAtStateChange)

    -- self.OnPrivateChatMsgUpdate = function( event,sessionId,singleMsg )
    --     self:UpdateChatList( chat_mgr_new.ENUM_CHANNEL.PRIVATE,sessionId,singleMsg)
    -- end
    -- event.Register(event.UPDATE_PRIVATE_CHAT_MSG,self.OnPrivateChatMsgUpdate)

    self.OnClickShowRank = function()
        ui_window_mgr:ShowModule("ui_chat_help_rank")

        local roleName = tostring(player_mgr.GetRoleName()) or ""
        local baseData = {
            id = alliance_data.GetUserAllianceId(),
            strName = alliance_data.GetUserAllianceName()
        }

        if baseData.id ~= 0 then
            local json_str = json.encode({
                guild_id = baseData.id,
                guild_name = baseData.strName,
                role_name = roleName
            })
            event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_clickeranking", json_str)
        end
    end
    if not util.IsObjNull(self.showRankBtn) then
        self.showRankBtn.onClick:AddListener(self.OnClickShowRank)
    end

    self.OnClickBtnRedEnvelope = function()
        local isOpen = festival_activity_mgr.IsActivityOpenByCodeType(festival_activity_cfg.ActivityCodeType.ChatRedPackage)
        if isOpen then
            -- ui_window_mgr:ShowModule("ui_anniversary_red_package_base")
        else
            flow_text.Add(lang.Get(17970))
            self.BtnRedEnvelope.gameObject:SetActive(false)
        end
    end
    self.BtnRedEnvelope.onClick:AddListener(self.OnClickBtnRedEnvelope)

    self.OnClickUnionInvite = function()
        -- net_chat_module_new.Send_PLEAGUEHELP_SEEKHELP()
        -- self:UpdateUnionInfo()
        --local sociaty_data = require "sociaty_data"
        --local time = sociaty_data.getleagueRecruitCD()
        --if time <= 0 then
        --    local net_leaguepro_module = require "net_leaguepro_module"
        --    net_leaguepro_module.C2SLeagueInfoReq(sociaty_data.GetLeagueData().id)
        --    local win = ui_window_mgr:ShowModule("ui_sociaty_detail")
        --    win:SetInputParam(3, sociaty_data.GetLeagueData().id, sociaty_data.GetLeagueData())
        --else
        --    flow_text.Add(lang.Get(103016))
        --end
        -- 发送联盟邀请
    end
    if not util.IsObjNull(self.Btn_Invite) then
        self.Btn_Invite.onClick:AddListener(self.OnClickUnionInvite)
    end
    --self.UpdateUnionInviteProxy = function()
    --    self:UpdateUnionInfo()
    --end
    --event.Register(event.UPDATE_LEAGUE_RECRUIT, self.UpdateUnionInviteProxy)

    self.UpdateHelpInfoProxy = function()
        self:UpdateHelpInfo()
    end
    event.Register(event.PLEAGUEHELP_INFO_CHANGE, self.UpdateHelpInfoProxy)
    event.Register(event.PLEAGUEGELP_REWARDINFO_CHANGE, self.UpdateHelpInfoProxy)

    self.OnClickAccept = function()
        local myHelpInfo = chat_mgr_new.GetMyHelpInfo()
        local flagID = myHelpInfo and myHelpInfo.flagID
        if flagID then
            net_chat_module_new.Send_PLEAGUEHELP_RECIEVEAWARD(flagID)
        end
    end

    self.OnClickPlease = function()
        local helpInfo = chat_mgr_new.GetMyHelpInfo()
        if helpInfo ~= nil and helpInfo.helpID ~= 0 then
            net_chat_module_new.Send_PLEAGUEHELP_SEEKHELP(helpInfo.helpID)
        else
            --打开请求奖励界面
            local ui_window_mgr = require "ui_window_mgr"
            ui_window_mgr:ShowModule("ui_help_reward_choose")
        end

        -- net_chat_module_new.Send_PLEAGUEHELP_SEEKHELP()

    end

    self.unionTipsBtnEvent = function()
        --判断联盟建筑物是否解锁
        --未解锁提示：解锁联盟中心建筑后解锁
        --已解锁跳转到推荐联盟界面
        local function_open_mgr = require "function_open_mgr"
        local isUnlock = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Alliance)
        if isUnlock then
            local alliance_mgr = require "alliance_mgr"
            alliance_mgr.ShowModule()
            self.closeUIEvent()
        else
            --建造联盟中心建筑后解锁
            flow_text.Add(lang.Get(600541))
        end
    end
    self.unionTipsBtn.onClick:AddListener(self.unionTipsBtnEvent)

    self.OnUpdatePageView = function(eventame, tMsg)
        self:InitTogList()
        if tMsg then
            -- dump(tMsg,"kasug chat---removeChat:")
            self.comChatList:RemoveData(tMsg)
            self:TryShowAllianceNotice()
        else
            self:UpdateView()
        end
    end
    event.Register(event.UPDATE_CHAT_VIEW, self.OnUpdatePageView)

    self.OnEndEditEvent = function(str)
        --self.inputField:DeactivateInputField()

        -- isOnEndEditTicker = util.DelayOneCall(0.1, function()
        if isOnEndEdit then
            self:ChangeViewMode(chat_mgr_new.ViewModeEnum.Mode1)
            --self:SendMsg(mq_common_pb.enSpeak_Text)
        end
        isOnEndEdit = true
        -- if self.timeTicker then
        --     util.RemoveDelayCall(self.timeTicker)
        --     self.timeTicker = nil
        -- end
        --  end)
        -- log.Warning("OnEndEditEvent")
    end
    if chat_mgr_pro.GetIsUseChatPro() ==false then
        self.inputField.onEndEdit:AddListener(self.OnEndEditEvent)
    else
        self.OnSubmitEvent = function(str)
            self:SendMsg(mq_common_pb.enSpeak_Text)
        end
        --   self.inputFieldTMP.onEndEdit:AddListener(self.OnEndEditEvent)
        self.inputFieldTMP.onSubmit:AddListener(self.OnSubmitEvent)
    end


    --发起发布公告流程按钮
    self.onShowAllianceNoticeBtnClick = function()
        ui_window_mgr:ShowModule("ui_alliance_notice_release_panel")
    end
    self.btn_showAllianceNoticeRelease.onClick:AddListener(self.onShowAllianceNoticeBtnClick)
    self.btn_topShowAllianceNotice.onClick:AddListener(self.onShowAllianceNoticeBtnClick)
    self.btn_topTipsClose.onClick:AddListener(function()
        local data = ui_chat_data_gw.GetChatTopTipsData()
        if data and data.isShow and data.data.closeFuncCB then
            data.data.closeFuncCB()
        end
        self:TopTipsHide()
    end)
    self.setTopTipsDataAndShow = function(funcName)
        self:CheckTopTipsDataAndShow()
    end
    event.Register(event.CHAT_SET_TOP_TIPS_AND_SHOW, self.setTopTipsDataAndShow)

    self.topTipsHide = function()
        self:TopTipsHide()
    end

    event.Register(event.CHAT_TOP_TIPS_HIDE, self.topTipsHide)
    self.onEmojiBackClick = function()
        self:ChangeViewMode(chat_mgr_new.ViewModeEnum.Mode1)
        self.btn_emojiBack:SetActive(false)
        self.btn_contentMask:SetActive(false)
    end

    self.btn_emojiBack.onClick:AddListener(self.onEmojiBackClick)
    
    self.onGo2BottomClick = function()
        if self.comChatList then
            self.comChatList:MoveToBot()
        end
    end
    self.btn_go2Bottom.onClick:AddListener(self.onGo2BottomClick)
    
    self.evt_CHAT_SHOW_GO2BOTTOM_BTN= function(evt,state)
        if self:IsValid() and self.btn_go2Bottom then
            self.btn_go2Bottom:SetActive(state)
        end
    end
    event.Register(event.CHAT_SHOW_GO2BOTTOM_BTN,self.evt_CHAT_SHOW_GO2BOTTOM_BTN)
end

function M:UnsubscribeEvents()

    self:UnsubscribeBtnEvents()

    event.Unregister(event.UPDATE_CHANNEL_DOT, self.OnChannelDotEvent)
    event.Unregister(event.UPDATE_CHAT_PAGE_CHANGE, self.OnPageStateChangeEvent)
    event.Unregister(event.UPDATE_CHAT_MSG, self.OnChatMsgUpdate)
    event.Unregister(event.UPDATE_CUR_CHANNEL, self.OnChannelChange)
    event.Unregister(event.AT_STATE_CHANGE, self.OnAtStateChange)
    --event.Unregister(event.ACTIVITY_PER_SECOND,self.TestEvent)

    event.Unregister(event.PLEAGUEHELP_INFO_CHANGE, self.UpdateHelpInfoProxy)
    event.Unregister(event.PLEAGUEGELP_REWARDINFO_CHANGE, self.UpdateHelpInfoProxy)

    event.Unregister(event.PLEAGUEGELP_HELP_ROLE_ID, self.OnHelpOther)
    --event.Unregister(event.UPDATE_LEAGUE_RECRUIT, self.UpdateUnionInviteProxy)
    event.Unregister(event.UPDATE_CHAT_VIEW, self.OnUpdatePageView)
    event.Unregister(event.CHAT_SET_TOP_TIPS_AND_SHOW, self.setTopTipsDataAndShow)
    event.Unregister(event.CHAT_TOP_TIPS_HIDE, self.topTipsHide)
    event.Unregister(event.SHOW_RESULT_MODULE, self.onShowModuleEvent)
    event.Unregister(event.UI_MODULE_CLOSE, self.onModuleCloseEvent)
    event.Unregister(event.CHAT_SHOW_GO2BOTTOM_BTN,self.evt_CHAT_SHOW_GO2BOTTOM_BTN)
end

function M:UnsubscribeBtnEvents()
    self.closeBtn.onClick:RemoveListener(self.closeUIEvent)
    self.emoji_page1.onValueChanged:RemoveListener(self.clickEmoji1Event)
    self.emoji_page2.onValueChanged:RemoveListener(self.clickEmoji2Event)
    self.settingButton.onClick:RemoveListener(self.settingButtonEvent)
    self.emojiBtn.onClick:RemoveListener(self.emojiBtnEvent)
    self.extensionBtn.onClick:RemoveListener(self.extensionBtnEvent)
    if chat_mgr_pro.GetIsUseChatPro() then
        self.inputFieldTMP.onValueChanged:RemoveListener(self.InputFieldTMPHandler)
        self.inputFieldTMP.onSubmit:RemoveListener(self.OnSubmitEvent)
    else
        self.inputField.onValueChanged:RemoveListener(self.InputFieldHandler)
        self.inputField.onEndEdit:RemoveListener(self.OnEndEditEvent)
    end
    event.Unregister(event.UPDATE_TASK_DATA, self.updateTaskDataEvent)

    self.likeBtn.onClick:RemoveListener(self.likeBtnEvent)
    self.atBtn.onClick:RemoveListener(self.atBtnEvent)
    self.sendBtn.onClick:RemoveListener(self.sendBtnEvent)
    self.mikeBtn.onClick:RemoveListener(self.mikeBtnEvent)
    self.unionTipsBtn.onClick:RemoveListener(self.unionTipsBtnEvent)
    self.btn_showAllianceNoticeRelease.onClick:RemoveListener(self.onShowAllianceNoticeBtnClick)
    self.btn_topShowAllianceNotice.onClick:RemoveListener(self.onShowAllianceNoticeBtnClick)
    self.btn_topTipsClose.onClick:RemoveAllListeners()
    self.btn_emojiBack.onClick:RemoveListener(self.onEmojiBackClick)
    self.btn_contentMask.onClick:RemoveAllListeners()
    self.btn_go2Bottom.onClick:RemoveListener(self.onGo2BottomClick)

    for i, v in ipairs(self.toggleData) do
        if v and v.obj and not util.IsObjNull(v.obj) then
            v.obj.onValueChanged:RemoveAllListeners()
        end
    end

    if self.showRankBtn and not util.IsObjNull(self.showRankBtn) then
        self.showRankBtn.onClick:RemoveListener(self.OnClickShowRank)
    end
    if self.Btn_Invite and not util.IsObjNull(self.Btn_Invite) then
        self.Btn_Invite.onClick:RemoveListener(self.OnClickUnionInvite)
    end

    if self.allianceNoticeItem then
        self.allianceNoticeItem:Dispose()
        self.allianceNoticeItem = nil
    end
end

function M:UpdateUnionInfo()
    local sociaty_data = require "sociaty_data"
    local baseData = sociaty_data.GetLeagueData()
    if not baseData or not baseData.id then
        return
    end
    if baseData.strName then
        self.myUnionName.text = lang.Get(16430) .. baseData.strName
    end
    local iconCfg = game_scheme:LeagueIcon_0(baseData.iIconID)
    --GetUserAllianceFlag
    if iconCfg then
        self.sociatyIconUI = self.sociatyIconUI or sociaty_icon.CUIContent():Init(self.myUnionIcon.transform, iconCfg, nil, 0.560)
        self.sociatyIconUI:UpdateData(iconCfg)
    end
    local titleCfg = nil --game_scheme:RoleTitle_0(baseData.iTitleID)
    if titleCfg then
        local data = { rivalType = titleCfg.rivalType, NameID = titleCfg.NameID, NameColor = titleCfg.NameColor, NameColorB = titleCfg.NameColorB, outline = titleCfg.outline }
        data.textScale = 1.3
        self.sociatyTitleUI = self.sociatyTitleUI or sociaty_title.CUIContent():Init(self.myUnionTitle.transform, data, nil, 0.560)
        self.sociatyTitleUI:UpdateData(data)
    end
    self.myUnionTitle.gameObject:SetActive(titleCfg)
    if baseData.iCount then
        local curLevel = 1
        local maxNum = sociaty_data.GetCurLvCfg(curLevel).memberLimit
        self.myUnionNum.text = lang.Get(16056) .. baseData.iCount .. "/" .. maxNum
    end
    local sociaty_data = require "sociaty_data"
    local reqTimes = sociaty_data.GetleagueRecruit()
    --print("reqTimes:",reqTimes)
    if reqTimes and reqTimes == 0 then
        self.myUnionCountDown.gameObject:SetActive(false)
        -- self.myHelpBtn.enabled = true
    else
        self.myUnionCountDown.gameObject:SetActive(false)
        local nextTime = sociaty_data.GetNextleagueRecruit()
        --print("reqTimes:",reqTimes,"nextTime:",nextTime,"GetServerTime:",net_login_module.GetServerTime())
        if nextTime and nextTime - net_login_module.GetServerTime() > 0 then
            if myUnionTicker then
                util.RemoveDelayCall(myUnionTicker)
                myUnionTicker = nil
            end
            self.myUnionCountDown.gameObject:SetActive(true)
            myUnionTicker = util.IntervalCall(1, function()
                if window and window:IsValid() and not util.IsObjNull(self.myUnionCountDown) then
                    if nextTime and nextTime - net_login_module.GetServerTime() > 0 then
                        local ui_utils = require "ui_utils"
                        self.myUnionCountDown.text = ui_utils.TimeCountdown(nextTime - net_login_module.GetServerTime())
                    else
                        self.myUnionCountDown.gameObject:SetActive(false)
                    end
                else
                    return true
                end
            end)
        else
            self.myUnionCountDown.gameObject:SetActive(false)
        end
    end
end

--求助刷新
function M:UpdateHelpInfo()
    local net_login_module = require "net_login_module"
    local ui_utils = require "ui_utils"
    local helpInfo = chat_mgr_new.GetMyHelpInfo()
    local prog = 0
    local progEnd = 1
    local cfg_help = nil
    if helpInfo then
        cfg_help = game_scheme:LeagueHelp_0(helpInfo.helpID)
        prog = helpInfo.prog or 0

        if cfg_help then
            progEnd = cfg_help.helpNum
            local cfg_reward = game_scheme:Reward_0(cfg_help.seekHelpReward.data[0])
            if cfg_reward then
                if cfg_reward.iRewardType == 1 then
                    --物品
                    self.iconUI = self.iconUI or goods_item.CGoodsItem():Init(self.myHelpIconRoot, nil, 0.55)
                    self.iconUI:SetGoods(nil, cfg_reward.arrParam[0], cfg_reward.arrParam[1], function()
                        iui_item_detail.Show(cfg_reward.arrParam[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, 1)
                    end)
                    self.iconUI:SetFrameBg(3)
                elseif cfg_reward.iRewardType == 2 then
                    --英雄
                    local hero_item = require "hero_item_new"
                    self.heroUI = self.heroUI or hero_item.CHeroItem():Init(self.myHelpIconRoot, function()
                        self.heroUI:DisplayInfo()
                    end, 0.55)
                    self.heroUI:SetHero({ heroID = cfg_reward.arrParam[0] }, function()
                        local ui_archive_detail = require "ui_archive_detail"
                        ui_archive_detail.SetData({ { heroID = cfg_reward.arrParam[0], starLv = cfg_reward.arrParam[1] } }, 1)
                        ui_window_mgr:ShowModule("ui_archive_detail", nil)
                    end)
                end
                self.myHelpInitObj.gameObject:SetActive(false)
            else
                self.myHelpInitObj.gameObject:SetActive(true)
            end
        else
            self.myHelpInitObj.gameObject:SetActive(true)
        end
        if prog > progEnd then
            prog = progEnd
        end
        self.myHelpProgressText.text = string.format("%d/%d", prog, progEnd)
        self.myHelpProgressImg.value = prog / progEnd
    else
        self.myHelpInitObj.gameObject:SetActive(true)
        self.myHelpProgressText.text = string.format("%d/%d", prog, progEnd)
        self.myHelpProgressImg.value = 0
    end
    self.myHelpBtn.onClick:RemoveAllListeners()
    if prog >= progEnd then
        self.myHelpCountDownTime.gameObject:SetActive(false)
        self.myHelpText.gameObject:SetActive(false)
        self.myProgressObj.gameObject:SetActive(true)
        self.myHelpTextWithProgress.gameObject:SetActive(true)
        self.myHelpTextWithProgress.text = lang.Get(16754)
        self.myHelpBtn.onClick:AddListener(self.OnClickAccept)
        self.myHelpGray:Switch(0)
        self.myHelpProgressImg.gameObject:SetActive(true)
        self.myHelpBtn.enabled = true
    else
        local reqTimes = chat_mgr_new.GetReqTimes()
        if reqTimes and reqTimes == 0 then
            self.myHelpCountDownTime.gameObject:SetActive(false)
            self.myHelpText.gameObject:SetActive(true)
            self.myProgressObj.gameObject:SetActive(false)
            self.myHelpBtn.onClick:AddListener(self.OnClickPlease)
            self.myHelpGray:Switch(0)
            self.myHelpProgressImg.gameObject:SetActive(true)
            self.myHelpBtn.enabled = true
        else
            self.myHelpCountDownTime.gameObject:SetActive(false)
            self.myHelpText.gameObject:SetActive(false)
            self.myProgressObj.gameObject:SetActive(true)
            local nextTime = chat_mgr_new.GetNextReqTimes()
            if nextTime and nextTime - net_login_module.GetServerTime() > 0 then
                if myHelpTicker then
                    util.RemoveDelayCall(myHelpTicker)
                    myHelpTicker = nil
                end
                self.myHelpBtn.enabled = false
                self.myHelpGray:Switch(1)
                self.myHelpTextWithProgress.gameObject:SetActive(false)
                self.myHelpCountDownTime.gameObject:SetActive(true)
                myHelpTicker = util.IntervalCall(1, function()
                    if window and window:IsValid() and not util.IsObjNull(self.myHelpCountDownTime) then
                        if nextTime and nextTime - net_login_module.GetServerTime() > 0 then
                            self.myHelpCountDownTime.text = ui_utils.TimeCountdown(nextTime - net_login_module.GetServerTime())
                        else
                            self.myHelpBtn.enabled = true
                            self.myHelpGray:Switch(0)
                            self.myHelpProgressImg.gameObject:SetActive(true)
                            self.myHelpCountDownTime.gameObject:SetActive(false)
                            self.myHelpTextWithProgress.gameObject:SetActive(true)
                            self.myHelpTextWithProgress.text = lang.Get(16753)
                            self.myHelpBtn.onClick:AddListener(self.OnClickPlease)
                        end
                    else
                        return true
                    end
                end)
            else
                self.myHelpBtn.enabled = true
                self.myHelpCountDownTime.gameObject:SetActive(false)
                self.myHelpGray:Switch(0)
                self.myHelpProgressImg.gameObject:SetActive(true)
                self.myHelpTextWithProgress.gameObject:SetActive(true)
                self.myHelpTextWithProgress.text = lang.Get(16753)
                self.myHelpBtn.onClick:AddListener(self.OnClickPlease)
            end
        end
    end
    self:UpdateHelpReddot()
end

function M:UpdateHelpReddot()
    local helpInfo = chat_mgr_new.GetMyHelpInfo()
    if helpInfo then
        local cfg_help = game_scheme:LeagueHelp_0(helpInfo.helpID)
        local maxNum = game_scheme:InitBattleProp_0(368).szParam.data[0]
        maxNum = maxNum or 0
        local todayNum = chat_mgr_new.GetTodayGetRewardTimes() or 0
        local nextTime = chat_mgr_new.GetNextReqTimes()
        if cfg_help then
            if todayNum < maxNum and nextTime and (nextTime - net_login_module.GetServerTime()) < 0 then
                self.myHelpRed.gameObject:SetActive(true)
            else
                self.myHelpRed.gameObject:SetActive(false)
            end
            if cfg_help.helpNum == helpInfo.prog then
                self.myHelpRed.gameObject:SetActive(true)
            end
        else
            local nextTime = chat_mgr_new.GetNextReqTimes()
            if nextTime and nextTime - net_login_module.GetServerTime() > 0 then
                self.myHelpRed.gameObject:SetActive(false)
            else
                self.myHelpRed.gameObject:SetActive(true)
            end
        end
    else
        self.myHelpRed.gameObject:SetActive(false)
    end
end
--初始化聊天频道
function M:InitTogList()
    local enumState = chat_mgr_new.enum_pState
    self.toggleData = {
        --语言频道
        [1] = { obj = self.togLang, texts = self.togTextLang,
                mapState = { enumState.lang }, mapChannel = chat_mgr_new.ENUM_CHANNEL.LANG, parentObj = self.togWorldBottom },
        --世界频道
        [2] = { obj = self.togWorld, texts = self.togTextWorld,
                mapState = { enumState.world }, mapChannel = chat_mgr_new.ENUM_CHANNEL.WORLD, parentObj = self.togWorldBottom },
        --联盟频道
        [3] = { obj = self.togUnion, texts = self.togTextUnion,
                mapState = { enumState.guide }, mapChannel = chat_mgr_new.ENUM_CHANNEL.GUIDE, parentObj = self.togUnionBottom },
        [4] = { obj = self.togManor, texts = self.togTextManor, isSelectChangeColor = true,
                mapState = { enumState.territory }, mapChannel = chat_mgr_new.ENUM_CHANNEL.TERRITORY },
        [5] = { obj = self.togRecruit, texts = self.togTextRecruit, isSelectChangeColor = true,
                mapState = { enumState.recruit }, mapChannel = chat_mgr_new.ENUM_CHANNEL.RECRUIT, moduleName = lua_pb.EModule_LeagueRecruit },
        --私聊频道
        [6] = { obj = self.togPrivate, texts = self.togTextPrivate, isSelectChangeColor = true,
                mapState = { enumState.privateList, enumState.privateView }, mapChannel = chat_mgr_new.ENUM_CHANNEL.PRIVATE, isBottomTog = true },
        [7] = { obj = self.togPeakGameField, texts = self.togTextPeakGameField, isSelectChangeColor = true,
                mapState = { enumState.peekGame }, mapChannel = chat_mgr_new.ENUM_CHANNEL.PEEKGAME },
        --世界页签
        [8] = { obj = self.togWorldBottom, texts = self.togTextWorldBottom, childPoint = { [1] = 1, [2] = 2 }, isBottomTog = true, defaultChild = 2, isSelectChangeColor = true },
        --联盟页签
        [9] = { obj = self.togUnionBottom, texts = self.togTextUnionBottom, childPoint = { [1] = 3, [2] = 10, [3] = 11 }, isBottomTog = true, defaultChild = 3, isSelectChangeColor = true },
        --联盟公告
        [10] = { obj = self.togUnionNotice, texts = self.togTextUnionNotice, mapState = { enumState.allianceNotice }, mapChannel = chat_mgr_new.ENUM_CHANNEL.GUIDE,
                 parentObj = self.togUnionBottom, getDotFunc = function()
                return 0
            end },
        --联盟R4R5频道
        [11] = { obj = self.togUnionR4R5, texts = self.togTextUnionR4R5, mapState = { enumState.r4r5 }, mapChannel = chat_mgr_new.ENUM_CHANNEL.R4R5,
                 parentObj = self.togUnionBottom, getDotFunc = function()
                if alliance_mgr.IsSelfR4R5() then
                    return chat_mgr_new.GetChannelDotProp()[chat_mgr_new.ENUM_CHANNEL.R4R5] or 0
                else
                    return 0
                end
            end },
    }

    --动态存储顶部频道
    self.TopTogDefine = {}
    local tmpChild
    for i = 0, self.tf_channelTogList_top.childCount - 1 do
        tmpChild = self.tf_channelTogList_top:GetChild(i)
        table.insert(self.TopTogDefine, { key = tmpChild.name, obj = tmpChild })
    end


    local pro = self:GetWeeklyTaskPro()
    if pro < 1 then
        canChange = true
        landID = math.random(939, 968)
        self.inputText = ""
        self:InitInputField()
        if self.inputFieldTMP then
            self.inputSendBtn.gameObject:SetActive(self.inputFieldTMP.text ~= "")
            self.sendBtn.gameObject:SetActive(self.inputFieldTMP.text ~= "")
            self.extensionBtn.gameObject:SetActive(self.inputFieldTMP.text == "")
        else
            self.sendBtn.gameObject:SetActive(self.inputField.text ~= "")
            self.extensionBtn.gameObject:SetActive(self.inputField.text == "")
        end

    end
    self:UpdateTogState()
    self:CheckMsgReaded()
    self:UpdateTogDot()
end

--首次刷新ui需要通知消息已读
function M:CheckMsgReaded()
    local rc = nil
    local pageState = chat_mgr_new.GetPageState()
    for _, v in ipairs(self.toggleData) do
        if v.mapState then
            for _, v2 in ipairs(v.mapState) do
                if v2 == pageState then
                    rc = v
                    break
                end
            end
            if rc then
                break
            end
        end
    end
    if rc then
        chat_mgr_new.NtfMsgReaded(rc.mapChannel)
        rc.obj.isOn = true
    end
end

--刷新频道选中效果
function M:UpdateTogState()
    local curPstate = chat_mgr_new.GetPageState()
    for i, v in ipairs(self.toggleData) do
        if v.mapState then
            for m, n in pairs(v.mapState) do
                if n == curPstate then
                    if v.parentObj then
                        v.parentObj.isOn = true
                        break
                    elseif v.obj then
                        v.obj.isOn = true
                        break
                    end
                end
            end
        end
    end

    ---初始化子频道显示
    for i, v in ipairs(self.toggleData) do
        self:RefreshTopTogShow(v)
    end

    for _, v in ipairs(self.toggleData) do
        if not v.obj.gameObject.activeSelf then
            v.obj.isOn = false
        end

        if v.obj.isOn and v.isSelectChangeColor then
            v.texts.color = { r = 1, g = 1, b = 1, a = 1 }
        else
            v.texts.color = { r = 18 / 255, g = 70 / 255, b = 95 / 255, a = 1 }
        end
    end
end

--刷新频道@提示
function M:UpdateTogDot()
    local channelDot = chat_mgr_new.GetChannelDotProp()
    local atState = chat_mgr_new.GetChannelAtState()
    if self.toggleData == nil then
        return
    end
    for _, v in ipairs(self.toggleData) do
        --如果是页签类型的，则要取子频道红点之和
        local dotNum = 0
        if v.childPoint then
            for _, v2 in ipairs(v.childPoint) do
                local tmpChannelDot = 0
                local getDotFunc = self.toggleData[v2].getDotFunc
                if getDotFunc then
                    --自定义获取红点方法
                    tmpChannelDot = getDotFunc()
                else
                    tmpChannelDot = channelDot[self.toggleData[v2].mapChannel] or 0
                end
                dotNum = dotNum + tmpChannelDot
            end
        elseif v.getDotFunc then
            dotNum = v.getDotFunc() or 0
        else
            dotNum = channelDot[v.mapChannel] or 0
        end
        local dotRoot = v.obj.transform:Find("dot")
        local numText = v.obj.transform:Find("dot/Text"):GetComponent(typeof(Text))
        if dotNum > 0 then
            dotRoot.gameObject:SetActive(true)
            if atState[v.mapChannel] and atState[v.mapChannel].pos ~= 0 and atState[v.mapChannel].new == true then
                numText.text = lang.Get(933)
            else
                numText.text = dotNum > 99 and "99+" or dotNum
            end
        else
            dotRoot.gameObject:SetActive(false)
        end
    end
end
--刷新消息数量
function M:UpdateChannel()
    local _, curChannelData = chat_mgr_new.GetCurLangChannelId()
    self.togTextLang.text = curChannelData and lang.Get(curChannelData.channelName) or lang.Get(670033)
end

function M:ChangePage()
    local pro = self:GetWeeklyTaskPro()
    local pageStat = chat_mgr_new.GetPageState()
    if pageStat == chat_mgr_new.enum_pState.lang then
        if pro < 1 then
            return
        end
    elseif pageStat == chat_mgr_new.enum_pState.privateView then
        return
    elseif pageStat == chat_mgr_new.enum_pState.territory then
        return
    end
    local show = chat_mgr_new.CanAskAndReceive() or chat_mgr_new.CanHelp()
    if show then
        chat_mgr_new.SetPageState(chat_mgr_new.enum_pState.guide, nil, false, false)
    end
end

function M:RefreshTopTogShow(togData)
    if not togData.isBottomTog or not togData.obj.isOn then
        return
    end
    if not togData.childPoint then
        self.tf_channelTogList_top:SetActive(false)
        return
    end

    function isContain(str, childPoint)
        for i, v in ipairs(childPoint) do
            if self.toggleData[v] and self.toggleData[v].obj.name == str then
                return self.toggleData[v]
            end
        end
        return nil
    end

    local showCount = 0
    for i, v in ipairs(self.TopTogDefine) do
        local tmpData = isContain(v.obj.name, togData.childPoint)
        local isShowChannel = false
        if tmpData then
            isShowChannel = self:CheckIsShowChannel(tmpData)
        end

        if isShowChannel then
            showCount = showCount + 1
        end
        v.obj:SetActive(isShowChannel)
    end
    self.tf_channelTogList_top:SetActive(showCount > 0)
end

function M:InitView()
    if chat_mgr_pro.GetIsUseChatPro() then

    else
        self.chatListSR.decelerationRate = 0.5
        self.chatListSR.scrollSensitivity = 15
    end

    self:UpdateUI()


    if chat_mgr_pro.GetIsUseChatPro() then
        local chat_content_pro = require "chat_content_pro"
        self.comChatList = chat_content_pro.NewObject()

        self.comChatList:Init(self.chatListView, function(msgData)--self.loopVerticalScrollRect.gameObject.transform
            --长按头像
            self.inputField.text = string.format("%s@%s ", self.inputField.text, msgData.name)
        end, self.curOrder, true)
    else
        local chat_content_list = require "chat_content_list"
        self.comChatList = chat_content_list.NewObject()
        self.comChatList:Init(self.chatList, function(msgData)
            --长按头像
            self.inputField.text = string.format("%s@%s ", self.inputField.text, msgData.name)
        end, self.curOrder, true)

        oversea_res.ChangeLangResNew("liaotian_help02", "ui_chat_main_new", self.showRankImg, true)
    end



end
--有人@玩家提示
function M:UpdataAtBtnState()
    local atState = chat_mgr_new.GetChannelAtState()
    local pageState = chat_mgr_new.GetPageState()
    local enumPState = chat_mgr_new.enum_pState
    self.atBtn.gameObject:SetActive(false)
    if pageState == enumPState.lang or pageState == enumPState.world or
            pageState == enumPState.guide or pageState == enumPState.territory or pageState == enumPState.recruit then
        local msgArrData = chat_mgr_new.GetMsgByType(pageState)
        local msgData = msgArrData[atState[pageState].pos]
        
        if msgData then
            -- 在处理后的数据中查找实际位置
            -- 保存实际位置供跳转使用
            atState[pageState].realPos = self.comChatList:GetIndex(msgData)
            self.atBtnText.text = msgData.name .. lang.Get(934)
            self.atBtn.gameObject:SetActive(true)
        end
    end
end
--在劳动节活动期间，显示劳动节集市按钮（跳转至劳动节集市界面）
function M:UpdateMayDayMarketBtnState()
    --在劳动节活动期间内
    local ui_pop_mgr = require "ui_pop_mgr"
    local isOpen = false

    local activity = festival_activity_mgr.GetAllActivity(3)--获取优先级最高的活动
    if activity then
        for _, v in ipairs(activity) do
            if v.headingCode == 139 then
                --兔子集市
                local actCfg = festival_activity_mgr.GetActivityCofig(v.headingCode, v.versionNumber)
                isOpen = ui_pop_mgr.CheckIsUnlock(actCfg.levelLimite.data[0], actCfg.levelLimite.data[1])
            end
        end
    end
    self.mayDayMarketBtn.gameObject:SetActive(isOpen and not ReviewingUtil.IsReviewing())
    self.mayDayNTFBtn.gameObject:SetActive(isOpen)

    if isOpen then
        local unforced_guide_mgr = require "unforced_guide_mgr"
        unforced_guide_mgr.Unlock(111)
        if unforced_guide_mgr.IsGuiding(113) then
            event.Trigger(event.DAY_RABBIT_OPEN_CHAT_WINDOW, nil, nil, nil, "Top")
        end
    end
end

--状态发生改变的时候刷新整个内容板块
function M:UpdateView()
    --  --print("刷新！！！！！！！！！！！！！聊天界面!!!!!!!!!!!!!!!!!!!!!")
    local enumChannel = chat_mgr_new.ENUM_CHANNEL
    local enumState = chat_mgr_new.enum_pState
    local pageState, curSessionData, isClose = chat_mgr_new.GetPageState()
    local itemDatas, styleDatas

    local comStep = function()
        ui_window_mgr:UnloadModule("ui_chat_session_list")
        ui_window_mgr:UnloadModule("ui_private_chat_panel")

        SetUIActiveByPos(self.chatListViewport, true)
        self.comChatList:SetListData(itemDatas, styleDatas)
        self.btn_go2Bottom:SetActive(false)
    end

    self.allianceNoticeObj:SetActive(false)
    self.btn_topShowAllianceNotice:SetActive(false)
    self.noAllianceNoticeTips:SetActive(false)
    self.helpFunc:SetActive(false)
    self.unionTips:SetActive(false)
    self.UnionInviteFunc:SetActive(false)
    self.chatListRect:SetActive(true)
    if chat_mgr_pro.GetIsUseChatPro()==false then
        self.chatListRect.offsetMin = { x = 0, y = 0 }
    else
        chat_mgr_pro.SetOffsetMin(self.chatListViewport,const.UI_SHOW_POSITION)
    end

    self.togAutoTranslation:SetActive(false)
    self.togAutoTranslationWorldRoot:SetActive(false)
    self.hasSwindle = false
    self.hasOtherUI = false
    self.SwindleTip:SetActive(false)
    self.inputRoot:SetActive(true)
    self.btn_contentMask:SetActive(false)
    self.btn_emojiBack:SetActive(false)
    self.r_ExtensionPanelView:SetActive(false)
    self.topTips:SetActive(false)
    --self.SwindleTip.anchoredPosition = self.swindleDefaultPos

    --底下没UI
    local NormalShow_SwindleTip = function()
        if self.hasSwindle == true then
            self.SwindleTip:SetActive(true)
            self.SwindleTip.anchoredPosition = self.swindleDefaultPos
            if chat_mgr_pro.GetIsUseChatPro()==false then
                self.chatListRect.offsetMin = { x = 0, y = 112 }
            else
                chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 112 })
            end
        end
    end
    --底下有ui
    local TwoShow_SwindleTip = function()
        if self.hasSwindle == true then
            self.SwindleTip:SetActive(true)
            if self.hasOtherUI == true then
                self.SwindleTip.anchoredPosition = self.swindleDefaultPos + self.swindleOnePos
                if chat_mgr_pro.GetIsUseChatPro()==false then
                    self.chatListRect.offsetMin = { x = 0, y = 224 }
                else
                    chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 224 })
                end
            else
                self.SwindleTip.anchoredPosition = self.swindleDefaultPos
                if chat_mgr_pro.GetIsUseChatPro()==false then
                    self.chatListRect.offsetMin = { x = 0, y = 112 }
                else
                    chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 112 })
                end
            end
        end
    end

    if pageState == enumState.lang then
        itemDatas, styleDatas = self:BulidListData(enumChannel.LANG)
        comStep()
        self.hasSwindle = self:CheckSwindle(itemDatas)
        NormalShow_SwindleTip()
        self:TryShowWorldAutoTranslationTog()
    elseif pageState == enumState.world then
        itemDatas, styleDatas = self:BulidListData(enumChannel.WORLD)
        comStep()
        self.hasSwindle = self:CheckSwindle(itemDatas)
        NormalShow_SwindleTip()
        self:TryShowWorldAutoTranslationTog()
    elseif pageState == enumState.recruit then
        itemDatas, styleDatas = self:BulidListData(enumChannel.RECRUIT)
        comStep()
        local sociaty_data = require("sociaty_data")
        local leagueiPosition = sociaty_data.GetSelfPosition()
        local leaguepro_pb = require "leaguepro_pb"

        if leagueiPosition == leaguepro_pb.emLeaguePosition_ceo or leagueiPosition == leaguepro_pb.emLeaguePosition_officer then
            self.UnionInviteFunc.gameObject:SetActive(true)
            if chat_mgr_pro.GetIsUseChatPro()==false then
                self.chatListRect.offsetMin = { x = 0, y = 112 }
            else
                chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 112 })

            end
            self.hasOtherUI = true
        end

        self.hasSwindle = self:CheckSwindle(itemDatas)
        TwoShow_SwindleTip()

    elseif pageState == enumState.guide then
        local isShow = self:CheckIsHaveAlliance()
        itemDatas, styleDatas = self:BulidListData(enumChannel.GUIDE, enumState.guide)
        if isShow then
            self.helpFunc.gameObject:SetActive(false)
            --self:RefreshChineseNewYearUI()
            --self.chatListRect.offsetMin = { x = 0, y = 112 }
            self.hasOtherUI = false
            sandbox_gather_mgr.RequestSandBoxMassTeam()

            self:TryShowAllianceNotice()
        end

        comStep()
        self.hasSwindle = self:CheckSwindle(itemDatas)
        --国内版本不显示自动翻译
        self:TryShowAutoTranslationTog()

        TwoShow_SwindleTip()
        self:CheckTopTipsDataAndShow()

    elseif pageState == enumState.territory then
        itemDatas, styleDatas = self:BulidListData(enumChannel.TERRITORY)
        comStep()
        self.hasSwindle = self:CheckSwindle(itemDatas)
        NormalShow_SwindleTip()
    elseif pageState == enumState.privateList then
        --self.chatList.gameObject:SetActive(false)
        self.btn_go2Bottom:SetActive(false)
        SetUIActiveByPos(self.chatListViewport, false)
        ui_window_mgr:UnloadModule("ui_private_chat_panel")
        ui_window_mgr:ShowModule("ui_chat_session_list")
        ----         --print("关闭聊天界面！！！！！！！！！！！！！")
    elseif pageState == enumState.privateView then
        --self.chatList.gameObject:SetActive(false)
        if chat_mgr_pro.GetIsUseChatPro() then
            SetUIActiveByPos(self.chatListViewport, true,true)
        else
            SetUIActiveByPos(self.chatListViewport, false)
        end

        ui_window_mgr:UnloadModule("ui_chat_session_list")
        local ui_private_chat_panel = require "ui_private_chat_panel"
        -- ui_private_chat_panel.Open(self.r_contentView,curSessionData.sessionId,curSessionData.toWorldid,
        -- curSessionData.toTitleName,curSessionData.toName,curSessionData.toFaceId,curSessionData.toLv)
        ui_private_chat_panel.Open(self.r_contentView)
        ----         --print("打开聊天界面！！！！！！！！！！！！！")
    elseif pageState == enumState.peekGame then
        itemDatas, styleDatas = self:BulidListData(enumChannel.PEEKGAME)
        comStep()
        self.hasSwindle = self:CheckSwindle(itemDatas)
        NormalShow_SwindleTip()

    elseif pageState == enumState.allianceNotice then
        self.inputRoot:SetActive(false)
        self.btn_topShowAllianceNotice:SetActive(alliance_mgr.IsSelfR4R5())
        --联盟公告页签
        itemDatas, styleDatas = self:BulidListData(enumChannel.GUIDE, enumState.allianceNotice)
        self.hasSwindle = self:CheckSwindle(itemDatas)
        NormalShow_SwindleTip()

        --无联盟，展示未加入提示
        self:TryShowHaveNotAllianceTips()
        comStep()
        self:CheckTopTipsDataAndShow()
    elseif pageState == enumState.r4r5 then
        self:CheckIsHaveAlliance()
        itemDatas, styleDatas = self:BulidListData(enumChannel.R4R5)

        self.hasSwindle = self:CheckSwindle(itemDatas)
        NormalShow_SwindleTip()
        comStep()
        self:CheckTopTipsDataAndShow()
    end
    self:UpdateMayDayMarketBtnState()

    if chat_mgr_pro.GetIsUseChatPro() then
        LayoutRebuilder.ForceRebuildLayoutImmediate(self.r_contentView)
        self.comChatList:ResetListView(true)
    end
end

--检测是否展示置顶联盟公告
function M:TryShowAllianceNotice()
    local pState = chat_mgr_new.GetPageState()
    if pState == chat_mgr_new.enum_pState.guide then
        if not self.allianceNoticeItem then
            local ui_chat_allianceNoticeObj = require "ui_chat_allianceNoticeObj"
            self.allianceNoticeItem = ui_chat_allianceNoticeObj.new()
            self.allianceNoticeItem:Init(self.allianceNoticeObj)
        end
        self.allianceNoticeItem:TryShowInTop()

    elseif pState == chat_mgr_new.enum_pState.allianceNotice then
        --隐藏无公告提示
        self:TryShowHaveNotAllianceTips()
    end
end

--检测是否显示无公告提示
function M:TryShowHaveNotAllianceTips()
    --无联盟，展示未加入提示
    if self:CheckIsHaveAlliance() then
        local enumChannel = chat_mgr_new.ENUM_CHANNEL
        local enumState = chat_mgr_new.enum_pState
        itemDatas, styleDatas = self:BulidListData(enumChannel.GUIDE, enumState.allianceNotice)
        self.noAllianceNoticeTips:SetActive(itemDatas ~= nil and util.get_len(itemDatas) == 0)
        self.btn_showAllianceNoticeRelease:SetActive(alliance_mgr.IsSelfR4R5())
    end
end

--检测是否展示无联盟提示
function M:CheckIsHaveAlliance()
    local allianceId = alliance_data.GetUserAllianceId()

    if allianceId == 0 then
        self.unionTips.gameObject:SetActive(true)
        self.chatListRect.gameObject:SetActive(false)
        return false
    end
    return true
end

--检测展示自动翻译按钮
function M:TryShowAutoTranslationTog()
    --国内版本不显示自动翻译
    if util.ShouldUseCustomSDKUI() then
        self.togAutoTranslation.gameObject:SetActive(true)
        local isAutoTranslation = chat_mgr_new.GetIsAutoTranslation()
        self.togAutoTranslation.isOn = isAutoTranslation
        self:IsOnToggleAutoTranslation(self.togAutoTranslation.isOn)
    end
end


--检测展示世界自动翻译按钮
function M:TryShowWorldAutoTranslationTog()
    --国内版本不显示自动翻译
    if util.ShouldUseCustomSDKUI() and not ReviewingUtil.IsReviewing() then
        UIUtil.SetActive(self.togAutoTranslationWorldRoot, true)
        local isOpen =chat_mgr_new.IsCanAutoTranslation(chat_mgr_new.enum_pState.world)
        UIUtil.SetActive(self.togAutoTranslationWorld, true)
        UIUtil.SetActive(self.btnAutoTranslationWorldLock, false)
        if isOpen then
            local isAutoTranslation = chat_mgr_new.GetIsAutoTranslation(chat_mgr_new.ENUM_TRANSLATION_TYPE.WORLD)
            self.togAutoTranslationWorld.isOn = isAutoTranslation
            self:IsOnToggleWorldAutoTranslation(self.togAutoTranslationWorld.isOn)
        end
    end
end

--通过移出入屏幕，显隐对象
function SetUIActiveByPos(transform, isActive,isPrivate)
    if not transform then
        return
    end

    if chat_mgr_pro.GetIsUseChatPro() == false then
        if isActive then
            transform.anchoredPosition = const.UI_SHOW_POSITION
        else
            transform.anchoredPosition = const.UI_HIDE_POSITION
        end
    else
        if isActive then
            if isPrivate then
                chat_mgr_pro.SetOffsetMin(transform,{ x = 0, y = 16 },{ x = 0, y = -56 })
            else
                chat_mgr_pro.SetOffsetMin(transform,{ x = 0, y = 16 },{ x = 0, y = -15 })
            end

        else
            chat_mgr_pro.GetContentPro():ClsView()
           chat_mgr_pro.SetOffsetMin(transform,const.UI_HIDE_POSITION)
        end
    end

end

--在仅当前频道和更新的消息的频道一致时刷新聊天内容
function M:UpdateChatList(channel, sessionId, msg, isHelp)
    local pageState, curSessionData, isClose = chat_mgr_new.GetPageState()
    local ENUM_CHANNEL = chat_mgr_new.ENUM_CHANNEL
    local enum_pState = chat_mgr_new.enum_pState
    local blockState = chat_mgr_new.GetChannelBlock(ENUM_CHANNEL)
    local selfRoleId = player_mgr.GetPlayerRoleID()
    local isMyMsg = msg and msg.roleid == selfRoleId
    --print("kasug---blockState 1:", blockState, isMyMsg)
    if blockState and not isMyMsg then
        return
    end
    if (pageState == enum_pState.lang and channel == ENUM_CHANNEL.LANG) or
            (pageState == enum_pState.world and channel == ENUM_CHANNEL.WORLD) or
            (pageState == enum_pState.guide and channel == ENUM_CHANNEL.GUIDE) or
            (pageState == enum_pState.recruit and channel == ENUM_CHANNEL.RECRUIT) or
            (pageState == enum_pState.territory and channel == ENUM_CHANNEL.TERRITORY) or
            (pageState == enum_pState.peekGame and channel == ENUM_CHANNEL.PEEKGAME) or
            (pageState == enum_pState.allianceNotice and channel == ENUM_CHANNEL.GUIDE) or
            (pageState == enum_pState.r4r5 and channel == ENUM_CHANNEL.R4R5) then
        if isHelp then
            self.comChatList:SetUpdateHelp(isHelp)
        else
            if msg then
                --单条数据
                if ui_chat_data_gw.CheckNewDataAddChatList(pageState, msg) then
                    local tmpData_tryGet = ui_chat_data_gw.TryGetChatDataByPageState(pageState, msg)--特殊数据获取
                    if tmpData_tryGet then
                        self.comChatList:AddData(tmpData_tryGet, isMyMsg and 1 or 0)
                    else
                        self.comChatList:AddData(msg, isMyMsg and 1 or 0)
                    end
                end
                
                if self.hasSwindle == false then
                    if msg.context then
                        --比较关键字 
                        -- swindle_warn_str_func.CheckContext(msg.context)
                        self.hasSwindle = self:CheckOneSwindle(msg.context)

                        if self.hasSwindle == true then
                            self.SwindleTip.gameObject:SetActive(true)
                            if self.hasOtherUI == true then
                                self.SwindleTip.anchoredPosition = self.swindleOnePos + self.swindleDefaultPos
                                if chat_mgr_pro.GetIsUseChatPro()==false then
                                    self.chatListRect.offsetMin = { x = 0, y = 224 }
                                else
                                    chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 224 })
                                end
                            else
                                self.SwindleTip.anchoredPosition = self.swindleDefaultPos
                                if chat_mgr_pro.GetIsUseChatPro()==false then
                                    self.chatListRect.offsetMin = { x = 0, y = 112 }
                                else
                                    chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 112 })
                                end
                            end

                            -- --print("set true",msg.context,"end")
                        end
                    end
                end
            else
                --全部更新
                local itemDatas, styleDatas = self:BulidListData(channel, pageState)
                self.comChatList:SetListData(itemDatas, styleDatas)

                self.hasSwindle = self:CheckSwindle(itemDatas)

                if self.hasSwindle == true then

                    self.SwindleTip.gameObject:SetActive(true)
                    if self.hasOtherUI == true then
                        self.SwindleTip.anchoredPosition = self.swindleDefaultPos + self.swindleOnePos
                        if chat_mgr_pro.GetIsUseChatPro()==false then
                            self.chatListRect.offsetMin = { x = 0, y = 224 }
                        else
                            chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 224 })
                        end
                    else
                        self.SwindleTip.anchoredPosition = self.swindleDefaultPos
                        if chat_mgr_pro.GetIsUseChatPro()==false then
                            self.chatListRect.offsetMin = { x = 0, y = 112 }
                        else
                            chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 112 })
                        end
                    end
                end
            end


        end
    end
end

--巅峰赛按照房间获取聊天数据
function M:BulidPeakGameListData()
    local peakGame_data = require("peakGame_data")
    return peakGame_data.GetChatMsgByRoomId()
end

function M:BulidListData(channelType, ePageState)
    local blockState = chat_mgr_new.GetChannelBlock(channelType)
    --print("kasug---blockState2:",blockState, channelType)
    local selfRoleId = player_mgr.GetPlayerRoleID()
    local itemDatas = {}
    local styleDatas = {}
    local rawDatas
    if (channelType == chat_mgr_new.ENUM_CHANNEL.PEEKGAME) then
        rawDatas = self:BulidPeakGameListData()
    else
        rawDatas = chat_mgr_new.GetMsgByType(channelType) or {}
    end

    local isCombo = false
    local tempA = {}
    local tempB = {} --以roleid为键 过滤重复觉得的消息
    local tempFun = function()
        if isCombo == true then
            if util.get_len(tempB) >= 2 then
                --形成combo
                local comboData = {}
                for k, v in pairs(tempB) do
                    table.insert(comboData, v)
                end
                table.insert(itemDatas, comboData)
                table.insert(styleDatas, 2) --2是 comboItem
            else
                --不形成combo
                for k, v in ipairs(tempA) do
                    table.insert(itemDatas, v)
                    table.insert(styleDatas, v.roleid == selfRoleId and 1 or 0)
                end
            end
            --重置记录
            tempA = {}
            tempB = {}
            isCombo = false
        end
    end

    for i = 1, #rawDatas do
        local rData = rawDatas[i]
        local canInsert = true
        if blockState then
            canInsert = rData.roleid == selfRoleId
        end
        if canInsert then
            if rData.sType == mq_common_pb.enSpeak_Like then
                if isCombo == false then
                    isCombo = true
                end
                table.insert(tempA, rData)
                tempB[rData.roleid] = rData
            else
                tempFun()

                table.insert(itemDatas, rData)
                table.insert(styleDatas, rData.roleid == selfRoleId and 1 or 0)  --1是selfSpeakItem 0 是ortherSpeakItem
            end
        end
    end
    tempFun()
    
    ui_chat_data_gw.FilterDataByPageState_Fun(ePageState, itemDatas, styleDatas)

    return itemDatas, styleDatas
end

function M:CheckSwindle(itemDatas)
    if itemDatas then
        local checkCount = game_scheme:InitBattleProp_0(1010).szParam.data[0] - 1

        local csvCount = game_scheme:KeywordSwindle_nums() - 1
        local csvData

        local itemCount = #itemDatas

        if itemCount < checkCount then
            checkCount = itemCount
        end

        for i = 0, checkCount, 1 do
            local data = itemDatas[itemCount - i]

            if self.hasSwindle == true then
                break
            end
            if data then
                -- --print("checkData",data.context)
                local context = data.context
                if context then
                    for csvi = 0, csvCount, 1 do
                        --  --print("i",csvi)
                        csvData = game_scheme:KeywordSwindle(csvi)
                        if csvData.swindle_word ~= "" then
                            if string.find(context, csvData.swindle_word) ~= nil then
                                --有关键字
                                return true
                            end
                        end
                    end
                end
            end
        end

        --print(self.hasSwindle,self.hasSwindle and "有诈骗关键字" or "没有诈骗关键字",csvCount)
    end
    return false
end

function M:CheckOneSwindle(str)
    if str == nil then
        return false
    end
    local csvCount = game_scheme:KeywordSwindle_nums() - 1
    local csvData
    for csvi = 0, csvCount, 1 do
        csvData = game_scheme:KeywordSwindle(csvi)
        if string.find(str, csvData.swindle_word) ~= nil then
            return true
        end
    end
    return false
end

--发送消息
function M:SendMsg(enSpeakType)
    -- --print("----发送消息",self.inputFieldText.text)1F47F

    local cInputField

    if chat_mgr_pro.GetIsUseChatPro() then
        cInputField=self.inputFieldTMP
    else
        cInputField=self.inputField
    end

    local cSendText=cInputField.text

    if string.match(cSendText, "\n") ~= nil then
        local str, num = string.gsub(cSendText, '\n', '\n')
        cInputField.text = str
        cSendText=cInputField.text
        -- --print("-----string.match(str,\"\\n\")----",self.inputFieldText.text)
        -- --print("替换换行个数：",num)
        --local limitLine = game_scheme:InitBattleProp_0(972).szParam.data[0]
        ---- --print("game_scheme:InitBattleProp_0(972)=",limitLine)
        --if num >= limitLine then
        --    flow_text.Add(lang.Get(100455))
        --    return
        --end
    end
    if chat_mgr_pro.GetIsUseChatPro() then
        cSendText=EmojiConverter.TMPToTextEmoji(cSendText)
        cSendText=string_util.EscapeTMPTagsKeepEmoji(cSendText)
    end
    local pageState, curSessionData, isClose = chat_mgr_new.GetPageState()
    if pageState == chat_mgr_new.enum_pState.privateList then
        flow_text.Add(lang.Get(909))
        return
    end

    if cSendText == "" and enSpeakType == mq_common_pb.enSpeak_Text then
        flow_text.Add(lang.Get(15283))
        return
    end

    if enSpeakType == mq_common_pb.enSpeak_Text and
            (pageState == chat_mgr_new.enum_pState.lang or pageState == chat_mgr_new.enum_pState.world or
                    pageState == chat_mgr_new.enum_pState.guide or pageState == chat_mgr_new.enum_pState.territory or
                    pageState == chat_mgr_new.enum_pState.recruit or pageState == chat_mgr_new.enum_pState.r4r5) then
        --类型改为@
        local names = chat_mgr_new.GetAtNamesByStr(cSendText)
        if #names > 0 then
            enSpeakType = mq_common_pb.enSpeak_At
        end
    end

    if pageState == chat_mgr_new.enum_pState.lang then
        if block_cache.IfBlock("chatSendMsg_lang", 10) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        net_chat_module_new.Send_CHAT_LANGUAGE_CHANNEL_SPEAK(enSpeakType, cSendText)

    elseif pageState == chat_mgr_new.enum_pState.world then
        if block_cache.IfBlock("chatSendMsg_world", 10) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_World, enSpeakType, cSendText)

    elseif pageState == chat_mgr_new.enum_pState.recruit then
        if block_cache.IfBlock("chatSendMsg_recruit", 10) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_Recruit, enSpeakType, cSendText)
    elseif pageState == chat_mgr_new.enum_pState.guide then
        if block_cache.IfBlock("chatSendMsg_guide", 1) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_Guild, enSpeakType, cSendText)

    elseif pageState == chat_mgr_new.enum_pState.territory then
        if block_cache.IfBlock("chatSendMsg_territory", 1) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_Territory, enSpeakType, cSendText)

    elseif pageState == chat_mgr_new.enum_pState.privateView then
        if block_cache.IfBlock("chatSendMsg_private", 1) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        local ui_private_chat_panel = require "ui_private_chat_panel"
        ui_private_chat_panel.SendMsg(enSpeakType, cSendText)

    elseif pageState == chat_mgr_new.enum_pState.peekGame then
        if block_cache.IfBlock("chatSendMsg_peekGame", 10) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return false
        end
        local peakGame_data = require("peakGame_data")

        net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_WMTOPRACE, enSpeakType, content, nil, nil, nil, peakGame_data.GetGroupRoomID())
    elseif pageState == chat_mgr_new.enum_pState.r4r5 then
        if block_cache.IfBlock("chatSendMsg_r4r5", 1) then
            flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
            return
        end
        net_chat_module_new.Send_CHAT_SPEAK(chat_pb.Channel_AllianceR4R5, enSpeakType, cSendText)
    end

    local inputText = cSendText

    cInputField.text = ""
    self.oldInputText=nil
    self.comChatList:MoveToBot()
    if chat_mgr_pro.GetIsUseChatPro() then
        --恢复位置
        local localPosition = self.inputFieldTMP.textComponent.rectTransform.localPosition
        localPosition.x =0
        self.inputFieldTMP.textComponent.rectTransform.localPosition = localPosition
    end


    --有关键字
    local swindle = self:CheckOneSwindle(inputText)
    if swindle == true then
        local swindleData = {

        }
        event.Trigger(event.GAME_EVENT_REPORT, "Swindle_trigger", swindleData)
        -- --print("聊天-诈骗关键字打点上报")
    end
    
    chat_mgr_new.ResetAtPlayerData()
end

function InitMoveToBot()
    window.r_channelTogList.gameObject:SetActive(true)
    window.r_emojiSelectView.gameObject:SetActive(false)
    window.r_toggleGroup.gameObject:SetActive(false)

    local ui_chat_session_list = require "ui_chat_session_list"
    ui_chat_session_list.SetListSizeDelta(true)
end

function M:CreateLayItem()
    --记录view默认高度
    self.viewDefaultHeight = self.auto_layout.sizeDelta.y
end

function M:ChangeViewMode(mode)

     log.Warning("ChangeViewMode", mode)

    --local sociaty_data = require("sociaty_data")
    --local leaguepro_pb = require "leaguepro_pb"
    if mode == curViewMode then
        return
    end
    local pageState = chat_mgr_new.GetPageState()
    if pageState == chat_mgr_new.enum_pState.privateList then
         flow_text.Add(lang.Get(909))
        if chat_mgr_pro.GetIsUseChatPro() then
            log.Warning("键盘隐藏bug 打印测试 DeactivateInputField ChangeViewMode:2758")
            self.inputFieldTMP:DeactivateInputField()
            mode=1
        else
            return
        end
      
    end
    if pageState == chat_mgr_new.enum_pState.privateView then
        if chat_mgr_pro.GetIsUseChatPro()==false then
            --给私聊视图推送当前视图模式
            local ui_private_chat_panel = require "ui_private_chat_panel"
            ui_private_chat_panel.ChangeViewMode(mode)
        end

    end

    if keyboardTicker then
        util.RemoveDelayCall(keyboardTicker)
        keyboardTicker = nil
    end

    curViewMode = mode
    local oldSize = self.auto_layout.sizeDelta
    if mode == 1 then
        --默认
        self.r_channelTogList.gameObject:SetActive(true)
        self.r_emojiSelectView.gameObject:SetActive(false)
        self.inputSelectView.gameObject:SetActive(false)
        self.r_ExtensionPanelView:SetActive(false)
        self.r_toggleGroup.gameObject:SetActive(false)
        self.btn_contentMask.gameObject:SetActive(false)
        self.closeBtn:SetActive(true)
        if self.inputFieldTMP then
            self.btn_emojiBack:SetActive(false)
            self.inputSendBtn.gameObject:SetActive(self.inputFieldTMP.text ~= "")
            self.sendBtn.gameObject:SetActive(self.inputFieldTMP.text ~= "")
            self.extensionBtn.gameObject:SetActive(self.inputFieldTMP.text == "")
        end

        --if sociaty_data.GetLeagueData() and sociaty_data.GetLeagueData().id ~= nil and pageState == chat_mgr_new.enum_pState.guide then
        --    self.helpFunc.gameObject:SetActive(true)
        --    self.chatListRect.offsetMin = { x = 0, y = 112 }
        --    self.hasOtherUI = true
        --end
        --if (sociaty_data.GetSelfPosition() == leaguepro_pb.emLeaguePosition_ceo or sociaty_data.GetSelfPosition() == leaguepro_pb.emLeaguePosition_officer) and pageState == chat_mgr_new.enum_pState.recruit then
        --    self.UnionInviteFunc.gameObject:SetActive(true)
        --    self.chatListRect.offsetMin = { x = 0, y = 112 }
        --    self.hasOtherUI = true
        --end
        --重新显示关键字
        if self.hasSwindle == true then
            self.SwindleTip.gameObject:SetActive(true)
            if self.hasOtherUI == true then
                if chat_mgr_pro.GetIsUseChatPro()==false then
                    self.chatListRect.offsetMin = { x = 0, y = 224 }
                else
                    chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 224 })
                end
            else
                if chat_mgr_pro.GetIsUseChatPro()==false then
                    self.chatListRect.offsetMin = { x = 0, y = 112 }
                else
                    chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 112 })

                end
            end
        end

        --self.auto_layout.sizeDelta = { x = oldSize.x, y = self.viewDefaultHeight } --备注：跟现在的ui自适应冲突，如果后面需要做键盘自适应，这里要启用
        local ui_chat_session_list = require "ui_chat_session_list"
        ui_chat_session_list.SetListSizeDelta(true)
    elseif mode == 2 then
        --显示软键盘
        self.r_emojiSelectView.gameObject:SetActive(false)
        if  self.inputFieldTMP then
            if self.inputSelectLayoutElement.preferredHeight<100 then
                self.r_channelTogList.gameObject:SetActive(true)
                self.inputSelectView.gameObject:SetActive(false)
                self.btn_emojiBack:SetActive(false)
                self.closeBtn:SetActive(true)
            else
                self.r_channelTogList.gameObject:SetActive(false)
                self.inputSelectView.gameObject:SetActive(true)
                self.btn_emojiBack:SetActive(true)
            end

        else
            self.r_channelTogList.gameObject:SetActive(true)
            self.btn_emojiBack:SetActive(true)
        end
        self.r_toggleGroup.gameObject:SetActive(false)
        self.r_ExtensionPanelView:SetActive(false)
       -- self.closeBtn:SetActive(false)

        self:SetContentMaskBtnFunc_EmojiBack()
        --隐藏诈骗提示
        if self.hasSwindle == true then
            self.SwindleTip.gameObject:SetActive(false)
            if chat_mgr_pro.GetIsUseChatPro()==false then
                self.chatListRect.offsetMin = { x = 0, y = 0 }
            else
                chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 16 })
            end
        end

    elseif mode == 3 then
        --显示表情面板
        self.inputSelectView.gameObject:SetActive(false)
        self.r_emojiSelectView.gameObject:SetActive(true)
        self.r_toggleGroup.gameObject:SetActive(true)
        self.r_ExtensionPanelView:SetActive(false)
        self:RefashMod3And4SizeDelta()

    elseif mode == 4 then
        self.r_emojiSelectView.gameObject:SetActive(false)
        self.inputSelectView.gameObject:SetActive(false)
        self.r_toggleGroup.gameObject:SetActive(false)
        self.r_ExtensionPanelView:SetActive(true)
        self:RefashMod3And4SizeDelta()
    end
    if chat_mgr_pro.GetIsUseChatPro() then
        LayoutRebuilder.ForceRebuildLayoutImmediate(self.r_contentView)
        self.comChatList:ResetListView(true)
    end

end

function M:RefashMod3And4SizeDelta()
    self.closeBtn:SetActive(false)
    self.r_channelTogList.gameObject:SetActive(false)
    self.btn_emojiBack:SetActive(true)
    self:SetContentMaskBtnFunc_EmojiBack()
    if alliance_data.GetUserAllianceId() ~= nil and pageState == chat_mgr_new.enum_pState.guide then
        self.helpFunc.gameObject:SetActive(false)
        self.UnionInviteFunc.gameObject:SetActive(false)
        if chat_mgr_pro.GetIsUseChatPro()==false then
            self.chatListRect.offsetMin = { x = 0, y = 0 }
        else
            chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 16 })
        end
    end
    local ui_chat_session_list = require "ui_chat_session_list"
    ui_chat_session_list.SetListSizeDelta(true)

    --隐藏诈骗提示
    if self.hasSwindle == true then
        self.SwindleTip.gameObject:SetActive(false)
        if chat_mgr_pro.GetIsUseChatPro()==false then
            self.chatListRect.offsetMin = { x = 0, y = 0 }
        else
            chat_mgr_pro.SetOffsetMin(self.chatListViewport,{ x = 0, y = 16 })
        end
    end

    if chat_mgr_pro.GetIsUseChatPro() then
        self.comChatList:MoveToBot()
    end
end
--///表情选择面板-------------------

--///List
local CListItem = com_class.CreateClass(CScrollListItemBase)
function CListItem:Create(goSkin)
    CListItem.__super.Create(self, goSkin)


    self.enmojiText = goSkin:GetComponent(typeof(InlineText))
    local ButtonItem = goSkin:GetComponent(typeof(Button))
    if ButtonItem then
        ButtonItem.onClick:RemoveAllListeners()
        local ButtonItemEvent = function()
            if chat_mgr_pro.GetIsUseChatPro() then
                window.inputFieldTMP.text = window.inputFieldTMP.text .. EmojiConverter.GetOutputText(self.enmojiText.rawText)
                window.inputFieldTMP:MoveTextEnd(false)
                --滚动动最后
                local rect =  window.inputFieldTMP.textComponent.rectTransform.rect
                if  window.inputFieldTMP.textComponent.preferredWidth > rect.width then
                    --Debug.Log("xMax=" + (inputFieldTMP.textComponent.preferredWidth - rect.width));
                    local localPosition = window.inputFieldTMP.textComponent.rectTransform.localPosition
                    localPosition.x = rect.width- window.inputFieldTMP.textComponent.preferredWidth;
                    window.inputFieldTMP.textComponent.rectTransform.localPosition = localPosition;
                end
            else
                window.inputField.text = window.inputField.text .. self.enmojiText.rawText
            end

        end
        ButtonItem.onClick:AddListener(ButtonItemEvent)
    end


   -- self.eventTriggerListener = goSkin:GetComponent(typeof(EventTriggerListener))

  --  self.eventTriggerListener:onClick('+', function()
     --   window.inputField.text = window.inputField.text .. self.enmojiText.rawText
   -- end)

end

function CListItem:Draw(data, i)
    if data.id ~= 0 then
        self.enmojiText.text = '[' .. data.id .. '&' .. data.tag .. "]"
    else
        self.enmojiText.text = '[' .. '&' .. data.tag .. "]"
    end
end

function M:EmojiListCreate()
    --create
    self.emoji_page1.isOn = true
    self.emojilistInstance = CScrollList:CreateInstance({})
    ------print(self.goItem,self.goContent)
    local goChildren = { goItem = self.goItem.gameObject,
                         goContent = self.goContent.gameObject,
                         itemName = "ListItem",
                         contentName = "Content" }
    self.emojilistInstance:Create(self.list, CListItem, goChildren)

    local datas = self:GetListData(2)
    self.emojilistInstance:SetListData(datas)
end
--///list end

local CListItemStandBy = com_class.CreateClass(CScrollListItemBase)
function CListItemStandBy:Create(goSkin)
    CListItemStandBy.__super.Create(self, goSkin)

    self.enmojiText = goSkin:GetComponent(typeof(InlineText))
    local ButtonItem = goSkin:GetComponent(typeof(Button))
    if ButtonItem then
        ButtonItem.onClick:RemoveAllListeners()
        local ButtonItemEvent = function()
            window.inputField.text = window.inputField.text .. self.enmojiText.rawText
        end
        ButtonItem.onClick:AddListener(ButtonItemEvent)
    end

    --self.eventTriggerListener = goSkin:GetComponent(typeof(EventTriggerListener))
    --
    --self.eventTriggerListener:onClick('+', function()
    --    window.inputField.text = window.inputField.text .. self.enmojiText.rawText
    --end)

end

function CListItemStandBy:Draw(data, i)
    if data.id ~= 0 then
        self.enmojiText.text = '[' .. data.id .. '&' .. data.tag .. "]"
    else
        self.enmojiText.text = '[' .. '&' .. data.tag .. "]"
    end
end

function M:EmojiListStandByCreate()
    self.emojilistStandByInstance = CScrollList:CreateInstance({})
    ------print(self.goItem,self.goContent)
    local goChildrenStandBy = { goItem = self.stadbyGoItem.gameObject,
                                goContent = self.stadbyGoContent.gameObject,
                                itemName = "ListItem",
                                contentName = "Content" }
    self.emojilistStandByInstance:Create(self.list, CListItem, goChildrenStandBy)

    --setdata

    local datas = self:GetListData(2)
    self.emojilistStandByInstance:SetListData(datas)
end

-- 根据显示页获取渲染数据
function M:GetListData(page)
    -- 1 冰川表情 2 qq表情
    if page == 1 then
        local datas = {}
        local listSpriteGroup = self.spriteGraphic.m_spriteAsset.ListSpriteGroup
        if listSpriteGroup then
            for i = 0, listSpriteGroup.Count - 1 do
                local sg = listSpriteGroup[i]
                if sg.Tag and sg.Tag ~= "" then
                    --Tag为空的图片不显示
                    table.insert(datas, { tag = sg.Tag, id = 0 })
                end
            end
        end
        return datas
    else
        local datas = {}
        --local listSpriteGroup1 = self.spriteGraphic1.m_spriteAsset.ListSpriteGroup
        --local listSpriteGroup2 = self.spriteGraphic2.m_spriteAsset.ListSpriteGroup
        --
        --if listSpriteGroup1 then
        --    for i = 0, listSpriteGroup1.Count - 1 do
        --        local sg = listSpriteGroup1[i]
        --        if sg.Tag and sg.Tag ~= "" then
        --            table.insert(datas, { tag = sg.Tag, id = 1 })
        --        end
        --    end
        --end
        --if listSpriteGroup2 then
        --    for i = 0, listSpriteGroup2.Count - 1 do
        --        local sg = listSpriteGroup2[i]
        --        if sg.Tag and sg.Tag ~= "" then
        --            table.insert(datas, { tag = sg.Tag, id = 2 })
        --        end
        --    end
        --end
        local spriteGraphic3 = self.spriteGraphic3.m_spriteAsset.ListSpriteGroup
        if spriteGraphic3 then
            for i = 0, spriteGraphic3.Count - 1 do
                local sg = spriteGraphic3[i]
                if sg.Tag and sg.Tag ~= "" then
                    table.insert(datas, { tag = sg.Tag, id = 4 })
                end
            end
        end

        return datas
    end
end

function M:EmojiListDispose()
    if self.emojilistInstance then
        self.emojilistInstance:Destroy()
        self.emojilistInstance = nil
    end

    if self.emojilistStandByInstance then
        self.emojilistStandByInstance:Destroy()
        self.emojilistStandByInstance = nil
    end
end
---检查发红包活动是否开启
function M:RefreshChineseNewYearUI()
    local isOpen = festival_activity_mgr.IsActivityOpenByCodeType(festival_activity_cfg.ActivityCodeType.ChatRedPackage)
    self.BtnRedEnvelope.gameObject:SetActive(isOpen)
end

--[[暂时保留 后续去掉，不管从哪个界面进入都是一样的]]
function M:SetInputParam(nType)
    entranceType = nType
end

function SetEntranceType(nType)
    entranceType = nType
end

function GetEntranceType()
    return entranceType
end

function GetCurrentChannel()
    if window and window:IsValid() then
        for i, v in ipairs(window.toggleData) do
            if v.obj.gameObject.activeSelf and v.obj.isOn then
                return v.mapChannel
            end
        end
    end
end

--检测是否展示顶部提示(仅联盟频道显示)
function M:CheckTopTipsDataAndShow()
    if window and window:IsValid() then
        local pageState = chat_mgr_new.GetPageState()
        local enumState = chat_mgr_new.enum_pState
        if pageState == enumState.r4r5 or pageState == enumState.guide or pageState == enumState.allianceNotice then
            local data = ui_chat_data_gw.GetChatTopTipsData()
            if data and data.isShow then
                self.topTipsText.text = data.data.txtInfo
                self.btn_topTipsFunc.onClick:RemoveAllListeners()
                self.btn_topTipsFunc.onClick:AddListener(function()
                    if data.data.clickFunc then
                        data.data.clickFunc()
                    end
                end)
                self.topTips:SetActive(true)
            end
        end
    end
end

function M:TopTipsHide()
    ui_chat_data_gw.SetChatTopTipsDataShow(false)
    self.topTips:SetActive(false)
end

--设置视口遮罩按钮功能为——退出emoji表情选择面板
function M:SetContentMaskBtnFunc_EmojiBack()
    self.btn_contentMask:SetActive(true)
    self.btn_contentMask.onClick:RemoveAllListeners()
    self.btn_contentMask.onClick:AddListener(self.onEmojiBackClick)
end

--------------------------------------
local mClass = class(ui_base, nil, M)

function Show()
    if window == nil then
        window = mClass()
        window._NAME = _NAME;
        if chat_mgr_pro.GetIsUseChatPro() then
            window:LoadUIResource("ui/prefabs/uichatmainpro.prefab", nil, nil, nil, false,true ,nil,false)
        else
            window:LoadUIResource("ui/prefabs/uichatmain.prefab", nil, nil, nil, false,true ,nil,false)
        end
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        if window and not window.recycle_ui then
            window = nil
        end
    end
end


